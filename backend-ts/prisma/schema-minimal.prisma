generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 最小化的用户表
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 最小化的配置表
model Config {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  type      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 最小化的日志表
model Log {
  id        String   @id @default(cuid())
  level     String
  message   String
  data      String?
  createdAt DateTime @default(now())
}
