import { PrismaClient } from '@prisma/client';
import process from 'process';

const prisma = new PrismaClient();

async function main() {
  console.log('开始数据库种子...');

  // 创建基础符号数据
  const btcSymbol = await prisma.symbols.upsert({
    where: { symbol: 'BTC/USDT' },
    update: {},
    create: {
      symbol: 'BTC/USDT',
      baseAsset: 'BTC',
      quoteAsset: 'USDT',
      isActive: true,
    },
  });

  console.log('✅ 符号数据创建完成:', btcSymbol);

  // 注意：不再创建模拟价格数据和历史数据
  // 所有数据必须来自真实的API源
  console.log('⚠️  不创建模拟数据 - 所有数据必须来自真实API源');

  console.log('🎉 数据库种子完成!');
}

main()
  .catch((e) => {
    console.error('❌ 数据库种子失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
