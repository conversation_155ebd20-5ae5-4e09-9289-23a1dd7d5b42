# 数据库管理指南

## 目录结构

```
prisma/
├── migrations/           # Prisma自动生成的迁移文件
│   ├── 20240115000000_add_timeframe_isolated_parameters/
│   ├── 20241223172300_add_ai_call_logging_system/
│   └── ...
├── archive/             # 已执行的历史脚本
├── schema.prisma        # 数据库Schema定义
└── DATABASE_MANAGEMENT.md
```

## 迁移管理原则

### 1. Prisma迁移 (推荐)
用于Schema结构变更，由Prisma自动管理：

```bash
# 创建新迁移
npx prisma migrate dev --name add_new_feature

# 部署迁移到生产环境
npx prisma migrate deploy

# 重置数据库（开发环境）
npx prisma migrate reset
```

### 2. 复杂数据操作
对于复杂的数据操作，建议：

- 优先使用Prisma Client进行数据操作
- 如需原生SQL，在应用代码中使用 `prisma.$executeRaw`
- 避免直接的SQL文件，保持数据操作的可追踪性

## 数据库操作最佳实践

### 复杂数据操作
对于需要复杂数据操作的场景，推荐以下方法：

```typescript
// 使用Prisma Client进行复杂操作
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 原生SQL查询
const result = await prisma.$queryRaw`
  SELECT * FROM "TradingSignals"
  WHERE "confidence" > 0.8
  AND "createdAt" > NOW() - INTERVAL '1 day'
`;

// 原生SQL执行
await prisma.$executeRaw`
  UPDATE "TradingSignals"
  SET "isActive" = false
  WHERE "createdAt" < NOW() - INTERVAL '7 days'
`;
```

## 最佳实践

### Schema变更
1. **优先使用Prisma迁移**
   - 自动版本管理
   - 回滚支持
   - 类型安全

2. **复杂变更分步进行**
   - 避免大型迁移
   - 便于问题定位
   - 降低风险

### 数据操作原则
- 优先使用Prisma Client API
- 复杂查询使用 `$queryRaw` 和 `$executeRaw`
- 保持操作的可追踪性和版本控制
- 避免直接的SQL文件操作

### 安全注意事项
1. **生产环境操作**
   - 必须先在测试环境验证
   - 执行前创建完整备份
   - 在维护窗口期执行

2. **代码审查**
   - 审查所有数据库操作代码
   - 测试回滚方案
   - 记录操作日志

## 数据库维护工具

### 监控脚本
```bash
# 数据库状态检查
tsx ../scripts/monitoring/database-monitor.ts status

# 性能监控
tsx ../scripts/monitoring/database-monitor.ts health
```

### 备份脚本
```bash
# 创建备份
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复备份
psql $DATABASE_URL < backup_file.sql
```

## 故障排除

### 迁移失败
```bash
# 查看迁移状态
npx prisma migrate status

# 标记迁移为已应用（谨慎使用）
npx prisma migrate resolve --applied "migration_name"

# 回滚到特定迁移
npx prisma migrate reset
```

### Schema不一致
```bash
# 从数据库拉取Schema
npx prisma db pull

# 推送Schema到数据库
npx prisma db push
```

## 版本控制

### 提交规范
- Prisma迁移文件必须提交
- 手写SQL脚本提交前需要审查
- 包含详细的提交说明

### 分支策略
- 数据库变更在独立分支开发
- 合并前进行完整测试
- 生产部署前进行最终验证

## 监控和告警

### 关键指标
- 迁移执行时间
- 数据库连接数
- 查询性能
- 存储空间使用

### 告警设置
- 迁移失败告警
- 性能异常告警
- 连接数过高告警

## 历史记录

### 2025-07-05 - 数据库管理规范化
- 移除过时的手写SQL脚本（命名规范已修复）
- 建立基于Prisma的数据库操作规范
- 创建数据库管理文档
