#!/usr/bin/env ts-node

/**
 * 验证冗余组件清理完整性
 * 检查项目中的引用，确保统一组件是唯一使用的
 */

import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface ReferenceCheck {
  file: string;
  line: number;
  content: string;
  issue: string;
}

interface CleanupVerificationResult {
  summary: {
    totalFiles: number;
    checkedFiles: number;
    issuesFound: number;
    cleanupSuccess: boolean;
  };
  issues: ReferenceCheck[];
  deprecatedReferences: ReferenceCheck[];
  movedFileReferences: ReferenceCheck[];
  unifiedComponentUsage: {
    component: string;
    usageCount: number;
    files: string[];
  }[];
}

class CleanupVerificationTool {
  private projectRoot: string;
  private result: CleanupVerificationResult;

  constructor() {
    this.projectRoot = process.cwd();
    this.result = {
      summary: {
        totalFiles: 0,
        checkedFiles: 0,
        issuesFound: 0,
        cleanupSuccess: true
      },
      issues: [],
      deprecatedReferences: [],
      movedFileReferences: [],
      unifiedComponentUsage: []
    };
  }

  async verify(): Promise<void> {
    console.log('🔍 开始验证冗余组件清理完整性...\n');

    // 1. 检查已移动文件的引用
    await this.checkMovedFileReferences();

    // 2. 检查已弃用服务的引用
    await this.checkDeprecatedServiceReferences();

    // 3. 验证统一组件使用情况
    await this.verifyUnifiedComponentUsage();

    // 4. 检查环境管理器导入路径
    await this.checkEnvironmentManagerImports();

    // 5. 检查Express启动文件引用
    await this.checkExpressStartupReferences();

    // 6. 生成报告
    await this.generateReport();
  }

  /**
   * 检查已移动文件的引用
   */
  private async checkMovedFileReferences(): Promise<void> {
    console.log('1️⃣ 检查已移动文件的引用...');

    const movedFiles = [
      'src/express-main-simple.ts',
      'src/express-main-minimal.ts', 
      'src/express-main-debug.ts'
    ];

    const sourceFiles = await glob('src/**/*.ts', {
      ignore: ['src/**/*.test.ts', 'src/**/*.spec.ts']
    });

    this.result.summary.totalFiles = sourceFiles.length;

    for (const sourceFile of sourceFiles) {
      this.result.summary.checkedFiles++;
      const content = fs.readFileSync(sourceFile, 'utf8');
      const lines = content.split('\n');

      lines.forEach((line, index) => {
        for (const movedFile of movedFiles) {
          if (line.includes(movedFile) && !line.trim().startsWith('//')) {
            this.result.movedFileReferences.push({
              file: sourceFile,
              line: index + 1,
              content: line.trim(),
              issue: `引用了已移动的文件: ${movedFile}`
            });
            this.result.summary.issuesFound++;
          }
        }
      });
    }

    console.log(`   检查了 ${sourceFiles.length} 个文件`);
    console.log(`   发现 ${this.result.movedFileReferences.length} 个已移动文件的引用\n`);
  }

  /**
   * 检查已弃用服务的引用
   */
  private async checkDeprecatedServiceReferences(): Promise<void> {
    console.log('2️⃣ 检查已弃用服务的引用...');

    const deprecatedServices = [
      'ShortTermLearningService',
      'LongTermLearningService'
    ];

    const sourceFiles = await glob('src/**/*.ts', {
      ignore: [
        'src/**/*.test.ts', 
        'src/**/*.spec.ts',
        'src/application/short-term-learning-service.ts',
        'src/application/long-term-learning-service.ts'
      ]
    });

    for (const sourceFile of sourceFiles) {
      const content = fs.readFileSync(sourceFile, 'utf8');
      const lines = content.split('\n');

      lines.forEach((line, index) => {
        for (const service of deprecatedServices) {
          if (line.includes(service) && !line.trim().startsWith('//')) {
            this.result.deprecatedReferences.push({
              file: sourceFile,
              line: index + 1,
              content: line.trim(),
              issue: `引用了已弃用的服务: ${service}`
            });
            this.result.summary.issuesFound++;
          }
        }
      });
    }

    console.log(`   发现 ${this.result.deprecatedReferences.length} 个已弃用服务的引用\n`);
  }

  /**
   * 验证统一组件使用情况
   */
  private async verifyUnifiedComponentUsage(): Promise<void> {
    console.log('3️⃣ 验证统一组件使用情况...');

    const unifiedComponents = [
      'UnifiedEnvironmentManager',
      'UnifiedConfigManager',
      'UnifiedAIServiceManager',
      'UnifiedDataMapper',
      'UnifiedLogger',
      'UnifiedMonitoringManager',
      'UnifiedLearningServiceManager'
    ];

    const sourceFiles = await glob('src/**/*.ts', {
      ignore: ['src/**/*.test.ts', 'src/**/*.spec.ts']
    });

    for (const component of unifiedComponents) {
      const usage = {
        component,
        usageCount: 0,
        files: [] as string[]
      };

      for (const sourceFile of sourceFiles) {
        const content = fs.readFileSync(sourceFile, 'utf8');
        if (content.includes(component)) {
          usage.usageCount++;
          usage.files.push(sourceFile);
        }
      }

      this.result.unifiedComponentUsage.push(usage);
      console.log(`   ${component}: ${usage.usageCount} 次使用`);
    }

    console.log();
  }

  /**
   * 检查环境管理器导入路径
   */
  private async checkEnvironmentManagerImports(): Promise<void> {
    console.log('4️⃣ 检查环境管理器导入路径...');

    const sourceFiles = await glob('src/**/*.ts', {
      ignore: ['src/**/*.test.ts', 'src/**/*.spec.ts']
    });

    const wrongImportPatterns = [
      'from \'../../config/config-validation\'',
      'from \'../config/config-validation\'',
      'from \'./config/config-validation\'',
      'from \'../../../../shared/infrastructure/config/config-validation\''
    ];

    for (const sourceFile of sourceFiles) {
      const content = fs.readFileSync(sourceFile, 'utf8');
      const lines = content.split('\n');

      lines.forEach((line, index) => {
        if (line.includes('UnifiedEnvironmentManager')) {
          for (const pattern of wrongImportPatterns) {
            if (line.includes(pattern)) {
              this.result.issues.push({
                file: sourceFile,
                line: index + 1,
                content: line.trim(),
                issue: '使用了错误的环境管理器导入路径'
              });
              this.result.summary.issuesFound++;
            }
          }
        }
      });
    }

    console.log(`   发现 ${this.result.issues.filter(i => i.issue.includes('环境管理器')).length} 个错误的导入路径\n`);
  }

  /**
   * 检查Express启动文件引用
   */
  private async checkExpressStartupReferences(): Promise<void> {
    console.log('5️⃣ 检查Express启动文件引用...');

    const packageJsonPath = path.join(this.projectRoot, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      // 检查scripts中是否还有对已移动文件的引用
      const scripts = packageJson.scripts || {};
      for (const [scriptName, scriptCommand] of Object.entries(scripts)) {
        if (typeof scriptCommand === 'string') {
          if (scriptCommand.includes('express-main-simple') || 
              scriptCommand.includes('express-main-minimal') || 
              scriptCommand.includes('express-main-debug')) {
            this.result.issues.push({
              file: 'package.json',
              line: 0,
              content: `${scriptName}: ${scriptCommand}`,
              issue: 'package.json中引用了已移动的Express启动文件'
            });
            this.result.summary.issuesFound++;
          }
        }
      }
    }

    console.log(`   检查了package.json脚本配置\n`);
  }

  /**
   * 生成验证报告
   */
  private async generateReport(): Promise<void> {
    console.log('📊 生成验证报告...\n');

    this.result.summary.cleanupSuccess = this.result.summary.issuesFound === 0;

    // 控制台输出
    console.log('=' .repeat(80));
    console.log('🎯 冗余组件清理验证报告');
    console.log('=' .repeat(80));

    console.log('\n📊 总体情况:');
    console.log(`   检查文件数: ${this.result.summary.checkedFiles}`);
    console.log(`   发现问题数: ${this.result.summary.issuesFound}`);
    console.log(`   清理状态: ${this.result.summary.cleanupSuccess ? '✅ 成功' : '❌ 需要修复'}`);

    if (this.result.movedFileReferences.length > 0) {
      console.log('\n🚨 已移动文件的引用问题:');
      this.result.movedFileReferences.forEach(ref => {
        console.log(`   ❌ ${ref.file}:${ref.line} - ${ref.issue}`);
        console.log(`      ${ref.content}`);
      });
    }

    if (this.result.deprecatedReferences.length > 0) {
      console.log('\n🚨 已弃用服务的引用问题:');
      this.result.deprecatedReferences.forEach(ref => {
        console.log(`   ❌ ${ref.file}:${ref.line} - ${ref.issue}`);
        console.log(`      ${ref.content}`);
      });
    }

    if (this.result.issues.length > 0) {
      console.log('\n🚨 其他问题:');
      this.result.issues.forEach(issue => {
        console.log(`   ❌ ${issue.file}:${issue.line} - ${issue.issue}`);
        console.log(`      ${issue.content}`);
      });
    }

    console.log('\n📈 统一组件使用统计:');
    this.result.unifiedComponentUsage.forEach(usage => {
      const status = usage.usageCount > 0 ? '✅' : '⚠️';
      console.log(`   ${status} ${usage.component}: ${usage.usageCount} 次使用`);
    });

    if (this.result.summary.cleanupSuccess) {
      console.log('\n🎉 恭喜！冗余组件清理完全成功！');
      console.log('   ✅ 所有已移动文件的引用已清理');
      console.log('   ✅ 所有已弃用服务的引用已清理');
      console.log('   ✅ 统一组件正在被正确使用');
      console.log('   ✅ 导入路径已统一');
    } else {
      console.log('\n⚠️ 发现需要修复的问题，请查看上述详细信息');
    }

    // 保存详细报告到文件
    const reportPath = path.join(this.projectRoot, 'docs/developer-guides/cleanup-verification-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.result, null, 2));
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  }
}

// 执行验证
async function main() {
  const verifier = new CleanupVerificationTool();
  await verifier.verify();
}

if (require.main === module) {
  main().catch(console.error);
}

export { CleanupVerificationTool };
