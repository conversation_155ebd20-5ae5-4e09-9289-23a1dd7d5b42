#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import { glob } from 'glob';

const prisma = new PrismaClient();

interface ValidationResult {
  category: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
}

async function comprehensiveDatabaseValidation() {
  const results: ValidationResult[] = [];
  
  try {
    console.log('🔍 开始全面数据库验证...\n');
    
    // 1. 验证数据库连接
    console.log('1️⃣ 验证数据库连接...');
    try {
      await prisma.$connect();
      results.push({
        category: '数据库连接',
        status: 'PASS',
        message: '数据库连接成功'
      });
    } catch (error) {
      results.push({
        category: '数据库连接',
        status: 'FAIL',
        message: '数据库连接失败',
        details: error
      });
      return results;
    }
    
    // 2. 验证表结构一致性
    console.log('2️⃣ 验证表结构一致性...');
    const dbTables = await prisma.$queryRaw<Array<{table_name: string}>>`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    const dbTableNames = new Set(dbTables.map(t => t.table_name));
    
    // 读取Prisma schema中的模型
    const schemaPath = 'prisma/schema.prisma';
    const schemaContent = fs.readFileSync(schemaPath, 'utf-8');
    const modelMatches = schemaContent.matchAll(/^model\s+(\w+)\s*{/gm);
    const schemaModels = Array.from(modelMatches).map(match => match[1]);
    
    if (dbTableNames.size === schemaModels.length) {
      results.push({
        category: '表结构一致性',
        status: 'PASS',
        message: `数据库表数量与Schema模型数量一致 (${dbTableNames.size}个)`
      });
    } else {
      results.push({
        category: '表结构一致性',
        status: 'FAIL',
        message: `数据库表数量(${dbTableNames.size})与Schema模型数量(${schemaModels.length})不一致`
      });
    }
    
    // 3. 验证代码中的Prisma引用
    console.log('3️⃣ 验证代码中的Prisma引用...');
    const codeFiles = await glob('src/**/*.ts', { 
      ignore: ['**/*.test.ts', '**/*.spec.ts', '**/node_modules/**'] 
    });
    
    const camelToPascalMap = new Map<string, string>();
    for (const tableName of dbTableNames) {
      const camelCase = tableName.charAt(0).toLowerCase() + tableName.slice(1);
      camelToPascalMap.set(camelCase, tableName);
    }
    
    let totalPrismaReferences = 0;
    let invalidReferences = 0;
    const invalidRefs: string[] = [];
    
    for (const file of codeFiles) {
      const content = fs.readFileSync(file, 'utf-8');
      const prismaMatches = content.matchAll(/prisma\.([a-zA-Z][a-zA-Z0-9]*)/g);
      
      for (const match of prismaMatches) {
        const tableName = match[1];
        if (['$connect', '$disconnect', '$queryRaw', '$executeRaw', '$transaction'].includes(tableName)) {
          continue;
        }
        
        totalPrismaReferences++;
        
        if (!camelToPascalMap.has(tableName)) {
          invalidReferences++;
          invalidRefs.push(`${file}: prisma.${tableName}`);
        }
      }
    }
    
    if (invalidReferences === 0) {
      results.push({
        category: 'Prisma引用验证',
        status: 'PASS',
        message: `所有${totalPrismaReferences}个Prisma引用都有效`
      });
    } else {
      results.push({
        category: 'Prisma引用验证',
        status: 'FAIL',
        message: `发现${invalidReferences}个无效的Prisma引用`,
        details: invalidRefs.slice(0, 10) // 只显示前10个
      });
    }
    
    // 4. 验证关键表的存在
    console.log('4️⃣ 验证关键表的存在...');
    const criticalTables = [
      'Users', 'TradingAccounts', 'Symbols', 'PriceData', 'TradingSignals',
      'AuditLog', 'UserProfiles', 'UserPreferences', 'ReasoningSessions',
      'DecisionPaths', 'ConfigHistory'
    ];
    
    const missingCriticalTables = criticalTables.filter(table => !dbTableNames.has(table));
    
    if (missingCriticalTables.length === 0) {
      results.push({
        category: '关键表验证',
        status: 'PASS',
        message: '所有关键表都存在'
      });
    } else {
      results.push({
        category: '关键表验证',
        status: 'FAIL',
        message: `缺少关键表: ${missingCriticalTables.join(', ')}`
      });
    }
    
    // 5. 验证表的基本结构
    console.log('5️⃣ 验证表的基本结构...');
    const sampleTables = ['Users', 'AuditLog', 'UserProfiles'];
    let structureIssues = 0;
    
    for (const tableName of sampleTables) {
      if (dbTableNames.has(tableName)) {
        try {
          const columns = await prisma.$queryRaw<Array<{column_name: string, data_type: string}>>`
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = ${tableName} AND table_schema = 'public'
          `;
          
          if (columns.length === 0) {
            structureIssues++;
          }
        } catch (error) {
          structureIssues++;
        }
      }
    }
    
    if (structureIssues === 0) {
      results.push({
        category: '表结构验证',
        status: 'PASS',
        message: '样本表结构验证通过'
      });
    } else {
      results.push({
        category: '表结构验证',
        status: 'WARNING',
        message: `${structureIssues}个表结构存在问题`
      });
    }
    
    // 6. 验证Prisma客户端生成状态
    console.log('6️⃣ 验证Prisma客户端生成状态...');
    try {
      // 尝试访问一个简单的模型
      await prisma.users.findFirst({ take: 1 });
      results.push({
        category: 'Prisma客户端',
        status: 'PASS',
        message: 'Prisma客户端正常工作'
      });
    } catch (error) {
      results.push({
        category: 'Prisma客户端',
        status: 'FAIL',
        message: 'Prisma客户端存在问题',
        details: error
      });
    }
    
  } catch (error) {
    results.push({
      category: '验证过程',
      status: 'FAIL',
      message: '验证过程中发生错误',
      details: error
    });
  } finally {
    await prisma.$disconnect();
  }
  
  // 输出结果
  console.log('\n📊 验证结果汇总:\n');
  
  const passCount = results.filter(r => r.status === 'PASS').length;
  const failCount = results.filter(r => r.status === 'FAIL').length;
  const warningCount = results.filter(r => r.status === 'WARNING').length;
  
  results.forEach(result => {
    const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${icon} ${result.category}: ${result.message}`);
    if (result.details && result.status !== 'PASS') {
      console.log(`   详情: ${JSON.stringify(result.details, null, 2)}`);
    }
  });
  
  console.log(`\n📈 总结:`);
  console.log(`   ✅ 通过: ${passCount}`);
  console.log(`   ❌ 失败: ${failCount}`);
  console.log(`   ⚠️  警告: ${warningCount}`);
  
  if (failCount === 0) {
    console.log('\n🎉 数据库验证完全通过！');
  } else {
    console.log('\n🚨 发现问题，需要修复！');
  }
  
  return results;
}

comprehensiveDatabaseValidation();
