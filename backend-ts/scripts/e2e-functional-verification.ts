#!/usr/bin/env ts-node

/**
 * 端到端功能验证脚本
 * 验证关键业务流程的实际执行情况，独立于单元测试
 */

import * as fs from 'fs';
import * as path from 'path';

interface FunctionalTest {
  name: string;
  description: string;
  test: () => Promise<TestResult>;
  critical: boolean;
}

interface TestResult {
  success: boolean;
  message: string;
  executionTime: number;
  details?: any;
  errors?: string[];
}

class E2EFunctionalVerifier {
  private tests: FunctionalTest[] = [];
  private results: Map<string, TestResult> = new Map();

  constructor() {
    this.setupTests();
  }

  private setupTests() {
    this.tests = [
      {
        name: 'module-imports',
        description: '核心模块导入测试',
        test: this.testModuleImports.bind(this),
        critical: true
      },
      {
        name: 'di-container',
        description: '依赖注入容器初始化测试',
        test: this.testDIContainer.bind(this),
        critical: true
      },
      {
        name: 'database-connection',
        description: '数据库连接测试',
        test: this.testDatabaseConnection.bind(this),
        critical: true
      },
      {
        name: 'unified-components',
        description: '统一组件实例化测试',
        test: this.testUnifiedComponents.bind(this),
        critical: true
      },
      {
        name: 'api-routes-loading',
        description: 'API路由加载测试',
        test: this.testApiRoutesLoading.bind(this),
        critical: false
      },
      {
        name: 'business-logic-flow',
        description: '核心业务逻辑流程测试',
        test: this.testBusinessLogicFlow.bind(this),
        critical: false
      }
    ];
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 开始端到端功能验证...\n');

    for (const test of this.tests) {
      console.log(`⏳ 测试: ${test.description}`);
      const startTime = Date.now();
      
      try {
        const result = await test.test();
        result.executionTime = Date.now() - startTime;
        this.results.set(test.name, result);
        
        const status = result.success ? '✅' : (test.critical ? '🔴' : '🟡');
        console.log(`${status} ${test.description}: ${result.message} (${result.executionTime}ms)`);
        
        if (result.errors && result.errors.length > 0) {
          console.log(`   错误: ${result.errors.slice(0, 3).join(', ')}`);
        }
      } catch (error) {
        const result: TestResult = {
          success: false,
          message: `测试执行失败: ${error.message}`,
          executionTime: Date.now() - startTime,
          errors: [error.stack]
        };
        this.results.set(test.name, result);
        
        const status = test.critical ? '🔴' : '🟡';
        console.log(`${status} ${test.description}: ${result.message}`);
      }
      console.log('');
    }

    this.generateReport();
  }

  private async testModuleImports(): Promise<TestResult> {
    const errors: string[] = [];
    const modules = [
      'src/shared/infrastructure/di/modular-container-manager',
      'src/shared/infrastructure/logging/unified-logger',
      'src/shared/infrastructure/database/unified-data-mapper',
      'src/shared/application/services/base-application-service'
    ];

    for (const modulePath of modules) {
      try {
        // 尝试动态导入模块
        const fullPath = path.resolve(modulePath + '.ts');
        if (!fs.existsSync(fullPath)) {
          errors.push(`模块文件不存在: ${modulePath}`);
          continue;
        }

        // 检查语法错误（通过读取文件内容）
        const content = fs.readFileSync(fullPath, 'utf8');
        if (content.includes('export') && content.includes('class')) {
          // 基本语法检查通过
        } else {
          errors.push(`模块语法可能有问题: ${modulePath}`);
        }
      } catch (error) {
        errors.push(`导入失败 ${modulePath}: ${error.message}`);
      }
    }

    return {
      success: errors.length === 0,
      message: errors.length === 0 ? '所有核心模块导入正常' : `${errors.length}个模块导入失败`,
      executionTime: 0,
      errors
    };
  }

  private async testDIContainer(): Promise<TestResult> {
    try {
      // 检查DI容器相关文件
      const containerFiles = [
        'src/shared/infrastructure/di/modular-container-manager.ts',
        'src/shared/infrastructure/di/base/base-container-module.ts',
        'src/shared/infrastructure/di/types.ts'
      ];

      const missing = containerFiles.filter(file => !fs.existsSync(file));
      
      if (missing.length > 0) {
        return {
          success: false,
          message: `DI容器文件缺失: ${missing.length}个`,
          executionTime: 0,
          errors: missing
        };
      }

      // 检查类型定义
      const typesContent = fs.readFileSync('src/shared/infrastructure/di/types.ts', 'utf8');
      if (!typesContent.includes('TYPES') || !typesContent.includes('export')) {
        return {
          success: false,
          message: 'DI类型定义不完整',
          executionTime: 0,
          errors: ['TYPES对象定义缺失']
        };
      }

      return {
        success: true,
        message: 'DI容器文件结构完整',
        executionTime: 0
      };
    } catch (error) {
      return {
        success: false,
        message: `DI容器检查失败: ${error.message}`,
        executionTime: 0,
        errors: [error.stack]
      };
    }
  }

  private async testDatabaseConnection(): Promise<TestResult> {
    try {
      // 检查Prisma相关文件
      const prismaFiles = [
        'prisma/schema.prisma',
        'src/shared/infrastructure/database/unified-data-mapper.ts'
      ];

      const missing = prismaFiles.filter(file => !fs.existsSync(file));
      
      if (missing.length > 0) {
        return {
          success: false,
          message: `数据库相关文件缺失: ${missing.length}个`,
          executionTime: 0,
          errors: missing
        };
      }

      // 检查环境变量
      if (!process.env.DATABASE_URL) {
        return {
          success: false,
          message: 'DATABASE_URL环境变量未设置',
          executionTime: 0,
          errors: ['缺少DATABASE_URL']
        };
      }

      return {
        success: true,
        message: '数据库配置文件完整',
        executionTime: 0
      };
    } catch (error) {
      return {
        success: false,
        message: `数据库检查失败: ${error.message}`,
        executionTime: 0,
        errors: [error.stack]
      };
    }
  }

  private async testUnifiedComponents(): Promise<TestResult> {
    const errors: string[] = [];
    const components = [
      'src/shared/application/services/base-application-service.ts',
      'src/shared/domain/entities/base-entity.ts',
      'src/shared/infrastructure/database/repository-base-service.ts',
      'src/shared/infrastructure/logging/unified-logger.ts'
    ];

    for (const component of components) {
      if (!fs.existsSync(component)) {
        errors.push(`统一组件缺失: ${component}`);
        continue;
      }

      const content = fs.readFileSync(component, 'utf8');
      if (!content.includes('export') || content.includes('// TODO') && content.includes('throw new Error')) {
        errors.push(`统一组件可能未完成: ${component}`);
      }
    }

    return {
      success: errors.length === 0,
      message: errors.length === 0 ? '统一组件文件完整' : `${errors.length}个组件有问题`,
      executionTime: 0,
      errors
    };
  }

  private async testApiRoutesLoading(): Promise<TestResult> {
    try {
      const routeFiles = [
        'src/api/routes/index.ts',
        'src/api/controllers'
      ];

      const missing = routeFiles.filter(file => !fs.existsSync(file));
      
      if (missing.length > 0) {
        return {
          success: false,
          message: `API路由文件缺失: ${missing.length}个`,
          executionTime: 0,
          errors: missing
        };
      }

      return {
        success: true,
        message: 'API路由文件存在',
        executionTime: 0
      };
    } catch (error) {
      return {
        success: false,
        message: `API路由检查失败: ${error.message}`,
        executionTime: 0,
        errors: [error.stack]
      };
    }
  }

  private async testBusinessLogicFlow(): Promise<TestResult> {
    // 这里只做基本的文件存在性检查，不实际执行业务逻辑
    const businessModules = [
      'src/contexts/trading-signals',
      'src/contexts/market-data',
      'src/contexts/risk-management'
    ];

    const missing = businessModules.filter(module => !fs.existsSync(module));
    
    if (missing.length > 0) {
      return {
        success: false,
        message: `业务模块缺失: ${missing.length}个`,
        executionTime: 0,
        errors: missing
      };
    }

    return {
      success: true,
      message: '业务模块目录存在',
      executionTime: 0
    };
  }

  private generateReport(): void {
    console.log('\n📊 端到端功能验证报告');
    console.log('='.repeat(50));

    const totalTests = this.results.size;
    const successCount = Array.from(this.results.values()).filter(r => r.success).length;
    const criticalFailures = Array.from(this.results.entries())
      .filter(([name, result]) => !result.success && this.tests.find(t => t.name === name)?.critical)
      .length;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${successCount}`);
    console.log(`失败: ${totalTests - successCount}`);
    console.log(`严重失败: ${criticalFailures}`);

    if (criticalFailures > 0) {
      console.log('\n🚨 严重问题阻止系统正常运行:');
      for (const [name, result] of this.results) {
        const test = this.tests.find(t => t.name === name);
        if (!result.success && test?.critical) {
          console.log(`  - ${test.description}: ${result.message}`);
        }
      }
      console.log('\n⚠️  建议: 在修复测试之前，必须先解决这些严重问题');
    } else {
      console.log('\n✅ 系统基本功能正常，可以开始测试修复工作');
    }

    // 保存报告
    const reportPath = 'e2e-functional-verification-report.json';
    const report = {
      timestamp: new Date().toISOString(),
      summary: { totalTests, successCount, criticalFailures },
      results: Object.fromEntries(this.results)
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  }
}

// 运行验证
if (require.main === module) {
  const verifier = new E2EFunctionalVerifier();
  verifier.runAllTests().catch(console.error);
}

export { E2EFunctionalVerifier };
