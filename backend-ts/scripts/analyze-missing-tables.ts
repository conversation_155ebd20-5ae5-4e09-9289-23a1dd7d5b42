#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import { glob } from 'glob';

const prisma = new PrismaClient();

interface TableAnalysis {
  tableName: string;
  usageCount: number;
  files: string[];
  recommendation: 'CREATE_TABLE' | 'MODIFY_CODE' | 'UNCLEAR';
  reason: string;
  suggestedFix?: string;
}

async function analyzeMissingTables() {
  try {
    console.log('🔍 分析缺失表的处理方案...\n');
    
    // 获取数据库中实际存在的表
    const existingTables = await prisma.$queryRaw<Array<{table_name: string}>>`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    const existingTableNames = new Set(existingTables.map(t => t.table_name));
    
    // 创建camelCase到PascalCase的映射
    const camelToPascalMap = new Map<string, string>();
    for (const tableName of existingTableNames) {
      const camelCase = tableName.charAt(0).toLowerCase() + tableName.slice(1);
      camelToPascalMap.set(camelCase, tableName);
    }
    
    // 扫描代码中的prisma.xxx引用
    const codeFiles = await glob('src/**/*.ts', { 
      ignore: ['**/*.test.ts', '**/*.spec.ts', '**/node_modules/**'] 
    });
    
    const referencedTables = new Map<string, string[]>();
    
    for (const file of codeFiles) {
      const content = fs.readFileSync(file, 'utf-8');
      const prismaMatches = content.matchAll(/prisma\.([a-zA-Z][a-zA-Z0-9]*)/g);
      
      for (const match of prismaMatches) {
        const tableName = match[1];
        if (['$connect', '$disconnect', '$queryRaw', '$executeRaw', '$transaction'].includes(tableName)) {
          continue;
        }
        
        if (!referencedTables.has(tableName)) {
          referencedTables.set(tableName, []);
        }
        referencedTables.get(tableName)!.push(file);
      }
    }
    
    // 分析每个缺失的表
    const analyses: TableAnalysis[] = [];
    
    for (const [tableName, files] of referencedTables.entries()) {
      if (!camelToPascalMap.has(tableName)) {
        const analysis = analyzeTable(tableName, files, existingTableNames);
        analyses.push(analysis);
      }
    }
    
    // 按推荐类型分组输出
    const createTables = analyses.filter(a => a.recommendation === 'CREATE_TABLE');
    const modifyCode = analyses.filter(a => a.recommendation === 'MODIFY_CODE');
    const unclear = analyses.filter(a => a.recommendation === 'UNCLEAR');
    
    console.log('📋 需要创建表的名单:\n');
    createTables.forEach(analysis => {
      console.log(`🆕 ${analysis.tableName} (使用${analysis.usageCount}次)`);
      console.log(`   原因: ${analysis.reason}`);
      console.log(`   文件: ${analysis.files.slice(0, 2).join(', ')}${analysis.files.length > 2 ? '...' : ''}`);
      console.log('');
    });
    
    console.log('🔧 需要修改代码的名单:\n');
    modifyCode.forEach(analysis => {
      console.log(`🔄 ${analysis.tableName} (使用${analysis.usageCount}次)`);
      console.log(`   原因: ${analysis.reason}`);
      if (analysis.suggestedFix) {
        console.log(`   建议: ${analysis.suggestedFix}`);
      }
      console.log(`   文件: ${analysis.files.slice(0, 2).join(', ')}${analysis.files.length > 2 ? '...' : ''}`);
      console.log('');
    });
    
    console.log('❓ 需要进一步分析的名单:\n');
    unclear.forEach(analysis => {
      console.log(`❓ ${analysis.tableName} (使用${analysis.usageCount}次)`);
      console.log(`   原因: ${analysis.reason}`);
      console.log(`   文件: ${analysis.files.slice(0, 2).join(', ')}${analysis.files.length > 2 ? '...' : ''}`);
      console.log('');
    });
    
    // 统计信息
    console.log('📊 统计信息:');
    console.log(`   需要创建表: ${createTables.length}`);
    console.log(`   需要修改代码: ${modifyCode.length}`);
    console.log(`   需要进一步分析: ${unclear.length}`);
    
  } catch (error) {
    console.error('分析过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function analyzeTable(tableName: string, files: string[], existingTables: Set<string>): TableAnalysis {
  const usageCount = files.length;
  const uniqueFiles = [...new Set(files)];
  
  // 分析逻辑
  
  // 1. 用户相关表 - 可能需要修改代码
  if (tableName.toLowerCase().includes('user')) {
    if (tableName === 'userProfiles' || tableName === 'UserProfiles') {
      return {
        tableName,
        usageCount,
        files: uniqueFiles,
        recommendation: 'MODIFY_CODE',
        reason: '应该使用现有的Users表',
        suggestedFix: '将userProfiles/UserProfiles改为users'
      };
    }
    
    if (tableName === 'userPreferences' || tableName === 'UserPreferences') {
      return {
        tableName,
        usageCount,
        files: uniqueFiles,
        recommendation: 'CREATE_TABLE',
        reason: '用户偏好设置是独立的业务概念，需要专门的表'
      };
    }
    
    if (tableName === 'userActivities') {
      return {
        tableName,
        usageCount,
        files: uniqueFiles,
        recommendation: 'MODIFY_CODE',
        reason: '可能应该使用SecurityEvents表',
        suggestedFix: '检查是否可以用securityEvents替代'
      };
    }
  }
  
  // 2. 审计日志
  if (tableName === 'auditLog') {
    return {
      tableName,
      usageCount,
      files: uniqueFiles,
      recommendation: 'CREATE_TABLE',
      reason: '审计日志是重要的合规功能，需要专门的表'
    };
  }
  
  // 3. 配置相关
  if (tableName.includes('config') || tableName.includes('Config')) {
    return {
      tableName,
      usageCount,
      files: uniqueFiles,
      recommendation: 'CREATE_TABLE',
      reason: '配置管理需要历史记录和版本控制'
    };
  }
  
  // 4. AI推理相关
  if (tableName.includes('reasoning') || tableName.includes('decision') || tableName.includes('ai')) {
    return {
      tableName,
      usageCount,
      files: uniqueFiles,
      recommendation: 'CREATE_TABLE',
      reason: 'AI推理追踪是核心功能，需要专门的表'
    };
  }
  
  // 5. 预测相关
  if (tableName.includes('prediction') || tableName.includes('Prediction')) {
    return {
      tableName,
      usageCount,
      files: uniqueFiles,
      recommendation: 'MODIFY_CODE',
      reason: '可能应该使用现有的预测表',
      suggestedFix: '检查是否可以使用ShortCyclePredictions, LongCyclePredictions等现有表'
    };
  }
  
  // 6. 其他情况
  return {
    tableName,
    usageCount,
    files: uniqueFiles,
    recommendation: 'UNCLEAR',
    reason: '需要进一步分析业务需求'
  };
}

analyzeMissingTables();
