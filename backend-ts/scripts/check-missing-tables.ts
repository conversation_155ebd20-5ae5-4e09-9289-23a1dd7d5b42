#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

const prisma = new PrismaClient();

interface MissingTableInfo {
  tableName: string;
  referencedIn: string[];
  usageCount: number;
}

async function checkMissingTables() {
  try {
    console.log('🔍 检查代码中引用但数据库中不存在的表...\n');
    
    // 1. 获取数据库中实际存在的表
    const existingTables = await prisma.$queryRaw<Array<{table_name: string}>>`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    const existingTableNames = new Set(existingTables.map(t => t.table_name));
    console.log(`数据库中存在 ${existingTableNames.size} 个表`);
    
    // 2. 扫描代码中的prisma.xxx引用
    const codeFiles = await glob('src/**/*.ts', { 
      ignore: ['**/*.test.ts', '**/*.spec.ts', '**/node_modules/**'] 
    });
    
    const referencedTables = new Map<string, string[]>();
    
    for (const file of codeFiles) {
      const content = fs.readFileSync(file, 'utf-8');
      
      // 匹配 prisma.tableName 模式
      const prismaMatches = content.matchAll(/prisma\.([a-zA-Z][a-zA-Z0-9]*)/g);
      
      for (const match of prismaMatches) {
        const tableName = match[1];
        
        // 跳过明显的方法调用
        if (['$connect', '$disconnect', '$queryRaw', '$executeRaw', '$transaction'].includes(tableName)) {
          continue;
        }
        
        if (!referencedTables.has(tableName)) {
          referencedTables.set(tableName, []);
        }
        referencedTables.get(tableName)!.push(file);
      }
    }
    
    // 3. 找出缺失的表
    const missingTables: MissingTableInfo[] = [];

    // 创建一个映射：camelCase -> PascalCase
    const camelToPascalMap = new Map<string, string>();
    for (const tableName of existingTableNames) {
      const camelCase = tableName.charAt(0).toLowerCase() + tableName.slice(1);
      camelToPascalMap.set(camelCase, tableName);
    }

    for (const [tableName, files] of referencedTables.entries()) {
      // 检查camelCase名称是否对应存在的PascalCase表
      if (!camelToPascalMap.has(tableName)) {
        missingTables.push({
          tableName,
          referencedIn: [...new Set(files)], // 去重
          usageCount: files.length
        });
      }
    }
    
    // 4. 输出结果
    if (missingTables.length === 0) {
      console.log('✅ 没有发现缺失的表！');
    } else {
      console.log(`❌ 发现 ${missingTables.length} 个缺失的表:\n`);
      
      missingTables.sort((a, b) => b.usageCount - a.usageCount);
      
      for (const missing of missingTables) {
        console.log(`📋 表名: ${missing.tableName}`);
        console.log(`   使用次数: ${missing.usageCount}`);
        console.log(`   引用文件:`);
        missing.referencedIn.forEach(file => {
          console.log(`     - ${file}`);
        });
        console.log('');
      }
    }
    
    // 5. 生成修复建议
    if (missingTables.length > 0) {
      console.log('🔧 修复建议:');
      console.log('1. 检查这些表是否应该存在于Prisma schema中');
      console.log('2. 如果需要，添加相应的模型定义到prisma/schema.prisma');
      console.log('3. 运行 npm run db:generate && npm run db:push 更新数据库');
      console.log('4. 如果表不需要，检查代码中的引用是否正确');
    }
    
  } catch (error) {
    console.error('检查过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkMissingTables();
