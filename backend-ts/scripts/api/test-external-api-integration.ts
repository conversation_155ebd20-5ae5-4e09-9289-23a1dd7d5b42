/**
 * 外部API集成测试脚本
 * 测试Binance、OKX等交易所API的连接状态
 */

import 'reflect-metadata';
import { config } from 'dotenv';
import { getLogger } from '../../src/config/logging';

// 加载环境变量
config();

const logger = getLogger('external-api-test');

async function testExternalAPIIntegration() {
  console.log('🌐 开始外部API集成测试...\n');

  try {
    // 1. 检查环境变量配置
    console.log('📋 检查外部API配置:');
    console.log(`   Binance API Key: ${process.env.BINANCE_API_KEY ? '✅ 已配置' : '❌ 未配置'}`);
    console.log(`   Binance Secret Key: ${process.env.BINANCE_SECRET_KEY ? '✅ 已配置' : '❌ 未配置'}`);
    console.log(`   Binance API URL: ${process.env.BINANCE_API_URL || 'https://fapi.binance.com'}`);
    console.log(`   Coinbase API URL: ${process.env.COINBASE_API_URL || 'https://api.coinbase.com'}\n`);

    // 2. 测试Binance公共API连接
    console.log('🔗 测试Binance公共API连接:');
    try {
      const response = await fetch('https://fapi.binance.com/fapi/v1/ping');
      if (response.ok) {
        console.log('   Binance API: ✅ 连接正常');
      } else {
        console.log('   Binance API: ❌ 连接失败');
      }
    } catch (error) {
      console.log(`   Binance API: ❌ ${error instanceof Error ? error.message : '连接失败'}`);
    }

    // 3. 测试Binance服务器时间
    console.log('\n⏰ 测试Binance服务器时间:');
    try {
      const response = await fetch('https://fapi.binance.com/fapi/v1/time');
      if (response.ok) {
        const data = await response.json();
        const serverTime = new Date(data.serverTime);
        console.log(`   服务器时间: ✅ ${serverTime.toISOString()}`);
      } else {
        console.log('   服务器时间: ❌ 获取失败');
      }
    } catch (error) {
      console.log(`   服务器时间: ❌ ${error instanceof Error ? error.message : '获取失败'}`);
    }

    // 4. 测试获取BTC价格数据
    console.log('\n💰 测试获取BTC价格数据:');
    try {
      const response = await fetch('https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT');
      if (response.ok) {
        const data = await response.json();
        console.log(`   BTC价格: ✅ $${parseFloat(data.lastPrice).toFixed(2)}`);
        console.log(`   24h变化: ${parseFloat(data.priceChangePercent).toFixed(2)}%`);
        console.log(`   24h成交量: ${parseFloat(data.volume).toFixed(2)} BTC`);
      } else {
        console.log('   BTC价格: ❌ 获取失败');
      }
    } catch (error) {
      console.log(`   BTC价格: ❌ ${error instanceof Error ? error.message : '获取失败'}`);
    }

    // 5. 测试OKX公共API连接
    console.log('\n🔗 测试OKX公共API连接:');
    try {
      const response = await fetch('https://www.okx.com/api/v5/public/time');
      if (response.ok) {
        const data = await response.json();
        if (data.code === '0') {
          console.log('   OKX API: ✅ 连接正常');
          console.log(`   服务器时间: ${new Date(parseInt(data.data[0].ts)).toISOString()}`);
        } else {
          console.log(`   OKX API: ❌ ${data.msg}`);
        }
      } else {
        console.log('   OKX API: ❌ 连接失败');
      }
    } catch (error) {
      console.log(`   OKX API: ❌ ${error instanceof Error ? error.message : '连接失败'}`);
    }

    // 6. 测试OKX获取BTC价格
    console.log('\n💰 测试OKX获取BTC价格:');
    try {
      const response = await fetch('https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT');
      if (response.ok) {
        const data = await response.json();
        if (data.code === '0' && data.data.length > 0) {
          const ticker = data.data[0];
          console.log(`   BTC价格: ✅ $${parseFloat(ticker.last).toFixed(2)}`);
          console.log(`   24h变化: ${(parseFloat(ticker.changePercent) * 100).toFixed(2)}%`);
          console.log(`   24h成交量: ${parseFloat(ticker.vol24h).toFixed(2)} BTC`);
        } else {
          console.log(`   BTC价格: ❌ ${data.msg || '数据格式错误'}`);
        }
      } else {
        console.log('   BTC价格: ❌ 获取失败');
      }
    } catch (error) {
      console.log(`   BTC价格: ❌ ${error instanceof Error ? error.message : '获取失败'}`);
    }

    // 7. 测试Coinbase公共API
    console.log('\n🔗 测试Coinbase公共API连接:');
    try {
      const response = await fetch('https://api.coinbase.com/v2/time');
      if (response.ok) {
        const data = await response.json();
        console.log('   Coinbase API: ✅ 连接正常');
        console.log(`   服务器时间: ${data.data.iso}`);
      } else {
        console.log('   Coinbase API: ❌ 连接失败');
      }
    } catch (error) {
      console.log(`   Coinbase API: ❌ ${error instanceof Error ? error.message : '连接失败'}`);
    }

    // 8. 测试CoinGecko API
    console.log('\n🔗 测试CoinGecko API连接:');
    try {
      const response = await fetch('https://api.coingecko.com/api/v3/ping');
      if (response.ok) {
        const data = await response.json();
        console.log(`   CoinGecko API: ✅ ${data.gecko_says || '连接正常'}`);
      } else {
        console.log('   CoinGecko API: ❌ 连接失败');
      }
    } catch (error) {
      console.log(`   CoinGecko API: ❌ ${error instanceof Error ? error.message : '连接失败'}`);
    }

    console.log('\n✅ 外部API集成测试完成');

  } catch (error) {
    console.error('❌ 外部API集成测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testExternalAPIIntegration().catch(console.error);
}

export { testExternalAPIIntegration };
