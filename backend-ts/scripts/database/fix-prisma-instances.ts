#!/usr/bin/env ts-node

/**
 * 修复PrismaClient实例化问题
 * 将所有 new PrismaClient() 替换为全局单例实例
 */

import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface FixResult {
  filePath: string;
  originalInstances: number;
  fixedInstances: number;
  success: boolean;
  error?: string;
}

class PrismaInstanceFixer {
  private results: FixResult[] = [];
  
  async fixAllPrismaInstances(): Promise<void> {
    console.log('🔧 开始修复所有PrismaClient实例化问题...');
    console.log('='.repeat(60));
    
    try {
      // 1. 查找所有包含 new PrismaClient() 的文件
      const files = await this.findFilesWithPrismaInstances();
      console.log(`找到 ${files.length} 个需要修复的文件`);
      
      // 2. 逐个修复文件
      for (const filePath of files) {
        await this.fixFile(filePath);
      }
      
      // 3. 生成修复报告
      this.generateFixReport();
      
    } catch (error) {
      console.error('❌ 修复过程中发生错误:', error);
      throw error;
    }
  }
  
  private async findFilesWithPrismaInstances(): Promise<string[]> {
    try {
      const { execSync } = require('child_process');
      const result = execSync(
        `grep -r "new PrismaClient" src --include="*.ts" -l`,
        { encoding: 'utf-8', cwd: process.cwd() }
      );
      return result.trim().split('\n').filter(line => line.length > 0);
    } catch (error) {
      return [];
    }
  }
  
  private async fixFile(filePath: string): Promise<void> {
    const result: FixResult = {
      filePath,
      originalInstances: 0,
      fixedInstances: 0,
      success: false
    };
    
    try {
      let content = fs.readFileSync(filePath, 'utf-8');
      
      // 计算原始实例数量
      const originalMatches = content.match(/new PrismaClient\(\)/g);
      result.originalInstances = originalMatches ? originalMatches.length : 0;
      
      if (result.originalInstances === 0) {
        result.success = true;
        this.results.push(result);
        return;
      }
      
      console.log(`\n🔄 修复文件: ${filePath} (${result.originalInstances}个实例)`);
      
      // 1. 添加全局实例导入
      if (!content.includes('getGlobalPrismaClient')) {
        content = this.addGlobalPrismaImport(content);
        console.log('  ✅ 添加全局PrismaClient导入');
      }
      
      // 2. 替换构造函数中的实例化
      content = this.replaceConstructorInstances(content);
      
      // 3. 替换局部变量实例化
      content = this.replaceLocalInstances(content);
      
      // 4. 删除警告注释
      content = this.removeWarningComments(content);
      
      // 计算修复后的实例数量
      const remainingMatches = content.match(/new PrismaClient\(\)/g);
      const remainingInstances = remainingMatches ? remainingMatches.length : 0;
      result.fixedInstances = result.originalInstances - remainingInstances;
      
      // 保存修复后的文件
      fs.writeFileSync(filePath, content, 'utf-8');
      
      result.success = true;
      console.log(`  ✅ 修复完成: ${result.fixedInstances}/${result.originalInstances}个实例`);
      
    } catch (error) {
      result.error = error instanceof Error ? error.message : String(error);
      console.log(`  ❌ 修复失败: ${result.error}`);
    }
    
    this.results.push(result);
  }
  
  private addGlobalPrismaImport(content: string): string {
    // 查找现有的PrismaClient导入
    const prismaImportRegex = /import\s*{\s*([^}]*PrismaClient[^}]*)\s*}\s*from\s*['"]@prisma\/client['"];?/;
    const match = content.match(prismaImportRegex);
    
    if (match) {
      // 在现有导入后添加全局实例导入
      const importStatement = `import { getGlobalPrismaClient } from '@shared/infrastructure/database/database';`;
      return content.replace(match[0], match[0] + '\n' + importStatement);
    } else {
      // 在文件开头添加导入
      const importStatement = `import { PrismaClient } from '@prisma/client';\nimport { getGlobalPrismaClient } from '@shared/infrastructure/database/database';\n`;
      return importStatement + content;
    }
  }
  
  private replaceConstructorInstances(content: string): string {
    // 替换构造函数中的 this.prisma = new PrismaClient()
    content = content.replace(
      /this\.prisma\s*=\s*new PrismaClient\(\s*{[^}]*}\s*\);?/g,
      'this.prisma = getGlobalPrismaClient();'
    );
    
    content = content.replace(
      /this\.prisma\s*=\s*new PrismaClient\(\s*\);?/g,
      'this.prisma = getGlobalPrismaClient();'
    );
    
    return content;
  }
  
  private replaceLocalInstances(content: string): string {
    // 替换局部变量的实例化
    content = content.replace(
      /const\s+prisma\s*=\s*new PrismaClient\(\s*{[^}]*}\s*\);?/g,
      'const prisma = getGlobalPrismaClient();'
    );
    
    content = content.replace(
      /const\s+prisma\s*=\s*new PrismaClient\(\s*\);?/g,
      'const prisma = getGlobalPrismaClient();'
    );
    
    // 替换其他变量名
    content = content.replace(
      /(\w+)\s*=\s*new PrismaClient\(\s*{[^}]*}\s*\);?/g,
      '$1 = getGlobalPrismaClient();'
    );
    
    content = content.replace(
      /(\w+)\s*=\s*new PrismaClient\(\s*\);?/g,
      '$1 = getGlobalPrismaClient();'
    );
    
    return content;
  }
  
  private removeWarningComments(content: string): string {
    // 删除之前添加的警告注释
    content = content.replace(
      /\/\/ ⚠️ 警告: 此文件直接使用 new PrismaClient\(\)\n/g,
      ''
    );
    
    content = content.replace(
      /\/\/ 建议迁移到统一数据库基础设施: UnifiedDatabaseManager\n/g,
      ''
    );
    
    content = content.replace(
      /\/\/ 参考: src\/shared\/infrastructure\/database\/unified-database-manager\.ts\n/g,
      ''
    );
    
    return content;
  }
  
  private generateFixReport(): void {
    console.log('\n📊 PrismaClient实例修复报告');
    console.log('='.repeat(60));
    
    const totalFiles = this.results.length;
    const successfulFixes = this.results.filter(r => r.success).length;
    const failedFixes = this.results.filter(r => !r.success).length;
    const totalOriginalInstances = this.results.reduce((sum, r) => sum + r.originalInstances, 0);
    const totalFixedInstances = this.results.reduce((sum, r) => sum + r.fixedInstances, 0);
    
    console.log(`\n✅ 修复统计:`);
    console.log(`  处理文件: ${totalFiles}`);
    console.log(`  成功修复: ${successfulFixes}`);
    console.log(`  修复失败: ${failedFixes}`);
    console.log(`  原始实例: ${totalOriginalInstances}`);
    console.log(`  已修复实例: ${totalFixedInstances}`);
    console.log(`  修复率: ${((totalFixedInstances / totalOriginalInstances) * 100).toFixed(1)}%`);
    
    if (failedFixes > 0) {
      console.log(`\n❌ 修复失败的文件:`);
      this.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  • ${r.filePath}: ${r.error}`);
        });
    }
    
    console.log(`\n📋 修复详情:`);
    this.results
      .filter(r => r.success && r.fixedInstances > 0)
      .forEach(r => {
        console.log(`  ✅ ${r.filePath}: ${r.fixedInstances}/${r.originalInstances}个实例`);
      });
    
    // 验证修复结果
    this.verifyFix();
  }
  
  private async verifyFix(): Promise<void> {
    console.log(`\n🔍 验证修复结果...`);
    
    try {
      const { execSync } = require('child_process');
      const result = execSync(
        `grep -r "new PrismaClient" src --include="*.ts" | wc -l`,
        { encoding: 'utf-8', cwd: process.cwd() }
      );
      
      const remainingInstances = parseInt(result.trim());
      
      if (remainingInstances === 0) {
        console.log(`🎉 完美！所有PrismaClient实例已修复`);
      } else {
        console.log(`⚠️ 仍有 ${remainingInstances} 个PrismaClient实例需要手动处理`);
        
        // 显示剩余的实例
        const remainingFiles = execSync(
          `grep -r "new PrismaClient" src --include="*.ts"`,
          { encoding: 'utf-8', cwd: process.cwd() }
        );
        
        console.log(`\n剩余实例:`);
        console.log(remainingFiles);
      }
    } catch (error) {
      console.log(`验证过程中发生错误:`, error);
    }
  }
}

// 主函数
async function main() {
  try {
    const fixer = new PrismaInstanceFixer();
    await fixer.fixAllPrismaInstances();
    
    console.log('\n🎉 PrismaClient实例修复完成！');
    console.log('问题6（数据库操作重复实现）的核心问题已解决');
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { PrismaInstanceFixer };
