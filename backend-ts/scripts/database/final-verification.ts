#!/usr/bin/env ts-node

/**
 * 问题6最终验证脚本
 * 验证数据库操作重复实现问题是否彻底解决
 */

import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface VerificationResult {
  category: string;
  status: 'RESOLVED' | 'PARTIAL' | 'REMAINING';
  details: string[];
  score: number; // 0-100
}

class FinalVerification {
  private results: VerificationResult[] = [];
  
  async verifyProblem6Resolution(): Promise<void> {
    console.log('🔍 最终验证：问题6（数据库操作重复实现）解决状态');
    console.log('='.repeat(70));
    
    try {
      // 1. 验证统一基础设施是否完整
      await this.verifyUnifiedInfrastructure();
      
      // 2. 验证重复实现清理状态
      await this.verifyDuplicateCleanup();
      
      // 3. 验证自动化工具完整性
      await this.verifyAutomationTools();
      
      // 4. 验证文档和示例
      await this.verifyDocumentationAndExamples();
      
      // 5. 验证架构改进效果
      await this.verifyArchitecturalImprovements();
      
      // 生成最终报告
      this.generateFinalReport();
      
    } catch (error) {
      console.error('❌ 验证过程中发生错误:', error);
      throw error;
    }
  }
  
  private async verifyUnifiedInfrastructure(): Promise<void> {
    console.log('\n1️⃣ 验证统一基础设施...');
    
    const requiredFiles = [
      'src/shared/infrastructure/database/unified-database-manager.ts',
      'src/shared/infrastructure/database/unified-base-repository.ts',
      'src/shared/infrastructure/database/unified-data-mapper.ts'
    ];
    
    const details: string[] = [];
    let score = 0;
    
    for (const filePath of requiredFiles) {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf-8');
        
        // 检查关键功能
        const hasExecuteWithMonitoring = content.includes('executeWithMonitoring');
        const hasWithTransaction = content.includes('withTransaction');
        const hasHealthCheck = content.includes('healthCheck');
        const hasDataMapping = content.includes('toDomain') && content.includes('toPersistence');
        
        if (filePath.includes('unified-database-manager')) {
          if (hasExecuteWithMonitoring && hasWithTransaction && hasHealthCheck) {
            details.push(`✅ ${path.basename(filePath)}: 功能完整`);
            score += 35;
          } else {
            details.push(`⚠️ ${path.basename(filePath)}: 功能不完整`);
            score += 15;
          }
        } else if (filePath.includes('unified-base-repository')) {
          if (hasExecuteWithMonitoring && hasWithTransaction) {
            details.push(`✅ ${path.basename(filePath)}: 功能完整`);
            score += 35;
          } else {
            details.push(`⚠️ ${path.basename(filePath)}: 功能不完整`);
            score += 15;
          }
        } else if (filePath.includes('unified-data-mapper')) {
          if (hasDataMapping) {
            details.push(`✅ ${path.basename(filePath)}: 功能完整`);
            score += 30;
          } else {
            details.push(`⚠️ ${path.basename(filePath)}: 功能不完整`);
            score += 10;
          }
        }
      } else {
        details.push(`❌ ${path.basename(filePath)}: 文件不存在`);
      }
    }
    
    this.results.push({
      category: '统一基础设施',
      status: score >= 90 ? 'RESOLVED' : score >= 60 ? 'PARTIAL' : 'REMAINING',
      details,
      score
    });
  }
  
  private async verifyDuplicateCleanup(): Promise<void> {
    console.log('\n2️⃣ 验证重复实现清理状态...');
    
    const details: string[] = [];
    let score = 100;
    
    // 检查executeWithMonitoring重复实现
    const executeWithMonitoringFiles = await this.findFilesWithPattern(
      'private async executeWithMonitoring'
    );
    
    if (executeWithMonitoringFiles.length <= 3) {
      details.push(`✅ executeWithMonitoring重复: ${executeWithMonitoringFiles.length}个 (可接受)`);
      score -= 0;
    } else {
      details.push(`⚠️ executeWithMonitoring重复: ${executeWithMonitoringFiles.length}个 (需要清理)`);
      score -= 20;
    }
    
    // 检查withTransaction重复实现
    const withTransactionFiles = await this.findFilesWithPattern(
      'private async withTransaction'
    );
    
    if (withTransactionFiles.length === 0) {
      details.push(`✅ withTransaction重复: 0个`);
    } else {
      details.push(`⚠️ withTransaction重复: ${withTransactionFiles.length}个`);
      score -= 15;
    }
    
    // 检查直接PrismaClient使用
    const directPrismaFiles = await this.findFilesWithPattern('new PrismaClient()');
    const markedFiles = await this.findFilesWithPattern('⚠️.*PrismaClient');
    
    if (markedFiles.length >= directPrismaFiles.length * 0.8) {
      details.push(`✅ 直接PrismaClient使用: ${directPrismaFiles.length}个，已标记: ${markedFiles.length}个`);
    } else {
      details.push(`⚠️ 直接PrismaClient使用: ${directPrismaFiles.length}个，已标记: ${markedFiles.length}个`);
      score -= 10;
    }
    
    this.results.push({
      category: '重复实现清理',
      status: score >= 90 ? 'RESOLVED' : score >= 70 ? 'PARTIAL' : 'REMAINING',
      details,
      score
    });
  }
  
  private async verifyAutomationTools(): Promise<void> {
    console.log('\n3️⃣ 验证自动化工具...');
    
    const requiredTools = [
      'scripts/database/repository-migration-detector.ts',
      'scripts/database/auto-migrate-repositories.ts',
      'scripts/database/complete-repository-cleanup.ts',
      'scripts/database/unified-database-verification.ts'
    ];
    
    const details: string[] = [];
    let score = 0;
    
    for (const toolPath of requiredTools) {
      if (fs.existsSync(toolPath)) {
        details.push(`✅ ${path.basename(toolPath)}: 存在`);
        score += 25;
      } else {
        details.push(`❌ ${path.basename(toolPath)}: 不存在`);
      }
    }
    
    this.results.push({
      category: '自动化工具',
      status: score >= 90 ? 'RESOLVED' : score >= 60 ? 'PARTIAL' : 'REMAINING',
      details,
      score
    });
  }
  
  private async verifyDocumentationAndExamples(): Promise<void> {
    console.log('\n4️⃣ 验证文档和示例...');
    
    const requiredDocs = [
      'docs/数据库操作统一化迁移指南.md',
      'docs/数据库操作统一化完成报告.md',
      'docs/问题6彻底解决验证报告.md',
      'src/shared/infrastructure/database/example-unified-repository.ts'
    ];
    
    const details: string[] = [];
    let score = 0;
    
    for (const docPath of requiredDocs) {
      if (fs.existsSync(docPath)) {
        details.push(`✅ ${path.basename(docPath)}: 存在`);
        score += 25;
      } else {
        details.push(`❌ ${path.basename(docPath)}: 不存在`);
      }
    }
    
    this.results.push({
      category: '文档和示例',
      status: score >= 90 ? 'RESOLVED' : score >= 60 ? 'PARTIAL' : 'REMAINING',
      details,
      score
    });
  }
  
  private async verifyArchitecturalImprovements(): Promise<void> {
    console.log('\n5️⃣ 验证架构改进效果...');
    
    const details: string[] = [];
    let score = 0;
    
    // 检查统一实现示例
    const unifiedRepositories = await glob('src/**/Unified*Repository.ts', { 
      cwd: process.cwd() 
    });
    
    if (unifiedRepositories.length >= 2) {
      details.push(`✅ 统一仓储示例: ${unifiedRepositories.length}个`);
      score += 30;
    } else {
      details.push(`⚠️ 统一仓储示例: ${unifiedRepositories.length}个 (建议增加)`);
      score += 15;
    }
    
    // 检查BaseRepository重构
    const baseRepoPath = 'src/shared/infrastructure/database/base-repository.ts';
    if (fs.existsSync(baseRepoPath)) {
      const content = fs.readFileSync(baseRepoPath, 'utf-8');
      if (content.includes('UnifiedBaseRepository')) {
        details.push(`✅ BaseRepository已重构为继承统一基础设施`);
        score += 35;
      } else {
        details.push(`⚠️ BaseRepository未重构`);
        score += 10;
      }
    }
    
    // 检查迁移建议注释
    const filesWithSuggestions = await this.findFilesWithPattern('建议迁移');
    if (filesWithSuggestions.length > 0) {
      details.push(`✅ 迁移建议注释: ${filesWithSuggestions.length}个文件`);
      score += 35;
    } else {
      details.push(`⚠️ 缺少迁移建议注释`);
      score += 10;
    }
    
    this.results.push({
      category: '架构改进',
      status: score >= 90 ? 'RESOLVED' : score >= 60 ? 'PARTIAL' : 'REMAINING',
      details,
      score
    });
  }
  
  private async findFilesWithPattern(pattern: string): Promise<string[]> {
    try {
      const { execSync } = require('child_process');
      const result = execSync(
        `grep -r "${pattern}" src --include="*.ts" -l`,
        { encoding: 'utf-8', cwd: process.cwd() }
      );
      return result.trim().split('\n').filter(line => line.length > 0);
    } catch (error) {
      return [];
    }
  }
  
  private generateFinalReport(): void {
    console.log('\n📊 问题6最终验证报告');
    console.log('='.repeat(70));
    
    const totalScore = this.results.reduce((sum, r) => sum + r.score, 0) / this.results.length;
    const resolvedCount = this.results.filter(r => r.status === 'RESOLVED').length;
    const partialCount = this.results.filter(r => r.status === 'PARTIAL').length;
    const remainingCount = this.results.filter(r => r.status === 'REMAINING').length;
    
    console.log(`\n🎯 总体评估:`);
    console.log(`  综合得分: ${totalScore.toFixed(1)}/100`);
    console.log(`  完全解决: ${resolvedCount}/${this.results.length}`);
    console.log(`  部分解决: ${partialCount}/${this.results.length}`);
    console.log(`  仍需处理: ${remainingCount}/${this.results.length}`);
    
    // 确定最终状态
    let finalStatus: string;
    let statusIcon: string;
    
    if (totalScore >= 90 && resolvedCount >= 4) {
      finalStatus = '✅ 问题6已彻底解决';
      statusIcon = '🎉';
    } else if (totalScore >= 75 && resolvedCount >= 3) {
      finalStatus = '⚠️ 问题6基本解决，有少量优化空间';
      statusIcon = '👍';
    } else {
      finalStatus = '❌ 问题6仍需进一步处理';
      statusIcon = '⚠️';
    }
    
    console.log(`\n${statusIcon} 最终结论: ${finalStatus}`);
    
    // 详细结果
    console.log(`\n📋 详细验证结果:`);
    for (const result of this.results) {
      const statusIcon = result.status === 'RESOLVED' ? '✅' : 
                        result.status === 'PARTIAL' ? '⚠️' : '❌';
      console.log(`\n  ${statusIcon} ${result.category} (${result.score}/100):`);
      for (const detail of result.details) {
        console.log(`    ${detail}`);
      }
    }
    
    // 建议
    if (totalScore < 90) {
      console.log(`\n💡 改进建议:`);
      if (partialCount > 0 || remainingCount > 0) {
        console.log(`  1. 完成剩余的统一基础设施功能`);
        console.log(`  2. 继续清理重复实现`);
        console.log(`  3. 完善自动化工具`);
      }
    } else {
      console.log(`\n🎊 恭喜！问题6已经彻底解决！`);
      console.log(`  数据库操作重复实现问题已完全消除`);
      console.log(`  统一基础设施已建立并正常工作`);
      console.log(`  项目架构得到显著改进`);
    }
    
    // 保存报告
    this.saveFinalReport(totalScore, finalStatus);
  }
  
  private saveFinalReport(totalScore: number, finalStatus: string): void {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        totalScore,
        finalStatus,
        results: this.results,
        summary: {
          resolved: this.results.filter(r => r.status === 'RESOLVED').length,
          partial: this.results.filter(r => r.status === 'PARTIAL').length,
          remaining: this.results.filter(r => r.status === 'REMAINING').length
        }
      };
      
      const reportPath = path.join(process.cwd(), 'docs', '问题6最终验证报告.json');
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf-8');
      console.log(`\n💾 详细报告已保存到: ${reportPath}`);
    } catch (error) {
      console.warn('保存报告失败:', error);
    }
  }
}

// 主函数
async function main() {
  try {
    const verification = new FinalVerification();
    await verification.verifyProblem6Resolution();
  } catch (error) {
    console.error('❌ 最终验证失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { FinalVerification };
