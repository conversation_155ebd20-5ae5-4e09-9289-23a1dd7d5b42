#!/usr/bin/env ts-node

/**
 * 仓储迁移检测器
 * 检测需要迁移到统一数据库基础设施的仓储文件
 */

import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface RepositoryAnalysis {
  filePath: string;
  className: string;
  hasExecuteWithMonitoring: boolean;
  hasWithTransaction: boolean;
  hasToDomain: boolean;
  hasToPersistence: boolean;
  hasDirectPrismaUsage: boolean;
  duplicatePatterns: string[];
  migrationPriority: 'HIGH' | 'MEDIUM' | 'LOW';
  estimatedEffort: string;
}

interface MigrationReport {
  totalFiles: number;
  needsMigration: number;
  highPriority: number;
  mediumPriority: number;
  lowPriority: number;
  repositories: RepositoryAnalysis[];
  summary: {
    duplicateExecuteWithMonitoring: number;
    duplicateWithTransaction: number;
    duplicateToDomain: number;
    duplicateToPersistence: number;
    directPrismaUsage: number;
  };
}

class RepositoryMigrationDetector {
  private readonly srcPath = path.join(process.cwd(), 'src');
  
  async detectRepositories(): Promise<MigrationReport> {
    console.log('🔍 检测需要迁移的仓储文件...');
    
    // 查找所有仓储文件
    const repositoryFiles = await this.findRepositoryFiles();
    console.log(`找到 ${repositoryFiles.length} 个仓储文件`);
    
    // 分析每个文件
    const analyses: RepositoryAnalysis[] = [];
    for (const filePath of repositoryFiles) {
      const analysis = await this.analyzeRepository(filePath);
      if (analysis) {
        analyses.push(analysis);
      }
    }
    
    // 生成报告
    const report = this.generateReport(analyses);
    
    // 输出报告
    this.printReport(report);
    
    // 保存详细报告
    await this.saveDetailedReport(report);
    
    return report;
  }
  
  private async findRepositoryFiles(): Promise<string[]> {
    const patterns = [
      'src/**/repositories/**/*Repository*.ts',
      'src/**/repositories/**/*repository*.ts',
      'src/**/infrastructure/**/*Repository*.ts',
      'src/**/infrastructure/**/*repository*.ts'
    ];
    
    const files: string[] = [];
    for (const pattern of patterns) {
      const matches = await glob(pattern, { cwd: process.cwd() });
      files.push(...matches);
    }
    
    // 去重并过滤
    return [...new Set(files)].filter(file => 
      !file.includes('.test.') && 
      !file.includes('.spec.') &&
      !file.includes('example') &&
      !file.includes('unified') &&
      !file.includes('base-repository')
    );
  }
  
  private async analyzeRepository(filePath: string): Promise<RepositoryAnalysis | null> {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const className = this.extractClassName(content);
      
      if (!className) {
        return null;
      }
      
      const analysis: RepositoryAnalysis = {
        filePath,
        className,
        hasExecuteWithMonitoring: this.hasPattern(content, /executeWithMonitoring/),
        hasWithTransaction: this.hasPattern(content, /withTransaction/),
        hasToDomain: this.hasPattern(content, /toDomain/),
        hasToPersistence: this.hasPattern(content, /toPersistence/),
        hasDirectPrismaUsage: this.hasPattern(content, /new PrismaClient|@inject.*PrismaClient/),
        duplicatePatterns: [],
        migrationPriority: 'LOW',
        estimatedEffort: '1-2小时'
      };
      
      // 检测重复模式
      analysis.duplicatePatterns = this.detectDuplicatePatterns(content);
      
      // 计算迁移优先级
      analysis.migrationPriority = this.calculatePriority(analysis);
      analysis.estimatedEffort = this.estimateEffort(analysis);
      
      return analysis;
    } catch (error) {
      console.warn(`分析文件失败: ${filePath}`, error);
      return null;
    }
  }
  
  private extractClassName(content: string): string | null {
    const classMatch = content.match(/export\s+class\s+(\w+)/);
    return classMatch ? classMatch[1] : null;
  }
  
  private hasPattern(content: string, pattern: RegExp): boolean {
    return pattern.test(content);
  }
  
  private detectDuplicatePatterns(content: string): string[] {
    const patterns: string[] = [];
    
    // 检测常见的重复模式
    const duplicateChecks = [
      { pattern: /private\s+async\s+executeWithMonitoring/, name: 'executeWithMonitoring方法' },
      { pattern: /protected\s+async\s+withTransaction/, name: 'withTransaction方法' },
      { pattern: /private\s+toDomain/, name: 'toDomain方法' },
      { pattern: /private\s+toPersistence/, name: 'toPersistence方法' },
      { pattern: /const\s+startTime\s*=\s*Date\.now/, name: '性能监控代码' },
      { pattern: /this\.logger\.(debug|info|warn|error).*操作/, name: '操作日志记录' },
      { pattern: /duration.*Date\.now.*startTime/, name: '执行时间计算' },
      { pattern: /慢查询检测/, name: '慢查询检测逻辑' },
      { pattern: /JSON\.parse.*JSON\.stringify/, name: 'JSON转换逻辑' },
      { pattern: /\.findUnique.*\.findMany.*\.create.*\.update/, name: '基础CRUD操作' }
    ];
    
    for (const check of duplicateChecks) {
      if (check.pattern.test(content)) {
        patterns.push(check.name);
      }
    }
    
    return patterns;
  }
  
  private calculatePriority(analysis: RepositoryAnalysis): 'HIGH' | 'MEDIUM' | 'LOW' {
    let score = 0;
    
    if (analysis.hasExecuteWithMonitoring) score += 3;
    if (analysis.hasWithTransaction) score += 2;
    if (analysis.hasToDomain && analysis.hasToPersistence) score += 2;
    if (analysis.hasDirectPrismaUsage) score += 1;
    if (analysis.duplicatePatterns.length > 5) score += 2;
    
    if (score >= 6) return 'HIGH';
    if (score >= 3) return 'MEDIUM';
    return 'LOW';
  }
  
  private estimateEffort(analysis: RepositoryAnalysis): string {
    const patternCount = analysis.duplicatePatterns.length;
    
    if (patternCount >= 7) return '3-4小时';
    if (patternCount >= 5) return '2-3小时';
    if (patternCount >= 3) return '1-2小时';
    return '30分钟-1小时';
  }
  
  private generateReport(analyses: RepositoryAnalysis[]): MigrationReport {
    const needsMigration = analyses.filter(a => 
      a.hasExecuteWithMonitoring || 
      a.hasWithTransaction || 
      a.duplicatePatterns.length > 0
    );
    
    return {
      totalFiles: analyses.length,
      needsMigration: needsMigration.length,
      highPriority: analyses.filter(a => a.migrationPriority === 'HIGH').length,
      mediumPriority: analyses.filter(a => a.migrationPriority === 'MEDIUM').length,
      lowPriority: analyses.filter(a => a.migrationPriority === 'LOW').length,
      repositories: analyses,
      summary: {
        duplicateExecuteWithMonitoring: analyses.filter(a => a.hasExecuteWithMonitoring).length,
        duplicateWithTransaction: analyses.filter(a => a.hasWithTransaction).length,
        duplicateToDomain: analyses.filter(a => a.hasToDomain).length,
        duplicateToPersistence: analyses.filter(a => a.hasToPersistence).length,
        directPrismaUsage: analyses.filter(a => a.hasDirectPrismaUsage).length
      }
    };
  }
  
  private printReport(report: MigrationReport): void {
    console.log('\n📊 仓储迁移分析报告');
    console.log('='.repeat(50));
    
    console.log(`\n📈 总体统计:`);
    console.log(`  总仓储文件数: ${report.totalFiles}`);
    console.log(`  需要迁移: ${report.needsMigration}`);
    console.log(`  高优先级: ${report.highPriority}`);
    console.log(`  中优先级: ${report.mediumPriority}`);
    console.log(`  低优先级: ${report.lowPriority}`);
    
    console.log(`\n🔍 重复实现统计:`);
    console.log(`  executeWithMonitoring重复: ${report.summary.duplicateExecuteWithMonitoring}`);
    console.log(`  withTransaction重复: ${report.summary.duplicateWithTransaction}`);
    console.log(`  toDomain重复: ${report.summary.duplicateToDomain}`);
    console.log(`  toPersistence重复: ${report.summary.duplicateToPersistence}`);
    console.log(`  直接Prisma使用: ${report.summary.directPrismaUsage}`);
    
    console.log(`\n🚨 高优先级迁移文件:`);
    const highPriority = report.repositories.filter(r => r.migrationPriority === 'HIGH');
    if (highPriority.length === 0) {
      console.log('  无高优先级文件');
    } else {
      highPriority.forEach(repo => {
        console.log(`  📁 ${repo.filePath}`);
        console.log(`     类名: ${repo.className}`);
        console.log(`     重复模式: ${repo.duplicatePatterns.length}个`);
        console.log(`     预估工作量: ${repo.estimatedEffort}`);
      });
    }
    
    console.log(`\n⚠️  中优先级迁移文件:`);
    const mediumPriority = report.repositories.filter(r => r.migrationPriority === 'MEDIUM');
    if (mediumPriority.length === 0) {
      console.log('  无中优先级文件');
    } else {
      mediumPriority.slice(0, 5).forEach(repo => {
        console.log(`  📁 ${repo.filePath} (${repo.estimatedEffort})`);
      });
      if (mediumPriority.length > 5) {
        console.log(`  ... 还有 ${mediumPriority.length - 5} 个文件`);
      }
    }
  }
  
  private async saveDetailedReport(report: MigrationReport): Promise<void> {
    const reportPath = path.join(process.cwd(), 'docs', '仓储迁移分析报告.json');
    
    try {
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf-8');
      console.log(`\n💾 详细报告已保存到: ${reportPath}`);
    } catch (error) {
      console.warn('保存详细报告失败:', error);
    }
  }
}

// 主函数
async function main() {
  try {
    const detector = new RepositoryMigrationDetector();
    await detector.detectRepositories();
  } catch (error) {
    console.error('❌ 检测过程中发生错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { RepositoryMigrationDetector };
