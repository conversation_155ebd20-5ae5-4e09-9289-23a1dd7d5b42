#!/usr/bin/env ts-node

/**
 * 统一数据库基础设施验证脚本
 * 验证新的统一数据库基础设施功能正常
 */

import { 
  unifiedDatabaseManager, 
  initializeUnifiedDatabase, 
  getUnifiedDatabaseClient,
  checkUnifiedDatabaseHealth,
  closeUnifiedDatabase 
} from '../../src/shared/infrastructure/database/unified-database-manager';

interface VerificationResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  message: string;
  duration?: number;
  error?: string;
}

class UnifiedDatabaseVerification {
  private results: VerificationResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🔍 开始验证统一数据库基础设施...');
    console.log('='.repeat(60));

    try {
      // 1. 测试数据库管理器初始化
      await this.testDatabaseInitialization();
      
      // 2. 测试健康检查
      await this.testHealthCheck();
      
      // 3. 测试操作监控
      await this.testOperationMonitoring();
      
      // 4. 测试事务处理
      await this.testTransactionHandling();
      
      // 5. 测试错误处理
      await this.testErrorHandling();
      
      // 6. 测试性能监控
      await this.testPerformanceMonitoring();
      
      // 生成报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 验证过程中发生严重错误:', error);
      this.addResult('CRITICAL_ERROR', 'FAIL', `严重错误: ${error}`);
    } finally {
      // 清理资源
      await this.cleanup();
    }
  }

  private async testDatabaseInitialization(): Promise<void> {
    console.log('\n1️⃣ 测试数据库管理器初始化...');
    
    const startTime = Date.now();
    
    try {
      // 测试初始化
      await initializeUnifiedDatabase();
      
      // 测试获取客户端
      const client = getUnifiedDatabaseClient();
      
      if (client) {
        this.addResult(
          '数据库初始化',
          'PASS',
          '统一数据库管理器初始化成功',
          Date.now() - startTime
        );
      } else {
        this.addResult(
          '数据库初始化',
          'FAIL',
          '无法获取数据库客户端'
        );
      }
    } catch (error) {
      this.addResult(
        '数据库初始化',
        'FAIL',
        '数据库初始化失败',
        Date.now() - startTime,
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  private async testHealthCheck(): Promise<void> {
    console.log('\n2️⃣ 测试健康检查功能...');
    
    const startTime = Date.now();
    
    try {
      const healthStatus = await checkUnifiedDatabaseHealth();
      
      if (healthStatus.isHealthy) {
        this.addResult(
          '健康检查',
          'PASS',
          `数据库健康，响应时间: ${healthStatus.responseTime}ms`,
          Date.now() - startTime
        );
      } else {
        this.addResult(
          '健康检查',
          'FAIL',
          `数据库不健康: ${healthStatus.lastError}`,
          Date.now() - startTime
        );
      }
    } catch (error) {
      this.addResult(
        '健康检查',
        'FAIL',
        '健康检查执行失败',
        Date.now() - startTime,
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  private async testOperationMonitoring(): Promise<void> {
    console.log('\n3️⃣ 测试操作监控功能...');
    
    const startTime = Date.now();
    
    try {
      // 测试带监控的操作
      const result = await unifiedDatabaseManager.executeWithMonitoring(
        { operation: 'test_monitoring' },
        async () => {
          // 模拟数据库操作
          await new Promise(resolve => setTimeout(resolve, 100));
          return 'test_result';
        }
      );
      
      if (result === 'test_result') {
        this.addResult(
          '操作监控',
          'PASS',
          '操作监控功能正常',
          Date.now() - startTime
        );
      } else {
        this.addResult(
          '操作监控',
          'FAIL',
          '操作监控返回结果不正确'
        );
      }
    } catch (error) {
      this.addResult(
        '操作监控',
        'FAIL',
        '操作监控测试失败',
        Date.now() - startTime,
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  private async testTransactionHandling(): Promise<void> {
    console.log('\n4️⃣ 测试事务处理功能...');
    
    const startTime = Date.now();
    
    try {
      // 测试事务
      const result = await unifiedDatabaseManager.withTransaction(
        async (tx) => {
          // 模拟事务操作
          return 'transaction_success';
        }
      );
      
      if (result === 'transaction_success') {
        this.addResult(
          '事务处理',
          'PASS',
          '事务处理功能正常',
          Date.now() - startTime
        );
      } else {
        this.addResult(
          '事务处理',
          'FAIL',
          '事务处理返回结果不正确'
        );
      }
    } catch (error) {
      this.addResult(
        '事务处理',
        'FAIL',
        '事务处理测试失败',
        Date.now() - startTime,
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  private async testErrorHandling(): Promise<void> {
    console.log('\n5️⃣ 测试错误处理功能...');
    
    const startTime = Date.now();
    
    try {
      // 测试错误处理
      await unifiedDatabaseManager.executeWithMonitoring(
        { operation: 'test_error_handling' },
        async () => {
          throw new Error('测试错误');
        }
      );
      
      // 如果到达这里，说明错误没有被正确抛出
      this.addResult(
        '错误处理',
        'FAIL',
        '错误没有被正确抛出'
      );
    } catch (error) {
      if (error instanceof Error && error.message === '测试错误') {
        this.addResult(
          '错误处理',
          'PASS',
          '错误处理功能正常',
          Date.now() - startTime
        );
      } else {
        this.addResult(
          '错误处理',
          'FAIL',
          '错误处理不正确',
          Date.now() - startTime,
          error instanceof Error ? error.message : String(error)
        );
      }
    }
  }

  private async testPerformanceMonitoring(): Promise<void> {
    console.log('\n6️⃣ 测试性能监控功能...');
    
    const startTime = Date.now();
    
    try {
      // 测试慢查询检测
      await unifiedDatabaseManager.executeWithMonitoring(
        { operation: 'test_slow_query', timeout: 5000 },
        async () => {
          // 模拟慢查询
          await new Promise(resolve => setTimeout(resolve, 1100));
          return 'slow_query_result';
        }
      );
      
      this.addResult(
        '性能监控',
        'PASS',
        '性能监控功能正常（慢查询检测）',
        Date.now() - startTime
      );
    } catch (error) {
      this.addResult(
        '性能监控',
        'FAIL',
        '性能监控测试失败',
        Date.now() - startTime,
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  private addResult(
    test: string,
    status: 'PASS' | 'FAIL' | 'SKIP',
    message: string,
    duration?: number,
    error?: string
  ): void {
    this.results.push({
      test,
      status,
      message,
      duration,
      error
    });
    
    const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⏭️';
    const durationText = duration ? ` (${duration}ms)` : '';
    console.log(`   ${statusIcon} ${test}: ${message}${durationText}`);
    
    if (error) {
      console.log(`      错误详情: ${error}`);
    }
  }

  private generateReport(): void {
    console.log('\n📊 验证报告');
    console.log('='.repeat(60));
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const skipped = this.results.filter(r => r.status === 'SKIP').length;
    const total = this.results.length;
    
    console.log(`\n📈 总体结果:`);
    console.log(`  总测试数: ${total}`);
    console.log(`  通过: ${passed}`);
    console.log(`  失败: ${failed}`);
    console.log(`  跳过: ${skipped}`);
    console.log(`  成功率: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log(`\n❌ 失败的测试:`);
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(result => {
          console.log(`  • ${result.test}: ${result.message}`);
          if (result.error) {
            console.log(`    错误: ${result.error}`);
          }
        });
    }
    
    const avgDuration = this.results
      .filter(r => r.duration)
      .reduce((sum, r) => sum + (r.duration || 0), 0) / 
      this.results.filter(r => r.duration).length;
    
    if (avgDuration) {
      console.log(`\n⏱️  平均执行时间: ${avgDuration.toFixed(1)}ms`);
    }
    
    // 保存报告
    this.saveReport();
  }

  private saveReport(): void {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const reportPath = path.join(process.cwd(), 'docs', '统一数据库验证报告.json');
      const report = {
        timestamp: new Date().toISOString(),
        summary: {
          total: this.results.length,
          passed: this.results.filter(r => r.status === 'PASS').length,
          failed: this.results.filter(r => r.status === 'FAIL').length,
          skipped: this.results.filter(r => r.status === 'SKIP').length
        },
        results: this.results
      };
      
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf-8');
      console.log(`\n💾 详细报告已保存到: ${reportPath}`);
    } catch (error) {
      console.warn('保存报告失败:', error);
    }
  }

  private async cleanup(): Promise<void> {
    try {
      await closeUnifiedDatabase();
      console.log('\n🧹 资源清理完成');
    } catch (error) {
      console.warn('资源清理失败:', error);
    }
  }
}

// 主函数
async function main() {
  try {
    const verification = new UnifiedDatabaseVerification();
    await verification.runAllTests();
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { UnifiedDatabaseVerification };
