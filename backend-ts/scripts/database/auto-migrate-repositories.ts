#!/usr/bin/env ts-node

/**
 * 自动迁移仓储脚本
 * 自动将现有仓储迁移到统一数据库基础设施，删除重复代码
 */

import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface MigrationTask {
  filePath: string;
  className: string;
  hasExecuteWithMonitoring: boolean;
  hasDirectPrismaUsage: boolean;
  needsCleanup: boolean;
}

class AutoRepositoryMigrator {
  private readonly srcPath = path.join(process.cwd(), 'src');
  private migratedCount = 0;
  private cleanedCount = 0;
  
  async migrateAllRepositories(): Promise<void> {
    console.log('🔧 开始自动迁移仓储到统一基础设施...');
    console.log('='.repeat(60));
    
    try {
      // 1. 查找需要迁移的文件
      const tasks = await this.findMigrationTasks();
      console.log(`找到 ${tasks.length} 个需要处理的文件`);
      
      // 2. 执行迁移
      for (const task of tasks) {
        await this.migrateRepository(task);
      }
      
      // 3. 清理重复的PrismaClient实例化
      await this.cleanupDirectPrismaUsage();
      
      // 4. 生成报告
      this.generateMigrationReport();
      
    } catch (error) {
      console.error('❌ 迁移过程中发生错误:', error);
      throw error;
    }
  }
  
  private async findMigrationTasks(): Promise<MigrationTask[]> {
    const repositoryFiles = await glob('src/**/repositories/**/*Repository*.ts', { 
      cwd: process.cwd() 
    });
    
    const tasks: MigrationTask[] = [];
    
    for (const filePath of repositoryFiles) {
      // 跳过已经是统一实现的文件
      if (filePath.includes('Unified') || 
          filePath.includes('example') || 
          filePath.includes('base-repository')) {
        continue;
      }
      
      try {
        const content = fs.readFileSync(filePath, 'utf-8');
        const className = this.extractClassName(content);
        
        if (className) {
          tasks.push({
            filePath,
            className,
            hasExecuteWithMonitoring: /private\s+async\s+executeWithMonitoring/.test(content),
            hasDirectPrismaUsage: /new PrismaClient|@inject.*PrismaClient/.test(content),
            needsCleanup: /private\s+async\s+executeWithMonitoring|toDomain|toPersistence/.test(content)
          });
        }
      } catch (error) {
        console.warn(`跳过文件 ${filePath}:`, error);
      }
    }
    
    return tasks;
  }
  
  private async migrateRepository(task: MigrationTask): Promise<void> {
    console.log(`\n🔄 处理文件: ${task.filePath}`);
    
    try {
      let content = fs.readFileSync(task.filePath, 'utf-8');
      let modified = false;
      
      // 1. 删除重复的executeWithMonitoring方法
      if (task.hasExecuteWithMonitoring) {
        content = this.removeExecuteWithMonitoring(content);
        modified = true;
        console.log('  ✅ 删除重复的executeWithMonitoring方法');
      }
      
      // 2. 删除重复的事务处理方法
      if (content.includes('withTransaction')) {
        content = this.removeWithTransaction(content);
        modified = true;
        console.log('  ✅ 删除重复的withTransaction方法');
      }
      
      // 3. 添加注释说明迁移状态
      if (modified) {
        content = this.addMigrationComment(content, task.className);
        
        // 保存修改后的文件
        fs.writeFileSync(task.filePath, content, 'utf-8');
        this.migratedCount++;
        console.log('  ✅ 文件迁移完成');
      } else {
        console.log('  ⏭️ 文件无需迁移');
      }
      
    } catch (error) {
      console.error(`  ❌ 迁移失败: ${error}`);
    }
  }
  
  private removeExecuteWithMonitoring(content: string): string {
    // 删除整个executeWithMonitoring方法
    const methodRegex = /\/\*\*[\s\S]*?\*\/\s*private\s+async\s+executeWithMonitoring<[^>]*>\s*\([^)]*\)\s*:\s*Promise<[^>]*>\s*\{[\s\S]*?\n\s*\}/g;
    
    let result = content.replace(methodRegex, '');
    
    // 如果上面的正则没有匹配到，尝试更简单的模式
    if (result === content) {
      const simpleRegex = /private\s+async\s+executeWithMonitoring[\s\S]*?^\s*\}/gm;
      result = content.replace(simpleRegex, '');
    }
    
    return result;
  }
  
  private removeWithTransaction(content: string): string {
    // 删除重复的withTransaction方法
    const methodRegex = /\/\*\*[\s\S]*?\*\/\s*protected\s+async\s+withTransaction<[^>]*>\s*\([^)]*\)\s*:\s*Promise<[^>]*>\s*\{[\s\S]*?\n\s*\}/g;
    
    let result = content.replace(methodRegex, '');
    
    // 如果上面的正则没有匹配到，尝试更简单的模式
    if (result === content) {
      const simpleRegex = /protected\s+async\s+withTransaction[\s\S]*?^\s*\}/gm;
      result = content.replace(simpleRegex, '');
    }
    
    return result;
  }
  
  private addMigrationComment(content: string, className: string): string {
    const comment = `
/**
 * 注意：此文件已部分迁移到统一数据库基础设施
 * 重复的executeWithMonitoring、withTransaction等方法已被删除
 * 建议完全迁移到 Unified${className} 实现
 * 
 * 迁移时间: ${new Date().toISOString()}
 */
`;
    
    // 在类定义前添加注释
    const classRegex = /(export\s+class\s+\w+)/;
    return content.replace(classRegex, comment + '$1');
  }
  
  private async cleanupDirectPrismaUsage(): Promise<void> {
    console.log('\n🧹 清理直接的PrismaClient使用...');
    
    // 查找所有直接使用new PrismaClient的文件
    const files = await glob('src/**/*.ts', { cwd: process.cwd() });
    
    for (const filePath of files) {
      // 跳过测试文件和脚本文件
      if (filePath.includes('.test.') || 
          filePath.includes('.spec.') ||
          filePath.includes('test-') ||
          filePath.includes('setup-') ||
          filePath.includes('scripts/')) {
        continue;
      }
      
      try {
        let content = fs.readFileSync(filePath, 'utf-8');
        
        // 检查是否有直接的PrismaClient实例化
        if (content.includes('new PrismaClient()')) {
          // 添加警告注释
          const warning = `
// ⚠️ 警告: 此文件直接使用 new PrismaClient()
// 建议迁移到统一数据库基础设施: UnifiedDatabaseManager
// 参考: src/shared/infrastructure/database/unified-database-manager.ts
`;
          
          // 在new PrismaClient()前添加警告
          content = content.replace(
            /(.*new PrismaClient\(\))/g, 
            warning + '$1'
          );
          
          fs.writeFileSync(filePath, content, 'utf-8');
          this.cleanedCount++;
          console.log(`  ✅ 添加迁移警告: ${filePath}`);
        }
      } catch (error) {
        // 忽略读取错误
      }
    }
  }
  
  private extractClassName(content: string): string | null {
    const classMatch = content.match(/export\s+class\s+(\w+)/);
    return classMatch ? classMatch[1] : null;
  }
  
  private generateMigrationReport(): void {
    console.log('\n📊 迁移报告');
    console.log('='.repeat(60));
    
    console.log(`\n✅ 迁移完成统计:`);
    console.log(`  已迁移文件: ${this.migratedCount}`);
    console.log(`  已清理文件: ${this.cleanedCount}`);
    
    console.log(`\n📋 下一步建议:`);
    console.log(`  1. 运行测试确保功能正常`);
    console.log(`  2. 逐步将仓储完全迁移到Unified实现`);
    console.log(`  3. 更新依赖注入配置`);
    console.log(`  4. 删除不再使用的旧仓储文件`);
    
    console.log(`\n🔗 相关文件:`);
    console.log(`  统一基础设施: src/shared/infrastructure/database/unified-*`);
    console.log(`  迁移指南: docs/数据库操作统一化迁移指南.md`);
    console.log(`  示例实现: src/shared/infrastructure/database/example-unified-repository.ts`);
  }
}

// 主函数
async function main() {
  try {
    const migrator = new AutoRepositoryMigrator();
    await migrator.migrateAllRepositories();
    
    console.log('\n🎉 自动迁移完成！');
    console.log('建议运行验证脚本: npx ts-node scripts/database/unified-database-verification.ts');
    
  } catch (error) {
    console.error('❌ 自动迁移失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { AutoRepositoryMigrator };
