import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import cron from 'node-cron';


/**
 * 自动数据更新
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

const prisma = new PrismaClient();

// 简单的日志函数
const log = (message: string, data?: any) => {
  console.log(`[${new Date().toISOString()}] ${message}`, data || '');
};

class AutoDataUpdater {
  private isRunning = false;
  private readonly timeframes = [
    { interval: '5m', name: '5分钟', intervalMs: 5 * 60 * 1000 },
    { interval: '15m', name: '15分钟', intervalMs: 15 * 60 * 1000 },
    { interval: '1h', name: '1小时', intervalMs: 60 * 60 * 1000 },
    { interval: '2h', name: '2小时', intervalMs: 2 * 60 * 60 * 1000 },
    { interval: '4h', name: '4小时', intervalMs: 4 * 60 * 60 * 1000 },
    { interval: '8h', name: '8小时', intervalMs: 8 * 60 * 60 * 1000 },
    { interval: '1d', name: '日线', intervalMs: 24 * 60 * 60 * 1000 },
    { interval: '3d', name: '3日线', intervalMs: 3 * 24 * 60 * 60 * 1000 }
  ];

  constructor() {
    this.setupGracefulShutdown();
  }

  /**
   * 启动自动更新服务
   */
  async start() {
    log('🚀 启动BTC数据自动更新服务...');

    // 立即执行一次更新
    await this.updateAllTimeframes();

    // 设置定时任务 - 每5分钟执行一次
    cron.schedule('*/5 * * * *', async () => {
      if (!this.isRunning) {
        await this.updateAllTimeframes();
      }
    });

    // 设置每日数据质量检查 - 每天凌晨2点
    cron.schedule('0 2 * * *', async () => {
      await this.performDailyMaintenance();
    });

    log('✅ 自动更新服务已启动');
    log('📅 更新频率: 每5分钟');
    log('🔧 维护时间: 每日凌晨2点');
  }

  /**
   * 更新所有时间周期的数据
   */
  private async updateAllTimeframes() {
    if (this.isRunning) {
      log('⚠️ 更新任务正在运行中，跳过本次更新');
      return;
    }

    this.isRunning = true;
    log('🔄 开始更新所有时间周期数据...');

    try {
      // 获取符号记录
      const symbolRecord = await prisma.symbols.findUnique({
        where: { symbol: 'BTC/USDT' }
      });

      if (!symbolRecord) {
        throw new Error('BTC/USDT符号记录不存在');
      }

      let totalNewRecords = 0;

      for (const timeframe of this.timeframes) {
        try {
          const newRecords = await this.updateTimeframeData(symbolRecord.id, timeframe);
          totalNewRecords += newRecords;
          
          // 添加延迟避免API限制
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          log(`❌ 更新${timeframe.name}数据失败:`, error.message);
        }
      }

      log(`✅ 数据更新完成，新增${totalNewRecords}条记录`);

    } catch (error) {
      log('❌ 数据更新失败:', error.message);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 更新单个时间周期的数据
   */
  private async updateTimeframeData(symbolId: string, timeframe: any): Promise<number> {
    try {
      // 获取该时间周期的最新数据时间
      const latestRecord = await prisma.historicalData.findFirst({
        where: {
          symbolId,
          timeframe: timeframe.interval
        },
        orderBy: { timestamp: 'desc' }
      });

      // 计算开始时间
      let startTime: number;
      if (latestRecord) {
        // 从最新记录的下一个时间点开始
        startTime = latestRecord.timestamp.getTime() + timeframe.intervalMs;
      } else {
        // 如果没有数据，从24小时前开始
        startTime = Date.now() - 24 * 60 * 60 * 1000;
      }

      const endTime = Date.now();

      // 如果开始时间已经超过当前时间，说明数据已经是最新的
      if (startTime >= endTime) {
        log(`📭 ${timeframe.name}数据已是最新，无需更新`);
        return 0;
      }

      // 调用Binance API获取新数据
      const response = await axios.get('https://api.binance.com/api/v3/klines', {
        params: {
          symbol: 'BTCUSDT',
          interval: timeframe.interval,
          startTime,
          endTime,
          limit: 1000
        },
        timeout: 30000
      });

      const klineData = response.data;

      if (klineData.length === 0) {
        log(`📭 ${timeframe.name}没有新数据`);
        return 0;
      }

      // 验证数据质量
      const firstKline = klineData[0];
      const openPrice = parseFloat(firstKline[1]);
      if (openPrice < 100 || openPrice > 1000000) {
        throw new Error(`${timeframe.name}数据异常: price=${openPrice}`);
      }

      // 准备批量插入数据
      const records = klineData.map((kline: any) => ({
        symbolId,
        timeframe: timeframe.interval,
        timestamp: new Date(kline[0]),
        openPrice: parseFloat(kline[1]),
        highPrice: parseFloat(kline[2]),
        lowPrice: parseFloat(kline[3]),
        closePrice: parseFloat(kline[4]),
        volume: parseFloat(kline[5])
      }));

      // 批量插入，跳过重复数据
      const result = await prisma.historicalData.createMany({
        data: records,
        skipDuplicates: false
      });

      log(`✅ ${timeframe.name}更新完成: 新增${result.count}条记录`);
      return result.count;

    } catch (error) {
      log(`❌ 更新${timeframe.name}失败:`, error.message);
      return 0;
    }
  }

  /**
   * 每日维护任务
   * 注意：历史价格数据永远不会被删除，只清理临时日志数据
   */
  private async performDailyMaintenance() {
    log('🔧 开始执行每日维护任务...');

    try {
      // 1. 数据质量检查
      await this.checkDataQuality();

      // 2. 清理过期的WebSocket消息日志（不是历史价格数据）
      await this.cleanupOldWebSocketMessages();

      // 3. 更新数据统计
      await this.updateDataStatistics();

      // 4. 检查数据完整性
      await this.checkDataIntegrity();

      log('✅ 每日维护任务完成');

    } catch (error) {
      log('❌ 每日维护任务失败:', error.message);
    }
  }

  /**
   * 数据质量检查
   */
  private async checkDataQuality() {
    log('🔍 执行数据质量检查...');

    try {
      const symbolRecord = await prisma.symbols.findUnique({
        where: { symbol: 'BTC/USDT' }
      });

      if (!symbolRecord) return;

      // 检查每个时间周期的数据质量
      for (const timeframe of this.timeframes) {
        const stats = await prisma.historicalData.aggregate({
          where: {
            symbolId: symbolRecord.id,
            timeframe: timeframe.interval,
            timestamp: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
            }
          },
          _count: { id: true }
        });

        log(`   ${timeframe.name}: ${stats._count.id || 0}条记录`);
      }

    } catch (error) {
      log('❌ 数据质量检查失败:', error.message);
    }
  }

  /**
   * 清理过期的WebSocket消息日志
   * 注意：这里只清理WebSocket通信日志，不会删除任何历史价格数据
   */
  private async cleanupOldWebSocketMessages() {
    log('🧹 清理过期WebSocket消息日志（保留历史价格数据）...');

    try {
      // WebSocket消息表不存在，跳过清理
      log(`✅ 跳过WebSocket消息清理（表不存在）`);

    } catch (error) {
      log('❌ 清理WebSocket消息日志失败:', error.message);
    }
  }

  /**
   * 更新数据统计
   */
  private async updateDataStatistics() {
    log('📊 更新数据统计...');

    try {
      const symbolRecord = await prisma.symbols.findUnique({
        where: { symbol: 'BTC/USDT' }
      });

      if (!symbolRecord) return;

      const totalCount = await prisma.historicalData.count({
        where: { symbolId: symbolRecord.id }
      });

      const latestRecord = await prisma.historicalData.findFirst({
        where: { symbolId: symbolRecord.id },
        orderBy: { timestamp: 'desc' }
      });

      const oldestRecord = await prisma.historicalData.findFirst({
        where: { symbolId: symbolRecord.id },
        orderBy: { timestamp: 'asc' }
      });

      if (latestRecord && oldestRecord) {
        const timeSpanDays = Math.round(
          (latestRecord.timestamp.getTime() - oldestRecord.timestamp.getTime()) / (24 * 60 * 60 * 1000)
        );

        log(`📈 数据统计: 总计${totalCount}条记录，时间跨度${timeSpanDays}天`);
        log(`   最早数据: ${oldestRecord.timestamp.toISOString()}`);
        log(`   最新数据: ${latestRecord.timestamp.toISOString()}`);
      }

    } catch (error) {
      log('❌ 更新数据统计失败:', error.message);
    }
  }

  /**
   * 检查数据完整性
   */
  private async checkDataIntegrity() {
    log('🔍 检查数据完整性...');

    try {
      const symbolRecord = await prisma.symbols.findUnique({
        where: { symbol: 'BTC/USDT' }
      });

      if (!symbolRecord) return;

      // 检查最近24小时是否有数据缺失
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      for (const timeframe of this.timeframes) {
        const count = await prisma.historicalData.count({
          where: {
            symbolId: symbolRecord.id,
            timeframe: timeframe.interval,
            timestamp: {
              gte: yesterday,
              lte: now
            }
          }
        });

        // 计算期望的记录数
        const expectedCount = Math.floor(24 * 60 * 60 * 1000 / timeframe.intervalMs);
        const completeness = (count / expectedCount * 100).toFixed(1);

        if (parseFloat(completeness) < 90) {
          log(`⚠️ ${timeframe.name}数据完整性警告: ${completeness}% (${count}/${expectedCount})`);
        } else {
          log(`✅ ${timeframe.name}数据完整性良好: ${completeness}%`);
        }
      }

    } catch (error) {
      log('❌ 数据完整性检查失败:', error.message);
    }
  }

  /**
   * 设置优雅关闭
   */
  private setupGracefulShutdown() {
    const shutdown = async (signal: string) => {
      log(`📴 收到${signal}信号，正在优雅关闭...`);
      
      if (this.isRunning) {
        log('⏳ 等待当前更新任务完成...');
        while (this.isRunning) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      await prisma.$disconnect();
      log('✅ 服务已安全关闭');
      if (typeof process !== 'undefined') {
        process.exit(0);
      }
    };

    if (typeof process !== 'undefined') {
      process.on('SIGINT', () => shutdown('SIGINT'));
      process.on('SIGTERM', () => shutdown('SIGTERM'));
    }
  }

  /**
   * 手动触发更新
   */
  async manualUpdate() {
    log('🔄 手动触发数据更新...');
    await this.updateAllTimeframes();
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      timeframes: this.timeframes.length,
      lastUpdate: new Date().toISOString()
    };
  }
}

// 主执行函数
async function main() {
  const updater = new AutoDataUpdater();
  
  // 启动自动更新服务
  await updater.start();

  // 保持进程运行
  if (typeof process !== 'undefined') {
    process.on('uncaughtException', (error) => {
      log('❌ 未捕获的异常:', error.message);
    });

    process.on('unhandledRejection', (reason) => {
      log('❌ 未处理的Promise拒绝:', reason);
    });
  }

  log('🎯 BTC数据自动更新服务正在运行...');
  log('💡 按 Ctrl+C 优雅关闭服务');
}

// 检查是否为主模块
if (typeof require !== 'undefined' && typeof module !== 'undefined' && require.main === module) {
  main().catch(console.error);
}

export { AutoDataUpdater };
