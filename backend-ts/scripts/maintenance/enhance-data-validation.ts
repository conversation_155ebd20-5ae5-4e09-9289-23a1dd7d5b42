/**
 * 增强数据验证机制，防止未来出现类似问题
 */

import { PrismaClient } from '@prisma/client';


/**
 * 数据验证增强
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

const prisma = new PrismaClient();

const logger = {
  info: (msg: string) => console.log(`ℹ️  ${msg}`),
  warn: (msg: string) => console.log(`⚠️  ${msg}`),
  error: (msg: string, error?: any) => {
    console.error(`❌ ${msg}`);
    if (error) console.error(error);
  }
};

async function main() {
  try {
    logger.info('🔧 开始增强数据验证机制...');

    // 1. 创建数据库约束检查函数
    await createValidationTriggers();

    // 2. 创建数据质量监控
    await setupDataQualityMonitoring();

    // 3. 创建自动修复机制
    await setupAutoRepairMechanism();

    logger.info('✅ 数据验证机制增强完成');

  } catch (error) {
    logger.error('❌ 增强过程中发生错误:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function createValidationTriggers() {
  logger.info('📋 创建数据库验证触发器...');

  // 创建符号格式验证函数
  const createValidationFunction = `
    CREATE OR REPLACE FUNCTION validate_symbol_format()
    RETURNS TRIGGER AS $$
    BEGIN
      -- 检查符号格式
      IF NEW.symbol !~ '^[A-Z0-9]{1,20}/[A-Z0-9]{1,20}$' THEN
        RAISE EXCEPTION 'Invalid symbol format: %. Expected format: BASE/QUOTE (e.g., BTC/USDT)', NEW.symbol;
      END IF;

      -- 检查基础资产和计价资产
      IF NEW.base_asset = '' OR NEW.base_asset IS NULL THEN
        RAISE EXCEPTION 'Base asset cannot be empty for symbol: %', NEW.symbol;
      END IF;

      IF NEW.quote_asset = '' OR NEW.quote_asset IS NULL THEN
        RAISE EXCEPTION 'Quote asset cannot be empty for symbol: %', NEW.symbol;
      END IF;

      -- 检查符号与资产的一致性
      IF split_part(NEW.symbol, '/', 1) != NEW.base_asset THEN
        RAISE EXCEPTION 'Base asset % does not match symbol %', NEW.base_asset, NEW.symbol;
      END IF;

      IF split_part(NEW.symbol, '/', 2) != NEW.quote_asset THEN
        RAISE EXCEPTION 'Quote asset % does not match symbol %', NEW.quote_asset, NEW.symbol;
      END IF;

      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  `;

  try {
    await prisma.$executeRawUnsafe(createValidationFunction);
    logger.info('✅ 验证函数创建成功');
  } catch (error) {
    logger.warn('⚠️  验证函数可能已存在，跳过创建');
  }

  // 创建触发器
  const createTrigger = `
    DROP TRIGGER IF EXISTS symbol_validation_trigger ON symbols;
    CREATE TRIGGER symbol_validation_trigger
      BEFORE INSERT OR UPDATE ON symbols
      FOR EACH ROW
      EXECUTE FUNCTION validate_symbol_format();
  `;

  try {
    await prisma.$executeRawUnsafe(createTrigger);
    logger.info('✅ 验证触发器创建成功');
  } catch (error) {
    logger.error('❌ 创建触发器失败:', error);
  }
}

async function setupDataQualityMonitoring() {
  logger.info('📊 设置数据质量监控...');

  // 创建数据质量检查视图
  const createQualityView = `
    CREATE OR REPLACE VIEW symbol_quality_check AS
    SELECT 
      id,
      symbol,
      base_asset,
      quote_asset,
      CASE 
        WHEN symbol !~ '^[A-Z0-9]{1,20}/[A-Z0-9]{1,20}$' THEN 'INVALID_FORMAT'
        WHEN base_asset = '' OR base_asset IS NULL THEN 'EMPTY_BASE_ASSET'
        WHEN quote_asset = '' OR quote_asset IS NULL THEN 'EMPTY_QUOTE_ASSET'
        WHEN split_part(symbol, '/', 1) != base_asset THEN 'BASE_ASSET_MISMATCH'
        WHEN split_part(symbol, '/', 2) != quote_asset THEN 'QUOTE_ASSET_MISMATCH'
        ELSE 'VALID'
      END as quality_status,
      created_at,
      updated_at
    FROM symbols;
  `;

  try {
    await prisma.$executeRawUnsafe(createQualityView);
    logger.info('✅ 数据质量检查视图创建成功');
  } catch (error) {
    logger.error('❌ 创建质量检查视图失败:', error);
  }
}

async function setupAutoRepairMechanism() {
  logger.info('🔧 设置自动修复机制...');

  // 创建自动修复函数
  const createRepairFunction = `
    CREATE OR REPLACE FUNCTION auto_repair_symbols()
    RETURNS TABLE(repaired_count INTEGER, error_count INTEGER) AS $$
    DECLARE
      repair_count INTEGER := 0;
      error_count INTEGER := 0;
      symbol_record RECORD;
    BEGIN
      -- 遍历所有有问题的符号
      FOR symbol_record IN 
        SELECT * FROM symbol_quality_check WHERE quality_status != 'VALID'
      LOOP
        BEGIN
          -- 尝试修复逻辑
          IF symbol_record.quality_status = 'INVALID_FORMAT' THEN
            -- 如果是格式问题，尝试智能修复
            IF symbol_record.symbol ~ '^[A-Z0-9]+USDT$' THEN
              UPDATE symbols 
              SET 
                symbol = regexp_replace(symbol_record.symbol, '([A-Z0-9]+)(USDT)$', '\\1/\\2'),
                base_asset = regexp_replace(symbol_record.symbol, '([A-Z0-9]+)USDT$', '\\1'),
                quote_asset = 'USDT'
              WHERE id = symbol_record.id;
              repair_count := repair_count + 1;
            END IF;
          ELSIF symbol_record.quality_status = 'EMPTY_BASE_ASSET' THEN
            -- 修复空的基础资产
            UPDATE symbols 
            SET base_asset = split_part(symbol_record.symbol, '/', 1)
            WHERE id = symbol_record.id;
            repair_count := repair_count + 1;
          ELSIF symbol_record.quality_status = 'EMPTY_QUOTE_ASSET' THEN
            -- 修复空的计价资产
            UPDATE symbols 
            SET quote_asset = split_part(symbol_record.symbol, '/', 2)
            WHERE id = symbol_record.id;
            repair_count := repair_count + 1;
          END IF;
        EXCEPTION
          WHEN OTHERS THEN
            error_count := error_count + 1;
        END;
      END LOOP;

      RETURN QUERY SELECT repair_count, error_count;
    END;
    $$ LANGUAGE plpgsql;
  `;

  try {
    await prisma.$executeRawUnsafe(createRepairFunction);
    logger.info('✅ 自动修复函数创建成功');
  } catch (error) {
    logger.error('❌ 创建自动修复函数失败:', error);
  }
}

main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
