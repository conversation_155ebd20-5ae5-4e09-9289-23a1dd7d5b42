#!/usr/bin/env tsx

/**
 * 技术债务评估脚本
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

interface TechnicalDebtReport {
  typeScriptErrors: number;
  schemaIssues: SchemaIssues;
  codeQualityIssues: CodeQualityIssues;
  databaseInconsistencies: DatabaseInconsistencies;
  totalDebtScore: number;
}

interface SchemaIssues {
  missingMapAnnotations: number;
  inconsistentNaming: string[];
  totalTables: number;
  problematicTables: string[];
}

interface CodeQualityIssues {
  unusedImports: number;
  unusedVariables: number;
  typeAssertions: number;
  anyTypes: number;
}

interface DatabaseInconsistencies {
  tableNameMismatches: string[];
  fieldNameMismatches: string[];
  typeConflicts: string[];
}

// 分析Schema文件
function analyzeSchema(): SchemaIssues {
  const schemaPath = 'prisma/schema.prisma';
  const schemaContent = fs.readFileSync(schemaPath, 'utf-8');
  
  // 提取所有model定义
  const modelMatches = schemaContent.match(/model\s+(\w+)\s*{[^}]+}/g) || [];
  const totalTables = modelMatches.length;
  
  // 检查@@map注解
  const mapAnnotations = schemaContent.match(/@@map\([^)]+\)/g) || [];
  const missingMapAnnotations = totalTables - mapAnnotations.length;
  
  // 检查命名不一致的表
  const inconsistentNaming: string[] = [];
  const problematicTables: string[] = [];
  
  modelMatches.forEach(model => {
    const modelName = model.match(/model\s+(\w+)/)?.[1];
    if (modelName) {
      // 检查是否使用下划线命名
      if (modelName.includes('_')) {
        inconsistentNaming.push(modelName);
      }
      
      // 检查是否缺少@@map
      if (!model.includes('@@map')) {
        problematicTables.push(modelName);
      }
    }
  });
  
  return {
    missingMapAnnotations,
    inconsistentNaming,
    totalTables,
    problematicTables
  };
}

// 分析TypeScript错误
function analyzeTypeScriptErrors(): number {
  try {
    execSync('npx tsc --noEmit', { stdio: 'pipe' });
    return 0;
  } catch (error: any) {
    const output = error.stdout?.toString() || '';
    const errorLines = output.split('\n').filter(line => line.includes('error TS'));
    return errorLines.length;
  }
}

// 分析代码质量问题
function analyzeCodeQuality(): CodeQualityIssues {
  const srcDir = 'src';
  let unusedImports = 0;
  let unusedVariables = 0;
  let typeAssertions = 0;
  let anyTypes = 0;
  
  function analyzeFile(filePath: string) {
    if (!filePath.endsWith('.ts')) return;
    
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // 统计 as any
    anyTypes += (content.match(/as any/g) || []).length;
    
    // 统计类型断言
    typeAssertions += (content.match(/as \w+/g) || []).length;
    
    // 简单统计未使用的导入（基于ESLint规则）
    const importLines = content.match(/^import.*from/gm) || [];
    const unusedImportPattern = /import\s+{\s*(\w+)\s*}/g;
    let match;
    while ((match = unusedImportPattern.exec(content)) !== null) {
      const importName = match[1];
      if (!content.includes(importName + '(') && !content.includes(importName + '.')) {
        unusedImports++;
      }
    }
  }
  
  function walkDir(dir: string) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      if (stat.isDirectory()) {
        walkDir(filePath);
      } else {
        analyzeFile(filePath);
      }
    });
  }
  
  if (fs.existsSync(srcDir)) {
    walkDir(srcDir);
  }
  
  return {
    unusedImports,
    unusedVariables,
    typeAssertions,
    anyTypes
  };
}

// 分析数据库不一致性
function analyzeDatabaseInconsistencies(): DatabaseInconsistencies {
  const tableNameMismatches: string[] = [];
  const fieldNameMismatches: string[] = [];
  const typeConflicts: string[] = [];
  
  // 已知的问题表
  const knownIssues = [
    'symbols vs symbol',
    'aiCallLogs vs aICallLog',
    'shortCyclePredictions vs shortCyclePrediction',
    'longCyclePredictions vs longCyclePrediction'
  ];
  
  tableNameMismatches.push(...knownIssues);
  
  // Symbol类型冲突
  typeConflicts.push('TradingSymbol vs JavaScript Symbol');
  
  return {
    tableNameMismatches,
    fieldNameMismatches,
    typeConflicts
  };
}

// 计算技术债务分数
function calculateDebtScore(report: Omit<TechnicalDebtReport, 'totalDebtScore'>): number {
  let score = 0;
  
  // TypeScript错误权重最高
  score += report.typeScriptErrors * 2;
  
  // Schema问题
  score += report.schemaIssues.missingMapAnnotations * 5;
  score += report.schemaIssues.inconsistentNaming.length * 3;
  
  // 代码质量问题
  score += report.codeQualityIssues.anyTypes * 1;
  score += report.codeQualityIssues.unusedImports * 0.5;
  
  // 数据库不一致性
  score += report.databaseInconsistencies.tableNameMismatches.length * 10;
  score += report.databaseInconsistencies.typeConflicts.length * 15;
  
  return Math.round(score);
}

// 生成报告
function generateReport(): TechnicalDebtReport {
  console.log('🔍 正在评估技术债务...\n');
  
  const typeScriptErrors = analyzeTypeScriptErrors();
  console.log(`📊 TypeScript错误: ${typeScriptErrors}`);
  
  const schemaIssues = analyzeSchema();
  console.log(`📊 Schema问题: ${schemaIssues.missingMapAnnotations}/${schemaIssues.totalTables} 表缺少@map`);
  
  const codeQualityIssues = analyzeCodeQuality();
  console.log(`📊 代码质量: ${codeQualityIssues.anyTypes} 个 'as any'`);
  
  const databaseInconsistencies = analyzeDatabaseInconsistencies();
  console.log(`📊 数据库不一致: ${databaseInconsistencies.tableNameMismatches.length} 个问题`);
  
  const report: TechnicalDebtReport = {
    typeScriptErrors,
    schemaIssues,
    codeQualityIssues,
    databaseInconsistencies,
    totalDebtScore: 0
  };
  
  report.totalDebtScore = calculateDebtScore(report);
  
  return report;
}

// 主函数
function main() {
  const report = generateReport();
  
  console.log('\n📋 技术债务评估报告');
  console.log('='.repeat(50));
  console.log(`总债务分数: ${report.totalDebtScore} 分`);
  console.log(`TypeScript错误: ${report.typeScriptErrors} 个`);
  console.log(`Schema问题: ${report.schemaIssues.missingMapAnnotations}/${report.schemaIssues.totalTables} 表需要修复`);
  console.log(`代码质量问题: ${report.codeQualityIssues.anyTypes} 个类型断言`);
  console.log(`数据库不一致: ${report.databaseInconsistencies.tableNameMismatches.length} 个`);
  
  // 保存详细报告
  fs.writeFileSync('technical-debt-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 详细报告已保存到: technical-debt-report.json');
  
  // 债务等级评估
  let debtLevel = '';
  if (report.totalDebtScore < 100) {
    debtLevel = '🟢 低';
  } else if (report.totalDebtScore < 500) {
    debtLevel = '🟡 中';
  } else if (report.totalDebtScore < 1000) {
    debtLevel = '🟠 高';
  } else {
    debtLevel = '🔴 极高';
  }
  
  console.log(`\n债务等级: ${debtLevel}`);
}

if (require.main === module) {
  main();
}
