import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';
import { UnifiedTechnicalIndicatorCalculator } from '../src/shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';


/**
 * 虚假数据清理
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

config();

const prisma = new PrismaClient();
const technicalCalculator = new UnifiedTechnicalIndicatorCalculator();

/**
 * 彻底清理假数据和模拟数据
 */
class FakeDataCleaner {
  async cleanupAllFakeData() {
    console.log('🧹 开始彻底清理假数据和模拟数据...');

    try {
      // 1. 检查并清理价格数据中的假数据
      await this.cleanupFakePriceData();

      // 2. 验证历史数据的真实性
      await this.validateHistoricalData();

      // 3. 重新生成正确的价格数据
      await this.regenerateCorrectPriceData();

      // 4. 验证系统数据完整性
      await this.validateSystemIntegrity();

      console.log('🎉 假数据清理完成！');

    } catch (error) {
      console.error('❌ 清理假数据失败:', error);
      throw error;
    }
  }

  private async cleanupFakePriceData() {
    console.log('\n🔍 检查价格数据中的假数据...');

    // 查找可疑的价格数据
    const suspiciousPrices = await prisma.priceData.findMany({
      where: {
        OR: [
          { price: { in: [45000, 46000, 44000, 43000, 42000, 50000] } }, // 常见的假价格
          { price: { lt: 1000 } }, // 过低的价格
          { price: { gt: 200000 } }, // 过高的价格
          { changePercent24h: { gt: 100 } }, // 不现实的变化
          { changePercent24h: { lt: -50 } }, // 不现实的下跌
        ]
      },
      include: {
        symbol: true
      }
    });

    console.log(`📊 发现 ${suspiciousPrices.length} 条可疑价格数据`);

    if (suspiciousPrices.length > 0) {
      console.log('🗑️  删除可疑价格数据...');
      
      for (const priceData of suspiciousPrices) {
        console.log(`   删除: ${priceData.symbol.symbol} - $${priceData.price} (${priceData.timestamp.toISOString()})`);
      }

      const deleteResult = await prisma.priceData.deleteMany({
        where: {
          id: {
            in: suspiciousPrices.map(p => p.id)
          }
        }
      });

      console.log(`✅ 删除了 ${deleteResult.count} 条假价格数据`);
    } else {
      console.log('✅ 未发现可疑价格数据');
    }
  }

  private async validateHistoricalData() {
    console.log('\n🔍 验证历史数据真实性...');

    const btcSymbol = await prisma.symbol.findFirst({
      where: { symbol: 'BTC/USDT' }
    });

    if (!btcSymbol) {
      throw new Error('未找到BTC/USDT符号');
    }

    // 检查历史数据的价格范围
    const priceStats = await prisma.historicalData.aggregate({
      where: { symbolId: btcSymbol.id },
      _min: { closePrice: true },
      _max: { closePrice: true },
      _avg: { closePrice: true },
      _count: true
    });

    console.log('📊 历史数据统计:');
    console.log(`   总记录数: ${priceStats._count}`);
    console.log(`   最低价: $${priceStats._min.closePrice}`);
    console.log(`   最高价: $${priceStats._max.closePrice}`);
    console.log(`   平均价: $${priceStats._avg.closePrice?.toFixed(2)}`);

    // 检查是否有不合理的价格
    const unreasonablePrices = await prisma.historicalData.findMany({
      where: {
        symbolId: btcSymbol.id,
        OR: [
          { closePrice: { lt: 1000 } },
          { closePrice: { gt: 200000 } },
          { openPrice: { lt: 1000 } },
          { openPrice: { gt: 200000 } },
          { highPrice: { lt: 1000 } },
          { highPrice: { gt: 200000 } },
          { lowPrice: { lt: 1000 } },
          { lowPrice: { gt: 200000 } }
        ]
      },
      take: 10
    });

    if (unreasonablePrices.length > 0) {
      console.log(`⚠️  发现 ${unreasonablePrices.length} 条不合理的历史价格数据`);
      unreasonablePrices.forEach(data => {
        console.log(`   ${data.timeframe} - ${data.timestamp.toISOString()}: $${data.closePrice}`);
      });
    } else {
      console.log('✅ 历史数据价格范围合理');
    }
  }

  private async regenerateCorrectPriceData() {
    console.log('\n🔄 重新生成正确的价格数据...');

    const btcSymbol = await prisma.symbol.findFirst({
      where: { symbol: 'BTC/USDT' }
    });

    if (!btcSymbol) {
      throw new Error('未找到BTC/USDT符号');
    }

    // 获取最新的历史数据
    const latestHistorical = await prisma.historicalData.findFirst({
      where: { 
        symbolId: btcSymbol.id,
        timeframe: '1h'
      },
      orderBy: { timestamp: 'desc' }
    });

    if (!latestHistorical) {
      throw new Error('未找到历史数据');
    }

    const currentPrice = parseFloat(latestHistorical.closePrice.toString());
    console.log(`📈 基于最新历史数据生成价格数据: $${currentPrice}`);

    // 获取24小时前的数据计算变化
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const yesterdayData = await prisma.historicalData.findFirst({
      where: {
        symbolId: btcSymbol.id,
        timeframe: '1d',
        timestamp: { lte: yesterday }
      },
      orderBy: { timestamp: 'desc' }
    });

    const yesterdayPrice = yesterdayData ? parseFloat(yesterdayData.closePrice.toString()) : currentPrice;
    const change24h = currentPrice - yesterdayPrice;
    const changePercent24h = (change24h / yesterdayPrice) * 100;

    // 获取24小时高低价
    const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recent24hData = await prisma.historicalData.findMany({
      where: {
        symbolId: btcSymbol.id,
        timeframe: '1h',
        timestamp: { gte: dayAgo }
      },
      orderBy: { timestamp: 'asc' }
    });

    let high24h = currentPrice;
    let low24h = currentPrice;
    let volume24h = 0;

    if (recent24hData.length > 0) {
      high24h = Math.max(...recent24hData.map(d => parseFloat(d.highPrice.toString())));
      low24h = Math.min(...recent24hData.map(d => parseFloat(d.lowPrice.toString())));
      volume24h = recent24hData.reduce((sum, d) => sum + parseFloat(d.volume.toString()), 0);
    }

    // 计算技术指标
    const technicalIndicators = await this.calculateTechnicalIndicators(btcSymbol.id);

    // 删除现有的价格数据
    await prisma.priceData.deleteMany({
      where: { symbolId: btcSymbol.id }
    });

    // 创建新的正确价格数据
    const newPriceData = await prisma.priceData.create({
      data: {
        symbolId: btcSymbol.id,
        price: currentPrice,
        change24h,
        changePercent24h,
        volume24h,
        high24h,
        low24h,
        marketCap: currentPrice * 19700000, // BTC流通量
        rsi14: technicalIndicators.rsi,
        macdLine: technicalIndicators.macd,
        macdSignal: technicalIndicators.macdSignal,
        macdHistogram: technicalIndicators.macdHistogram,
        bollingerUpper: technicalIndicators.bollingerUpper,
        bollingerMiddle: technicalIndicators.bollingerMiddle,
        bollingerLower: technicalIndicators.bollingerLower,
        stochasticK: technicalIndicators.stochasticK,
        stochasticD: technicalIndicators.stochasticD,
        williamsR: technicalIndicators.williamsR,
        cci: technicalIndicators.cci,
        atr: technicalIndicators.atr,
        supportLevel: technicalIndicators.supportLevel,
        resistanceLevel: technicalIndicators.resistanceLevel,
        fearGreedIndex: 50,
        socialSentiment: 0,
        dataQuality: 1.0,
        dataFreshness: 1.0,
        timestamp: new Date()
      }
    });

    console.log('✅ 重新生成的价格数据:');
    console.log(`   价格: $${newPriceData.price}`);
    console.log(`   24h变化: ${newPriceData.changePercent24h.toFixed(2)}%`);
    console.log(`   24h高: $${newPriceData.high24h}`);
    console.log(`   24h低: $${newPriceData.low24h}`);
    console.log(`   成交量: ${newPriceData.volume24h.toFixed(2)}`);
  }

  private async calculateTechnicalIndicators(symbolId: string) {
    // 获取最近200条1小时数据用于计算技术指标
    const historicalData = await prisma.historicalData.findMany({
      where: {
        symbolId,
        timeframe: '1h'
      },
      orderBy: { timestamp: 'desc' },
      take: 200
    });

    if (historicalData.length < 50) {
      console.warn('⚠️  历史数据不足，使用默认技术指标值');
      return this.getDefaultTechnicalIndicators();
    }

    // 反转数组，使其按时间正序排列
    const data = historicalData.reverse();
    const closes = data.map(d => parseFloat(d.closePrice.toString()));
    const highs = data.map(d => parseFloat(d.highPrice.toString()));
    const lows = data.map(d => parseFloat(d.lowPrice.toString()));

    // 计算技术指标 - 使用统一技术指标计算器
    const rsiResult = technicalCalculator.calculateRSI(closes, 14);
    const macdResult = technicalCalculator.calculateMACD(closes);

    const rsi = rsiResult.value;
    const macd = { macd: macdResult.macd, signal: macdResult.signal, histogram: macdResult.histogram };

    // 计算布林带 - 使用统一技术指标计算器
    const bollingerResult = technicalCalculator.calculateBollingerBands(closes, 20, 2);
    const bollinger = {
      upper: bollingerResult.upperBand,
      middle: bollingerResult.middleBand,
      lower: bollingerResult.lowerBand
    };

    // 计算其他指标
    const stochastic = this.calculateStochastic(highs, lows, closes, 14);
    const williamsR = this.calculateWilliamsR(highs, lows, closes, 14);
    const cci = this.calculateCCI(highs, lows, closes, 20);
    const atr = this.calculateATR(highs, lows, closes, 14);
    const supportResistance = this.calculateSupportResistance(highs, lows, closes);

    return {
      rsi,
      macd: macd.macd,
      macdSignal: macd.signal,
      macdHistogram: macd.histogram,
      bollingerUpper: bollinger.upper,
      bollingerMiddle: bollinger.middle,
      bollingerLower: bollinger.lower,
      stochasticK: stochastic.k,
      stochasticD: stochastic.d,
      williamsR,
      cci,
      atr,
      supportLevel: supportResistance.support,
      resistanceLevel: supportResistance.resistance
    };
  }

  // 注意：calculateRSI方法已移除
  // 现在统一使用UnifiedTechnicalIndicatorCalculator进行RSI计算

  // 注意：calculateMACD方法已移除
  // 现在统一使用UnifiedTechnicalIndicatorCalculator进行MACD计算

  // 注意：calculateEMA和calculateBollingerBands方法已移除
  // 现在统一使用UnifiedTechnicalIndicatorCalculator进行所有技术指标计算

  private calculateStochastic(highs: number[], lows: number[], closes: number[], period: number) {
    if (closes.length < period) {
      return { k: 50, d: 50 };
    }

    const recentHighs = highs.slice(-period);
    const recentLows = lows.slice(-period);
    const currentClose = closes[closes.length - 1];

    const highestHigh = Math.max(...recentHighs);
    const lowestLow = Math.min(...recentLows);

    const k = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
    const d = k * 0.9; // 简化计算

    return { k, d };
  }

  private calculateWilliamsR(highs: number[], lows: number[], closes: number[], period: number): number {
    if (closes.length < period) return -50;

    const recentHighs = highs.slice(-period);
    const recentLows = lows.slice(-period);
    const currentClose = closes[closes.length - 1];

    const highestHigh = Math.max(...recentHighs);
    const lowestLow = Math.min(...recentLows);

    return ((highestHigh - currentClose) / (highestHigh - lowestLow)) * -100;
  }

  private calculateCCI(highs: number[], lows: number[], closes: number[], period: number): number {
    if (closes.length < period) return 0;

    const typicalPrices = closes.map((close, i) => (highs[i] + lows[i] + close) / 3);
    const recentTypicalPrices = typicalPrices.slice(-period);
    
    const sma = recentTypicalPrices.reduce((sum, tp) => sum + tp, 0) / period;
    const meanDeviation = recentTypicalPrices.reduce((sum, tp) => sum + Math.abs(tp - sma), 0) / period;

    const currentTypicalPrice = typicalPrices[typicalPrices.length - 1];
    return (currentTypicalPrice - sma) / (0.015 * meanDeviation);
  }

  private calculateATR(highs: number[], lows: number[], closes: number[], period: number): number {
    if (closes.length < 2) return 0;

    const trueRanges = [];
    for (let i = 1; i < closes.length; i++) {
      const tr1 = highs[i] - lows[i];
      const tr2 = Math.abs(highs[i] - closes[i - 1]);
      const tr3 = Math.abs(lows[i] - closes[i - 1]);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }

    const recentTR = trueRanges.slice(-period);
    return recentTR.reduce((sum, tr) => sum + tr, 0) / recentTR.length;
  }

  private calculateSupportResistance(highs: number[], lows: number[], closes: number[]) {
    if (closes.length === 0) {
      return { support: 0, resistance: 0 };
    }

    const recentData = closes.slice(-50);
    const support = Math.min(...recentData) * 0.98;
    const resistance = Math.max(...recentData) * 1.02;

    return { support, resistance };
  }

  private getDefaultTechnicalIndicators() {
    return {
      rsi: 50,
      macd: 0,
      macdSignal: 0,
      macdHistogram: 0,
      bollingerUpper: 0,
      bollingerMiddle: 0,
      bollingerLower: 0,
      stochasticK: 50,
      stochasticD: 50,
      williamsR: -50,
      cci: 0,
      atr: 0,
      supportLevel: 0,
      resistanceLevel: 0
    };
  }

  private async validateSystemIntegrity() {
    console.log('\n🔍 验证系统数据完整性...');

    const btcSymbol = await prisma.symbol.findFirst({
      where: { symbol: 'BTC/USDT' }
    });

    if (!btcSymbol) {
      throw new Error('未找到BTC/USDT符号');
    }

    // 检查价格数据
    const priceData = await prisma.priceData.findFirst({
      where: { symbolId: btcSymbol.id },
      orderBy: { timestamp: 'desc' }
    });

    if (!priceData) {
      throw new Error('未找到价格数据');
    }

    // 检查历史数据
    const historicalCount = await prisma.historicalData.count({
      where: { symbolId: btcSymbol.id }
    });

    console.log('✅ 系统数据完整性验证:');
    console.log(`   BTC符号: ${btcSymbol.symbol}`);
    console.log(`   当前价格: $${priceData.price}`);
    console.log(`   历史数据: ${historicalCount} 条`);
    console.log(`   价格数据时间: ${priceData.timestamp.toISOString()}`);

    // 验证价格合理性
    const currentPrice = parseFloat(priceData.price.toString());
    if (currentPrice < 10000 || currentPrice > 200000) {
      throw new Error(`价格不合理: $${currentPrice}`);
    }

    console.log('✅ 系统数据完整性验证通过');
  }
}

// 运行清理
async function main() {
  const cleaner = new FakeDataCleaner();
  
  try {
    await cleaner.cleanupAllFakeData();
    console.log('\n🎉 假数据清理和验证完成！');
    console.log('💡 现在可以重新测试交易信号API');
  } catch (error) {
    console.error('\n❌ 清理失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}
