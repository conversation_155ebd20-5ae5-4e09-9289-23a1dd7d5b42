import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';


/**
 * 数据保护设置
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

config();

const prisma = new PrismaClient();

/**
 * 设置数据保护机制
 */
class DataProtectionSetup {
  async setupDataProtection() {
    console.log('🛡️ 开始设置数据保护机制...\n');

    try {
      await prisma.$connect();
      console.log('✅ 数据库连接成功\n');

      // 1. 创建审计日志表
      await this.createAuditLogTable();

      // 2. 创建数据保护触发器
      await this.createDataProtectionTriggers();

      // 3. 创建数据备份表
      await this.createBackupTable();

      // 4. 验证保护机制
      await this.verifyProtectionMechanism();

      console.log('\n🎉 数据保护机制设置完成！');

    } catch (error) {
      console.error('❌ 数据保护设置失败:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  private async createAuditLogTable() {
    console.log('📝 创建审计日志表...');
    console.log('─'.repeat(50));

    try {
      // 创建审计日志表
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS data_audit_log (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          table_name VARCHAR(255) NOT NULL,
          operation VARCHAR(50) NOT NULL,
          record_id UUID,
          old_values JSONB,
          new_values JSONB,
          user_id VARCHAR(255),
          timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          ip_address INET,
          user_agent TEXT,
          reason TEXT
        );
      `;

      // 创建索引
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON data_audit_log(table_name);
      `;
      
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON data_audit_log(timestamp);
      `;
      
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_audit_log_operation ON data_audit_log(operation);
      `;

      console.log('✅ 审计日志表创建成功');
    } catch (error) {
      console.log(`⚠️ 审计日志表创建失败: ${error.message}`);
    }
  }

  private async createDataProtectionTriggers() {
    console.log('\n🔒 创建数据保护触发器...');
    console.log('─'.repeat(50));

    try {
      // 创建审计触发器函数
      await prisma.$executeRaw`
        CREATE OR REPLACE FUNCTION audit_trigger_function()
        RETURNS TRIGGER AS $$
        BEGIN
          IF TG_OP = 'DELETE' THEN
            INSERT INTO data_audit_log (table_name, operation, record_id, old_values)
            VALUES (TG_TABLE_NAME, TG_OP, OLD.id, row_to_json(OLD));
            RETURN OLD;
          ELSIF TG_OP = 'UPDATE' THEN
            INSERT INTO data_audit_log (table_name, operation, record_id, old_values, new_values)
            VALUES (TG_TABLE_NAME, TG_OP, NEW.id, row_to_json(OLD), row_to_json(NEW));
            RETURN NEW;
          ELSIF TG_OP = 'INSERT' THEN
            INSERT INTO data_audit_log (table_name, operation, record_id, new_values)
            VALUES (TG_TABLE_NAME, TG_OP, NEW.id, row_to_json(NEW));
            RETURN NEW;
          END IF;
          RETURN NULL;
        END;
        $$ LANGUAGE plpgsql;
      `;

      // 创建历史数据保护触发器函数
      await prisma.$executeRaw`
        CREATE OR REPLACE FUNCTION protect_historical_data()
        RETURNS TRIGGER AS $$
        BEGIN
          -- 检查是否尝试删除受保护的数据
          IF TG_OP = 'DELETE' AND OLD.is_protected = true THEN
            RAISE EXCEPTION 'Cannot delete protected historical data. Record ID: %', OLD.id;
          END IF;
          
          -- 检查是否尝试修改受保护数据的核心字段
          IF TG_OP = 'UPDATE' AND OLD.is_protected = true THEN
            -- 检查核心字段是否被修改
            IF OLD.open_price != NEW.open_price OR 
               OLD.high_price != NEW.high_price OR 
               OLD.low_price != NEW.low_price OR 
               OLD.close_price != NEW.close_price OR 
               OLD.volume != NEW.volume OR 
               OLD.timestamp != NEW.timestamp OR 
               OLD.timeframe != NEW.timeframe OR 
               OLD.symbol_id != NEW.symbol_id THEN
              RAISE EXCEPTION 'Cannot modify core fields of protected historical data. Record ID: %', OLD.id;
            END IF;
          END IF;
          
          IF TG_OP = 'DELETE' THEN
            RETURN OLD;
          ELSE
            RETURN NEW;
          END IF;
        END;
        $$ LANGUAGE plpgsql;
      `;

      // 为历史数据表创建保护触发器
      await prisma.$executeRaw`
        DROP TRIGGER IF EXISTS protect_historical_data_trigger ON historical_data;
      `;
      
      await prisma.$executeRaw`
        CREATE TRIGGER protect_historical_data_trigger
        BEFORE UPDATE OR DELETE ON historical_data
        FOR EACH ROW EXECUTE FUNCTION protect_historical_data();
      `;

      // 为历史数据表创建审计触发器
      await prisma.$executeRaw`
        DROP TRIGGER IF EXISTS audit_historical_data_trigger ON historical_data;
      `;
      
      await prisma.$executeRaw`
        CREATE TRIGGER audit_historical_data_trigger
        AFTER INSERT OR UPDATE OR DELETE ON historical_data
        FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
      `;

      // 为价格数据表创建审计触发器
      await prisma.$executeRaw`
        DROP TRIGGER IF EXISTS audit_price_data_trigger ON price_data;
      `;
      
      await prisma.$executeRaw`
        CREATE TRIGGER audit_price_data_trigger
        AFTER INSERT OR UPDATE OR DELETE ON price_data
        FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
      `;

      console.log('✅ 数据保护触发器创建成功');
    } catch (error) {
      console.log(`⚠️ 数据保护触发器创建失败: ${error.message}`);
    }
  }

  private async createBackupTable() {
    console.log('\n💾 创建数据备份表...');
    console.log('─'.repeat(50));

    try {
      // 创建历史数据备份表
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS historical_data_backup (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          original_id UUID NOT NULL,
          symbol_id UUID NOT NULL,
          timeframe VARCHAR(10) NOT NULL,
          timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
          open_price DECIMAL(20, 8) NOT NULL,
          high_price DECIMAL(20, 8) NOT NULL,
          low_price DECIMAL(20, 8) NOT NULL,
          close_price DECIMAL(20, 8) NOT NULL,
          volume DECIMAL(20, 8) NOT NULL,
          trades INTEGER,
          quote_volume DECIMAL(20, 8),
          data_quality DECIMAL(3, 2) DEFAULT 1.0,
          is_protected BOOLEAN DEFAULT true,
          data_source VARCHAR(50) DEFAULT 'binance',
          backup_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          backup_reason TEXT,
          created_at TIMESTAMP WITH TIME ZONE NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE NOT NULL
        );
      `;

      // 创建备份表索引
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_backup_original_id ON historical_data_backup(original_id);
      `;
      
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_backup_symbol_timeframe ON historical_data_backup(symbol_id, timeframe);
      `;
      
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_backup_timestamp ON historical_data_backup(backup_timestamp);
      `;

      console.log('✅ 数据备份表创建成功');
    } catch (error) {
      console.log(`⚠️ 数据备份表创建失败: ${error.message}`);
    }
  }

  private async verifyProtectionMechanism() {
    console.log('\n🔍 验证数据保护机制...');
    console.log('─'.repeat(50));

    // 检查触发器是否存在
    const triggers = await prisma.$queryRaw`
      SELECT trigger_name, event_manipulation, event_object_table
      FROM information_schema.triggers
      WHERE trigger_schema = 'public'
      AND (trigger_name LIKE '%protect%' OR trigger_name LIKE '%audit%')
      ORDER BY event_object_table, trigger_name;
    `;

    console.log('📋 已创建的触发器:');
    if (Array.isArray(triggers) && triggers.length > 0) {
      triggers.forEach((trigger: any) => {
        console.log(`   ✅ ${trigger.trigger_name} (${trigger.event_object_table}) - ${trigger.event_manipulation}`);
      });
    } else {
      console.log('   ⚠️ 未找到保护触发器');
    }

    // 检查表是否存在
    const tables = await prisma.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name IN ('data_audit_log', 'historical_data_backup')
      ORDER BY table_name;
    `;

    console.log('\n📋 保护相关表:');
    if (Array.isArray(tables) && tables.length > 0) {
      tables.forEach((table: any) => {
        console.log(`   ✅ ${table.table_name}`);
      });
    } else {
      console.log('   ⚠️ 未找到保护相关表');
    }

    // 检查数据保护状态
    const totalData = await prisma.historicalData.count();
    const protectedData = await prisma.historicalData.count({
      where: { isProtected: true }
    });

    console.log('\n📊 数据保护状态:');
    console.log(`   总数据: ${totalData} 条`);
    console.log(`   受保护: ${protectedData} 条 (${(protectedData/totalData*100).toFixed(1)}%)`);

    if (protectedData === totalData) {
      console.log('   ✅ 所有历史数据都已受保护');
    } else {
      console.log('   ⚠️ 部分数据未受保护');
    }
  }
}

// 运行数据保护设置
async function main() {
  const setup = new DataProtectionSetup();
  
  try {
    await setup.setupDataProtection();
    console.log('\n✅ 数据保护机制设置完成！');
    console.log('\n💡 建议:');
    console.log('   • 定期检查审计日志以监控数据访问');
    console.log('   • 定期备份重要数据到备份表');
    console.log('   • 监控保护触发器的性能影响');
  } catch (error) {
    console.error('\n❌ 数据保护设置失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}
