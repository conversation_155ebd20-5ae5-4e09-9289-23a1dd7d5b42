#!/usr/bin/env tsx

/**
 * 全局数据库架构审计工具
 * 
 * 从项目全局角度深度分析数据库设计问题：
 * 1. 架构一致性分析
 * 2. 数据冗余和重复分析
 * 3. 关系完整性检查
 * 4. 性能瓶颈识别
 * 5. 扩展性评估
 * 6. 安全性审计
 */

import fs from 'fs';
import path from 'path';
import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';

config();

interface DatabaseAuditReport {
  architecturalIssues: ArchitecturalIssue[];
  redundancyProblems: RedundancyProblem[];
  relationshipIssues: RelationshipIssue[];
  performanceBottlenecks: PerformanceBottleneck[];
  scalabilityRisks: ScalabilityRisk[];
  securityConcerns: SecurityConcern[];
  recommendations: Recommendation[];
  overallScore: number;
}

interface ArchitecturalIssue {
  type: 'naming_inconsistency' | 'design_pattern_violation' | 'normalization_issue';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedTables: string[];
  impact: string;
  solution: string;
}

interface RedundancyProblem {
  type: 'duplicate_tables' | 'redundant_fields' | 'unnecessary_complexity';
  tables: string[];
  description: string;
  wastedStorage: string;
  solution: string;
}

interface RelationshipIssue {
  type: 'missing_foreign_key' | 'circular_dependency' | 'orphaned_records';
  tables: string[];
  description: string;
  dataIntegrityRisk: string;
  solution: string;
}

interface PerformanceBottleneck {
  type: 'missing_index' | 'inefficient_query_pattern' | 'large_table_scan';
  table: string;
  description: string;
  estimatedImpact: string;
  solution: string;
}

interface ScalabilityRisk {
  type: 'single_table_growth' | 'relationship_complexity' | 'json_field_abuse';
  description: string;
  riskLevel: 'low' | 'medium' | 'high';
  solution: string;
}

interface SecurityConcern {
  type: 'sensitive_data_exposure' | 'weak_access_control' | 'audit_trail_missing';
  description: string;
  severity: 'low' | 'medium' | 'high';
  solution: string;
}

interface Recommendation {
  priority: 'immediate' | 'short_term' | 'long_term';
  category: string;
  description: string;
  effort: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
}

class ComprehensiveDatabaseAuditor {
  private prisma: PrismaClient;
  private schemaContent: string;
  private models: any[] = [];

  constructor() {
    this.prisma = new PrismaClient();
    this.schemaContent = fs.readFileSync(path.resolve(__dirname, '../prisma/schema.prisma'), 'utf-8');
    this.parseModels();
  }

  /**
   * 执行全面的数据库审计
   */
  async runComprehensiveAudit(): Promise<DatabaseAuditReport> {
    console.log('🔍 开始全面数据库架构审计...\n');

    const report: DatabaseAuditReport = {
      architecturalIssues: await this.analyzeArchitecturalIssues(),
      redundancyProblems: await this.analyzeRedundancyProblems(),
      relationshipIssues: await this.analyzeRelationshipIssues(),
      performanceBottlenecks: await this.analyzePerformanceBottlenecks(),
      scalabilityRisks: await this.analyzeScalabilityRisks(),
      securityConcerns: await this.analyzeSecurityConcerns(),
      recommendations: [],
      overallScore: 0
    };

    // 生成综合建议
    report.recommendations = this.generateRecommendations(report);
    report.overallScore = this.calculateOverallScore(report);

    return report;
  }

  /**
   * 解析Schema模型
   */
  private parseModels(): void {
    const modelRegex = /model\s+(\w+)\s*{([^}]*)}/gs;
    let match;

    while ((match = modelRegex.exec(this.schemaContent)) !== null) {
      const modelName = match[1];
      const modelBody = match[2];
      
      this.models.push({
        name: modelName,
        body: modelBody,
        fields: this.parseFields(modelBody),
        relations: this.parseRelations(modelBody),
        indexes: this.parseIndexes(modelBody)
      });
    }
  }

  /**
   * 解析字段
   */
  private parseFields(modelBody: string): any[] {
    const fieldRegex = /(\w+)\s+(\w+[\w\[\]?]*)/g;
    const fields = [];
    let match;

    while ((match = fieldRegex.exec(modelBody)) !== null) {
      if (!match[1].startsWith('@@') && !match[1].startsWith('//')) {
        fields.push({
          name: match[1],
          type: match[2]
        });
      }
    }

    return fields;
  }

  /**
   * 解析关系
   */
  private parseRelations(modelBody: string): any[] {
    const relationRegex = /(\w+)\s+(\w+[\[\]]*)\s+@relation/g;
    const relations = [];
    let match;

    while ((match = relationRegex.exec(modelBody)) !== null) {
      relations.push({
        field: match[1],
        type: match[2]
      });
    }

    return relations;
  }

  /**
   * 解析索引
   */
  private parseIndexes(modelBody: string): any[] {
    const indexRegex = /@@index\(\[([^\]]+)\]\)/g;
    const indexes = [];
    let match;

    while ((match = indexRegex.exec(modelBody)) !== null) {
      indexes.push({
        fields: match[1].split(',').map(f => f.trim())
      });
    }

    return indexes;
  }

  /**
   * 分析架构问题
   */
  private async analyzeArchitecturalIssues(): Promise<ArchitecturalIssue[]> {
    console.log('🏗️ 分析架构问题...');
    
    const issues: ArchitecturalIssue[] = [];

    // 1. 命名不一致性检查
    const namingIssues = this.checkNamingConsistency();
    issues.push(...namingIssues);

    // 2. 设计模式违反检查
    const patternIssues = this.checkDesignPatterns();
    issues.push(...patternIssues);

    // 3. 规范化问题检查
    const normalizationIssues = this.checkNormalization();
    issues.push(...normalizationIssues);

    console.log(`   发现 ${issues.length} 个架构问题`);
    return issues;
  }

  /**
   * 检查命名一致性
   */
  private checkNamingConsistency(): ArchitecturalIssue[] {
    const issues: ArchitecturalIssue[] = [];
    
    // 检查模型命名模式
    const inconsistentModels = this.models.filter(model => {
      // 检查是否遵循PascalCase
      return !/^[A-Z][a-zA-Z0-9]*$/.test(model.name);
    });

    if (inconsistentModels.length > 0) {
      issues.push({
        type: 'naming_inconsistency',
        severity: 'medium',
        description: '模型命名不遵循PascalCase规范',
        affectedTables: inconsistentModels.map(m => m.name),
        impact: '降低代码可读性和维护性',
        solution: '重命名模型以遵循PascalCase规范'
      });
    }

    // 检查字段命名模式
    const fieldNamingIssues = this.models.filter(model => {
      return model.fields.some((field: any) => {
        // 检查是否遵循camelCase
        return !/^[a-z][a-zA-Z0-9]*$/.test(field.name);
      });
    });

    if (fieldNamingIssues.length > 0) {
      issues.push({
        type: 'naming_inconsistency',
        severity: 'low',
        description: '字段命名不遵循camelCase规范',
        affectedTables: fieldNamingIssues.map(m => m.name),
        impact: '影响代码一致性',
        solution: '重命名字段以遵循camelCase规范'
      });
    }

    return issues;
  }

  /**
   * 检查设计模式
   */
  private checkDesignPatterns(): ArchitecturalIssue[] {
    const issues: ArchitecturalIssue[] = [];

    // 检查时间戳字段一致性
    const timestampInconsistency = this.models.filter(model => {
      const hasCreatedAt = model.fields.some((f: any) => f.name === 'createdAt');
      const hasUpdatedAt = model.fields.some((f: any) => f.name === 'updatedAt');
      return hasCreatedAt !== hasUpdatedAt; // 应该同时存在或同时不存在
    });

    if (timestampInconsistency.length > 0) {
      issues.push({
        type: 'design_pattern_violation',
        severity: 'medium',
        description: '时间戳字段不一致（createdAt和updatedAt应该成对出现）',
        affectedTables: timestampInconsistency.map(m => m.name),
        impact: '审计追踪不完整',
        solution: '为所有实体表添加完整的时间戳字段'
      });
    }

    return issues;
  }

  /**
   * 检查规范化问题
   */
  private checkNormalization(): ArchitecturalIssue[] {
    const issues: ArchitecturalIssue[] = [];

    // 检查过度使用JSON字段
    const jsonHeavyModels = this.models.filter(model => {
      const jsonFields = model.fields.filter((f: any) => f.type === 'Json');
      return jsonFields.length > 3; // 超过3个JSON字段可能有问题
    });

    if (jsonHeavyModels.length > 0) {
      issues.push({
        type: 'normalization_issue',
        severity: 'medium',
        description: '过度使用JSON字段，可能违反数据库规范化原则',
        affectedTables: jsonHeavyModels.map(m => m.name),
        impact: '查询性能下降，数据完整性难以保证',
        solution: '考虑将JSON字段拆分为独立的关系表'
      });
    }

    return issues;
  }
