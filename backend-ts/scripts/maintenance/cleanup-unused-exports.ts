#!/usr/bin/env npx tsx

/**
 * 清理未使用的导出脚本
 * 基于ts-unused-exports的输出，自动清理未使用的导出
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

interface UnusedExport {
  filePath: string;
  exports: string[];
}

class UnusedExportsCleanup {
  private srcDir = path.join(process.cwd(), 'src');
  private backupDir = path.join(process.cwd(), 'backups', 'unused-exports-cleanup');
  private cleanupResults: { filesProcessed: number; exportsRemoved: number; } = {
    filesProcessed: 0,
    exportsRemoved: 0
  };

  async runCleanup(): Promise<void> {
    console.log('🧹 开始清理未使用的导出...\n');

    // 创建备份目录
    this.ensureBackupDirectory();

    // 获取未使用的导出列表
    const unusedExports = await this.getUnusedExports();
    
    if (unusedExports.length === 0) {
      console.log('✅ 没有发现未使用的导出');
      return;
    }

    console.log(`📊 发现 ${unusedExports.length} 个文件包含未使用的导出\n`);

    // 处理每个文件
    for (const fileExports of unusedExports) {
      await this.cleanupFileExports(fileExports);
    }

    // 显示清理结果
    this.displayResults();
  }

  private ensureBackupDirectory(): void {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  private async getUnusedExports(): Promise<UnusedExport[]> {
    try {
      // 运行ts-unused-exports
      const output = execSync('npx ts-unused-exports tsconfig.json --excludeDeclarationFiles', {
        encoding: 'utf8',
        stdio: 'pipe'
      });

      return this.parseUnusedExportsOutput(output);
    } catch (error: any) {
      // ts-unused-exports 在发现未使用导出时返回非零退出码
      if (error.stdout) {
        return this.parseUnusedExportsOutput(error.stdout);
      }
      console.error('❌ 运行ts-unused-exports失败:', error.message);
      return [];
    }
  }

  private parseUnusedExportsOutput(output: string): UnusedExport[] {
    const lines = output.split('\n').filter(line => line.trim());
    const unusedExports: UnusedExport[] = [];

    for (const line of lines) {
      if (line.includes('modules with unused exports')) {
        continue;
      }

      const match = line.match(/^(.+?):\s*(.+)$/);
      if (match) {
        const filePath = match[1].trim();
        const exportsStr = match[2].trim();
        
        // 解析导出列表
        const exports = exportsStr.split(',').map(exp => exp.trim()).filter(exp => exp);
        
        if (exports.length > 0) {
          unusedExports.push({ filePath, exports });
        }
      }
    }

    return unusedExports;
  }

  private async cleanupFileExports(fileExports: UnusedExport): Promise<void> {
    const { filePath, exports } = fileExports;
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return;
    }

    console.log(`🔧 处理文件: ${path.relative(process.cwd(), filePath)}`);
    console.log(`   未使用导出: ${exports.join(', ')}`);

    try {
      // 创建备份
      const backupPath = this.createBackup(filePath);
      console.log(`   💾 备份到: ${path.relative(process.cwd(), backupPath)}`);

      // 读取文件内容
      const content = fs.readFileSync(filePath, 'utf8');
      let modifiedContent = content;
      let removedCount = 0;

      // 移除未使用的导出
      for (const exportName of exports) {
        const result = this.removeExport(modifiedContent, exportName);
        if (result.modified) {
          modifiedContent = result.content;
          removedCount++;
        }
      }

      // 如果有修改，写入文件
      if (removedCount > 0) {
        fs.writeFileSync(filePath, modifiedContent);
        console.log(`   ✅ 移除了 ${removedCount} 个未使用的导出`);
        
        this.cleanupResults.filesProcessed++;
        this.cleanupResults.exportsRemoved += removedCount;
      } else {
        console.log(`   ⚠️  没有找到可移除的导出`);
      }

    } catch (error) {
      console.error(`   ❌ 处理失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }

    console.log('');
  }

  private createBackup(filePath: string): string {
    const relativePath = path.relative(this.srcDir, filePath);
    const backupPath = path.join(this.backupDir, relativePath);
    
    // 确保备份目录存在
    const backupDir = path.dirname(backupPath);
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // 复制文件
    fs.copyFileSync(filePath, backupPath);
    return backupPath;
  }

  private removeExport(content: string, exportName: string): { content: string; modified: boolean } {
    let modified = false;
    let newContent = content;

    // 匹配各种导出模式
    const patterns = [
      // export const/let/var name = ...
      new RegExp(`^export\\s+(const|let|var)\\s+${this.escapeRegex(exportName)}\\s*[=:].*?;?$`, 'gm'),
      
      // export function name(...) { ... }
      new RegExp(`^export\\s+function\\s+${this.escapeRegex(exportName)}\\s*\\([^)]*\\)\\s*\\{[^}]*\\}`, 'gm'),
      
      // export class name { ... }
      new RegExp(`^export\\s+class\\s+${this.escapeRegex(exportName)}\\s*\\{[\\s\\S]*?^\\}`, 'gm'),
      
      // export interface name { ... }
      new RegExp(`^export\\s+interface\\s+${this.escapeRegex(exportName)}\\s*\\{[\\s\\S]*?^\\}`, 'gm'),
      
      // export type name = ...
      new RegExp(`^export\\s+type\\s+${this.escapeRegex(exportName)}\\s*=.*?;?$`, 'gm'),
      
      // export enum name { ... }
      new RegExp(`^export\\s+enum\\s+${this.escapeRegex(exportName)}\\s*\\{[\\s\\S]*?^\\}`, 'gm'),
      
      // export { name }
      new RegExp(`export\\s*\\{[^}]*\\b${this.escapeRegex(exportName)}\\b[^}]*\\}`, 'g'),
      
      // 从export { ... }中移除单个导出
      new RegExp(`(export\\s*\\{[^}]*),\\s*${this.escapeRegex(exportName)}\\s*([^}]*\\})`, 'g'),
      new RegExp(`(export\\s*\\{)\\s*${this.escapeRegex(exportName)}\\s*,([^}]*\\})`, 'g'),
      new RegExp(`export\\s*\\{\\s*${this.escapeRegex(exportName)}\\s*\\}\\s*;?`, 'g')
    ];

    for (const pattern of patterns) {
      const originalContent = newContent;
      newContent = newContent.replace(pattern, (match, ...groups) => {
        // 对于export { ... }的情况，需要特殊处理
        if (match.includes('{') && match.includes('}')) {
          // 如果只有一个导出，删除整行
          if (match.match(new RegExp(`\\b${this.escapeRegex(exportName)}\\b`, 'g'))?.length === 1 &&
              !match.replace(exportName, '').match(/\b\w+\b/)) {
            return '';
          }
          // 否则只删除这个导出
          return match.replace(new RegExp(`\\s*,?\\s*${this.escapeRegex(exportName)}\\s*,?\\s*`, 'g'), (m, offset, string) => {
            // 处理逗号
            if (m.includes(',')) {
              return m.startsWith(',') ? '' : ', ';
            }
            return '';
          });
        }
        return '';
      });
      
      if (newContent !== originalContent) {
        modified = true;
      }
    }

    // 清理空的export行
    newContent = newContent.replace(/^export\s*\{\s*\}\s*;?\s*$/gm, '');
    
    // 清理多余的空行
    newContent = newContent.replace(/\n\s*\n\s*\n/g, '\n\n');

    return { content: newContent, modified };
  }

  private escapeRegex(str: string): string {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  private displayResults(): void {
    console.log('📊 清理结果统计');
    console.log('='.repeat(50));
    console.log(`处理文件数: ${this.cleanupResults.filesProcessed}`);
    console.log(`移除导出数: ${this.cleanupResults.exportsRemoved}`);
    
    if (this.cleanupResults.filesProcessed > 0) {
      console.log(`\n💾 备份位置: ${path.relative(process.cwd(), this.backupDir)}`);
      console.log('\n✅ 清理完成！建议运行以下命令验证：');
      console.log('   npm run build');
      console.log('   npm run test');
    }
  }
}

// 运行清理
async function main() {
  const cleanup = new UnusedExportsCleanup();
  try {
    await cleanup.runCleanup();
  } catch (error) {
    console.error('❌ 清理失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { UnusedExportsCleanup };
