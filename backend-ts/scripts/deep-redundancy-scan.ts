#!/usr/bin/env ts-node

/**
 * 深度冗余扫描工具
 * 检查项目中是否还有其他潜在的重复实现
 */

import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface PotentialDuplicate {
  pattern: string;
  files: string[];
  severity: 'high' | 'medium' | 'low';
  description: string;
}

class DeepRedundancyScanTool {
  private projectRoot: string;
  private potentialDuplicates: PotentialDuplicate[] = [];

  constructor() {
    this.projectRoot = process.cwd();
  }

  async scan(): Promise<void> {
    console.log('🔍 开始深度冗余扫描...\n');

    // 1. 检查类似命名的文件
    await this.scanSimilarFileNames();

    // 2. 检查重复的类名
    await this.scanDuplicateClassNames();

    // 3. 检查重复的接口定义
    await this.scanDuplicateInterfaces();

    // 4. 检查重复的服务实现
    await this.scanDuplicateServices();

    // 5. 检查重复的工具函数
    await this.scanDuplicateUtilities();

    // 6. 生成报告
    await this.generateReport();
  }

  /**
   * 扫描相似文件名
   */
  private async scanSimilarFileNames(): Promise<void> {
    console.log('1️⃣ 扫描相似文件名...');

    const sourceFiles = await glob('src/**/*.ts', {
      ignore: ['src/**/*.test.ts', 'src/**/*.spec.ts']
    });

    const fileGroups: Map<string, string[]> = new Map();

    for (const file of sourceFiles) {
      const basename = path.basename(file, '.ts');
      const normalizedName = basename
        .replace(/[-_]/g, '')
        .toLowerCase()
        .replace(/service$|manager$|handler$|controller$/, '');

      if (!fileGroups.has(normalizedName)) {
        fileGroups.set(normalizedName, []);
      }
      fileGroups.get(normalizedName)!.push(file);
    }

    for (const [name, files] of fileGroups) {
      if (files.length > 1) {
        // 过滤掉已知的合理重复
        const filteredFiles = files.filter(file => 
          !file.includes('examples/') &&
          !file.includes('test') &&
          !file.includes('spec')
        );

        if (filteredFiles.length > 1) {
          this.potentialDuplicates.push({
            pattern: `相似文件名: ${name}`,
            files: filteredFiles,
            severity: 'medium',
            description: `发现${filteredFiles.length}个相似命名的文件`
          });
        }
      }
    }

    console.log(`   发现 ${this.potentialDuplicates.length} 组相似文件名\n`);
  }

  /**
   * 扫描重复的类名
   */
  private async scanDuplicateClassNames(): Promise<void> {
    console.log('2️⃣ 扫描重复的类名...');

    const sourceFiles = await glob('src/**/*.ts', {
      ignore: ['src/**/*.test.ts', 'src/**/*.spec.ts', 'examples/**']
    });

    const classNames: Map<string, string[]> = new Map();

    for (const file of sourceFiles) {
      const content = fs.readFileSync(file, 'utf8');
      const classMatches = content.match(/export\s+class\s+(\w+)/g);

      if (classMatches) {
        for (const match of classMatches) {
          const className = match.replace(/export\s+class\s+/, '');
          if (!classNames.has(className)) {
            classNames.set(className, []);
          }
          classNames.get(className)!.push(file);
        }
      }
    }

    for (const [className, files] of classNames) {
      if (files.length > 1) {
        this.potentialDuplicates.push({
          pattern: `重复类名: ${className}`,
          files,
          severity: 'high',
          description: `类 ${className} 在${files.length}个文件中定义`
        });
      }
    }

    console.log(`   发现 ${classNames.size} 个类定义\n`);
  }

  /**
   * 扫描重复的接口定义
   */
  private async scanDuplicateInterfaces(): Promise<void> {
    console.log('3️⃣ 扫描重复的接口定义...');

    const sourceFiles = await glob('src/**/*.ts', {
      ignore: ['src/**/*.test.ts', 'src/**/*.spec.ts', 'examples/**']
    });

    const interfaces: Map<string, string[]> = new Map();

    for (const file of sourceFiles) {
      const content = fs.readFileSync(file, 'utf8');
      const interfaceMatches = content.match(/export\s+interface\s+(\w+)/g);

      if (interfaceMatches) {
        for (const match of interfaceMatches) {
          const interfaceName = match.replace(/export\s+interface\s+/, '');
          if (!interfaces.has(interfaceName)) {
            interfaces.set(interfaceName, []);
          }
          interfaces.get(interfaceName)!.push(file);
        }
      }
    }

    for (const [interfaceName, files] of interfaces) {
      if (files.length > 1) {
        this.potentialDuplicates.push({
          pattern: `重复接口: ${interfaceName}`,
          files,
          severity: 'medium',
          description: `接口 ${interfaceName} 在${files.length}个文件中定义`
        });
      }
    }

    console.log(`   发现 ${interfaces.size} 个接口定义\n`);
  }

  /**
   * 扫描重复的服务实现
   */
  private async scanDuplicateServices(): Promise<void> {
    console.log('4️⃣ 扫描重复的服务实现...');

    const servicePatterns = [
      'HttpClient',
      'Logger',
      'Cache',
      'Database',
      'Config',
      'Monitor',
      'Event',
      'Validation'
    ];

    const sourceFiles = await glob('src/**/*.ts', {
      ignore: ['src/**/*.test.ts', 'src/**/*.spec.ts', 'examples/**']
    });

    for (const pattern of servicePatterns) {
      const matchingFiles: string[] = [];

      for (const file of sourceFiles) {
        const content = fs.readFileSync(file, 'utf8');
        if (content.includes(pattern) && 
            (content.includes(`class ${pattern}`) || 
             content.includes(`${pattern}Service`) ||
             content.includes(`${pattern}Manager`))) {
          matchingFiles.push(file);
        }
      }

      if (matchingFiles.length > 2) { // 允许统一组件 + 1个实现
        this.potentialDuplicates.push({
          pattern: `潜在重复服务: ${pattern}`,
          files: matchingFiles,
          severity: 'medium',
          description: `发现${matchingFiles.length}个可能的${pattern}相关实现`
        });
      }
    }

    console.log(`   检查了 ${servicePatterns.length} 种服务模式\n`);
  }

  /**
   * 扫描重复的工具函数
   */
  private async scanDuplicateUtilities(): Promise<void> {
    console.log('5️⃣ 扫描重复的工具函数...');

    const utilityPatterns = [
      'formatDate',
      'validateEmail',
      'generateId',
      'parseJson',
      'deepClone',
      'debounce',
      'throttle'
    ];

    const sourceFiles = await glob('src/**/*.ts', {
      ignore: ['src/**/*.test.ts', 'src/**/*.spec.ts', 'examples/**']
    });

    for (const pattern of utilityPatterns) {
      const matchingFiles: string[] = [];

      for (const file of sourceFiles) {
        const content = fs.readFileSync(file, 'utf8');
        if (content.includes(`function ${pattern}`) || 
            content.includes(`const ${pattern} =`) ||
            content.includes(`export const ${pattern}`)) {
          matchingFiles.push(file);
        }
      }

      if (matchingFiles.length > 1) {
        this.potentialDuplicates.push({
          pattern: `重复工具函数: ${pattern}`,
          files: matchingFiles,
          severity: 'low',
          description: `函数 ${pattern} 在${matchingFiles.length}个文件中定义`
        });
      }
    }

    console.log(`   检查了 ${utilityPatterns.length} 种工具函数模式\n`);
  }

  /**
   * 生成报告
   */
  private async generateReport(): Promise<void> {
    console.log('📊 生成深度扫描报告...\n');

    console.log('=' .repeat(80));
    console.log('🔍 深度冗余扫描报告');
    console.log('=' .repeat(80));

    const highSeverity = this.potentialDuplicates.filter(d => d.severity === 'high');
    const mediumSeverity = this.potentialDuplicates.filter(d => d.severity === 'medium');
    const lowSeverity = this.potentialDuplicates.filter(d => d.severity === 'low');

    console.log('\n📊 扫描结果统计:');
    console.log(`   🔴 高严重性问题: ${highSeverity.length}`);
    console.log(`   🟡 中等严重性问题: ${mediumSeverity.length}`);
    console.log(`   🟢 低严重性问题: ${lowSeverity.length}`);
    console.log(`   📊 总计: ${this.potentialDuplicates.length}`);

    if (highSeverity.length > 0) {
      console.log('\n🔴 高严重性问题:');
      highSeverity.forEach(duplicate => {
        console.log(`\n   ❌ ${duplicate.pattern}`);
        console.log(`      ${duplicate.description}`);
        duplicate.files.forEach(file => {
          console.log(`      - ${file}`);
        });
      });
    }

    if (mediumSeverity.length > 0) {
      console.log('\n🟡 中等严重性问题:');
      mediumSeverity.forEach(duplicate => {
        console.log(`\n   ⚠️ ${duplicate.pattern}`);
        console.log(`      ${duplicate.description}`);
        duplicate.files.forEach(file => {
          console.log(`      - ${file}`);
        });
      });
    }

    if (lowSeverity.length > 0) {
      console.log('\n🟢 低严重性问题:');
      lowSeverity.forEach(duplicate => {
        console.log(`\n   ℹ️ ${duplicate.pattern}`);
        console.log(`      ${duplicate.description}`);
        duplicate.files.forEach(file => {
          console.log(`      - ${file}`);
        });
      });
    }

    if (this.potentialDuplicates.length === 0) {
      console.log('\n🎉 恭喜！未发现明显的重复实现！');
      console.log('   ✅ 项目结构清洁');
      console.log('   ✅ 统一组件策略成功');
      console.log('   ✅ 代码质量优秀');
    } else {
      console.log('\n💡 建议:');
      console.log('   1. 优先处理高严重性问题');
      console.log('   2. 评估中等严重性问题的必要性');
      console.log('   3. 低严重性问题可以在重构时处理');
    }

    // 保存详细报告
    const reportPath = path.join(this.projectRoot, 'docs/developer-guides/deep-redundancy-scan-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      scanDate: new Date().toISOString(),
      summary: {
        total: this.potentialDuplicates.length,
        high: highSeverity.length,
        medium: mediumSeverity.length,
        low: lowSeverity.length
      },
      duplicates: this.potentialDuplicates
    }, null, 2));

    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  }
}

// 执行扫描
async function main() {
  const scanner = new DeepRedundancyScanTool();
  await scanner.scan();
}

if (require.main === module) {
  main().catch(console.error);
}

export { DeepRedundancyScanTool };
