#!/usr/bin/env tsx

/**
 * 数据真实性守护者
 * 自动化检查机制，防止虚假数据再次出现
 * 集成到CI/CD流程中，确保代码质量
 */

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { PrismaClient } from '@prisma/client';

interface AuthenticityViolation {
  file: string;
  line: number;
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  category: string;
  description: string;
  codeSnippet: string;
  recommendation: string;
  riskLevel: number; // 1-10
}

class DataAuthenticityGuardian {
  private readonly prisma = new PrismaClient();
  private violations: AuthenticityViolation[] = [];
  private readonly srcPath = path.join(process.cwd(), 'src');

  // 严格禁止的硬编码价格（常见的虚假价格）
  private readonly FORBIDDEN_PRICES = [
    50000, 45000, 46000, 44000, 43000, 42000, 48000, 52000, 55000,
    100000, 60000, 65000, 70000, 75000, 80000, 85000, 90000, 95000,
    3000, 2500, 3500, 4000, // ETH价格
    400, 300, 500, 600, // BNB价格
    1.5, 1.0, 2.0, // ADA价格
    100, 80, 120, 150 // SOL价格
  ];

  // 严格禁止的虚假实现关键词
  private readonly FORBIDDEN_KEYWORDS = [
    '模拟实现', '临时实现', '简化实现', '测试实现', '占位符实现',
    '模拟AI', '模拟交易', '模拟数据', '模拟执行', '虚假实现',
    'mock implementation', 'fake implementation', 'dummy implementation',
    'placeholder implementation', 'temporary implementation'
  ];

  // 严格禁止的随机数滥用模式
  private readonly FORBIDDEN_RANDOM_PATTERNS = [
    /Math\.random\(\).*(?:price|volume|amount|cost|value)/gi,
    /(?:price|volume|amount|cost|value).*Math\.random\(\)/gi,
    /Math\.random\(\).*(?:confidence|strength|score)/gi,
    /(?:confidence|strength|score).*Math\.random\(\)/gi
  ];

  async runAuthenticityCheck(): Promise<boolean> {
    console.log('🛡️ 数据真实性守护者 - 开始检查...');
    console.log('='.repeat(80));

    try {
      // 1. 检查硬编码价格违规
      await this.checkHardcodedPriceViolations();
      
      // 2. 检查虚假实现关键词
      await this.checkFakeImplementationKeywords();
      
      // 3. 检查随机数滥用
      await this.checkRandomNumberAbuse();
      
      // 4. 检查数据库数据真实性
      await this.checkDatabaseDataAuthenticity();
      
      // 5. 检查配置文件合规性
      await this.checkConfigurationCompliance();
      
      // 6. 生成守护报告
      const isCompliant = this.generateGuardianReport();
      
      return isCompliant;
      
    } catch (error) {
      console.error('❌ 数据真实性检查失败:', error);
      return false;
    } finally {
      await this.cleanup();
    }
  }

  private async checkHardcodedPriceViolations(): Promise<void> {
    console.log('\n🔍 检查硬编码价格违规...');
    
    for (const price of this.FORBIDDEN_PRICES) {
      try {
        const result = execSync(
          `grep -rn "\\b${price}\\b" ${this.srcPath} --include="*.ts" --exclude-dir=__tests__ --exclude-dir=tests`,
          { encoding: 'utf8' }
        );

        const lines = result.trim().split('\n');
        for (const line of lines) {
          if (line.trim() && !this.isTestFile(line)) {
            const [filePath, lineNum, code] = line.split(':');
            
            // 跳过注释中的价格（如警告注释）
            if (code.includes('//') && code.includes('警告')) {
              continue;
            }
            
            this.violations.push({
              file: path.relative(process.cwd(), filePath),
              line: parseInt(lineNum),
              severity: 'CRITICAL',
              category: '🚨 硬编码价格违规',
              description: `发现被禁止的硬编码价格: ${price}`,
              codeSnippet: code.trim(),
              recommendation: '立即移除硬编码价格，使用真实市场数据API',
              riskLevel: 10
            });
          }
        }
      } catch (error) {
        // 没有找到匹配项，这是好事
      }
    }
  }

  private async checkFakeImplementationKeywords(): Promise<void> {
    console.log('\n🔍 检查虚假实现关键词...');
    
    for (const keyword of this.FORBIDDEN_KEYWORDS) {
      try {
        const result = execSync(
          `grep -rn "${keyword}" ${this.srcPath} --include="*.ts" --exclude-dir=__tests__ --exclude-dir=tests`,
          { encoding: 'utf8' }
        );

        const lines = result.trim().split('\n');
        for (const line of lines) {
          if (line.trim() && !this.isTestFile(line)) {
            const [filePath, lineNum, code] = line.split(':');
            
            // 跳过错误消息中的关键词（如"拒绝虚假实现"）
            if (code.includes('throw new Error') && code.includes('拒绝')) {
              continue;
            }
            
            // 跳过已经修复的注释
            if (code.includes('使用真实实现') || code.includes('真实实现') || code.includes('完整实现')) {
              continue;
            }
            
            this.violations.push({
              file: path.relative(process.cwd(), filePath),
              line: parseInt(lineNum),
              severity: 'HIGH',
              category: '⚠️ 虚假实现关键词',
              description: `发现虚假实现关键词: ${keyword}`,
              codeSnippet: code.trim(),
              recommendation: '移除虚假实现标记，确保使用真实逻辑',
              riskLevel: 8
            });
          }
        }
      } catch (error) {
        // 没有找到匹配项，这是好事
      }
    }
  }

  private async checkRandomNumberAbuse(): Promise<void> {
    console.log('\n🔍 检查随机数滥用...');
    
    const files = this.getAllTSFiles(this.srcPath);
    
    for (const file of files) {
      if (this.isTestFile(file)) continue;
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        const lines = content.split('\n');
        
        lines.forEach((line, index) => {
          for (const pattern of this.FORBIDDEN_RANDOM_PATTERNS) {
            if (pattern.test(line)) {
              this.violations.push({
                file: path.relative(process.cwd(), file),
                line: index + 1,
                severity: 'CRITICAL',
                category: '🎲 随机数滥用',
                description: '使用随机数生成业务关键数据',
                codeSnippet: line.trim(),
                recommendation: '使用真实数据源替换随机数生成',
                riskLevel: 9
              });
            }
          }
        });
      } catch (error) {
        console.warn(`⚠️ 读取文件失败: ${file}`);
      }
    }
  }

  private async checkDatabaseDataAuthenticity(): Promise<void> {
    console.log('\n🔍 检查数据库数据真实性...');
    
    try {
      // 检查价格数据表中的可疑数据
      const suspiciousPrices = await this.prisma.priceData.findMany({
        where: {
          OR: [
            { price: { in: this.FORBIDDEN_PRICES } },
            { price: { lt: 100 } }, // 过低的价格
            { price: { gt: 500000 } }, // 过高的价格
            { changePercent24h: { gt: 200 } }, // 不现实的变化
            { changePercent24h: { lt: -90 } } // 不现实的下跌
          ]
        },
        take: 5
      });

      for (const priceData of suspiciousPrices) {
        this.violations.push({
          file: 'database/priceData',
          line: 0,
          severity: 'HIGH',
          category: '🗄️ 数据库可疑数据',
          description: `数据库中发现可疑价格: ${priceData.price}`,
          codeSnippet: `ID: ${priceData.id}, Price: ${priceData.price}`,
          recommendation: '清理数据库中的可疑数据，确保数据来源真实',
          riskLevel: 7
        });
      }

      // 检查历史数据完整性
      const historicalDataCount = await this.prisma.historicalData.count();
      if (historicalDataCount < 100000) {
        this.violations.push({
          file: 'database/historicalData',
          line: 0,
          severity: 'MEDIUM',
          category: '📊 历史数据不足',
          description: `历史数据量不足: ${historicalDataCount.toLocaleString()}条`,
          codeSnippet: `historicalData.count() = ${historicalDataCount}`,
          recommendation: '增加历史数据导入，确保有足够的真实数据支撑分析',
          riskLevel: 5
        });
      }

    } catch (error) {
      console.error('   ❌ 数据库检查失败:', error);
    }
  }

  private async checkConfigurationCompliance(): Promise<void> {
    console.log('\n🔍 检查配置文件合规性...');
    
    const configFile = path.join(this.srcPath, 'shared/infrastructure/config/unified-default-config.ts');
    if (fs.existsSync(configFile)) {
      const content = fs.readFileSync(configFile, 'utf8');
      
      // 检查是否有未标记的硬编码价格
      if (content.includes('DEFAULT_BTC_PRICE: 50000,') && !content.includes('⚠️ 警告')) {
        this.violations.push({
          file: path.relative(process.cwd(), configFile),
          line: 140,
          severity: 'MEDIUM',
          category: '⚙️ 配置合规性',
          description: '配置文件中的硬编码价格缺少警告标记',
          codeSnippet: 'DEFAULT_BTC_PRICE: 50000,',
          recommendation: '为配置中的硬编码值添加警告注释',
          riskLevel: 4
        });
      }
    }
  }

  private generateGuardianReport(): boolean {
    console.log('\n🛡️ 数据真实性守护报告');
    console.log('='.repeat(80));

    const criticalViolations = this.violations.filter(v => v.severity === 'CRITICAL');
    const highViolations = this.violations.filter(v => v.severity === 'HIGH');
    const mediumViolations = this.violations.filter(v => v.severity === 'MEDIUM');
    const lowViolations = this.violations.filter(v => v.severity === 'LOW');

    const totalRiskScore = this.violations.reduce((sum, v) => sum + v.riskLevel, 0);
    const avgRiskScore = this.violations.length > 0 ? totalRiskScore / this.violations.length : 0;

    console.log(`总违规数: ${this.violations.length}`);
    console.log(`🔴 严重违规: ${criticalViolations.length}`);
    console.log(`🟠 高风险违规: ${highViolations.length}`);
    console.log(`🟡 中风险违规: ${mediumViolations.length}`);
    console.log(`🟢 低风险违规: ${lowViolations.length}`);
    console.log(`📊 平均风险评分: ${avgRiskScore.toFixed(1)}/10`);

    // 判断是否通过检查
    const isCompliant = criticalViolations.length === 0 && highViolations.length === 0;
    
    if (isCompliant) {
      console.log('\n✅ 数据真实性检查通过！');
      console.log('🛡️ 系统符合数据真实性要求');
    } else {
      console.log('\n❌ 数据真实性检查失败！');
      console.log('🚨 发现严重的数据真实性违规');
      
      // 显示最严重的违规
      if (criticalViolations.length > 0) {
        console.log('\n🔴 严重违规详情:');
        criticalViolations.slice(0, 5).forEach((violation, index) => {
          console.log(`\n${index + 1}. ${violation.category}`);
          console.log(`   文件: ${violation.file}:${violation.line}`);
          console.log(`   描述: ${violation.description}`);
          console.log(`   代码: ${violation.codeSnippet}`);
          console.log(`   风险: ${violation.riskLevel}/10`);
          console.log(`   建议: ${violation.recommendation}`);
        });
      }
    }

    // 保存详细报告
    this.saveGuardianReport(isCompliant, avgRiskScore);
    
    return isCompliant;
  }

  private saveGuardianReport(isCompliant: boolean, avgRiskScore: number): void {
    const report = {
      timestamp: new Date().toISOString(),
      compliant: isCompliant,
      riskScore: avgRiskScore,
      summary: {
        total: this.violations.length,
        critical: this.violations.filter(v => v.severity === 'CRITICAL').length,
        high: this.violations.filter(v => v.severity === 'HIGH').length,
        medium: this.violations.filter(v => v.severity === 'MEDIUM').length,
        low: this.violations.filter(v => v.severity === 'LOW').length
      },
      violations: this.violations
    };

    const reportsDir = path.join(process.cwd(), 'authenticity-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const reportPath = path.join(reportsDir, `data-authenticity-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 详细守护报告已保存: ${reportPath}`);
  }

  private getAllTSFiles(dir: string): string[] {
    const files: string[] = [];
    
    try {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...this.getAllTSFiles(fullPath));
        } else if (stat.isFile() && item.endsWith('.ts') && !item.endsWith('.d.ts')) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`⚠️ 读取目录失败: ${dir}`);
    }
    
    return files;
  }

  private isTestFile(line: string): boolean {
    return line.includes('test') ||
           line.includes('spec') ||
           line.includes('__tests__') ||
           line.includes('.test.') ||
           line.includes('.spec.') ||
           line.includes('mock');
  }

  private async cleanup(): Promise<void> {
    await this.prisma.$disconnect();
  }
}

// 运行守护检查
async function main() {
  const guardian = new DataAuthenticityGuardian();
  const isCompliant = await guardian.runAuthenticityCheck();
  
  // 设置退出码，用于CI/CD流程
  process.exit(isCompliant ? 0 : 1);
}

if (require.main === module) {
  main().catch((error) => {
    console.error('守护者运行失败:', error);
    process.exit(1);
  });
}

export { DataAuthenticityGuardian };
