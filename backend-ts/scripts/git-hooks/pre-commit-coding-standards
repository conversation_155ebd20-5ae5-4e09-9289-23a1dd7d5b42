#!/bin/sh

# Git Pre-commit Hook - 编码规范检查
# 
# 这个脚本会在每次 git commit 前自动运行编码规范检查
# 如果发现严重问题，会阻止提交

echo "🔍 运行编码规范检查..."

# 运行编码规范检测
npm run check:coding-standards > /tmp/coding-standards-check.log 2>&1
EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ 编码规范检查通过"
    exit 0
else
    echo "❌ 编码规范检查失败"
    echo ""
    echo "发现以下问题："
    echo "==========================================="
    
    # 显示检查结果的摘要
    grep -E "(总问题数|严重问题|中等问题|轻微问题)" /tmp/coding-standards-check.log || true
    
    echo ""
    echo "详细报告已保存到: ./coding-standards-report.json"
    echo ""
    echo "请修复以下严重问题后再提交："
    echo "==========================================="
    
    # 显示前5个严重问题
    grep -A 3 "🔴" /tmp/coding-standards-check.log | head -20 || true
    
    echo ""
    echo "💡 修复建议："
    echo "1. 使用 crypto.randomUUID() 替代 Math.random()"
    echo "2. 修复错误处理，抛出明确错误而非返回 null"
    echo "3. 使用统一的基础设施组件"
    echo ""
    echo "运行以下命令查看完整报告："
    echo "npm run check:coding-standards"
    echo ""
    echo "如果需要强制提交（不推荐），请使用："
    echo "git commit --no-verify"
    
    exit 1
fi