#!/bin/bash

# Git Pre-commit Hook
# 在提交前自动运行质量检查，防止违规代码进入版本控制

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Pre-commit 质量检查开始...${NC}"

# 获取暂存的文件
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(ts|js)$' | grep -v test | grep -v spec || true)

if [ -z "$STAGED_FILES" ]; then
    echo -e "${GREEN}✅ 没有需要检查的TypeScript文件${NC}"
    exit 0
fi

echo -e "${BLUE}📋 检查以下文件:${NC}"
echo "$STAGED_FILES" | sed 's/^/  - /'

# 检查计数器
VIOLATIONS=0

# 1. 检查getDefault方法
echo -e "${YELLOW}🔍 检查getDefault方法...${NC}"
for file in $STAGED_FILES; do
    if grep -n "getDefault[A-Z]" "$file" 2>/dev/null; then
        echo -e "${RED}❌ 发现getDefault方法: $file${NC}"
        VIOLATIONS=$((VIOLATIONS + 1))
    fi
done

# 2. 检查PrismaClient实例
echo -e "${YELLOW}🔍 检查PrismaClient实例...${NC}"
PRISMA_COUNT=0
for file in $STAGED_FILES; do
    if grep -n "new PrismaClient" "$file" 2>/dev/null; then
        echo -e "${RED}❌ 发现PrismaClient实例: $file${NC}"
        PRISMA_COUNT=$((PRISMA_COUNT + 1))
        VIOLATIONS=$((VIOLATIONS + 1))
    fi
done

if [ $PRISMA_COUNT -gt 1 ]; then
    echo -e "${RED}❌ 发现多个PrismaClient实例，违反单例模式${NC}"
fi

# 3. 检查模拟数据
echo -e "${YELLOW}🔍 检查模拟数据...${NC}"
for file in $STAGED_FILES; do
    if grep -n -i "mock\|fake\|stub\|simulate\|模拟" "$file" 2>/dev/null | grep -v "test\|spec"; then
        echo -e "${RED}❌ 发现模拟数据: $file${NC}"
        VIOLATIONS=$((VIOLATIONS + 1))
    fi
done

# 4. 检查降级逻辑关键词
echo -e "${YELLOW}🔍 检查降级逻辑关键词...${NC}"
for file in $STAGED_FILES; do
    if grep -n -i "降级\|fallback\|degraded\|backup.*logic" "$file" 2>/dev/null; then
        echo -e "${RED}❌ 发现降级逻辑关键词: $file${NC}"
        VIOLATIONS=$((VIOLATIONS + 1))
    fi
done

# 5. 检查catch块中的默认值返回
echo -e "${YELLOW}🔍 检查catch块中的默认值返回...${NC}"
for file in $STAGED_FILES; do
    # 简单的模式匹配检查catch块中的return
    if grep -A 10 "catch" "$file" 2>/dev/null | grep -B 5 -A 5 "return.*{" | grep -q "catch"; then
        echo -e "${YELLOW}⚠️ 可能的catch块默认值返回: $file${NC}"
        echo -e "${YELLOW}   请手动检查catch块是否返回了默认值而不是抛出错误${NC}"
    fi
done

# 6. 运行ESLint检查（如果可用）
if command -v npx >/dev/null 2>&1; then
    echo -e "${YELLOW}🔍 运行ESLint检查...${NC}"
    for file in $STAGED_FILES; do
        if ! npx eslint "$file" --quiet 2>/dev/null; then
            echo -e "${RED}❌ ESLint检查失败: $file${NC}"
            VIOLATIONS=$((VIOLATIONS + 1))
        fi
    done
fi

# 7. 检查TypeScript编译
if command -v npx >/dev/null 2>&1; then
    echo -e "${YELLOW}🔍 检查TypeScript编译...${NC}"
    if ! npx tsc --noEmit --skipLibCheck 2>/dev/null; then
        echo -e "${RED}❌ TypeScript编译检查失败${NC}"
        VIOLATIONS=$((VIOLATIONS + 1))
    fi
fi

# 结果汇总
echo ""
echo -e "${BLUE}📊 Pre-commit检查结果:${NC}"
if [ $VIOLATIONS -eq 0 ]; then
    echo -e "${GREEN}✅ 所有检查通过，允许提交${NC}"
    echo -e "${GREEN}🎉 代码质量符合要求${NC}"
    exit 0
else
    echo -e "${RED}❌ 发现 $VIOLATIONS 个违规问题${NC}"
    echo -e "${RED}💥 提交被阻止，请修复问题后重新提交${NC}"
    echo ""
    echo -e "${YELLOW}💡 修复建议:${NC}"
    echo "  1. 删除所有getDefault开头的方法"
    echo "  2. 使用DI容器中的PrismaClient单例"
    echo "  3. 移除模拟数据，使用真实数据源"
    echo "  4. 移除降级逻辑，改为抛出明确错误"
    echo "  5. 修复ESLint和TypeScript编译错误"
    echo ""
    echo -e "${BLUE}🔧 如需跳过检查（不推荐），使用: git commit --no-verify${NC}"
    exit 1
fi
