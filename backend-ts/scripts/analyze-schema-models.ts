#!/usr/bin/env tsx

import fs from 'fs';
import path from 'path';

interface ModelInfo {
  name: string;
  fields: string[];
  relations: string[];
  indexes: string[];
  hasMapAnnotation: boolean;
}

function analyzeSchema() {
  console.log('🔍 分析Prisma Schema中的所有模型...\n');
  
  const schemaPath = path.join(process.cwd(), 'prisma/schema.prisma');
  const schemaContent = fs.readFileSync(schemaPath, 'utf-8');
  
  const models: ModelInfo[] = [];
  const lines = schemaContent.split('\n');
  
  let currentModel: ModelInfo | null = null;
  let inModel = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // 检测模型开始
    const modelMatch = line.match(/^model\s+(\w+)\s*{/);
    if (modelMatch) {
      currentModel = {
        name: modelMatch[1],
        fields: [],
        relations: [],
        indexes: [],
        hasMapAnnotation: false
      };
      inModel = true;
      continue;
    }
    
    // 检测模型结束
    if (inModel && line === '}') {
      if (currentModel) {
        models.push(currentModel);
        currentModel = null;
      }
      inModel = false;
      continue;
    }
    
    if (inModel && currentModel) {
      // 检测字段
      const fieldMatch = line.match(/^(\w+)\s+/);
      if (fieldMatch && !line.startsWith('@@')) {
        const fieldName = fieldMatch[1];
        
        // 检测关系字段
        if (line.includes('@relation') || line.includes('[]')) {
          currentModel.relations.push(fieldName);
        } else {
          currentModel.fields.push(fieldName);
        }
      }
      
      // 检测索引
      if (line.startsWith('@@index') || line.startsWith('@@unique')) {
        currentModel.indexes.push(line);
      }
      
      // 检测@map注解
      if (line.includes('@@map(')) {
        currentModel.hasMapAnnotation = true;
      }
    }
  }
  
  // 输出分析结果
  console.log(`📊 发现 ${models.length} 个模型:\n`);
  
  // 按字母顺序排序
  models.sort((a, b) => a.name.localeCompare(b.name));
  
  // 检查命名规范
  const namingIssues: string[] = [];
  const duplicateNames: string[] = [];
  const nameSet = new Set<string>();
  
  for (const model of models) {
    // 检查重复名称
    const lowerName = model.name.toLowerCase();
    if (nameSet.has(lowerName)) {
      duplicateNames.push(model.name);
    }
    nameSet.add(lowerName);
    
    // 检查命名规范
    if (!/^[A-Z][a-zA-Z0-9]*$/.test(model.name)) {
      namingIssues.push(`${model.name} - 不符合PascalCase规范`);
    }
    
    console.log(`📋 ${model.name}`);
    console.log(`   字段数: ${model.fields.length}`);
    console.log(`   关系数: ${model.relations.length}`);
    console.log(`   索引数: ${model.indexes.length}`);
    if (model.hasMapAnnotation) {
      console.log(`   ⚠️  包含@map注解`);
    }
    
    // 显示一些字段示例
    if (model.fields.length > 0) {
      const sampleFields = model.fields.slice(0, 3);
      console.log(`   字段示例: ${sampleFields.join(', ')}${model.fields.length > 3 ? '...' : ''}`);
    }
    
    console.log('');
  }
  
  // 输出问题总结
  if (namingIssues.length > 0) {
    console.log('❌ 命名规范问题:');
    namingIssues.forEach(issue => console.log(`   - ${issue}`));
    console.log('');
  }
  
  if (duplicateNames.length > 0) {
    console.log('❌ 重复模型名:');
    duplicateNames.forEach(name => console.log(`   - ${name}`));
    console.log('');
  }
  
  if (namingIssues.length === 0 && duplicateNames.length === 0) {
    console.log('✅ 所有模型命名规范正确！');
  }
  
  // 统计信息
  const totalFields = models.reduce((sum, model) => sum + model.fields.length, 0);
  const totalRelations = models.reduce((sum, model) => sum + model.relations.length, 0);
  const modelsWithMap = models.filter(model => model.hasMapAnnotation).length;
  
  console.log('📈 统计信息:');
  console.log(`   总模型数: ${models.length}`);
  console.log(`   总字段数: ${totalFields}`);
  console.log(`   总关系数: ${totalRelations}`);
  console.log(`   包含@map注解的模型: ${modelsWithMap}`);
}

analyzeSchema();
