/**
 * 生成双轨制系统的TypeScript类型定义
 * 运行命令: npx ts-node scripts/generate-dual-track-types.ts
 */

import { writeFileSync } from 'fs';
import { join } from 'path';

const generateDualTrackTypes = () => {
  const typeDefinitions = `
/**
 * 双轨制交易系统类型定义
 * 自动生成，请勿手动修改
 */

// 账户类型枚举
export enum AccountType {
  SIMULATION = 'SIMULATION',
  LIVE_BINANCE = 'LIVE_BINANCE'
}

// 执行来源枚举
export enum ExecutionSource {
  SIMULATION = 'SIMULATION',
  BINANCE_LIVE = 'BINANCE_LIVE'
}

// API凭证接口
export interface ApiCredentials {
  apiKey: string;
  secretKey: string;
  passphrase?: string;
  testnet: boolean;
  permissions: string[];
  encryptedAt?: Date;
}

// 同步设置接口
export interface SyncSettings {
  autoSync: boolean;
  syncRiskParameters: boolean;
  syncPositionSizing: boolean;
  syncStopLossSettings: boolean;
  riskMultiplier: number;
  syncInterval: number; // 秒
  lastSyncAt?: Date;
  conflictResolution: 'MANUAL' | 'AUTO_SIMULATION' | 'AUTO_LIVE';
}

// 策略同步日志
export interface StrategySyncLog {
  id: string;
  fromAccountId: string;
  toAccountId: string;
  syncType: 'FULL' | 'PARTIAL' | 'RISK_PARAMS' | 'POSITION_SIZING';
  syncedParameters: Record<string, any>;
  conflicts?: Record<string, any>;
  status: 'PENDING' | 'SUCCESS' | 'FAILED';
  errorMessage?: string;
  createdAt: Date;
  completedAt?: Date;
}

// 双轨制配置
export interface DualTrackConfig {
  id: string;
  configKey: string;
  configValue: any;
  description?: string;
  category: 'SIMULATION' | 'LIVE_TRADING' | 'SYNC' | 'RISK';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 性能对比数据
export interface PerformanceComparison {
  accountType: AccountType;
  executionSource: ExecutionSource;
  totalTrades: number;
  avgPnl: number;
  winRate: number;
  totalVolume: number;
  avgCommission: number;
  maxDrawdown: number;
  sharpeRatio: number;
  lastUpdated: Date;
}

// 双轨制统计数据
export interface DualTrackStatistics {
  executionSource: ExecutionSource;
  totalTrades: number;
  totalPnl: number;
  winRate: number;
  avgTradeDuration: string; // ISO duration string
  maxDrawdown: number;
  totalCommission: number;
}

// 扩展的交易账户属性
export interface ExtendedTradingAccountProps {
  id: string;
  name: string;
  initialCapital: number;
  currentBalance: number;
  availableBalance: number;
  totalPnl: number;
  maxDrawdown: number;
  dailyPnl: number;
  isActive: boolean;
  riskSettings: {
    maxDrawdownLimit: number;
    dailyLossLimit: number;
    maxPositions: number;
    riskPerTrade: number;
    leverage: number;
  };
  // 双轨制扩展字段
  accountType: AccountType;
  executionEngine: string;
  apiCredentials?: ApiCredentials;
  syncSettings?: SyncSettings;
  parentAccountId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 扩展的交易订单属性
export interface ExtendedTradingOrderProps {
  id: string;
  positionId?: string;
  accountId: string;
  symbolId: string;
  orderType: string;
  side: string;
  quantity: number;
  price?: number;
  stopPrice?: number;
  status: string;
  exchangeOrderId?: string;
  filledQuantity: number;
  averagePrice?: number;
  commission: number;
  // 双轨制扩展字段
  executionSource: ExecutionSource;
  engineMetadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  filledAt?: Date;
}

// 扩展的交易仓位属性
export interface ExtendedTradingPositionProps {
  id: string;
  accountId: string;
  symbolId: string;
  signalId?: string;
  side: string;
  entryPrice: number;
  quantity: number;
  leverage: number;
  marginUsed: number;
  stopLoss?: number;
  takeProfit?: number;
  currentPrice?: number;
  unrealizedPnl: number;
  realizedPnl: number;
  status: string;
  pyramidLevel: number;
  atrValue?: number;
  // 双轨制扩展字段
  executionSource: ExecutionSource;
  syncedFromId?: string;
  openedAt: Date;
  closedAt?: Date;
  metadata?: Record<string, any>;
}

// 双轨制错误类型
export class DualTrackError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly accountType?: AccountType,
    public readonly executionSource?: ExecutionSource
  ) {
    super(message);
    this.name = 'DualTrackError';
  }
}

// 双轨制事件类型
export interface DualTrackEvent {
  id: string;
  eventType: 'ACCOUNT_SWITCH' | 'STRATEGY_SYNC' | 'ENGINE_ERROR' | 'RISK_ALERT';
  accountId: string;
  accountType: AccountType;
  executionSource: ExecutionSource;
  data: Record<string, any>;
  timestamp: Date;
}

// 双轨制配置常量
export const DUAL_TRACK_CONSTANTS = {
  DEFAULT_SYNC_INTERVAL: 300, // 5分钟
  DEFAULT_RISK_MULTIPLIER: 0.5, // 真实交易风险减半
  MAX_API_RETRIES: 3,
  CONNECTION_TIMEOUT: 10000, // 10秒
  DEFAULT_SLIPPAGE: 0.0002, // 0.02%
  DEFAULT_COMMISSION: 0.001, // 0.1%
  DEFAULT_SUCCESS_RATE: 0.995, // 99.5%
} as const;

// 双轨制验证规则
export const DUAL_TRACK_VALIDATION = {
  ACCOUNT_TYPE_VALUES: ['SIMULATION', 'LIVE_BINANCE'] as const,
  EXECUTION_SOURCE_VALUES: ['SIMULATION', 'BINANCE_LIVE'] as const,
  SYNC_TYPE_VALUES: ['FULL', 'PARTIAL', 'RISK_PARAMS', 'POSITION_SIZING'] as const,
  STATUS_VALUES: ['PENDING', 'SUCCESS', 'FAILED'] as const,
  CONFIG_CATEGORIES: ['SIMULATION', 'LIVE_TRADING', 'SYNC', 'RISK'] as const,
} as const;

// 类型守卫函数
export const isDualTrackAccount = (account: any): account is ExtendedTradingAccountProps => {
  return account && 
         typeof account.accountType === 'string' && 
         DUAL_TRACK_VALIDATION.ACCOUNT_TYPE_VALUES.includes(account.accountType);
};

export const isSimulationAccount = (account: ExtendedTradingAccountProps): boolean => {
  return account.accountType === AccountType.SIMULATION;
};

export const isLiveAccount = (account: ExtendedTradingAccountProps): boolean => {
  return account.accountType === AccountType.LIVE_BINANCE;
};

// 导出所有类型
export type {
  ApiCredentials,
  SyncSettings,
  StrategySyncLog,
  DualTrackConfig,
  PerformanceComparison,
  DualTrackStatistics,
  ExtendedTradingAccountProps,
  ExtendedTradingOrderProps,
  ExtendedTradingPositionProps,
  DualTrackEvent
};
`;

  const outputPath = join(__dirname, '../src/contexts/trading-execution/domain/types/dual-track.types.ts');
  
  try {
    writeFileSync(outputPath, typeDefinitions.trim());
    console.log('✅ 双轨制类型定义生成成功:', outputPath);
  } catch (error) {
    console.error('❌ 生成类型定义失败:', error);
    process.exit(1);
  }
};

// 运行生成器
if (require.main === module) {
  generateDualTrackTypes();
}

export { generateDualTrackTypes };
