/**
 * 生成初始风险评估数据脚本
 * 为现有的活跃交易账户生成基于真实数据的风险评估
 */

import { PrismaClient } from '@prisma/client';
import { TYPES } from '../src/shared/infrastructure/di/types';
import { setupContainer, getContainer } from '../src/shared/infrastructure/di/container';
import { RiskAssessmentApplicationService } from '../src/contexts/risk-management/application/services/risk-assessment-application-service';
import { TradingExecutionApplicationService } from '../src/contexts/trading-execution/application/services/trading-execution-application-service';
import { Position, PositionType } from '../src/contexts/risk-management/domain/value-objects/position';
import { MarketDataContext } from '../src/contexts/risk-management/domain/value-objects/market-data-context';


/**
 * 风险评估生成
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

const prisma = new PrismaClient();

/**
 * 创建默认市场数据上下文
 */
function createDefaultMarketDataContext(symbol: string, currentPrice: number): MarketDataContext {
  return {
    symbol: symbol.toUpperCase(),
    currentPrice,
    volume24h: 1000000,
    volatility: 0.02,
    trend: 'SIDEWAYS',
    marketPhase: 'ACCUMULATION',
    sentiment: 'NEUTRAL',
    technicalIndicators: {
      rsi: 50,
      macd: 0,
      bollinger: {
        upper: currentPrice * 1.02,
        middle: currentPrice,
        lower: currentPrice * 0.98
      },
      movingAverages: {
        ma20: currentPrice,
        ma50: currentPrice,
        ma200: currentPrice
      }
    },
    orderBook: {
      bids: [{ price: currentPrice * 0.999, quantity: 100 }],
      asks: [{ price: currentPrice * 1.001, quantity: 100 }]
    },
    timestamp: new Date(),
    dataQuality: 'MEDIUM'
  };
}

/**
 * 获取账户的主要持仓
 */
async function getAccountMainPosition(
  accountId: string, 
  tradingService: TradingExecutionApplicationService
): Promise<Position> {
  try {
    const positions = await tradingService.getActivePositions(accountId);
    
    if (positions.length > 0) {
      // 使用第一个活跃持仓
      const pos = positions[0];
      return Position.create(
        pos.symbol?.symbol || 'BTC',
        pos.side === 'LONG' ? PositionType.LONG : PositionType.SHORT,
        pos.quantity,
        pos.entryPrice,
        pos.currentPrice || pos.entryPrice,
        new Date(pos.openedAt),
        {
          leverage: pos.leverage,
          marginUsed: pos.marginUsed,
          liquidationPrice: pos.liquidationPrice,
          stopLoss: pos.stopLoss,
          takeProfit: pos.takeProfit
        }
      );
    } else {
      // 创建零持仓
      return Position.create(
        'BTC',
        PositionType.LONG,
        0,
        46000,
        46000,
        new Date()
      );
    }
  } catch (error) {
    console.error(`获取账户 ${accountId} 持仓失败:`, error);
    // 返回默认零持仓
    return Position.create('BTC', PositionType.LONG, 0, 46000, 46000, new Date());
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始生成初始风险评估数据...');

  try {
    // 设置DI容器
    await setupContainer();
    const container = getContainer();
    const riskAssessmentService = container.get<RiskAssessmentApplicationService>(
      TYPES.RiskManagement.RiskAssessmentApplicationService
    );
    const tradingExecutionService = container.get<TradingExecutionApplicationService>(
      TYPES.TradingExecution.TradingExecutionApplicationService
    );

    // 获取所有活跃的交易账户
    const accounts = await prisma.tradingAccounts.findMany({
      where: {
        isActive: true,
        userId: { not: undefined }
      },
      include: {
        user: true
      }
    });

    console.log(`📊 找到 ${accounts.length} 个活跃交易账户`);

    let successCount = 0;
    let errorCount = 0;

    // 为每个账户生成风险评估
    for (const account of accounts) {
      try {
        console.log(`\n🔍 处理账户: ${account.name} (${account.id})`);
        console.log(`👤 用户: ${account.user?.username || 'Unknown'}`);

        // 获取账户的主要持仓
        const position = await getAccountMainPosition(account.id, tradingExecutionService);

        console.log(`📈 持仓信息: ${position.symbol} ${position.quantity > 0 ? 'LONG' : 'ZERO'} ${position.quantity}`);

        // 生成风险评估
        const assessmentResult = await riskAssessmentService.assessRisk({
          portfolioId: account.id,
          position: position,
          marketData: createDefaultMarketDataContext(position.symbol, position.currentPrice)
        });

        console.log(`✅ 风险评估生成成功`);
        console.log(`   风险等级: ${assessmentResult.assessment.riskLevel.level}`);
        console.log(`   风险分数: ${assessmentResult.assessment.riskLevel.score}`);
        
        successCount++;
      } catch (error) {
        console.error(`❌ 账户 ${account.id} 风险评估生成失败:`, error);
        errorCount++;
      }
    }

    console.log(`\n📈 生成完成统计:`);
    console.log(`   成功: ${successCount} 个账户`);
    console.log(`   失败: ${errorCount} 个账户`);
    console.log(`   总计: ${accounts.length} 个账户`);

    // 验证生成的风险评估数据
    const riskAssessmentCount = await prisma.riskAssessments.count({
      where: {
        portfolioId: { in: accounts.map(a => a.id) }
      }
    });

    console.log(`\n🔍 验证结果:`);
    console.log(`   数据库中的风险评估记录: ${riskAssessmentCount} 条`);

    console.log('\n🎉 初始风险评估数据生成完成！');

  } catch (error) {
    console.error('❌ 生成初始风险评估数据失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as generateInitialRiskAssessments };
