import { PrismaClient } from '@prisma/client';


/**
 * 预测报告生成
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

const prisma = new PrismaClient();

async function generatePredictionReport() {
  try {
    console.log('📊 短期预测和验证报告');
    console.log('='.repeat(80));
    console.log();

    // 获取BTC符号
    const btcSymbol = await prisma.symbol.findUnique({
      where: { symbol: 'BTC/USDT' }
    });

    if (!btcSymbol) {
      console.log('❌ 未找到BTC/USDT符号');
      return;
    }

    // 获取所有短期预测，按时间倒序
    const predictions = await prisma.shortCyclePrediction.findMany({
      where: { symbolId: btcSymbol.id },
      orderBy: { predictionTimestamp: 'desc' },
      include: {
        symbol: true
      }
    });

    console.log(`📈 总预测数量: ${predictions.length}`);
    console.log();

    // 统计信息
    const verifiedCount = predictions.filter(p => p.isVerified).length;
    const pendingCount = predictions.length - verifiedCount;
    const correctPredictions = predictions.filter(p => 
      p.isVerified && parseFloat(p.accuracyScore?.toString() || '0') > 0.5
    ).length;

    console.log('📊 统计概览:');
    console.log(`   已验证预测: ${verifiedCount}`);
    console.log(`   待验证预测: ${pendingCount}`);
    console.log(`   正确预测: ${correctPredictions}`);
    console.log(`   总体准确率: ${verifiedCount > 0 ? (correctPredictions / verifiedCount * 100).toFixed(1) : 0}%`);
    console.log();

    console.log('📋 详细预测记录:');
    console.log('-'.repeat(80));

    predictions.forEach((prediction, index) => {
      const predictionTime = new Date(prediction.predictionTimestamp).toLocaleString('zh-CN');
      const targetTime = new Date(prediction.targetVerificationTime).toLocaleString('zh-CN');
      const predictedValue = parseFloat(prediction.predictedValue.toString()).toFixed(2);
      const confidence = (parseFloat(prediction.confidence.toString()) * 100).toFixed(1);
      
      console.log(`${index + 1}. 预测ID: ${prediction.id}`);
      console.log(`   预测时间: ${predictionTime}`);
      console.log(`   目标验证时间: ${targetTime}`);
      console.log(`   预测类型: ${prediction.predictionType}`);
      console.log(`   预测价格: $${predictedValue}`);
      console.log(`   预测方向: ${prediction.predictedDirection}`);
      console.log(`   置信度: ${confidence}%`);
      
      if (prediction.isVerified) {
        const verificationTime = prediction.verificationTimestamp ? 
          new Date(prediction.verificationTimestamp).toLocaleString('zh-CN') : '未知';
        const actualValue = prediction.actualValue ? 
          parseFloat(prediction.actualValue.toString()).toFixed(2) : '未知';
        const accuracyScore = prediction.accuracyScore ? 
          (parseFloat(prediction.accuracyScore.toString()) * 100).toFixed(1) : '0';
        const actualDirection = prediction.actualDirection || '未知';
        
        console.log(`   ✅ 验证状态: 已验证`);
        console.log(`   验证时间: ${verificationTime}`);
        console.log(`   实际价格: $${actualValue}`);
        console.log(`   实际方向: ${actualDirection}`);
        console.log(`   准确率: ${accuracyScore}%`);
        
        const isCorrect = parseFloat(prediction.accuracyScore?.toString() || '0') > 0.5;
        console.log(`   结果: ${isCorrect ? '✅ 预测正确' : '❌ 预测错误'}`);
      } else {
        console.log(`   ⏳ 验证状态: 待验证`);
      }
      
      console.log();
    });

    // 按日期分组统计
    console.log('📅 按日期分组统计:');
    console.log('-'.repeat(80));
    
    const groupedByDate = predictions.reduce((acc, prediction) => {
      const date = new Date(prediction.predictionTimestamp).toDateString();
      if (!acc[date]) {
        acc[date] = { total: 0, verified: 0, correct: 0 };
      }
      acc[date].total++;
      if (prediction.isVerified) {
        acc[date].verified++;
        if (parseFloat(prediction.accuracyScore?.toString() || '0') > 0.5) {
          acc[date].correct++;
        }
      }
      return acc;
    }, {} as Record<string, { total: number; verified: number; correct: number }>);

    Object.entries(groupedByDate).forEach(([date, stats]) => {
      const accuracy = stats.verified > 0 ? (stats.correct / stats.verified * 100).toFixed(1) : '0';
      console.log(`${date}:`);
      console.log(`   总预测: ${stats.total}, 已验证: ${stats.verified}, 正确: ${stats.correct}`);
      console.log(`   准确率: ${accuracy}%`);
      console.log();
    });

    // 获取学习指标
    console.log('📈 学习指标历史:');
    console.log('-'.repeat(80));
    
    const metrics = await prisma.shortCycleMetrics.findMany({
      where: { symbolId: btcSymbol.id },
      orderBy: { calculatedAt: 'desc' },
      take: 10
    });

    if (metrics.length > 0) {
      metrics.forEach((metric, index) => {
        const calculatedTime = new Date(metric.calculatedAt).toLocaleString('zh-CN');
        const accuracyRate = (parseFloat(metric.accuracyRate.toString()) * 100).toFixed(1);
        const avgConfidence = (parseFloat(metric.averageConfidence?.toString() || '0') * 100).toFixed(1);
        
        console.log(`${index + 1}. 计算时间: ${calculatedTime}`);
        console.log(`   时间窗口: ${metric.timeWindow}`);
        console.log(`   预测类型: ${metric.predictionType}`);
        console.log(`   准确率: ${accuracyRate}%`);
        console.log(`   预测总数: ${metric.predictionCount}`);
        console.log(`   正确预测: ${metric.correctPredictions}`);
        console.log(`   平均置信度: ${avgConfidence}%`);
        console.log();
      });
    } else {
      console.log('暂无学习指标记录');
    }

  } catch (error) {
    console.error('❌ 生成报告失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

generatePredictionReport(); 