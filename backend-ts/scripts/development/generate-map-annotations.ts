#!/usr/bin/env tsx

/**
 * 自动生成Prisma Schema @map注解脚本
 * 
 * 功能：
 * 1. 扫描schema.prisma文件
 * 2. 为所有缺少@@map注解的模型添加注解
 * 3. 使用标准命名规范（PascalCase模型名 -> snake_case表名）
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// 配置
const SCHEMA_PATH = path.resolve(__dirname, '../prisma/schema.prisma');
const BACKUP_DIR = path.resolve(__dirname, '../prisma/backups');

// 确保备份目录存在
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

/**
 * 将PascalCase转换为snake_case
 */
function toSnakeCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .toLowerCase();
}

/**
 * 创建schema备份
 */
function createBackup(): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = path.join(BACKUP_DIR, `schema_${timestamp}.prisma`);
  
  fs.copyFileSync(SCHEMA_PATH, backupPath);
  console.log(`📁 Schema备份已创建: ${backupPath}`);
  
  return backupPath;
}

/**
 * 提取模型定义
 */
function extractModels(schemaContent: string): { name: string, content: string, hasMap: boolean }[] {
  const modelRegex = /model\s+(\w+)\s*{([^}]*)}/gs;
  const models: { name: string, content: string, hasMap: boolean }[] = [];
  
  let match;
  while ((match = modelRegex.exec(schemaContent)) !== null) {
    const name = match[1];
    const content = match[2];
    const hasMap = content.includes('@@map(');
    
    models.push({ name, content, hasMap });
  }
  
  return models;
}

/**
 * 添加@map注解
 */
function addMapAnnotations(schemaContent: string): string {
  const models = extractModels(schemaContent);
  let modifiedContent = schemaContent;
  let addedCount = 0;
  
  for (const model of models) {
    if (model.hasMap) continue;
    
    const tableName = toSnakeCase(model.name);
    const modelRegex = new RegExp(`(model\\s+${model.name}\\s*{[^}]*?)(\\n})`, 'gs');
    
    modifiedContent = modifiedContent.replace(modelRegex, (match, modelBody, closingBrace) => {
      addedCount++;
      return `${modelBody}\n  @@map("${tableName}")${closingBrace}`;
    });
  }
  
  console.log(`✅ 已添加 ${addedCount} 个@map注解`);
  return modifiedContent;
}

/**
 * 验证Schema
 */
function validateSchema(): boolean {
  try {
    execSync('npx prisma validate', { stdio: 'pipe' });
    console.log('✅ Schema验证通过');
    return true;
  } catch (error) {
    console.error('❌ Schema验证失败:', error);
    return false;
  }
}

/**
 * 恢复备份
 */
function restoreBackup(backupPath: string): void {
  fs.copyFileSync(backupPath, SCHEMA_PATH);
  console.log(`🔄 已恢复备份: ${backupPath}`);
}

/**
 * 分析当前Schema状态
 */
function analyzeSchema(): void {
  const schemaContent = fs.readFileSync(SCHEMA_PATH, 'utf-8');
  const models = extractModels(schemaContent);
  
  const totalModels = models.length;
  const modelsWithMap = models.filter(m => m.hasMap).length;
  const modelsWithoutMap = totalModels - modelsWithMap;
  
  console.log('📊 Schema分析结果:');
  console.log(`总模型数: ${totalModels}`);
  console.log(`已有@map注解: ${modelsWithMap}`);
  console.log(`缺少@map注解: ${modelsWithoutMap}`);
  
  if (modelsWithoutMap > 0) {
    console.log('\n缺少@map注解的模型:');
    models
      .filter(m => !m.hasMap)
      .forEach(m => {
        console.log(`- ${m.name} -> ${toSnakeCase(m.name)}`);
      });
  }
}

/**
 * 主函数
 */
function main(): void {
  console.log('🚀 开始生成@map注解...\n');
  
  // 分析当前状态
  analyzeSchema();
  
  // 创建备份
  const backupPath = createBackup();
  
  try {
    // 读取schema内容
    const schemaContent = fs.readFileSync(SCHEMA_PATH, 'utf-8');
    
    // 添加@map注解
    const modifiedContent = addMapAnnotations(schemaContent);
    
    // 写入修改后的内容
    fs.writeFileSync(SCHEMA_PATH, modifiedContent);
    
    // 验证schema
    if (!validateSchema()) {
      throw new Error('Schema验证失败，正在恢复备份');
    }
    
    console.log('\n✅ @map注解生成完成！');
    console.log('\n📋 后续步骤:');
    console.log('1. 检查生成的@map注解是否符合预期');
    console.log('2. 运行 npm run db:generate 更新Prisma客户端');
    console.log('3. 如需应用到数据库，运行 npm run db:push');
    
  } catch (error) {
    console.error('❌ 操作失败:', error);
    restoreBackup(backupPath);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export {
  toSnakeCase,
  addMapAnnotations,
  validateSchema,
  analyzeSchema
};
