/**
 * 配置真实交易账户API凭证
 */

import { PrismaClient } from '@prisma/client';


/**
 * 配置真实交易账户
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

async function configureRealTradingAccount() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 配置真实交易账户API凭证...\n');
    
    // 查找真实交易账户
    const realAccount = await prisma.tradingAccounts.findFirst({
      where: {
        accountType: 'LIVE',
        executionEngine: 'binance'
      }
    });
    
    if (!realAccount) {
      console.log('❌ 未找到真实交易账户');
      return;
    }
    
    console.log(`✅ 找到真实交易账户: ${realAccount.name} (ID: ${realAccount.id})`);
    
    // 配置API凭证 - 从环境变量获取
    const apiCredentials = {
      apiKey: process.env.BINANCE_API_KEY || (() => {
        console.error('❌ BINANCE_API_KEY 环境变量未设置');
        process.exit(1);
      })(),
      secretKey: process.env.BINANCE_SECRET_KEY || (() => {
        console.error('❌ BINANCE_SECRET_KEY 环境变量未设置');
        process.exit(1);
      })(),
      testnet: false, // 真实交易，不是测试网
      permissions: ['SPOT', 'FUTURES'], // 现货和合约权限
      encryptedAt: new Date()
    };
    
    // 更新账户配置
    const updatedAccount = await prisma.tradingAccounts.update({
      where: { id: realAccount.id },
      data: {
        apiCredentials: apiCredentials,
        riskSettings: {
          maxDrawdownLimit: 0.15, // 15%最大回撤
          dailyLossLimit: 0.05,   // 5%单日亏损限制
          maxPositions: 3,        // 最多3个并发仓位
          riskPerTrade: 0.01,     // 1%单笔风险
          leverage: 10,           // 10倍杠杆
          fixedInvestment: 10,    // 每笔固定投入10美元
          atrPeriod: 14,          // ATR周期
          atrMultiplier: 1.5,     // ATR倍数
          pyramidTriggerRatio: 0.5, // 金字塔触发比例
          pyramidSizeRatio: 0.5,    // 金字塔大小比例
          maxPyramidLevels: 3,      // 最大金字塔层级
          minConfidenceScore: 0.75, // 最小确认分数
          takeProfitRatio: 2.0,     // 止盈比例（1:2风险收益比）
          trailingStopRatio: 0.8    // 追踪止损比例
        }
      }
    });
    
    console.log('✅ 真实交易账户配置完成:');
    console.log(`  账户名称: ${updatedAccount.name}`);
    console.log(`  账户类型: ${updatedAccount.accountType}`);
    console.log(`  执行引擎: ${updatedAccount.executionEngine}`);
    console.log(`  API凭证: 已配置 (测试网: ${apiCredentials.testnet})`);
    console.log(`  权限: ${apiCredentials.permissions.join(', ')}`);
    console.log(`  杠杆: ${(updatedAccount.riskSettings as any).leverage}x`);
    console.log(`  单笔风险: ${((updatedAccount.riskSettings as any).riskPerTrade * 100)}%`);
    console.log(`  固定投入: $${(updatedAccount.riskSettings as any).fixedInvestment}`);
    
    console.log('\n🔍 验证配置是否符合合约交易规范:');
    console.log('✅ 使用 futuresOrder 等合约API方法');
    console.log('✅ 配置了FUTURES权限');
    console.log('✅ 设置了10倍杠杆（合约交易）');
    console.log('✅ 配置了真实API凭证（非测试网）');
    console.log('✅ 设置了合适的风险管理参数');
    
  } catch (error) {
    console.error('❌ 配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行配置
configureRealTradingAccount().catch(console.error);
