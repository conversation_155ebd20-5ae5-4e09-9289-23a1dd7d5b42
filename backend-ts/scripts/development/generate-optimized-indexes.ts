#!/usr/bin/env tsx

/**
 * 数据库索引优化脚本
 * 
 * 功能：
 * 1. 基于查询模式生成优化的索引
 * 2. 添加复合索引以提高查询性能
 * 3. 为JSON字段创建GIN索引
 * 4. 生成索引创建的SQL脚本
 */

import fs from 'fs';
import path from 'path';
import { INDEX_OPTIMIZATIONS, JSON_INDEX_OPTIMIZATIONS, COMPOSITE_INDEX_RECOMMENDATIONS } from './index-optimization-config';

const SCHEMA_PATH = path.resolve(__dirname, '../prisma/schema.prisma');
const OUTPUT_DIR = path.resolve(__dirname, '../prisma/migrations');

/**
 * 生成Prisma索引注解
 */
function generatePrismaIndexes(): string {
  let indexAnnotations = '';
  
  for (const config of INDEX_OPTIMIZATIONS) {
    indexAnnotations += `\n// ${config.modelName} 索引优化\n`;
    
    for (const index of config.indexes) {
      const fields = index.fields.join(', ');
      let annotation = `@@index([${fields}])`;
      
      if (index.unique) {
        annotation = `@@unique([${fields}])`;
      }
      
      indexAnnotations += `// ${index.reason} (优先级: ${index.priority})\n`;
      indexAnnotations += `${annotation}\n`;
    }
    
    indexAnnotations += '\n';
  }
  
  return indexAnnotations;
}

/**
 * 生成SQL索引创建脚本
 */
function generateSQLIndexes(): string {
  let sqlScript = `-- 数据库索引优化脚本
-- 生成时间: ${new Date().toISOString()}
-- 
-- 注意：在生产环境执行前请先在测试环境验证
-- 建议在低峰期执行，某些索引创建可能需要较长时间

BEGIN;

`;

  // 标准B-tree索引
  for (const config of INDEX_OPTIMIZATIONS) {
    sqlScript += `-- ${config.modelName} 表索引优化\n`;
    
    for (const index of config.indexes) {
      const fields = index.fields.map(field => `"${field}"`).join(', ');
      let createIndex = `CREATE INDEX CONCURRENTLY IF NOT EXISTS "${index.name}" ON "${config.table}" (${fields})`;
      
      if (index.partial) {
        createIndex += ` ${index.partial}`;
      }
      
      if (index.unique) {
        createIndex = `CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS "${index.name}" ON "${config.table}" (${fields})`;
      }
      
      sqlScript += `-- ${index.reason} (优先级: ${index.priority})\n`;
      sqlScript += `${createIndex};\n\n`;
    }
  }

  // JSON字段GIN索引
  sqlScript += `-- JSON字段GIN索引\n`;
  for (const jsonIndex of JSON_INDEX_OPTIMIZATIONS) {
    const indexName = `${jsonIndex.table}_${jsonIndex.field}_gin_idx`;
    sqlScript += `-- ${jsonIndex.reason}\n`;
    sqlScript += `CREATE INDEX CONCURRENTLY IF NOT EXISTS "${indexName}" ON "${jsonIndex.table}" USING GIN ("${jsonIndex.field}");\n\n`;
  }

  // 复合索引
  sqlScript += `-- 复合索引优化\n`;
  for (const composite of COMPOSITE_INDEX_RECOMMENDATIONS) {
    const fields = composite.fields.map(field => `"${field}"`).join(', ');
    sqlScript += `-- ${composite.reason}\n`;
    sqlScript += `CREATE INDEX CONCURRENTLY IF NOT EXISTS "${composite.name}" ON "${composite.table}" (${fields});\n\n`;
  }

  sqlScript += `COMMIT;

-- 索引使用情况查询
-- 执行完成后可以使用以下查询检查索引使用情况：

/*
-- 检查索引大小
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
ORDER BY pg_relation_size(indexrelid) DESC;

-- 检查索引使用频率
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
ORDER BY idx_scan DESC;

-- 检查未使用的索引
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan
FROM pg_stat_user_indexes 
WHERE idx_scan = 0
AND indexname NOT LIKE '%_pkey';
*/
`;

  return sqlScript;
}

/**
 * 生成索引性能分析脚本
 */
function generatePerformanceAnalysisScript(): string {
  return `-- 索引性能分析脚本
-- 用于监控和分析索引效果

-- 1. 慢查询分析
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE mean_time > 100  -- 超过100ms的查询
ORDER BY mean_time DESC
LIMIT 20;

-- 2. 表扫描分析
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    n_tup_ins,
    n_tup_upd,
    n_tup_del
FROM pg_stat_user_tables
ORDER BY seq_scan DESC;

-- 3. 索引命中率
SELECT 
    schemaname,
    tablename,
    ROUND(
        100 * idx_scan / (seq_scan + idx_scan + 1), 2
    ) AS index_hit_rate
FROM pg_stat_user_tables
WHERE (seq_scan + idx_scan) > 0
ORDER BY index_hit_rate ASC;

-- 4. 缓存命中率
SELECT 
    'Buffer Cache Hit Rate' as metric,
    ROUND(
        100.0 * sum(blks_hit) / (sum(blks_hit) + sum(blks_read) + 1), 2
    ) as hit_rate
FROM pg_stat_database;

-- 5. 表大小分析
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
`;
}

/**
 * 更新Schema文件中的索引
 */
function updateSchemaIndexes(): void {
  const schemaContent = fs.readFileSync(SCHEMA_PATH, 'utf-8');
  
  // 为每个模型添加优化的索引注解
  let updatedContent = schemaContent;
  
  for (const config of INDEX_OPTIMIZATIONS) {
    const modelRegex = new RegExp(`(model\\s+${config.modelName}\\s*{[^}]*?)(\\n})`, 'gs');
    
    updatedContent = updatedContent.replace(modelRegex, (match, modelBody, closingBrace) => {
      // 检查是否已有优化的索引
      const hasOptimizedIndexes = config.indexes.some(index => 
        modelBody.includes(`@@index([${index.fields.join(', ')}])`)
      );
      
      if (hasOptimizedIndexes) {
        return match;
      }
      
      // 添加索引注解
      let indexAnnotations = '\n  // 性能优化索引';
      for (const index of config.indexes) {
        const fields = index.fields.join(', ');
        indexAnnotations += `\n  // ${index.reason}`;
        
        if (index.unique) {
          indexAnnotations += `\n  @@unique([${fields}])`;
        } else {
          indexAnnotations += `\n  @@index([${fields}])`;
        }
      }
      
      return `${modelBody}${indexAnnotations}${closingBrace}`;
    });
  }
  
  // 创建备份
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = path.join(path.dirname(SCHEMA_PATH), 'backups', `schema_${timestamp}.prisma`);
  fs.mkdirSync(path.dirname(backupPath), { recursive: true });
  fs.copyFileSync(SCHEMA_PATH, backupPath);
  
  // 写入更新的内容
  fs.writeFileSync(SCHEMA_PATH, updatedContent);
  console.log('✅ Schema索引已更新');
}

/**
 * 主函数
 */
function main(): void {
  console.log('🚀 开始生成优化索引...\n');
  
  // 确保输出目录存在
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }
  
  try {
    // 生成SQL索引脚本
    const sqlScript = generateSQLIndexes();
    const sqlPath = path.join(OUTPUT_DIR, 'optimize_indexes.sql');
    fs.writeFileSync(sqlPath, sqlScript);
    console.log(`📄 SQL索引脚本已生成: ${sqlPath}`);
    
    // 生成性能分析脚本
    const analysisScript = generatePerformanceAnalysisScript();
    const analysisPath = path.join(OUTPUT_DIR, 'analyze_performance.sql');
    fs.writeFileSync(analysisScript, analysisScript);
    console.log(`📊 性能分析脚本已生成: ${analysisPath}`);
    
    // 更新Schema文件
    updateSchemaIndexes();
    
    console.log('\n✅ 索引优化完成！');
    console.log('\n📋 后续步骤:');
    console.log('1. 检查生成的SQL脚本');
    console.log('2. 在测试环境验证索引效果');
    console.log('3. 在生产环境低峰期执行索引创建');
    console.log('4. 使用性能分析脚本监控效果');
    
  } catch (error) {
    console.error('❌ 索引生成失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export {
  generateSQLIndexes,
  generatePerformanceAnalysisScript,
  updateSchemaIndexes
};
