/**
 * 初始化币安执行引擎脚本
 * 设置API凭证并连接币安引擎
 */

import { setupContainer, getContainer } from '../src/shared/infrastructure/di/container';
import { TYPES } from '../src/shared/infrastructure/di/types';
import { BinanceEngine } from '../src/contexts/trading-execution/domain/services/binance-engine';
import { ExecutionEngineRouter } from '../src/contexts/trading-execution/domain/services/execution-engine-router';
import { ApiCredentials } from '../src/contexts/trading-execution/domain/types/dual-track.types';
import { getEnvironment } from '../src/config/environment';

async function main() {
  console.log('🚀 开始初始化币安执行引擎...');

  try {
    // 设置DI容器
    await setupContainer();
    const container = getContainer();

    // 获取币安引擎和路由器
    const binanceEngine = container.get<BinanceEngine>(TYPES.TradingExecution.BinanceEngine);
    const engineRouter = container.get<ExecutionEngineRouter>(TYPES.TradingExecution.ExecutionEngineRouter);

    console.log('📋 检查环境变量配置...');

    const env = getEnvironment();

    if (!env.BINANCE_API_KEY || !env.BINANCE_SECRET_KEY) {
      throw new Error('缺少币安API凭证环境变量：BINANCE_API_KEY 和 BINANCE_SECRET_KEY');
    }

    console.log('✅ 环境变量配置正常');
    console.log(`   API Key: ${env.BINANCE_API_KEY.substring(0, 8)}****${env.BINANCE_API_KEY.substring(env.BINANCE_API_KEY.length - 8)}`);

    // 创建API凭证
    const credentials: ApiCredentials = {
      apiKey: env.BINANCE_API_KEY,
      secretKey: env.BINANCE_SECRET_KEY,
      testnet: false, // 使用真实网络
      permissions: ['SPOT', 'FUTURES'],
      encryptedAt: new Date()
    };

    console.log('🔐 设置币安引擎凭证...');
    await binanceEngine.setCredentials(credentials);

    console.log('🔗 连接币安引擎...');
    await binanceEngine.connect();

    console.log('✅ 币安引擎连接成功！');
    console.log(`   引擎类型: ${binanceEngine.engineType}`);
    console.log(`   连接状态: ${binanceEngine.isConnected()}`);
    console.log(`   是否为真实交易: ${binanceEngine.isLive}`);

    // 注册引擎到路由器
    console.log('📝 注册引擎到路由器...');
    engineRouter.registerEngine(binanceEngine);

    console.log('✅ 引擎注册成功！');

    // 测试币安API连接
    console.log('🧪 测试币安API连接...');
    
    try {
      // 测试获取BTC价格
      const btcPrice = await binanceEngine.getCurrentPrice('BTCUSDT');
      console.log(`✅ BTC当前价格: $${btcPrice.toFixed(2)}`);

      // 测试获取账户信息（这会调用真实的币安API）
      console.log('🏦 测试获取账户信息...');
      const accountBalance = await binanceEngine.getAccountBalance('test-account-id');
      
      console.log('✅ 账户信息获取成功：');
      console.log(`   总余额: ${accountBalance.totalBalance.toFixed(2)} ${accountBalance.currency}`);
      console.log(`   可用余额: ${accountBalance.availableBalance.toFixed(2)} ${accountBalance.currency}`);
      console.log(`   已用保证金: ${accountBalance.marginUsed.toFixed(2)} ${accountBalance.currency}`);
      console.log(`   未实现盈亏: ${accountBalance.unrealizedPnl.toFixed(2)} ${accountBalance.currency}`);

    } catch (error) {
      console.error('⚠️ API测试失败（这可能是正常的，如果账户没有期货权限）:', error);
      console.log('💡 提示：确保币安账户已开通期货交易权限');
    }

    console.log('\n🎉 币安引擎初始化完成！');
    console.log('💡 现在真实交易账户可以获取真实的余额数据了');

  } catch (error) {
    console.error('❌ 币安引擎初始化失败:', error);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as initializeBinanceEngine };
