/**
 * 配置系统只交易BTC
 */

import { PrismaClient } from '@prisma/client';


/**
 * BTC交易配置
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

async function configureBTCOnlyTrading() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 配置系统只交易BTC...\n');
    
    // 1. 检查当前交易对
    console.log('1️⃣ 检查当前交易对配置...');
    const allSymbols = await prisma.symbols.findMany({
      where: { isActive: true },
      orderBy: { symbol: 'asc' }
    });
    
    const btcSymbols = allSymbols.filter(s => s.symbol.includes('BTC'));
    const nonBtcSymbols = allSymbols.filter(s => !s.symbol.includes('BTC'));
    
    console.log(`✅ 找到 ${allSymbols.length} 个活跃交易对`);
    console.log(`  BTC相关: ${btcSymbols.length} 个`);
    console.log(`  非BTC: ${nonBtcSymbols.length} 个`);
    
    // 显示BTC交易对
    console.log('\n📊 BTC相关交易对:');
    btcSymbols.forEach(s => {
      console.log(`  ${s.symbol} (ID: ${s.id})`);
    });
    
    // 2. 禁用所有非BTC交易对
    if (nonBtcSymbols.length > 0) {
      console.log('\n2️⃣ 禁用非BTC交易对...');
      
      const disableResult = await prisma.symbols.updateMany({
        where: {
          id: { in: nonBtcSymbols.map(s => s.id) }
        },
        data: {
          isActive: false,
          status: 'DISABLED'
        }
      });
      
      console.log(`✅ 已禁用 ${disableResult.count} 个非BTC交易对`);
    } else {
      console.log('\n2️⃣ 无需禁用非BTC交易对（已经没有活跃的非BTC交易对）');
    }
    
    // 3. 确保BTC/USDT是活跃的
    console.log('\n3️⃣ 确保BTC/USDT交易对活跃...');
    
    let btcUsdtSymbol = await prisma.symbols.findFirst({
      where: { symbol: 'BTC/USDT' }
    });
    
    if (!btcUsdtSymbol) {
      // 创建BTC/USDT交易对
      btcUsdtSymbol = await prisma.symbols.create({
        data: {
          symbol: 'BTC/USDT',
          baseAsset: 'BTC',
          quoteAsset: 'USDT',
          isActive: true,
          status: 'TRADING',
          exchange: 'binance',
          tradingRules: {
            tickSize: 0.01,
            stepSize: 0.00001,
            minNotional: 10.0,
            maxNotional: 1000000.0,
            minQty: 0.00001,
            maxQty: 1000.0
          },
          metadata: {
            contractType: 'PERPETUAL',
            marginAsset: 'USDT',
            pricePrecision: 2,
            quantityPrecision: 5
          }
        }
      });
      console.log('✅ 已创建BTC/USDT交易对');
    } else if (!btcUsdtSymbol.isActive) {
      // 激活BTC/USDT交易对
      await prisma.symbols.update({
        where: { id: btcUsdtSymbol.id },
        data: {
          isActive: true,
          status: 'TRADING'
        }
      });
      console.log('✅ 已激活BTC/USDT交易对');
    } else {
      console.log('✅ BTC/USDT交易对已经是活跃状态');
    }
    
    // 4. 更新真实交易账户配置，添加交易对限制
    console.log('\n4️⃣ 更新真实交易账户配置...');
    
    const realAccount = await prisma.tradingAccounts.findFirst({
      where: {
        accountType: 'LIVE',
        executionEngine: 'binance'
      }
    });
    
    if (realAccount) {
      const currentRiskSettings = realAccount.riskSettings as any;
      
      const updatedRiskSettings = {
        ...currentRiskSettings,
        // 添加交易对限制
        allowedSymbols: ['BTC/USDT', 'BTCUSDT'], // 只支持BTC期货交易对
        tradingRestrictions: {
          onlyBTC: true,
          allowedBaseAssets: ['BTC'],
          allowedQuoteAssets: ['USDT'],
          blockedSymbols: [], // 可以添加明确禁止的交易对
        },
        // 更新其他设置以适应BTC交易
        leverage: 10,           // 10倍杠杆
        fixedInvestment: 10,    // 每笔固定投入10美元
        riskPerTrade: 0.01,     // 1%单笔风险
        maxPositions: 1,        // 只允许1个BTC仓位
        minConfidenceScore: 0.75, // 最小确认分数
        takeProfitRatio: 2.0,     // 止盈比例（1:2风险收益比）
        trailingStopRatio: 0.8    // 追踪止损比例
      };
      
      await prisma.tradingAccounts.update({
        where: { id: realAccount.id },
        data: {
          riskSettings: updatedRiskSettings
        }
      });
      
      console.log('✅ 已更新真实交易账户配置');
      console.log('  - 限制只交易BTC相关交易对');
      console.log('  - 最大仓位数: 1个');
      console.log('  - 允许的交易对: BTC/USDT');
    }
    
    // 5. 更新模拟交易账户配置
    console.log('\n5️⃣ 更新模拟交易账户配置...');
    
    const simAccount = await prisma.tradingAccounts.findFirst({
      where: {
        accountType: 'SIMULATION',
        executionEngine: 'simulation'
      }
    });
    
    if (simAccount) {
      const currentRiskSettings = simAccount.riskSettings as any;
      
      const updatedRiskSettings = {
        ...currentRiskSettings,
        allowedSymbols: ['BTC/USDT', 'BTCUSDT'],
        tradingRestrictions: {
          onlyBTC: true,
          allowedBaseAssets: ['BTC'],
          allowedQuoteAssets: ['USDT'],
          blockedSymbols: [],
        },
        maxPositions: 1, // 只允许1个BTC仓位
      };
      
      await prisma.tradingAccounts.update({
        where: { id: simAccount.id },
        data: {
          riskSettings: updatedRiskSettings
        }
      });
      
      console.log('✅ 已更新模拟交易账户配置');
    }
    
    // 6. 验证配置结果
    console.log('\n6️⃣ 验证配置结果...');
    
    const activeSymbols = await prisma.symbols.findMany({
      where: { isActive: true },
      orderBy: { symbol: 'asc' }
    });
    
    console.log(`✅ 当前活跃交易对: ${activeSymbols.length} 个`);
    activeSymbols.forEach(s => {
      console.log(`  ${s.symbol} (${s.status})`);
    });
    
    console.log('\n🎯 BTC专用交易配置完成:');
    console.log('✅ 已禁用所有非BTC交易对');
    console.log('✅ 确保BTC/USDT交易对活跃');
    console.log('✅ 更新了账户交易限制');
    console.log('✅ 设置最大仓位数为1个');
    console.log('✅ 系统现在只能交易BTC');
    
  } catch (error) {
    console.error('❌ 配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行配置
configureBTCOnlyTrading().catch(console.error);
