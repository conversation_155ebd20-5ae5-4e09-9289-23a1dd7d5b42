/**
 * 数据库索引优化配置
 * 
 * 基于查询模式和性能分析的索引优化建议
 */

export interface IndexConfig {
  table: string;
  modelName: string;
  indexes: {
    name: string;
    fields: string[];
    type: 'btree' | 'gin' | 'gist' | 'hash';
    unique?: boolean;
    partial?: string;
    reason: string;
    priority: 'high' | 'medium' | 'low';
  }[];
}

/**
 * 高性能索引配置
 * 基于实际查询模式优化
 */
export const INDEX_OPTIMIZATIONS: IndexConfig[] = [
  {
    table: 'ai_call_logs',
    modelName: 'AiCallLogs',
    indexes: [
      {
        name: 'ai_call_logs_user_time_idx',
        fields: ['userId', 'createdAt'],
        type: 'btree',
        reason: '用户AI调用历史查询',
        priority: 'high'
      },
      {
        name: 'ai_call_logs_model_type_time_idx',
        fields: ['modelName', 'callType', 'createdAt'],
        type: 'btree',
        reason: '按模型和类型的性能分析',
        priority: 'high'
      },
      {
        name: 'ai_call_logs_cache_performance_idx',
        fields: ['cacheHit', 'responseTimeMs'],
        type: 'btree',
        reason: '缓存性能分析',
        priority: 'medium'
      },
      {
        name: 'ai_call_logs_cost_analysis_idx',
        fields: ['costUsd', 'createdAt'],
        type: 'btree',
        reason: '成本分析和报告',
        priority: 'high'
      },
      {
        name: 'ai_call_logs_quality_idx',
        fields: ['qualityScore', 'confidenceScore'],
        type: 'btree',
        partial: 'WHERE qualityScore IS NOT NULL',
        reason: '质量评估查询',
        priority: 'medium'
      }
    ]
  },
  {
    table: 'trading_signals',
    modelName: 'TradingSignals',
    indexes: [
      {
        name: 'trading_signals_symbol_type_time_idx',
        fields: ['symbolId', 'signalType', 'createdAt'],
        type: 'btree',
        reason: '按交易对和信号类型查询',
        priority: 'high'
      },
      {
        name: 'trading_signals_active_confidence_idx',
        fields: ['isActive', 'confidence', 'createdAt'],
        type: 'btree',
        reason: '活跃高置信度信号查询',
        priority: 'high'
      },
      {
        name: 'trading_signals_strategy_symbol_idx',
        fields: ['strategyType', 'symbolId'],
        type: 'btree',
        reason: '策略类型分组查询',
        priority: 'medium'
      },
      {
        name: 'trading_signals_timeframe_idx',
        fields: ['timeframe', 'createdAt'],
        type: 'btree',
        reason: '时间框架分析',
        priority: 'medium'
      }
    ]
  },
  {
    table: 'price_data',
    modelName: 'PriceData',
    indexes: [
      {
        name: 'price_data_symbol_time_idx',
        fields: ['symbolId', 'timestamp'],
        type: 'btree',
        reason: '价格时序查询',
        priority: 'high'
      },
      {
        name: 'price_data_quality_time_idx',
        fields: ['dataQuality', 'timestamp'],
        type: 'btree',
        reason: '数据质量监控',
        priority: 'medium'
      },
      {
        name: 'price_data_cache_expiry_idx',
        fields: ['cacheExpiresAt'],
        type: 'btree',
        partial: 'WHERE cacheExpiresAt IS NOT NULL',
        reason: '缓存过期清理',
        priority: 'high'
      },
      {
        name: 'price_data_technical_indicators_idx',
        fields: ['symbolId', 'rsi14', 'macdLine'],
        type: 'btree',
        partial: 'WHERE rsi14 IS NOT NULL AND macdLine IS NOT NULL',
        reason: '技术指标查询',
        priority: 'medium'
      }
    ]
  },
  {
    table: 'historical_data',
    modelName: 'HistoricalData',
    indexes: [
      {
        name: 'historical_data_symbol_timeframe_time_idx',
        fields: ['symbolId', 'timeframe', 'timestamp'],
        type: 'btree',
        reason: '历史数据时序查询',
        priority: 'high'
      },
      {
        name: 'historical_data_source_time_idx',
        fields: ['dataSource', 'timestamp'],
        type: 'btree',
        reason: '数据源分析',
        priority: 'medium'
      },
      {
        name: 'historical_data_quality_idx',
        fields: ['dataQuality', 'timestamp'],
        type: 'btree',
        reason: '数据质量监控',
        priority: 'medium'
      },
      {
        name: 'historical_data_protected_idx',
        fields: ['isProtected', 'timestamp'],
        type: 'btree',
        reason: '数据保护查询',
        priority: 'low'
      }
    ]
  },
  {
    table: 'risk_assessments',
    modelName: 'RiskAssessments',
    indexes: [
      {
        name: 'risk_assessments_symbol_level_time_idx',
        fields: ['symbolId', 'riskLevel', 'createdAt'],
        type: 'btree',
        reason: '风险等级查询',
        priority: 'high'
      },
      {
        name: 'risk_assessments_score_time_idx',
        fields: ['overallRiskScore', 'createdAt'],
        type: 'btree',
        reason: '风险评分排序',
        priority: 'high'
      },
      {
        name: 'risk_assessments_active_idx',
        fields: ['isActive', 'createdAt'],
        type: 'btree',
        reason: '活跃风险评估',
        priority: 'medium'
      }
    ]
  },
  {
    table: 'symbols',
    modelName: 'Symbols',
    indexes: [
      {
        name: 'symbols_active_exchange_idx',
        fields: ['isActive', 'exchange'],
        type: 'btree',
        reason: '活跃交易对查询',
        priority: 'high'
      },
      {
        name: 'symbols_base_quote_idx',
        fields: ['baseAsset', 'quoteAsset'],
        type: 'btree',
        reason: '资产对查询',
        priority: 'medium'
      },
      {
        name: 'symbols_symbol_unique_idx',
        fields: ['symbol'],
        type: 'btree',
        unique: true,
        reason: '交易对唯一性',
        priority: 'high'
      }
    ]
  },
  {
    table: 'short_cycle_predictions',
    modelName: 'ShortCyclePredictions',
    indexes: [
      {
        name: 'short_predictions_symbol_type_time_idx',
        fields: ['symbolId', 'predictionType', 'predictionTimestamp'],
        type: 'btree',
        reason: '短期预测查询',
        priority: 'high'
      },
      {
        name: 'short_predictions_verification_idx',
        fields: ['targetVerificationTime', 'isVerified'],
        type: 'btree',
        reason: '预测验证调度',
        priority: 'high'
      },
      {
        name: 'short_predictions_accuracy_idx',
        fields: ['accuracyScore', 'confidence'],
        type: 'btree',
        partial: 'WHERE accuracyScore IS NOT NULL',
        reason: '预测准确性分析',
        priority: 'medium'
      }
    ]
  },
  {
    table: 'long_cycle_predictions',
    modelName: 'LongCyclePredictions',
    indexes: [
      {
        name: 'long_predictions_symbol_type_time_idx',
        fields: ['symbolId', 'predictionType', 'predictionTimestamp'],
        type: 'btree',
        reason: '长期预测查询',
        priority: 'high'
      },
      {
        name: 'long_predictions_verification_idx',
        fields: ['targetVerificationTime', 'isVerified'],
        type: 'btree',
        reason: '预测验证调度',
        priority: 'high'
      },
      {
        name: 'long_predictions_horizon_idx',
        fields: ['predictionHorizon', 'createdAt'],
        type: 'btree',
        reason: '预测时间范围分析',
        priority: 'medium'
      }
    ]
  },
  {
    table: 'macro_cycle_predictions',
    modelName: 'MacroCyclePredictions',
    indexes: [
      {
        name: 'macro_predictions_symbol_type_time_idx',
        fields: ['symbolId', 'predictionType', 'predictionTimestamp'],
        type: 'btree',
        reason: '宏观预测查询',
        priority: 'high'
      },
      {
        name: 'macro_predictions_verification_idx',
        fields: ['targetVerificationTime', 'isVerified'],
        type: 'btree',
        reason: '预测验证调度',
        priority: 'high'
      },
      {
        name: 'macro_predictions_strategy_idx',
        fields: ['strategyType', 'confidence'],
        type: 'btree',
        reason: '策略类型分析',
        priority: 'medium'
      }
    ]
  },
  {
    table: 'users',
    modelName: 'Users',
    indexes: [
      {
        name: 'users_email_unique_idx',
        fields: ['email'],
        type: 'btree',
        unique: true,
        reason: '邮箱唯一性',
        priority: 'high'
      },
      {
        name: 'users_username_unique_idx',
        fields: ['username'],
        type: 'btree',
        unique: true,
        reason: '用户名唯一性',
        priority: 'high'
      },
      {
        name: 'users_active_role_idx',
        fields: ['isActive', 'role'],
        type: 'btree',
        reason: '活跃用户角色查询',
        priority: 'medium'
      },
      {
        name: 'users_last_login_idx',
        fields: ['lastLoginAt'],
        type: 'btree',
        partial: 'WHERE lastLoginAt IS NOT NULL',
        reason: '用户活跃度分析',
        priority: 'low'
      }
    ]
  }
];

/**
 * JSON字段的GIN索引配置
 */
export const JSON_INDEX_OPTIMIZATIONS = [
  {
    table: 'ai_call_logs',
    field: 'marketContext',
    reason: '市场上下文查询'
  },
  {
    table: 'ai_call_logs',
    field: 'inputData',
    reason: '输入数据查询'
  },
  {
    table: 'trading_signals',
    field: 'metadata',
    reason: '信号元数据查询'
  },
  {
    table: 'risk_assessments',
    field: 'riskFactors',
    reason: '风险因子查询'
  }
];

/**
 * 复合索引优化建议
 */
export const COMPOSITE_INDEX_RECOMMENDATIONS = [
  {
    table: 'ai_call_logs',
    name: 'ai_calls_performance_analysis',
    fields: ['modelName', 'responseTimeMs', 'costUsd', 'createdAt'],
    reason: '综合性能分析查询'
  },
  {
    table: 'trading_signals',
    name: 'signals_trading_analysis',
    fields: ['symbolId', 'signalType', 'confidence', 'isActive', 'createdAt'],
    reason: '交易信号综合分析'
  },
  {
    table: 'price_data',
    name: 'price_technical_analysis',
    fields: ['symbolId', 'timestamp', 'rsi14', 'macdLine', 'dataQuality'],
    reason: '技术分析综合查询'
  }
];

/**
 * 分区表建议
 */
export const PARTITIONING_RECOMMENDATIONS = [
  {
    table: 'ai_call_logs',
    strategy: 'RANGE',
    column: 'createdAt',
    interval: 'MONTH',
    reason: '按月分区，提高历史数据查询性能'
  },
  {
    table: 'historical_data',
    strategy: 'RANGE',
    column: 'timestamp',
    interval: 'MONTH',
    reason: '按月分区，优化时序数据查询'
  },
  {
    table: 'price_data',
    strategy: 'RANGE',
    column: 'timestamp',
    interval: 'WEEK',
    reason: '按周分区，优化实时数据查询'
  }
];
