/**
 * 邀请码生成脚本
 * 用于生成初始的邀请码，供用户注册使用
 */

import { PrismaClient } from '@prisma/client';
import { randomBytes } from 'crypto';

const prisma = new PrismaClient();

/**
 * 生成随机邀请码
 */
function generateInvitationCode(): string {
  // 生成8字节随机数据，转换为16进制字符串
  const randomData = randomBytes(8);
  return randomData.toString('hex').toUpperCase();
}

/**
 * 生成多个邀请码
 */
async function generateInvitationCodes(count: number, expiresInDays?: number): Promise<void> {
  console.log(`🎫 开始生成 ${count} 个邀请码...`);

  const expiresAt = expiresInDays 
    ? new Date(Date.now() + expiresInDays * 24 * 60 * 60 * 1000)
    : null;

  const codes: string[] = [];
  const createdCodes: any[] = [];

  // 生成唯一的邀请码
  for (let i = 0; i < count; i++) {
    let code: string;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      code = generateInvitationCode();
      attempts++;
      
      if (attempts > maxAttempts) {
        throw new Error(`生成唯一邀请码失败，尝试了 ${maxAttempts} 次`);
      }
    } while (codes.includes(code));

    codes.push(code);

    try {
      const invitationCode = await prisma.invitationCodes.create({
        data: {
          code,
          expiresAt,
          isUsed: false,
        }
      });

      createdCodes.push(invitationCode);
      console.log(`✅ 生成邀请码: ${code} ${expiresAt ? `(过期时间: ${expiresAt.toISOString().split('T')[0]})` : '(永不过期)'}`);
    } catch (error) {
      console.error(`❌ 创建邀请码失败 ${code}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  console.log(`\n🎉 成功生成 ${createdCodes.length} 个邀请码!`);
  
  if (createdCodes.length > 0) {
    console.log('\n📋 邀请码列表:');
    console.log('================');
    createdCodes.forEach((code, index) => {
      console.log(`${index + 1}. ${code.code}`);
    });
    console.log('================');
  }
}

/**
 * 查看邀请码统计
 */
async function showInvitationStats(): Promise<void> {
  console.log('📊 邀请码统计信息\n');

  const [total, unused, used, expired] = await Promise.all([
    prisma.invitationCodes.count(),
    prisma.invitationCodes.count({ where: { isUsed: false } }),
    prisma.invitationCodes.count({ where: { isUsed: true } }),
    prisma.invitationCodes.count({
      where: {
        isUsed: false,
        expiresAt: {
          lt: new Date()
        }
      }
    })
  ]);

  console.log(`总邀请码数量: ${total}`);
  console.log(`未使用: ${unused}`);
  console.log(`已使用: ${used}`);
  console.log(`已过期: ${expired}`);
  console.log(`可用: ${unused - expired}`);

  // 显示最近的邀请码
  const recentCodes = await prisma.invitationCodes.findMany({
    where: { isUsed: false },
    orderBy: { createdAt: 'desc' },
    take: 10
  });

  if (recentCodes.length > 0) {
    console.log('\n🎫 最近的可用邀请码:');
    console.log('================');
    recentCodes.forEach((code, index) => {
      const status = code.expiresAt && code.expiresAt < new Date() ? '(已过期)' : '(可用)';
      const expiry = code.expiresAt ? ` - 过期: ${code.expiresAt.toISOString().split('T')[0]}` : ' - 永不过期';
      console.log(`${index + 1}. ${code.code} ${status}${expiry}`);
    });
    console.log('================');
  }
}

/**
 * 清理过期的邀请码
 */
async function cleanupExpiredCodes(): Promise<void> {
  console.log('🧹 清理过期的邀请码...');

  const result = await prisma.invitationCodes.deleteMany({
    where: {
      isUsed: false,
      expiresAt: {
        lt: new Date()
      }
    }
  });

  console.log(`✅ 清理完成，删除了 ${result.count} 个过期的邀请码`);
}

/**
 * 生成管理员邀请码
 */
async function generateAdminInvitationCode(): Promise<void> {
  console.log('👑 生成管理员邀请码...');

  const code = 'ADMIN-' + generateInvitationCode();
  
  try {
    const invitationCode = await prisma.invitationCodes.create({
      data: {
        code,
        isUsed: false,
        // 管理员邀请码永不过期
        expiresAt: null,
      }
    });

    console.log(`✅ 管理员邀请码生成成功: ${code}`);
    console.log(`⚠️  请妥善保管此邀请码，它可以用于创建管理员账户`);
  } catch (error) {
    console.error(`❌ 生成管理员邀请码失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 主函数
 */
async function main() {
  const command = process.argv[2];
  const param = process.argv[3];

  try {
    switch (command) {
      case 'generate':
        const count = parseInt(param) || 5;
        const expiresInDays = process.argv[4] ? parseInt(process.argv[4]) : undefined;
        await generateInvitationCodes(count, expiresInDays);
        break;
        
      case 'admin':
        await generateAdminInvitationCode();
        break;
        
      case 'stats':
        await showInvitationStats();
        break;
        
      case 'cleanup':
        await cleanupExpiredCodes();
        break;
        
      default:
        console.log('邀请码管理工具');
        console.log('');
        console.log('用法:');
        console.log('  npm run invitation generate [数量] [过期天数]  # 生成邀请码');
        console.log('  npm run invitation admin                      # 生成管理员邀请码');
        console.log('  npm run invitation stats                      # 查看统计信息');
        console.log('  npm run invitation cleanup                    # 清理过期邀请码');
        console.log('');
        console.log('示例:');
        console.log('  npm run invitation generate 10 30  # 生成10个邀请码，30天后过期');
        console.log('  npm run invitation generate 5      # 生成5个永不过期的邀请码');
        break;
    }
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { generateInvitationCodes, showInvitationStats, cleanupExpiredCodes };
