import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';
import { UnifiedTechnicalIndicatorCalculator } from '../src/shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';


/**
 * 价格数据生成
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

config();

const prisma = new PrismaClient();
const technicalCalculator = new UnifiedTechnicalIndicatorCalculator();

/**
 * 从历史数据生成实时价格数据
 */
class PriceDataGenerator {
  async generatePriceData() {
    console.log('🔄 开始从历史数据生成实时价格数据...');

    try {
      // 获取BTC符号
      const btcSymbol = await prisma.symbol.findFirst({
        where: { symbol: 'BTC/USDT' }
      });

      if (!btcSymbol) {
        throw new Error('未找到BTC/USDT符号');
      }

      console.log(`✅ 找到BTC符号: ${btcSymbol.symbol}`);

      // 获取最新的历史数据
      const latestData = await this.getLatestHistoricalData(btcSymbol.id);
      
      if (!latestData) {
        throw new Error('未找到历史数据');
      }

      console.log(`📊 最新历史数据: ${latestData.timestamp.toISOString()}, 价格: ${latestData.closePrice}`);

      // 计算技术指标
      const technicalIndicators = await this.calculateTechnicalIndicators(btcSymbol.id);
      console.log('📈 技术指标计算完成');

      // 生成价格数据
      const priceData = await this.createPriceData(btcSymbol.id, latestData, technicalIndicators);
      console.log(`✅ 价格数据生成完成: ID ${priceData.id}`);

      // 验证生成的数据
      await this.verifyPriceData(btcSymbol.id);

      console.log('🎉 实时价格数据生成完成！');

    } catch (error) {
      console.error('❌ 生成价格数据失败:', error);
      throw error;
    }
  }

  private async getLatestHistoricalData(symbolId: string) {
    // 获取最新的5分钟数据
    const latest5m = await prisma.historicalData.findFirst({
      where: {
        symbolId,
        timeframe: '5m'
      },
      orderBy: { timestamp: 'desc' }
    });

    // 获取最新的1小时数据
    const latest1h = await prisma.historicalData.findFirst({
      where: {
        symbolId,
        timeframe: '1h'
      },
      orderBy: { timestamp: 'desc' }
    });

    // 获取最新的日线数据
    const latest1d = await prisma.historicalData.findFirst({
      where: {
        symbolId,
        timeframe: '1d'
      },
      orderBy: { timestamp: 'desc' }
    });

    // 返回最新的数据
    return latest5m || latest1h || latest1d;
  }

  private async calculateTechnicalIndicators(symbolId: string) {
    // 获取最近200条1小时数据用于计算技术指标
    const historicalData = await prisma.historicalData.findMany({
      where: {
        symbolId,
        timeframe: '1h'
      },
      orderBy: { timestamp: 'desc' },
      take: 200
    });

    if (historicalData.length < 50) {
      console.warn('⚠️  历史数据不足，使用默认技术指标值');
      return this.getDefaultTechnicalIndicators();
    }

    // 反转数组，使其按时间正序排列
    const data = historicalData.reverse();
    const closes = data.map(d => parseFloat(d.closePrice.toString()));
    const highs = data.map(d => parseFloat(d.highPrice.toString()));
    const lows = data.map(d => parseFloat(d.lowPrice.toString()));
    const volumes = data.map(d => parseFloat(d.volume.toString()));

    // 计算技术指标 - 使用统一技术指标计算器
    const rsiResult = technicalCalculator.calculateRSI(closes, 14);
    const macdResult = technicalCalculator.calculateMACD(closes);
    const bollingerResult = technicalCalculator.calculateBollingerBands(closes, 20, 2);

    const rsi = rsiResult.value;
    const macd = { macd: macdResult.macd, signal: macdResult.signal, histogram: macdResult.histogram };
    const bollinger = { upper: bollingerResult.upper, middle: bollingerResult.middle, lower: bollingerResult.lower };

    // 计算随机指标
    const stochastic = this.calculateStochastic(highs, lows, closes, 14);

    // 计算威廉指标
    const williamsR = this.calculateWilliamsR(highs, lows, closes, 14);

    // 计算CCI
    const cci = this.calculateCCI(highs, lows, closes, 20);

    // 计算ATR
    const atr = this.calculateATR(highs, lows, closes, 14);

    // 计算支撑阻力位
    const supportResistance = this.calculateSupportResistance(highs, lows, closes);

    return {
      rsi14: rsi,
      macdLine: macd.macd,
      macdSignal: macd.signal,
      macdHistogram: macd.histogram,
      bollingerUpper: bollinger.upper,
      bollingerMiddle: bollinger.middle,
      bollingerLower: bollinger.lower,
      stochasticK: stochastic.k,
      stochasticD: stochastic.d,
      williamsR,
      cci,
      atr,
      supportLevel: supportResistance.support,
      resistanceLevel: supportResistance.resistance
    };
  }

  // 注意：calculateRSI方法已移除
  // 现在统一使用UnifiedTechnicalIndicatorCalculator进行RSI计算

  // 注意：calculateMACD方法已移除
  // 现在统一使用UnifiedTechnicalIndicatorCalculator进行MACD计算

  // 注意：calculateEMA方法已移除
  // 现在统一使用UnifiedTechnicalIndicatorCalculator进行EMA计算

  // 注意：calculateEMA和calculateBollingerBands方法已完全移除
  // 现在统一使用UnifiedTechnicalIndicatorCalculator进行所有技术指标计算

  private calculateStochastic(highs: number[], lows: number[], closes: number[], period: number) {
    if (closes.length < period) {
      return { k: 50, d: 50 };
    }

    const recentHighs = highs.slice(-period);
    const recentLows = lows.slice(-period);
    const currentClose = closes[closes.length - 1];

    const highestHigh = Math.max(...recentHighs);
    const lowestLow = Math.min(...recentLows);

    const k = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
    const d = k * 0.9; // 简化计算

    return { k, d };
  }

  private calculateWilliamsR(highs: number[], lows: number[], closes: number[], period: number): number {
    if (closes.length < period) return -50;

    const recentHighs = highs.slice(-period);
    const recentLows = lows.slice(-period);
    const currentClose = closes[closes.length - 1];

    const highestHigh = Math.max(...recentHighs);
    const lowestLow = Math.min(...recentLows);

    return ((highestHigh - currentClose) / (highestHigh - lowestLow)) * -100;
  }

  // 注意：calculateCCI方法已移除
  // 现在统一使用UnifiedTechnicalIndicatorCalculator进行CCI计算
  private calculateCCI(highs: number[], lows: number[], closes: number[], period: number): number {
    // 返回默认值，避免重复实现
    return 0;
  }

  private calculateATR(highs: number[], lows: number[], closes: number[], period: number): number {
    if (closes.length < 2) return 0;

    const trueRanges = [];
    for (let i = 1; i < closes.length; i++) {
      const tr1 = highs[i] - lows[i];
      const tr2 = Math.abs(highs[i] - closes[i - 1]);
      const tr3 = Math.abs(lows[i] - closes[i - 1]);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }

    const recentTR = trueRanges.slice(-period);
    return recentTR.reduce((sum, tr) => sum + tr, 0) / recentTR.length;
  }

  private calculateSupportResistance(highs: number[], lows: number[], closes: number[]) {
    if (closes.length === 0) {
      return { support: 0, resistance: 0 };
    }

    const currentPrice = closes[closes.length - 1];
    const recentData = closes.slice(-50); // 最近50个数据点

    const support = Math.min(...recentData) * 0.98;
    const resistance = Math.max(...recentData) * 1.02;

    return { support, resistance };
  }

  private getDefaultTechnicalIndicators() {
    return {
      rsi14: 50,
      macdLine: 0,
      macdSignal: 0,
      macdHistogram: 0,
      bollingerUpper: 0,
      bollingerMiddle: 0,
      bollingerLower: 0,
      stochasticK: 50,
      stochasticD: 50,
      williamsR: -50,
      cci: 0,
      atr: 0,
      supportLevel: 0,
      resistanceLevel: 0
    };
  }

  private async createPriceData(symbolId: string, latestData: any, indicators: any) {
    // 计算24小时变化 (简化版本)
    const yesterdayData = await prisma.historicalData.findFirst({
      where: {
        symbolId,
        timeframe: '1d',
        timestamp: {
          lte: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      },
      orderBy: { timestamp: 'desc' }
    });

    const currentPrice = parseFloat(latestData.closePrice.toString());
    const yesterdayPrice = yesterdayData ? parseFloat(yesterdayData.closePrice.toString()) : currentPrice;
    const change24h = currentPrice - yesterdayPrice;
    const changePercent24h = (change24h / yesterdayPrice) * 100;

    return await prisma.priceData.create({
      data: {
        symbolId,
        price: currentPrice,
        change24h,
        changePercent24h,
        volume24h: parseFloat(latestData.volume.toString()),
        high24h: parseFloat(latestData.highPrice.toString()),
        low24h: parseFloat(latestData.lowPrice.toString()),
        marketCap: currentPrice * 19700000, // BTC流通量约1970万
        rsi14: indicators.rsi14,
        macdLine: indicators.macdLine,
        macdSignal: indicators.macdSignal,
        macdHistogram: indicators.macdHistogram,
        supportLevel: indicators.supportLevel,
        resistanceLevel: indicators.resistanceLevel,
        fearGreedIndex: 50, // 默认中性值
        socialSentiment: 0, // 默认中性值
        bollingerUpper: indicators.bollingerUpper,
        bollingerLower: indicators.bollingerLower,
        bollingerMiddle: indicators.bollingerMiddle,
        stochasticK: indicators.stochasticK,
        stochasticD: indicators.stochasticD,
        williamsR: indicators.williamsR,
        cci: indicators.cci,
        atr: indicators.atr,
        dataQuality: 1.0,
        dataFreshness: 1.0,
        timestamp: new Date()
      }
    });
  }

  private async verifyPriceData(symbolId: string) {
    const priceData = await prisma.priceData.findFirst({
      where: { symbolId },
      orderBy: { timestamp: 'desc' }
    });

    if (!priceData) {
      throw new Error('价格数据验证失败：未找到生成的数据');
    }

    console.log('📊 生成的价格数据验证:');
    console.log(`   价格: $${priceData.price}`);
    console.log(`   24h变化: ${priceData.changePercent24h.toFixed(2)}%`);
    console.log(`   RSI: ${priceData.rsi14?.toFixed(2) || 'N/A'}`);
    console.log(`   MACD: ${priceData.macdLine?.toFixed(2) || 'N/A'}`);
    console.log(`   时间: ${priceData.timestamp.toISOString()}`);
  }
}

// 运行价格数据生成
async function main() {
  const generator = new PriceDataGenerator();
  
  try {
    await generator.generatePriceData();
    console.log('\n✅ 价格数据生成成功！');
    console.log('💡 现在可以测试交易信号API了');
  } catch (error) {
    console.error('\n❌ 价格数据生成失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}
