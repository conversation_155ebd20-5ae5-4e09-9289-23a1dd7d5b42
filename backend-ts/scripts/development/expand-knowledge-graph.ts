import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';
import winston from 'winston';
import { KnowledgeInitializer } from '../src/contexts/ai-reasoning/infrastructure/knowledge/knowledge-initializer';

config();

const prisma = new PrismaClient();

// 创建简单的日志器
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.simple(),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * 扩展知识图谱脚本
 * 清理现有知识并重新初始化扩展的知识图谱
 */
class KnowledgeGraphExpander {
  private knowledgeInitializer: KnowledgeInitializer;

  constructor() {
    this.knowledgeInitializer = new KnowledgeInitializer(prisma, logger);
  }

  async expandKnowledgeGraph() {
    console.log('🧠 开始扩展知识图谱...');

    try {
      await prisma.$connect();
      console.log('✅ 数据库连接成功');

      // 1. 备份现有知识
      await this.backupExistingKnowledge();

      // 2. 清理现有知识图谱
      await this.cleanupExistingKnowledge();

      // 3. 重新初始化扩展的知识图谱
      await this.knowledgeInitializer.initializeKnowledgeGraph();

      // 4. 验证知识图谱
      await this.validateKnowledgeGraph();

      // 5. 生成知识图谱报告
      await this.generateKnowledgeReport();

      console.log('🎉 知识图谱扩展完成！');

    } catch (error) {
      console.error('❌ 知识图谱扩展失败:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  private async backupExistingKnowledge() {
    console.log('\n📦 备份现有知识...');

    const existingEntities = await prisma.knowledgeEntity.findMany({
      include: {
        searchIndices: true,
        sourceRelations: true,
        targetRelations: true
      }
    });

    const existingRelations = await prisma.knowledgeRelation.findMany();

    console.log(`📊 备份统计:`);
    console.log(`   实体数量: ${existingEntities.length}`);
    console.log(`   关系数量: ${existingRelations.length}`);

    // 可以选择将备份保存到文件
    // const backup = {
    //   entities: existingEntities,
    //   relations: existingRelations,
    //   timestamp: new Date().toISOString()
    // };
    // await fs.writeFile('knowledge-backup.json', JSON.stringify(backup, null, 2));

    console.log('✅ 知识备份完成');
  }

  private async cleanupExistingKnowledge() {
    console.log('\n🧹 清理现有知识图谱...');

    // 删除搜索索引
    const deletedIndices = await prisma.knowledgeSearchIndex.deleteMany({});
    console.log(`🗑️ 删除搜索索引: ${deletedIndices.count} 条`);

    // 删除关系
    const deletedRelations = await prisma.knowledgeRelation.deleteMany({});
    console.log(`🗑️ 删除知识关系: ${deletedRelations.count} 条`);

    // 删除实体
    const deletedEntities = await prisma.knowledgeEntity.deleteMany({});
    console.log(`🗑️ 删除知识实体: ${deletedEntities.count} 条`);

    console.log('✅ 知识图谱清理完成');
  }

  private async validateKnowledgeGraph() {
    console.log('\n🔍 验证知识图谱...');

    const entityCount = await prisma.knowledgeEntity.count();
    const relationCount = await prisma.knowledgeRelation.count();
    const indexCount = await prisma.knowledgeSearchIndex.count();

    console.log(`📊 验证结果:`);
    console.log(`   知识实体: ${entityCount} 个`);
    console.log(`   知识关系: ${relationCount} 个`);
    console.log(`   搜索索引: ${indexCount} 个`);

    // 验证各个领域的知识分布
    const domainStats = await prisma.knowledgeEntity.groupBy({
      by: ['domain'],
      _count: {
        id: true
      }
    });

    console.log(`\n📈 领域分布:`);
    for (const stat of domainStats) {
      console.log(`   ${stat.domain}: ${stat._count.id} 个实体`);
    }

    // 验证知识类型分布
    const typeStats = await prisma.knowledgeEntity.groupBy({
      by: ['type'],
      _count: {
        id: true
      }
    });

    console.log(`\n🏷️ 类型分布:`);
    for (const stat of typeStats) {
      console.log(`   ${stat.type}: ${stat._count.id} 个实体`);
    }

    if (entityCount >= 50) {
      console.log('✅ 知识图谱规模达标 (≥50个实体)');
    } else {
      console.log(`⚠️ 知识图谱规模不足 (${entityCount}/50个实体)`);
    }

    console.log('✅ 知识图谱验证完成');
  }

  private async generateKnowledgeReport() {
    console.log('\n📋 生成知识图谱报告...');

    const entities = await prisma.knowledgeEntity.findMany({
      orderBy: [
        { domain: 'asc' },
        { type: 'asc' },
        { name: 'asc' }
      ]
    });

    console.log('\n📚 知识图谱详细清单:');
    console.log('═'.repeat(80));

    let currentDomain = '';
    let currentType = '';

    for (const entity of entities) {
      if (entity.domain !== currentDomain) {
        currentDomain = entity.domain;
        console.log(`\n🏢 领域: ${currentDomain.toUpperCase()}`);
        console.log('─'.repeat(60));
      }

      if (entity.type !== currentType) {
        currentType = entity.type;
        console.log(`\n  📂 类型: ${currentType}`);
      }

      const confidence = (entity.confidence * 100).toFixed(0);
      console.log(`    • ${entity.name} (置信度: ${confidence}%)`);
      console.log(`      ${entity.description}`);
    }

    console.log('\n✅ 知识图谱报告生成完成');
  }
}

// 运行扩展脚本
async function main() {
  const expander = new KnowledgeGraphExpander();
  
  try {
    await expander.expandKnowledgeGraph();
    console.log('\n🎯 知识图谱扩展任务完成');
    console.log('💡 建议: 重启应用以加载新的知识图谱');
  } catch (error) {
    console.error('\n💥 扩展失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
