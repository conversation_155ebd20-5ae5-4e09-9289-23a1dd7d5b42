#!/usr/bin/env ts-node

/**
 * 查询可用模型脚本
 * 通过API查询OpenAI和Claude的可用模型
 */

import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../.env') });

interface ModelInfo {
  id: string;
  provider: string;
  created?: number;
  owned_by?: string;
  description?: string;
  capabilities?: string[];
  isThinking?: boolean;
}

class ModelQueryService {
  private openaiClient: OpenAI;
  private anthropicClient: Anthropic;

  constructor() {
    const openaiApiKey = process.env.OPENAI_API_KEY;
    const openaiBaseURL = process.env.OPENAI_BASE_URL || 'https://api.openai.com';
    const anthropicApiKey = process.env.ANTHROPIC_API_KEY;
    const anthropicBaseURL = process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com';

    console.log('🔧 API配置信息:');
    console.log(`   OpenAI Base URL: ${openaiBaseURL}`);
    console.log(`   Anthropic Base URL: ${anthropicBaseURL}`);
    console.log(`   OpenAI API Key: ${openaiApiKey ? `${openaiApiKey.substring(0, 10)}...` : '未设置'}`);
    console.log(`   Anthropic API Key: ${anthropicApiKey ? `${anthropicApiKey.substring(0, 10)}...` : '未设置'}`);
    console.log('');

    this.openaiClient = new OpenAI({
      apiKey: openaiApiKey,
      baseURL: openaiBaseURL,
    });

    this.anthropicClient = new Anthropic({
      apiKey: anthropicApiKey,
      baseURL: anthropicBaseURL,
    });
  }

  /**
   * 查询OpenAI可用模型
   */
  async queryOpenAIModels(): Promise<ModelInfo[]> {
    try {
      console.log('🔍 查询OpenAI可用模型...');
      
      const response = await this.openaiClient.models.list();
      const models: ModelInfo[] = response.data.map(model => ({
        id: model.id,
        provider: 'OpenAI',
        created: model.created,
        owned_by: model.owned_by,
        isThinking: model.id.includes('o1') || model.id.includes('reasoning')
      }));

      // 过滤和分类模型
      const gptModels = models.filter(m => 
        m.id.includes('gpt') || 
        m.id.includes('chatgpt') || 
        m.id.includes('o1')
      ).sort((a, b) => a.id.localeCompare(b.id));

      console.log(`✅ 找到 ${gptModels.length} 个OpenAI模型`);
      
      return gptModels;
    } catch (error) {
      console.error('❌ 查询OpenAI模型失败:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * 查询Claude可用模型（通过中转服务）
   */
  async queryClaudeModels(): Promise<ModelInfo[]> {
    try {
      console.log('🔍 查询Claude可用模型...');
      
      // 由于Anthropic API没有直接的模型列表端点，我们通过中转服务查询
      // 或者使用已知的Claude模型列表
      const knownClaudeModels = [
        'claude-3-5-sonnet-20241022',
        'claude-3-5-sonnet-20240620', 
        'claude-3-5-haiku-20241022',
        'claude-3-opus-20240229',
        'claude-3-sonnet-20240229',
        'claude-3-haiku-20240307'
      ];

      // 测试每个模型是否可用
      const availableModels: ModelInfo[] = [];
      
      for (const modelId of knownClaudeModels) {
        try {
          // 发送一个简单的测试请求
          const testResponse = await this.anthropicClient.messages.create({
            model: modelId,
            max_tokens: 10,
            messages: [
              {
                role: 'user',
                content: 'Hi'
              }
            ]
          });

          if (testResponse) {
            availableModels.push({
              id: modelId,
              provider: 'Anthropic',
              description: this.getClaudeModelDescription(modelId),
              isThinking: false // Claude模型目前都不是思考模型
            });
            console.log(`  ✅ ${modelId} - 可用`);
          }
        } catch (error) {
          console.log(`  ❌ ${modelId} - 不可用: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      console.log(`✅ 找到 ${availableModels.length} 个可用的Claude模型`);
      
      return availableModels;
    } catch (error) {
      console.error('❌ 查询Claude模型失败:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * 获取Claude模型描述
   */
  private getClaudeModelDescription(modelId: string): string {
    const descriptions: Record<string, string> = {
      'claude-3-5-sonnet-20241022': 'Claude 3.5 Sonnet (最新版) - 平衡性能和成本的强大模型',
      'claude-3-5-sonnet-20240620': 'Claude 3.5 Sonnet - 平衡性能和成本',
      'claude-3-5-haiku-20241022': 'Claude 3.5 Haiku (最新版) - 快速且经济的模型',
      'claude-3-opus-20240229': 'Claude 3 Opus - 最强大的推理能力',
      'claude-3-sonnet-20240229': 'Claude 3 Sonnet - 平衡性能',
      'claude-3-haiku-20240307': 'Claude 3 Haiku - 快速响应'
    };
    return descriptions[modelId] || '未知模型';
  }

  /**
   * 推荐最佳模型
   */
  recommendBestModels(openaiModels: ModelInfo[], claudeModels: ModelInfo[]): void {
    console.log('\n🎯 模型推荐分析:\n');

    // 过滤掉思考模型
    const nonThinkingOpenAI = openaiModels.filter(m => !m.isThinking);
    const nonThinkingClaude = claudeModels.filter(m => !m.isThinking);

    console.log('💡 强大且性价比高的推荐模型:\n');

    // OpenAI推荐
    console.log('🔵 OpenAI推荐:');
    const recommendedOpenAI = [
      'gpt-4-turbo',
      'gpt-4-turbo-2024-04-09',
      'gpt-4o',
      'gpt-4o-2024-11-20',
      'gpt-4o-mini'
    ];

    recommendedOpenAI.forEach(modelId => {
      const found = nonThinkingOpenAI.find(m => m.id === modelId);
      if (found) {
        console.log(`  ✅ ${modelId} - 可用`);
      } else {
        console.log(`  ❓ ${modelId} - 需要验证`);
      }
    });

    // Claude推荐
    console.log('\n🟣 Claude推荐:');
    const recommendedClaude = [
      'claude-3-5-sonnet-20241022',
      'claude-3-5-sonnet-20240620',
      'claude-3-opus-20240229'
    ];

    recommendedClaude.forEach(modelId => {
      const found = nonThinkingClaude.find(m => m.id === modelId);
      if (found) {
        console.log(`  ✅ ${modelId} - ${found.description}`);
      } else {
        console.log(`  ❓ ${modelId} - 需要验证`);
      }
    });

    console.log('\n🏆 最终推荐配置:');
    console.log('  主力模型: claude-3-5-sonnet-20241022 (最新Claude 3.5)');
    console.log('  备用模型: gpt-4o (OpenAI最新)');
    console.log('  经济模型: gpt-4o-mini (快速且便宜)');
    console.log('  高端模型: claude-3-opus-20240229 (最强推理)');
  }

  /**
   * 运行完整的模型查询
   */
  async runCompleteQuery(): Promise<void> {
    console.log('🚀 开始查询所有可用模型\n');

    const [openaiModels, claudeModels] = await Promise.all([
      this.queryOpenAIModels(),
      this.queryClaudeModels()
    ]);

    console.log('\n📊 查询结果汇总:');
    console.log(`  OpenAI模型: ${openaiModels.length}个`);
    console.log(`  Claude模型: ${claudeModels.length}个`);

    // 显示所有可用模型
    if (openaiModels.length > 0) {
      console.log('\n🔵 OpenAI可用模型:');
      openaiModels.forEach(model => {
        const thinkingFlag = model.isThinking ? ' [思考模型]' : '';
        console.log(`  - ${model.id}${thinkingFlag}`);
      });
    }

    if (claudeModels.length > 0) {
      console.log('\n🟣 Claude可用模型:');
      claudeModels.forEach(model => {
        console.log(`  - ${model.id} (${model.description})`);
      });
    }

    // 推荐最佳模型
    this.recommendBestModels(openaiModels, claudeModels);
  }
}

// 主函数
async function main() {
  try {
    const queryService = new ModelQueryService();
    await queryService.runCompleteQuery();
  } catch (error) {
    console.error('❌ 查询失败:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { ModelQueryService };
