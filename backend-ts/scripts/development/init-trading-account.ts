/**
 * 初始化默认交易账户脚本
 */

import { PrismaClient } from '@prisma/client';


/**
 * 交易账户初始化
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

const prisma = new PrismaClient();

async function initTradingAccount() {
  try {
    console.log('🚀 开始初始化交易账户...');

    // 检查是否已存在交易账户
    const existingAccounts = await prisma.tradingAccount.findMany();
    
    if (existingAccounts.length > 0) {
      console.log('✅ 交易账户已存在，跳过初始化');
      console.log(`现有账户数量: ${existingAccounts.length}`);
      existingAccounts.forEach(account => {
        console.log(`- ${account.name}: $${account.currentBalance}`);
      });
      return;
    }

    // 创建默认交易账户
    const defaultAccount = await prisma.tradingAccount.create({
      data: {
        name: '主交易账户',
        initialCapital: 100.00,
        currentBalance: 100.00,
        availableBalance: 100.00,
        totalPnl: 0.00,
        maxDrawdown: 0.00,
        dailyPnl: 0.00,
        isActive: true,
        riskSettings: {
          leverage: 10,
          maxPositions: 3,
          riskPerTrade: 0.01,
          maxDrawdownLimit: 0.15,
          dailyLossLimit: 0.05,
          fixedInvestment: 10
        }
      }
    });

    console.log('✅ 默认交易账户创建成功:');
    console.log(`账户ID: ${defaultAccount.id}`);
    console.log(`账户名称: ${defaultAccount.name}`);
    console.log(`初始资金: $${defaultAccount.initialCapital}`);
    console.log(`当前余额: $${defaultAccount.currentBalance}`);

    // 创建初始交易统计记录
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    await prisma.tradingStatistics.create({
      data: {
        accountId: defaultAccount.id,
        date: today,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        totalPnl: 0.00,
        grossProfit: 0.00,
        grossLoss: 0.00,
        maxDrawdown: 0.00,
        winRate: 0.00,
        profitFactor: 0.00,
        sharpeRatio: 0.00
      }
    });

    console.log('✅ 初始交易统计记录创建成功');
    console.log('🎉 交易账户初始化完成！');

  } catch (error) {
    console.error('❌ 交易账户初始化失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 执行初始化
initTradingAccount()
  .then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
