#!/usr/bin/env tsx

/**
 * 检查数据库实际结构
 */

import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';


/**
 * 数据库结构检查
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

config();

async function inspectDatabaseStructure() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 检查数据库实际结构...\n');

    // 查看AiCallLogs表结构
    console.log('📋 AiCallLogs表字段:');
    const aiCallLogsColumns = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'AiCallLogs' 
      ORDER BY ordinal_position
    `;
    console.log(aiCallLogsColumns);

    console.log('\n📋 TradingSignals表字段:');
    const tradingSignalsColumns = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'TradingSignals' 
      ORDER BY ordinal_position
    `;
    console.log(tradingSignalsColumns);

    console.log('\n📋 PriceData表字段:');
    const priceDataColumns = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'PriceData' 
      ORDER BY ordinal_position
    `;
    console.log(priceDataColumns);

    console.log('\n📋 所有表名:');
    const allTables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    console.log(allTables);

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

inspectDatabaseStructure();
