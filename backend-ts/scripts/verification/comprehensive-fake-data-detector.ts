#!/usr/bin/env tsx

/**
 * 全面的虚假数据和实现检测器
 * 系统性地扫描项目中的所有虚假实现、硬编码数据、模拟数据等问题
 */

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { PrismaClient } from '@prisma/client';

interface FakeImplementationIssue {
  file: string;
  line: number;
  type: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  category: string;
  description: string;
  codeSnippet: string;
  recommendation: string;
  actualValue?: any;
  expectedSource?: string;
}

class ComprehensiveFakeDataDetector {
  private readonly prisma = new PrismaClient();
  private issues: FakeImplementationIssue[] = [];
  private readonly srcPath = path.join(process.cwd(), 'src');

  // 常见的虚假价格数据
  private readonly FAKE_PRICES = [
    50000, 45000, 46000, 44000, 43000, 42000, 48000, 52000, 55000,
    100000, 60000, 65000, 70000, 75000, 80000, 85000, 90000, 95000,
    3000, 2500, 3500, 4000, // ETH价格
    400, 300, 500, 600, // BNB价格
    1.5, 1.0, 2.0, // ADA价格
    100, 80, 120, 150 // SOL价格
  ];

  // 虚假实现关键词
  private readonly FAKE_KEYWORDS = [
    '模拟实现', '临时实现', '简化实现', '测试实现', '占位符实现',
    '模拟AI', '模拟交易', '模拟数据', '模拟执行', '虚假实现',
    'mock', 'fake', 'dummy', 'simulate', 'placeholder',
    'Math.random()', '默认价格', 'default price', 'hardcoded'
  ];

  async runComprehensiveDetection(): Promise<void> {
    console.log('🔍 开始全面的虚假数据和实现检测...');
    console.log('='.repeat(80));

    try {
      // 1. 检测硬编码价格数据
      await this.detectHardcodedPrices();
      
      // 2. 检测虚假实现关键词
      await this.detectFakeImplementationKeywords();
      
      // 3. 检测Math.random()滥用
      await this.detectMathRandomAbuse();
      
      // 4. 检测数据库中的虚假数据
      await this.detectFakeDataInDatabase();
      
      // 5. 检测总是返回固定值的方法
      await this.detectFixedReturnMethods();
      
      // 6. 检测测试数据泄露
      await this.detectTestDataLeakage();
      
      // 7. 检测外部API连接问题
      await this.detectExternalApiIssues();
      
      // 8. 生成报告
      this.generateComprehensiveReport();
      
    } catch (error) {
      console.error('❌ 检测过程中发生错误:', error);
    } finally {
      await this.cleanup();
    }
  }

  private async detectHardcodedPrices(): Promise<void> {
    console.log('\n1️⃣ 检测硬编码价格数据...');
    
    const pricePatterns = this.FAKE_PRICES.map(price => 
      `\\b${price}\\b(?!.*test|.*spec|.*mock)`
    );

    for (const pattern of pricePatterns) {
      try {
        const result = execSync(
          `grep -rn "${pattern}" ${this.srcPath} --include="*.ts" --exclude-dir=__tests__ --exclude-dir=tests`,
          { encoding: 'utf8' }
        );

        const lines = result.trim().split('\n');
        for (const line of lines) {
          if (line.trim() && !this.isTestFile(line)) {
            const [filePath, lineNum, code] = line.split(':');
            const price = this.FAKE_PRICES.find(p => code.includes(p.toString()));
            
            this.issues.push({
              file: path.relative(process.cwd(), filePath),
              line: parseInt(lineNum),
              type: 'CRITICAL',
              category: '💰 硬编码价格数据',
              description: `发现硬编码价格: ${price}`,
              codeSnippet: code.trim(),
              recommendation: '使用真实的市场数据API获取当前价格',
              actualValue: price,
              expectedSource: '外部市场数据API'
            });
          }
        }
      } catch (error) {
        // 没有找到匹配项
      }
    }
  }

  private async detectFakeImplementationKeywords(): Promise<void> {
    console.log('\n2️⃣ 检测虚假实现关键词...');
    
    for (const keyword of this.FAKE_KEYWORDS) {
      try {
        const result = execSync(
          `grep -rn "${keyword}" ${this.srcPath} --include="*.ts" --exclude-dir=__tests__ --exclude-dir=tests`,
          { encoding: 'utf8' }
        );

        const lines = result.trim().split('\n');
        for (const line of lines) {
          if (line.trim() && !this.isTestFile(line)) {
            const [filePath, lineNum, code] = line.split(':');
            
            // 跳过正当的错误消息
            if (code.includes('throw new Error') && code.includes('拒绝')) {
              continue;
            }
            
            this.issues.push({
              file: path.relative(process.cwd(), filePath),
              line: parseInt(lineNum),
              type: 'HIGH',
              category: '🚨 虚假实现标记',
              description: `发现虚假实现关键词: ${keyword}`,
              codeSnippet: code.trim(),
              recommendation: '立即实现真实逻辑，移除虚假标记'
            });
          }
        }
      } catch (error) {
        // 没有找到匹配项
      }
    }
  }

  private async detectMathRandomAbuse(): Promise<void> {
    console.log('\n3️⃣ 检测Math.random()滥用...');
    
    const randomPatterns = [
      'Math\\.random\\(\\).*price',
      'Math\\.random\\(\\).*volume',
      'Math\\.random\\(\\).*confidence',
      'Math\\.random\\(\\).*strength',
      'price.*Math\\.random\\(\\)',
      'volume.*Math\\.random\\(\\)'
    ];

    for (const pattern of randomPatterns) {
      try {
        const result = execSync(
          `grep -rn "${pattern}" ${this.srcPath} --include="*.ts" --exclude-dir=__tests__ --exclude-dir=tests`,
          { encoding: 'utf8' }
        );

        const lines = result.trim().split('\n');
        for (const line of lines) {
          if (line.trim() && !this.isTestFile(line)) {
            const [filePath, lineNum, code] = line.split(':');
            
            this.issues.push({
              file: path.relative(process.cwd(), filePath),
              line: parseInt(lineNum),
              type: 'CRITICAL',
              category: '🎲 Math.random()滥用',
              description: '使用随机数生成业务数据',
              codeSnippet: code.trim(),
              recommendation: '使用真实的数据源替换随机数生成'
            });
          }
        }
      } catch (error) {
        // 没有找到匹配项
      }
    }
  }

  private async detectFakeDataInDatabase(): Promise<void> {
    console.log('\n4️⃣ 检测数据库中的虚假数据...');
    
    try {
      // 检查价格数据表中的可疑数据
      const suspiciousPrices = await this.prisma.priceData.findMany({
        where: {
          OR: [
            { price: { in: this.FAKE_PRICES } },
            { price: { lt: 1000 } }, // 过低的价格
            { price: { gt: 200000 } }, // 过高的价格
            { changePercent24h: { gt: 100 } }, // 不现实的变化
            { changePercent24h: { lt: -50 } } // 不现实的下跌
          ]
        },
        include: { Symbols: true },
        take: 10
      });

      for (const priceData of suspiciousPrices) {
        this.issues.push({
          file: 'database/priceData',
          line: 0,
          type: 'HIGH',
          category: '🗄️ 数据库虚假数据',
          description: `可疑的价格数据: ${priceData.price}`,
          codeSnippet: `Symbol: ${priceData.Symbols?.symbol}, Price: ${priceData.price}`,
          recommendation: '清理数据库中的虚假价格数据，使用真实市场数据',
          actualValue: priceData.price
        });
      }

      // 检查历史数据中的模式
      const historicalDataCount = await this.prisma.historicalData.count();
      console.log(`   📊 历史数据总数: ${historicalDataCount.toLocaleString()}`);

      if (historicalDataCount === 0) {
        this.issues.push({
          file: 'database/historicalData',
          line: 0,
          type: 'CRITICAL',
          category: '🗄️ 缺少历史数据',
          description: '数据库中没有历史数据',
          codeSnippet: 'historicalData table is empty',
          recommendation: '导入真实的历史市场数据'
        });
      }

    } catch (error) {
      console.error('   ❌ 数据库检查失败:', error);
    }
  }

  private async detectFixedReturnMethods(): Promise<void> {
    console.log('\n5️⃣ 检测总是返回固定值的方法...');
    
    const fixedReturnPatterns = [
      'return 50000;',
      'return 45000;',
      'return true; *\\/\\/ 虚假',
      'return \\[\\]; *\\/\\/ 模拟',
      'return null; *\\/\\/ 临时'
    ];

    for (const pattern of fixedReturnPatterns) {
      try {
        const result = execSync(
          `grep -rn "${pattern}" ${this.srcPath} --include="*.ts" --exclude-dir=__tests__ --exclude-dir=tests`,
          { encoding: 'utf8' }
        );

        const lines = result.trim().split('\n');
        for (const line of lines) {
          if (line.trim() && !this.isTestFile(line)) {
            const [filePath, lineNum, code] = line.split(':');
            
            this.issues.push({
              file: path.relative(process.cwd(), filePath),
              line: parseInt(lineNum),
              type: 'HIGH',
              category: '🔒 固定返回值',
              description: '方法总是返回固定值',
              codeSnippet: code.trim(),
              recommendation: '实现动态的业务逻辑'
            });
          }
        }
      } catch (error) {
        // 没有找到匹配项
      }
    }
  }

  private async detectTestDataLeakage(): Promise<void> {
    console.log('\n6️⃣ 检测测试数据泄露...');
    
    const testDataPatterns = [
      'testData',
      'mockData',
      'sampleData',
      'dummyData',
      'fakeData'
    ];

    for (const pattern of testDataPatterns) {
      try {
        const result = execSync(
          `grep -rn "${pattern}" ${this.srcPath} --include="*.ts" --exclude-dir=__tests__ --exclude-dir=tests`,
          { encoding: 'utf8' }
        );

        const lines = result.trim().split('\n');
        for (const line of lines) {
          if (line.trim() && !this.isTestFile(line)) {
            const [filePath, lineNum, code] = line.split(':');
            
            this.issues.push({
              file: path.relative(process.cwd(), filePath),
              line: parseInt(lineNum),
              type: 'MEDIUM',
              category: '🧪 测试数据泄露',
              description: `发现测试数据标识: ${pattern}`,
              codeSnippet: code.trim(),
              recommendation: '移除测试数据，使用真实数据源'
            });
          }
        }
      } catch (error) {
        // 没有找到匹配项
      }
    }
  }

  private async detectExternalApiIssues(): Promise<void> {
    console.log('\n7️⃣ 检测外部API连接问题...');
    
    // 这里可以添加对外部API连接的检查
    // 暂时跳过，因为需要实际的网络请求
    console.log('   ⏭️  外部API检查已跳过（需要网络连接）');
  }

  private generateComprehensiveReport(): void {
    console.log('\n📊 虚假数据和实现检测报告');
    console.log('='.repeat(80));

    const criticalIssues = this.issues.filter(i => i.type === 'CRITICAL');
    const highIssues = this.issues.filter(i => i.type === 'HIGH');
    const mediumIssues = this.issues.filter(i => i.type === 'MEDIUM');
    const lowIssues = this.issues.filter(i => i.type === 'LOW');

    console.log(`总问题数: ${this.issues.length}`);
    console.log(`🔴 严重问题: ${criticalIssues.length}`);
    console.log(`🟠 高优先级: ${highIssues.length}`);
    console.log(`🟡 中优先级: ${mediumIssues.length}`);
    console.log(`🟢 低优先级: ${lowIssues.length}`);

    // 按类别统计
    const categoryStats = this.issues.reduce((acc, issue) => {
      acc[issue.category] = (acc[issue.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log('\n📋 问题分类统计:');
    Object.entries(categoryStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([category, count]) => {
        console.log(`  ${category}: ${count}个`);
      });

    // 显示最严重的问题
    if (criticalIssues.length > 0) {
      console.log('\n🔴 严重问题详情:');
      criticalIssues.slice(0, 10).forEach((issue, index) => {
        console.log(`\n${index + 1}. ${issue.category}`);
        console.log(`   文件: ${issue.file}:${issue.line}`);
        console.log(`   描述: ${issue.description}`);
        console.log(`   代码: ${issue.codeSnippet}`);
        console.log(`   建议: ${issue.recommendation}`);
      });
    }

    // 保存详细报告
    this.saveDetailedReport();
  }

  private saveDetailedReport(): void {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.issues.length,
        critical: this.issues.filter(i => i.type === 'CRITICAL').length,
        high: this.issues.filter(i => i.type === 'HIGH').length,
        medium: this.issues.filter(i => i.type === 'MEDIUM').length,
        low: this.issues.filter(i => i.type === 'LOW').length
      },
      issues: this.issues
    };

    const reportsDir = path.join(process.cwd(), 'verification-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const reportPath = path.join(reportsDir, `comprehensive-fake-data-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 详细报告已保存: ${reportPath}`);
  }

  private isTestFile(line: string): boolean {
    return line.includes('test') ||
           line.includes('spec') ||
           line.includes('__tests__') ||
           line.includes('.test.') ||
           line.includes('.spec.') ||
           line.includes('mock');
  }

  private async cleanup(): Promise<void> {
    await this.prisma.$disconnect();
  }
}

// 运行检测
async function main() {
  const detector = new ComprehensiveFakeDataDetector();
  await detector.runComprehensiveDetection();
}

if (require.main === module) {
  main().catch(console.error);
}

export { ComprehensiveFakeDataDetector };
