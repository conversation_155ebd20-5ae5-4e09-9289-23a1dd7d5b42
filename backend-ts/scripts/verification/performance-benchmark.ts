#!/usr/bin/env tsx
/**
 * 性能基准测试工具
 * 用于建立和验证系统性能基准
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../src/shared/infrastructure/di/types';
import { IBasicLogger } from '../../src/shared/infrastructure/logging/interfaces/basic-logger.interface';
import { performance } from 'perf_hooks';
import fs from 'fs/promises';
import path from 'path';

interface BenchmarkConfig {
  name: string;
  description: string;
  iterations: number;
  warmupIterations: number;
  concurrency: number;
  timeout: number;
  endpoint?: string;
  payload?: any;
}

interface BenchmarkMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  medianResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  throughput: number; // requests per second
  errorRate: number;
  memoryUsage: {
    before: NodeJS.MemoryUsage;
    after: NodeJS.MemoryUsage;
    peak: NodeJS.MemoryUsage;
  };
  cpuUsage: {
    before: NodeJS.CpuUsage;
    after: NodeJS.CpuUsage;
  };
}

interface BenchmarkResult {
  config: BenchmarkConfig;
  metrics: BenchmarkMetrics;
  timestamp: Date;
  duration: number;
  passed: boolean;
  issues: string[];
  recommendations: string[];
}

@injectable()
export class PerformanceBenchmark {
  private readonly reportsDir = path.join(process.cwd(), 'verification-reports', 'performance');

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger
  ) {}

  /**
   * 运行完整的性能基准测试套件
   */
  async runBenchmarkSuite(): Promise<BenchmarkResult[]> {
    this.logger.info('🚀 开始性能基准测试套件...');

    try {
      await fs.mkdir(this.reportsDir, { recursive: true });

      const benchmarkConfigs = this.getBenchmarkConfigurations();
      const results: BenchmarkResult[] = [];

      for (const config of benchmarkConfigs) {
        this.logger.info(`📊 执行性能测试: ${config.name}`, { config });
        
        const result = await this.runBenchmark(config);
        results.push(result);

        // 测试间隔，让系统稳定
        await this.sleep(5000);
      }

      // 生成综合报告
      await this.generateSuiteReport(results);

      this.logger.info('✅ 性能基准测试套件完成', { 
        totalTests: results.length,
        passedTests: results.filter(r => r.passed).length
      });

      return results;
    } catch (error) {
      this.logger.error('❌ 性能基准测试套件失败', { error });
      throw error;
    }
  }

  /**
   * 获取基准测试配置
   */
  private getBenchmarkConfigurations(): BenchmarkConfig[] {
    return [
      {
        name: '市场数据获取性能测试',
        description: '测试获取市场数据的响应时间和吞吐量',
        iterations: 100,
        warmupIterations: 10,
        concurrency: 10,
        timeout: 5000,
        endpoint: '/api/market-data/BTCUSDT'
      },
      {
        name: '风险评估性能测试',
        description: '测试风险评估计算的性能',
        iterations: 50,
        warmupIterations: 5,
        concurrency: 5,
        timeout: 10000,
        endpoint: '/api/risk/assess',
        payload: {
          symbol: 'BTCUSDT',
          quantity: 1.0,
          side: 'BUY'
        }
      },
      {
        name: '交易信号生成性能测试',
        description: '测试交易信号生成的性能',
        iterations: 30,
        warmupIterations: 3,
        concurrency: 3,
        timeout: 15000,
        endpoint: '/api/trading-signals/generate',
        payload: {
          symbol: 'BTCUSDT',
          timeframe: '1h'
        }
      },
      {
        name: '健康检查性能测试',
        description: '测试系统健康检查的响应性能',
        iterations: 200,
        warmupIterations: 20,
        concurrency: 20,
        timeout: 2000,
        endpoint: '/health'
      },
      {
        name: '缓存性能测试',
        description: '测试缓存系统的读写性能',
        iterations: 500,
        warmupIterations: 50,
        concurrency: 50,
        timeout: 1000,
        endpoint: '/api/cache/test'
      }
    ];
  }

  /**
   * 运行单个基准测试
   */
  private async runBenchmark(config: BenchmarkConfig): Promise<BenchmarkResult> {
    const startTime = Date.now();
    
    try {
      // 预热
      await this.warmup(config);

      // 记录初始状态
      const memoryBefore = process.memoryUsage();
      const cpuBefore = process.cpuUsage();

      // 执行基准测试
      const responseTimes = await this.executeBenchmark(config);

      // 记录结束状态
      const memoryAfter = process.memoryUsage();
      const cpuAfter = process.cpuUsage(cpuBefore);

      // 计算指标
      const metrics = this.calculateMetrics(responseTimes, memoryBefore, memoryAfter, cpuBefore, cpuAfter);

      // 验证结果
      const { passed, issues, recommendations } = this.validateBenchmarkResult(config, metrics);

      const result: BenchmarkResult = {
        config,
        metrics,
        timestamp: new Date(),
        duration: Date.now() - startTime,
        passed,
        issues,
        recommendations
      };

      // 保存结果
      await this.saveBenchmarkResult(result);

      return result;
    } catch (error) {
      this.logger.error('基准测试执行失败', { config: config.name, error });
      throw error;
    }
  }

  /**
   * 预热测试
   */
  private async warmup(config: BenchmarkConfig): Promise<void> {
    this.logger.debug('🔥 预热测试', { iterations: config.warmupIterations });

    for (let i = 0; i < config.warmupIterations; i++) {
      try {
        await this.executeRequest(config);
      } catch (error) {
        // 预热期间忽略错误
      }
    }

    // 预热后等待系统稳定
    await this.sleep(1000);
  }

  /**
   * 执行基准测试
   */
  private async executeBenchmark(config: BenchmarkConfig): Promise<number[]> {
    const responseTimes: number[] = [];
    const concurrentBatches = Math.ceil(config.iterations / config.concurrency);

    for (let batch = 0; batch < concurrentBatches; batch++) {
      const batchSize = Math.min(config.concurrency, config.iterations - batch * config.concurrency);
      
      const batchPromises = Array.from({ length: batchSize }, async () => {
        const startTime = performance.now();
        try {
          await this.executeRequest(config);
          return performance.now() - startTime;
        } catch (error) {
          // 返回负值表示失败
          return -1;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      responseTimes.push(...batchResults);

      // 批次间隔
      if (batch < concurrentBatches - 1) {
        await this.sleep(100);
      }
    }

    return responseTimes;
  }

  /**
   * 执行单个请求
   */
  private async executeRequest(config: BenchmarkConfig): Promise<any> {
    // 这里应该执行真实的HTTP请求
    // 暂时模拟请求执行
    const delay = Math.random() * 200 + 50; // 50-250ms
    await this.sleep(delay);
    
    // 模拟偶尔的失败
    if (Math.random() < 0.05) { // 5% 失败率
      throw new Error('模拟请求失败');
    }

    return { status: 'success', data: {} };
  }

  /**
   * 计算性能指标
   */
  private calculateMetrics(
    responseTimes: number[],
    memoryBefore: NodeJS.MemoryUsage,
    memoryAfter: NodeJS.MemoryUsage,
    cpuBefore: NodeJS.CpuUsage,
    cpuAfter: NodeJS.CpuUsage
  ): BenchmarkMetrics {
    const successfulTimes = responseTimes.filter(t => t > 0);
    const failedRequests = responseTimes.filter(t => t < 0).length;
    
    successfulTimes.sort((a, b) => a - b);

    const totalRequests = responseTimes.length;
    const successfulRequests = successfulTimes.length;
    
    const averageResponseTime = successfulTimes.length > 0 
      ? successfulTimes.reduce((a, b) => a + b, 0) / successfulTimes.length 
      : 0;

    const medianResponseTime = successfulTimes.length > 0
      ? this.percentile(successfulTimes, 50)
      : 0;

    const p95ResponseTime = successfulTimes.length > 0
      ? this.percentile(successfulTimes, 95)
      : 0;

    const p99ResponseTime = successfulTimes.length > 0
      ? this.percentile(successfulTimes, 99)
      : 0;

    const minResponseTime = successfulTimes.length > 0 ? Math.min(...successfulTimes) : 0;
    const maxResponseTime = successfulTimes.length > 0 ? Math.max(...successfulTimes) : 0;

    // 计算吞吐量（每秒请求数）
    const totalTimeSeconds = successfulTimes.reduce((a, b) => a + b, 0) / 1000;
    const throughput = totalTimeSeconds > 0 ? successfulRequests / totalTimeSeconds : 0;

    const errorRate = totalRequests > 0 ? failedRequests / totalRequests : 0;

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime,
      medianResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      minResponseTime,
      maxResponseTime,
      throughput,
      errorRate,
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        peak: memoryAfter // 简化实现，实际应该监控峰值
      },
      cpuUsage: {
        before: cpuBefore,
        after: cpuAfter
      }
    };
  }

  /**
   * 计算百分位数
   */
  private percentile(arr: number[], p: number): number {
    const index = Math.ceil((p / 100) * arr.length) - 1;
    return arr[Math.max(0, index)];
  }

  /**
   * 验证基准测试结果
   */
  private validateBenchmarkResult(
    config: BenchmarkConfig,
    metrics: BenchmarkMetrics
  ): { passed: boolean; issues: string[]; recommendations: string[] } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查错误率
    if (metrics.errorRate > 0.05) { // 5%
      issues.push(`错误率过高: ${(metrics.errorRate * 100).toFixed(2)}%`);
      recommendations.push('需要改进错误处理和系统稳定性');
    }

    // 检查响应时间
    if (metrics.averageResponseTime > config.timeout * 0.5) {
      issues.push(`平均响应时间过长: ${metrics.averageResponseTime.toFixed(0)}ms`);
      recommendations.push('需要优化性能或调整超时设置');
    }

    // 检查P95响应时间
    if (metrics.p95ResponseTime > config.timeout * 0.8) {
      issues.push(`P95响应时间过长: ${metrics.p95ResponseTime.toFixed(0)}ms`);
      recommendations.push('需要优化长尾延迟');
    }

    // 检查吞吐量
    const expectedThroughput = config.concurrency * 0.8; // 期望达到并发数的80%
    if (metrics.throughput < expectedThroughput) {
      issues.push(`吞吐量低于预期: ${metrics.throughput.toFixed(1)} req/s`);
      recommendations.push('需要优化系统吞吐量');
    }

    // 检查内存使用
    const memoryIncrease = metrics.memoryUsage.after.heapUsed - metrics.memoryUsage.before.heapUsed;
    if (memoryIncrease > 100 * 1024 * 1024) { // 100MB
      issues.push(`内存使用增长过多: ${(memoryIncrease / 1024 / 1024).toFixed(1)}MB`);
      recommendations.push('检查是否存在内存泄漏');
    }

    const passed = issues.length === 0;

    return { passed, issues, recommendations };
  }

  /**
   * 保存基准测试结果
   */
  private async saveBenchmarkResult(result: BenchmarkResult): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `benchmark-${result.config.name.replace(/\s+/g, '-')}-${timestamp}.json`;
    const filepath = path.join(this.reportsDir, filename);

    await fs.writeFile(filepath, JSON.stringify(result, null, 2));
    this.logger.info('📄 基准测试结果已保存', { filepath });
  }

  /**
   * 生成测试套件报告
   */
  private async generateSuiteReport(results: BenchmarkResult[]): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `benchmark-suite-${timestamp}.json`;
    const filepath = path.join(this.reportsDir, filename);

    const suiteReport = {
      timestamp: new Date(),
      totalTests: results.length,
      passedTests: results.filter(r => r.passed).length,
      failedTests: results.filter(r => !r.passed).length,
      summary: {
        averageResponseTime: results.reduce((sum, r) => sum + r.metrics.averageResponseTime, 0) / results.length,
        averageThroughput: results.reduce((sum, r) => sum + r.metrics.throughput, 0) / results.length,
        averageErrorRate: results.reduce((sum, r) => sum + r.metrics.errorRate, 0) / results.length
      },
      results
    };

    await fs.writeFile(filepath, JSON.stringify(suiteReport, null, 2));
    this.logger.info('📊 基准测试套件报告已生成', { filepath });
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export { PerformanceBenchmark, BenchmarkConfig, BenchmarkResult, BenchmarkMetrics };
