#!/usr/bin/env tsx
/**
 * 第一阶段全局验证报告生成器
 * 汇总所有测试结果，生成综合验证报告
 */

import { getLogger } from '../../src/shared/infrastructure/logging/logger-factory';
import { ILogger } from '../../src/shared/infrastructure/logging/logger.interface';
import * as fs from 'fs/promises';
import * as path from 'path';

interface TestSummary {
  testName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  successRate: number;
  status: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
  reportPath?: string;
}

interface GlobalVerificationReport {
  timestamp: Date;
  phase: string;
  overallStatus: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'CRITICAL';
  totalTests: number;
  totalPassed: number;
  totalFailed: number;
  overallSuccessRate: number;
  testSummaries: TestSummary[];
  recommendations: string[];
  criticalIssues: string[];
  nextSteps: string[];
}

class GlobalVerificationReporter {
  private readonly logger: ILogger;
  private readonly reportsDir: string;

  constructor() {
    this.logger = getLogger('global-verification-reporter');
    this.reportsDir = path.join(process.cwd(), 'verification-reports');
  }

  /**
   * 生成全局验证报告
   */
  async generateGlobalReport(): Promise<GlobalVerificationReport> {
    this.logger.info('📊 开始生成第一阶段全局验证报告...');

    try {
      // 收集所有测试结果
      const testSummaries = await this.collectTestSummaries();

      // 计算总体指标
      const totalTests = testSummaries.reduce((sum, test) => sum + test.totalTests, 0);
      const totalPassed = testSummaries.reduce((sum, test) => sum + test.passedTests, 0);
      const totalFailed = testSummaries.reduce((sum, test) => sum + test.failedTests, 0);
      const overallSuccessRate = totalTests > 0 ? totalPassed / totalTests : 0;

      // 确定总体状态
      const overallStatus = this.determineOverallStatus(overallSuccessRate, testSummaries);

      // 生成建议和下一步
      const recommendations = this.generateRecommendations(testSummaries);
      const criticalIssues = this.identifyCriticalIssues(testSummaries);
      const nextSteps = this.generateNextSteps(overallStatus, testSummaries);

      const report: GlobalVerificationReport = {
        timestamp: new Date(),
        phase: '第一阶段 1.1-1.3',
        overallStatus,
        totalTests,
        totalPassed,
        totalFailed,
        overallSuccessRate,
        testSummaries,
        recommendations,
        criticalIssues,
        nextSteps
      };

      // 保存报告
      await this.saveReport(report);

      // 输出报告摘要
      this.outputReportSummary(report);

      return report;

    } catch (error) {
      this.logger.error('生成全局验证报告失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 收集所有测试结果摘要
   */
  private async collectTestSummaries(): Promise<TestSummary[]> {
    const summaries: TestSummary[] = [];

    // 定义测试配置
    const testConfigs = [
      { name: '基础验证测试', expectedRate: 1.0, dir: '', pattern: 'simple-verification-test-*.json' },
      { name: '故障转移机制测试', expectedRate: 0.8, dir: 'failover-tests', pattern: 'failover-test-*.json' },
      { name: '缓存一致性测试', expectedRate: 0.7, dir: 'cache-tests', pattern: 'cache-consistency-test-*.json' },
      { name: '数据质量监控测试', expectedRate: 0.9, dir: 'data-quality-tests', pattern: 'data-quality-test-*.json' },
      { name: '风险强制执行测试', expectedRate: 0.9, dir: 'risk-enforcement-tests', pattern: 'risk-enforcement-test-*.json' },
      { name: '实时风险监控测试', expectedRate: 0.7, dir: 'real-time-monitoring-tests', pattern: 'real-time-monitoring-test-*.json' },
      { name: '订单状态管理测试', expectedRate: 0.6, dir: 'order-status-tests', pattern: 'order-status-test-*.json' },
      { name: '交易执行监控测试', expectedRate: 0.8, dir: 'execution-monitoring-tests', pattern: 'execution-monitoring-test-*.json' }
    ];

    for (const config of testConfigs) {
      try {
        const summary = await this.getLatestTestSummary(config.name, config.dir, config.pattern);
        if (summary) {
          summary.status = this.determineTestStatus(summary.successRate, config.expectedRate);
          summaries.push(summary);
        }
      } catch (error) {
        this.logger.warn(`无法获取测试摘要: ${config.name}`, {
          error: error instanceof Error ? error.message : String(error)
        });
        
        // 添加默认摘要表示测试未运行
        summaries.push({
          testName: config.name,
          totalTests: 0,
          passedTests: 0,
          failedTests: 0,
          successRate: 0,
          status: 'POOR'
        });
      }
    }

    return summaries;
  }

  /**
   * 获取最新的测试摘要
   */
  private async getLatestTestSummary(testName: string, subDir: string, pattern: string): Promise<TestSummary | null> {
    const testDir = subDir ? path.join(this.reportsDir, subDir) : this.reportsDir;
    
    try {
      const files = await fs.readdir(testDir);
      const reportFiles = files.filter(file => file.match(pattern.replace('*', '.*')));
      
      if (reportFiles.length === 0) {
        return null;
      }

      // 获取最新的报告文件
      const latestFile = reportFiles.sort().pop()!;
      const reportPath = path.join(testDir, latestFile);
      
      const reportContent = await fs.readFile(reportPath, 'utf-8');
      const report = JSON.parse(reportContent);

      return {
        testName,
        totalTests: report.totalTests || 0,
        passedTests: report.passedTests || 0,
        failedTests: report.failedTests || 0,
        successRate: report.totalTests > 0 ? (report.passedTests / report.totalTests) : 0,
        status: 'FAIR', // 将在后面设置
        reportPath
      };
    } catch (error) {
      this.logger.warn(`读取测试报告失败: ${testName}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 确定测试状态
   */
  private determineTestStatus(actualRate: number, expectedRate: number): 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' {
    if (actualRate >= expectedRate) {
      return 'EXCELLENT';
    } else if (actualRate >= expectedRate * 0.8) {
      return 'GOOD';
    } else if (actualRate >= expectedRate * 0.6) {
      return 'FAIR';
    } else {
      return 'POOR';
    }
  }

  /**
   * 确定总体状态
   */
  private determineOverallStatus(overallSuccessRate: number, testSummaries: TestSummary[]): 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'CRITICAL' {
    const criticalTests = testSummaries.filter(test => test.status === 'POOR').length;
    const excellentTests = testSummaries.filter(test => test.status === 'EXCELLENT').length;
    const goodTests = testSummaries.filter(test => test.status === 'GOOD').length;

    if (criticalTests > 3) {
      return 'CRITICAL';
    } else if (overallSuccessRate >= 0.85 && excellentTests >= 4) {
      return 'EXCELLENT';
    } else if (overallSuccessRate >= 0.75 && (excellentTests + goodTests) >= 5) {
      return 'GOOD';
    } else if (overallSuccessRate >= 0.6) {
      return 'FAIR';
    } else {
      return 'POOR';
    }
  }

  /**
   * 生成建议
   */
  private generateRecommendations(testSummaries: TestSummary[]): string[] {
    const recommendations: string[] = [];

    const poorTests = testSummaries.filter(test => test.status === 'POOR');
    const fairTests = testSummaries.filter(test => test.status === 'FAIR');

    if (poorTests.length > 0) {
      recommendations.push(`优先修复状态为POOR的测试: ${poorTests.map(t => t.testName).join(', ')}`);
    }

    if (fairTests.length > 0) {
      recommendations.push(`改进状态为FAIR的测试: ${fairTests.map(t => t.testName).join(', ')}`);
    }

    // 特定建议
    const cacheTest = testSummaries.find(t => t.testName.includes('缓存一致性'));
    if (cacheTest && cacheTest.status === 'POOR') {
      recommendations.push('缓存一致性问题需要重点关注，建议检查缓存同步机制');
    }

    const orderTest = testSummaries.find(t => t.testName.includes('订单状态'));
    if (orderTest && orderTest.status === 'POOR') {
      recommendations.push('订单状态管理需要完善，建议加强状态同步和验证逻辑');
    }

    const failoverTest = testSummaries.find(t => t.testName.includes('故障转移'));
    if (failoverTest && failoverTest.status === 'POOR') {
      recommendations.push('故障转移机制需要优化，建议改进断路器和恢复逻辑');
    }

    if (recommendations.length === 0) {
      recommendations.push('系统整体状态良好，建议继续保持并进入下一阶段开发');
    }

    return recommendations;
  }

  /**
   * 识别关键问题
   */
  private identifyCriticalIssues(testSummaries: TestSummary[]): string[] {
    const criticalIssues: string[] = [];

    testSummaries.forEach(test => {
      if (test.status === 'POOR' && test.successRate < 0.3) {
        criticalIssues.push(`${test.testName}: 成功率过低 (${(test.successRate * 100).toFixed(1)}%)`);
      }
    });

    // 检查核心功能
    const coreTests = ['风险强制执行测试', '数据质量监控测试'];
    coreTests.forEach(testName => {
      const test = testSummaries.find(t => t.testName === testName);
      if (test && test.status === 'POOR') {
        criticalIssues.push(`核心功能异常: ${testName}`);
      }
    });

    return criticalIssues;
  }

  /**
   * 生成下一步计划
   */
  private generateNextSteps(overallStatus: string, testSummaries: TestSummary[]): string[] {
    const nextSteps: string[] = [];

    switch (overallStatus) {
      case 'EXCELLENT':
        nextSteps.push('✅ 第一阶段验证通过，可以开始第二阶段开发');
        nextSteps.push('📋 建议进行生产环境部署前的最终检查');
        nextSteps.push('📊 建立持续监控和告警机制');
        break;

      case 'GOOD':
        nextSteps.push('✅ 第一阶段基本完成，建议修复少量问题后进入第二阶段');
        nextSteps.push('🔧 优先处理状态为FAIR和POOR的测试项');
        nextSteps.push('📋 加强测试覆盖率和稳定性');
        break;

      case 'FAIR':
        nextSteps.push('⚠️ 第一阶段需要进一步完善');
        nextSteps.push('🔧 重点修复关键功能问题');
        nextSteps.push('📋 建议完成修复后再次运行全局验证');
        break;

      case 'POOR':
      case 'CRITICAL':
        nextSteps.push('❌ 第一阶段存在严重问题，不建议进入第二阶段');
        nextSteps.push('🚨 立即修复关键问题');
        nextSteps.push('🔧 重新设计和实现有问题的模块');
        nextSteps.push('📋 完成修复后重新进行全面验证');
        break;
    }

    return nextSteps;
  }

  /**
   * 保存报告
   */
  private async saveReport(report: GlobalVerificationReport): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportFile = path.join(this.reportsDir, `global-verification-report-${timestamp}.json`);

    await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
    this.logger.info('📄 全局验证报告已保存', { reportFile });
  }

  /**
   * 输出报告摘要
   */
  private outputReportSummary(report: GlobalVerificationReport): void {
    console.log('\n' + '='.repeat(80));
    console.log('🔍 第一阶段 1.1-1.3 全局验证报告');
    console.log('='.repeat(80));
    
    console.log(`📊 总体状态: ${this.getStatusEmoji(report.overallStatus)} ${report.overallStatus}`);
    console.log(`📈 总体成功率: ${(report.overallSuccessRate * 100).toFixed(1)}%`);
    console.log(`📋 总测试数: ${report.totalTests}`);
    console.log(`✅ 通过测试: ${report.totalPassed}`);
    console.log(`❌ 失败测试: ${report.totalFailed}`);

    console.log('\n📊 各模块测试状态:');
    report.testSummaries.forEach(test => {
      console.log(`  ${this.getStatusEmoji(test.status)} ${test.testName}: ${(test.successRate * 100).toFixed(1)}% (${test.passedTests}/${test.totalTests})`);
    });

    if (report.criticalIssues.length > 0) {
      console.log('\n🚨 关键问题:');
      report.criticalIssues.forEach(issue => {
        console.log(`  ❗ ${issue}`);
      });
    }

    console.log('\n💡 建议:');
    report.recommendations.forEach(rec => {
      console.log(`  📝 ${rec}`);
    });

    console.log('\n🎯 下一步:');
    report.nextSteps.forEach(step => {
      console.log(`  ${step}`);
    });

    console.log('\n📁 详细报告: verification-reports/global-verification-report-*.json');
    console.log('='.repeat(80));
  }

  /**
   * 获取状态表情符号
   */
  private getStatusEmoji(status: string): string {
    switch (status) {
      case 'EXCELLENT': return '🟢';
      case 'GOOD': return '🟡';
      case 'FAIR': return '🟠';
      case 'POOR': return '🔴';
      case 'CRITICAL': return '🚨';
      default: return '⚪';
    }
  }
}

// 主函数
async function main() {
  console.log('🔍 启动第一阶段全局验证报告生成...');

  try {
    const reporter = new GlobalVerificationReporter();
    await reporter.generateGlobalReport();
  } catch (error) {
    console.error('\n❌ 全局验证报告生成失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { GlobalVerificationReporter, GlobalVerificationReport };
