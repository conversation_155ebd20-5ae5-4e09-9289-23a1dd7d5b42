#!/usr/bin/env tsx
/**
 * 第一阶段验证主脚本
 * 协调运行所有验证工具并生成综合报告
 */

import { Container } from 'inversify';
import { TYPES } from '../../src/shared/infrastructure/di/types';
import { IBasicLogger } from '../../src/shared/infrastructure/logging/interfaces/basic-logger.interface';
import { getLogger } from '../../src/shared/infrastructure/logging/logger-factory';
import { initializeAndGetContainer } from '../../src/shared/infrastructure/di/modular-container-manager';

// 验证工具
import { Stage1VerificationSetup } from './stage1-verification-setup';
import { DataQualityValidator } from './data-quality-validator';
import { ChaosTestingFramework } from './chaos-testing-framework';
import { PerformanceBenchmark } from './performance-benchmark';
import { VerificationReporter } from './verification-reporter';

interface VerificationOptions {
  skipSetup?: boolean;
  skipDataQuality?: boolean;
  skipChaosTests?: boolean;
  skipPerformance?: boolean;
  testDuration?: number; // 分钟
  symbols?: string[];
  generateReport?: boolean;
}

class Stage1VerificationRunner {
  private container: Container | null = null;
  private logger: IBasicLogger;

  constructor() {
    this.logger = getLogger('verification');
  }

  /**
   * 初始化DI容器
   */
  private async initializeContainer(): Promise<void> {
    if (this.container) return;

    try {
      // 使用项目的统一DI容器
      this.container = await initializeAndGetContainer();
      this.logger.info('✅ DI容器初始化完成');
    } catch (error) {
      this.logger.error('❌ DI容器初始化失败', { error });
      throw error;
    }
  }

  /**
   * 获取服务实例
   */
  private getService<T>(serviceType: symbol | string): T {
    if (!this.container) {
      throw new Error('容器未初始化');
    }
    return this.container.get<T>(serviceType);
  }

  /**
   * 运行完整的第一阶段验证
   */
  async runFullVerification(options: VerificationOptions = {}): Promise<void> {
    const startTime = Date.now();

    this.logger.info('🚀 开始第一阶段完整验证', { options });

    try {
      // 0. 初始化DI容器
      await this.initializeContainer();

      // 1. 验证环境搭建
      if (!options.skipSetup) {
        await this.runSetup();
      }

      // 2. 数据质量验证
      if (!options.skipDataQuality) {
        await this.runDataQualityTests(options);
      }

      // 3. 故障注入测试
      if (!options.skipChaosTests) {
        await this.runChaosTests();
      }

      // 4. 性能基准测试
      if (!options.skipPerformance) {
        await this.runPerformanceTests();
      }

      // 5. 生成综合报告
      if (options.generateReport !== false) {
        await this.generateReport();
      }

      const duration = Date.now() - startTime;
      this.logger.info('✅ 第一阶段验证完成', { 
        duration: Math.round(duration / 1000),
        durationMinutes: Math.round(duration / 60000)
      });

    } catch (error) {
      this.logger.error('❌ 第一阶段验证失败', { error });
      throw error;
    }
  }

  /**
   * 运行验证环境搭建
   */
  private async runSetup(): Promise<void> {
    this.logger.info('🔧 开始验证环境搭建...');
    
    try {
      const setup = this.container.get<Stage1VerificationSetup>(Stage1VerificationSetup);
      await setup.initializeVerificationEnvironment();
      
      this.logger.info('✅ 验证环境搭建完成');
    } catch (error) {
      this.logger.error('❌ 验证环境搭建失败', { error });
      throw error;
    }
  }

  /**
   * 运行数据质量测试
   */
  private async runDataQualityTests(options: VerificationOptions): Promise<void> {
    this.logger.info('🔍 开始数据质量验证...');
    
    try {
      const validator = this.container.get<DataQualityValidator>(DataQualityValidator);
      const symbols = options.symbols || ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'];
      const duration = (options.testDuration || 10) * 60 * 1000; // 转换为毫秒

      for (const symbol of symbols) {
        this.logger.info(`📊 验证 ${symbol} 数据质量...`);
        const report = await validator.validateMarketData(symbol, duration);
        
        this.logger.info(`✅ ${symbol} 数据质量验证完成`, {
          qualityScore: report.qualityScore,
          issues: report.issues.length
        });
      }
      
      this.logger.info('✅ 数据质量验证完成');
    } catch (error) {
      this.logger.error('❌ 数据质量验证失败', { error });
      throw error;
    }
  }

  /**
   * 运行故障注入测试
   */
  private async runChaosTests(): Promise<void> {
    this.logger.info('🔥 开始故障注入测试...');
    
    try {
      const chaosFramework = this.container.get<ChaosTestingFramework>(ChaosTestingFramework);
      const results = await chaosFramework.runChaosTestSuite();
      
      const passedTests = results.filter(r => r.passed).length;
      this.logger.info('✅ 故障注入测试完成', {
        totalTests: results.length,
        passedTests,
        failedTests: results.length - passedTests
      });
    } catch (error) {
      this.logger.error('❌ 故障注入测试失败', { error });
      throw error;
    }
  }

  /**
   * 运行性能基准测试
   */
  private async runPerformanceTests(): Promise<void> {
    this.logger.info('📈 开始性能基准测试...');
    
    try {
      const benchmark = this.container.get<PerformanceBenchmark>(PerformanceBenchmark);
      const results = await benchmark.runBenchmarkSuite();
      
      const passedTests = results.filter(r => r.passed).length;
      this.logger.info('✅ 性能基准测试完成', {
        totalTests: results.length,
        passedTests,
        failedTests: results.length - passedTests
      });
    } catch (error) {
      this.logger.error('❌ 性能基准测试失败', { error });
      throw error;
    }
  }

  /**
   * 生成综合报告
   */
  private async generateReport(): Promise<void> {
    this.logger.info('📊 开始生成验证报告...');
    
    try {
      const reporter = this.container.get<VerificationReporter>(VerificationReporter);
      const summary = await reporter.generateStage1Report();
      
      this.logger.info('✅ 验证报告生成完成', {
        status: summary.overallStatus,
        issues: summary.issues.length,
        recommendations: summary.recommendations.length
      });

      // 输出关键信息到控制台
      this.printSummary(summary);
    } catch (error) {
      this.logger.error('❌ 验证报告生成失败', { error });
      throw error;
    }
  }

  /**
   * 打印验证摘要
   */
  private printSummary(summary: any): void {
    console.log('\n' + '='.repeat(60));
    console.log('🎯 第一阶段验证摘要');
    console.log('='.repeat(60));
    console.log(`📊 整体状态: ${summary.overallStatus}`);
    console.log(`🎯 系统稳定性: ${(summary.metrics.systemStability * 100).toFixed(1)}%`);
    console.log(`📈 数据质量: ${(summary.metrics.dataQuality * 100).toFixed(1)}%`);
    console.log(`⚡ 性能评分: ${(summary.metrics.performanceScore * 100).toFixed(1)}%`);
    console.log(`🛡️ 可靠性评分: ${(summary.metrics.reliabilityScore * 100).toFixed(1)}%`);
    
    if (summary.issues.length > 0) {
      console.log('\n⚠️ 发现的问题:');
      summary.issues.forEach((issue: any, index: number) => {
        console.log(`  ${index + 1}. [${issue.severity}] ${issue.description}`);
      });
    }

    if (summary.nextSteps.length > 0) {
      console.log('\n📋 下一步行动:');
      summary.nextSteps.forEach((step: string, index: number) => {
        console.log(`  ${index + 1}. ${step}`);
      });
    }

    console.log('='.repeat(60));
  }

  /**
   * 运行快速验证（用于开发阶段）
   */
  async runQuickVerification(): Promise<void> {
    this.logger.info('⚡ 开始快速验证...');

    const options: VerificationOptions = {
      skipSetup: false,
      skipDataQuality: false,
      skipChaosTests: true, // 跳过耗时的故障测试
      skipPerformance: false,
      testDuration: 2, // 只测试2分钟
      symbols: ['BTCUSDT'], // 只测试一个交易对
      generateReport: true
    };

    await this.runFullVerification(options);
  }

  /**
   * 运行生产验证（完整测试）
   */
  async runProductionVerification(): Promise<void> {
    this.logger.info('🏭 开始生产级验证...');

    const options: VerificationOptions = {
      skipSetup: false,
      skipDataQuality: false,
      skipChaosTests: false,
      skipPerformance: false,
      testDuration: 30, // 测试30分钟
      symbols: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT'], // 测试多个交易对
      generateReport: true
    };

    await this.runFullVerification(options);
  }
}

// 命令行接口
async function main() {
  const args = process.argv.slice(2);
  const mode = args[0] || 'quick';

  const runner = new Stage1VerificationRunner();

  try {
    switch (mode) {
      case 'quick':
        console.log('🚀 运行快速验证模式...');
        await runner.runQuickVerification();
        break;
      
      case 'full':
      case 'production':
        console.log('🏭 运行完整验证模式...');
        await runner.runProductionVerification();
        break;
      
      case 'setup-only':
        console.log('🔧 仅运行环境搭建...');
        const setupRunner = new Stage1VerificationRunner();
        await setupRunner.runFullVerification({
          skipDataQuality: true,
          skipChaosTests: true,
          skipPerformance: true,
          generateReport: false
        });
        break;
      
      default:
        console.log('❌ 未知模式:', mode);
        console.log('可用模式: quick, full, production, setup-only');
        process.exit(1);
    }

    console.log('\n✅ 验证完成！查看 verification-reports 目录获取详细报告。');
  } catch (error) {
    console.error('\n❌ 验证失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { Stage1VerificationRunner, VerificationOptions };
