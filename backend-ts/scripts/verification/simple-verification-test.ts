#!/usr/bin/env tsx
/**
 * 简化验证测试脚本
 * 测试验证工具的基本功能，使用项目现有的架构和组件
 */

import { getLogger } from '../../src/shared/infrastructure/logging/logger-factory';
import { ILogger } from '../../src/shared/infrastructure/logging/logger.interface';
import * as fs from 'fs/promises';
import * as path from 'path';

interface SimpleTestResult {
  testName: string;
  passed: boolean;
  message: string;
  duration: number;
  timestamp: Date;
}

class SimpleVerificationTest {
  private readonly logger: ILogger;
  private readonly reportsDir: string;

  constructor() {
    this.logger = getLogger('simple-verification-test');
    this.reportsDir = path.join(process.cwd(), 'verification-reports');
  }

  /**
   * 运行简化验证测试
   */
  async runTests(): Promise<SimpleTestResult[]> {
    this.logger.info('🧪 开始简化验证测试...');

    const results: SimpleTestResult[] = [];

    try {
      // 1. 测试报告目录创建
      results.push(await this.testReportDirectoryCreation());

      // 2. 测试日志系统
      results.push(await this.testLoggingSystem());

      // 3. 测试文件操作
      results.push(await this.testFileOperations());

      // 4. 测试基准数据生成
      results.push(await this.testBaselineGeneration());

      // 5. 测试模拟数据质量检查
      results.push(await this.testDataQualityCheck());

      // 生成测试报告
      await this.generateTestReport(results);

      const passedTests = results.filter(r => r.passed).length;
      this.logger.info('✅ 简化验证测试完成', {
        totalTests: results.length,
        passedTests,
        failedTests: results.length - passedTests
      });

      return results;
    } catch (error) {
      this.logger.error('❌ 简化验证测试失败', { error });
      throw error;
    }
  }

  /**
   * 测试报告目录创建
   */
  private async testReportDirectoryCreation(): Promise<SimpleTestResult> {
    const startTime = Date.now();
    const testName = '报告目录创建测试';

    try {
      // 创建验证报告目录结构
      await fs.mkdir(this.reportsDir, { recursive: true });
      await fs.mkdir(path.join(this.reportsDir, 'stage1'), { recursive: true });
      await fs.mkdir(path.join(this.reportsDir, 'baselines'), { recursive: true });
      await fs.mkdir(path.join(this.reportsDir, 'monitoring'), { recursive: true });
      await fs.mkdir(path.join(this.reportsDir, 'data-quality'), { recursive: true });

      // 验证目录是否创建成功
      const stats = await fs.stat(this.reportsDir);
      if (!stats.isDirectory()) {
        throw new Error('报告目录创建失败');
      }

      return {
        testName,
        passed: true,
        message: '报告目录结构创建成功',
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        message: `报告目录创建失败: ${error instanceof Error ? error.message : String(error)}`,
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    }
  }

  /**
   * 测试日志系统
   */
  private async testLoggingSystem(): Promise<SimpleTestResult> {
    const startTime = Date.now();
    const testName = '日志系统测试';

    try {
      // 测试各种日志级别
      this.logger.info('测试信息日志');
      this.logger.warn('测试警告日志');
      this.logger.debug('测试调试日志');
      this.logger.error('测试错误日志');

      // 测试带元数据的日志
      this.logger.info('测试带元数据的日志', {
        testData: { key: 'value' },
        timestamp: new Date(),
        testNumber: 123
      });

      return {
        testName,
        passed: true,
        message: '日志系统功能正常',
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        message: `日志系统测试失败: ${error instanceof Error ? error.message : String(error)}`,
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    }
  }

  /**
   * 测试文件操作
   */
  private async testFileOperations(): Promise<SimpleTestResult> {
    const startTime = Date.now();
    const testName = '文件操作测试';

    try {
      const testFile = path.join(this.reportsDir, 'test-file.json');
      const testData = {
        timestamp: new Date(),
        testData: 'Hello, World!',
        numbers: [1, 2, 3, 4, 5]
      };

      // 写入文件
      await fs.writeFile(testFile, JSON.stringify(testData, null, 2));

      // 读取文件
      const readData = await fs.readFile(testFile, 'utf-8');
      const parsedData = JSON.parse(readData);

      // 验证数据
      if (parsedData.testData !== testData.testData) {
        throw new Error('文件读写数据不一致');
      }

      // 清理测试文件
      await fs.unlink(testFile);

      return {
        testName,
        passed: true,
        message: '文件操作功能正常',
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        message: `文件操作测试失败: ${error instanceof Error ? error.message : String(error)}`,
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    }
  }

  /**
   * 测试基准数据生成
   */
  private async testBaselineGeneration(): Promise<SimpleTestResult> {
    const startTime = Date.now();
    const testName = '基准数据生成测试';

    try {
      const baseline = {
        timestamp: new Date(),
        system: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          nodeVersion: process.version
        },
        testMetrics: {
          responseTime: Math.random() * 200 + 50, // 50-250ms
          throughput: Math.random() * 100 + 50,   // 50-150 req/s
          errorRate: Math.random() * 0.05,        // 0-5%
          dataQuality: 0.95 + Math.random() * 0.05 // 95-100%
        }
      };

      const baselineFile = path.join(this.reportsDir, 'baselines', 'test-baseline.json');
      await fs.writeFile(baselineFile, JSON.stringify(baseline, null, 2));

      // 验证文件是否创建成功
      const stats = await fs.stat(baselineFile);
      if (!stats.isFile()) {
        throw new Error('基准文件创建失败');
      }

      return {
        testName,
        passed: true,
        message: '基准数据生成成功',
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        message: `基准数据生成失败: ${error instanceof Error ? error.message : String(error)}`,
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    }
  }

  /**
   * 测试模拟数据质量检查
   */
  private async testDataQualityCheck(): Promise<SimpleTestResult> {
    const startTime = Date.now();
    const testName = '数据质量检查测试';

    try {
      // 模拟市场数据
      const mockData = [
        { symbol: 'BTCUSDT', price: 45000, volume: 1000, timestamp: new Date() },
        { symbol: 'BTCUSDT', price: 45100, volume: 1200, timestamp: new Date() },
        { symbol: 'BTCUSDT', price: -100, volume: 800, timestamp: new Date() }, // 异常价格
        { symbol: 'BTCUSDT', price: 44900, volume: -50, timestamp: new Date() }, // 异常成交量
      ];

      const issues = [];
      let validDataCount = 0;

      for (const data of mockData) {
        // 价格检查
        if (data.price <= 0) {
          issues.push({
            type: 'INVALID_PRICE',
            value: data.price,
            symbol: data.symbol
          });
        } else {
          validDataCount++;
        }

        // 成交量检查
        if (data.volume < 0) {
          issues.push({
            type: 'NEGATIVE_VOLUME',
            value: data.volume,
            symbol: data.symbol
          });
        }
      }

      const qualityScore = validDataCount / mockData.length;

      // 保存质量检查结果
      const qualityReport = {
        timestamp: new Date(),
        totalChecks: mockData.length,
        validData: validDataCount,
        issues: issues,
        qualityScore: qualityScore
      };

      const reportFile = path.join(this.reportsDir, 'data-quality', 'test-quality-report.json');
      await fs.writeFile(reportFile, JSON.stringify(qualityReport, null, 2));

      return {
        testName,
        passed: true,
        message: `数据质量检查完成，质量评分: ${(qualityScore * 100).toFixed(1)}%`,
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        message: `数据质量检查失败: ${error instanceof Error ? error.message : String(error)}`,
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    }
  }

  /**
   * 生成测试报告
   */
  private async generateTestReport(results: SimpleTestResult[]): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportFile = path.join(this.reportsDir, `simple-verification-test-${timestamp}.json`);

    const report = {
      timestamp: new Date(),
      totalTests: results.length,
      passedTests: results.filter(r => r.passed).length,
      failedTests: results.filter(r => !r.passed).length,
      results: results
    };

    await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
    this.logger.info('📄 测试报告已生成', { reportFile });
  }
}

// 主函数
async function main() {
  console.log('🧪 启动简化验证测试...');

  try {
    const test = new SimpleVerificationTest();
    const results = await test.runTests();

    const passedTests = results.filter(r => r.passed).length;
    const totalTests = results.length;

    console.log('\n' + '='.repeat(50));
    console.log('📊 简化验证测试结果');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (passedTests === totalTests) {
      console.log('\n✅ 所有测试通过！验证工具基础功能正常。');
      console.log('📋 可以继续进行下一步：数据源故障处理机制开发');
    } else {
      console.log('\n⚠️ 部分测试失败，需要检查和修复：');
      results.filter(r => !r.passed).forEach(result => {
        console.log(`  ❌ ${result.testName}: ${result.message}`);
      });
    }

    console.log('\n📁 查看详细报告: verification-reports/');
  } catch (error) {
    console.error('\n❌ 测试执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { SimpleVerificationTest, SimpleTestResult };
