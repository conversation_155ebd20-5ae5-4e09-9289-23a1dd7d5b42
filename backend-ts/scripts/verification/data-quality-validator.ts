#!/usr/bin/env tsx
/**
 * 数据质量验证器
 * 用于验证市场数据的质量和一致性
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../src/shared/infrastructure/di/types';
import { IBasicLogger } from '../../src/shared/infrastructure/logging/interfaces/basic-logger.interface';
import * as fs from 'fs/promises';
import * as path from 'path';

interface DataQualityIssue {
  type: 'INVALID_PRICE' | 'NEGATIVE_VOLUME' | 'STALE_DATA' | 'MISSING_DATA' | 'PRICE_OUTLIER' | 'VOLUME_OUTLIER';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  value: any;
  expected?: any;
  timestamp: Date;
  symbol: string;
  source?: string;
  description: string;
}

interface DataQualityReport {
  symbol: string;
  testDuration: number; // 毫秒
  totalChecks: number;
  issues: DataQualityIssue[];
  qualityScore: number; // 0-1
  metrics: {
    averagePrice: number;
    priceStdDev: number;
    averageVolume: number;
    volumeStdDev: number;
    dataFreshness: number; // 平均数据年龄（毫秒）
    sourceDistribution: Record<string, number>;
  };
  recommendations: string[];
}

interface MarketDataPoint {
  symbol: string;
  price: number;
  volume: number;
  timestamp: Date;
  source: string;
  bid?: number;
  ask?: number;
  high24h?: number;
  low24h?: number;
  change24h?: number;
}

@injectable()
export class DataQualityValidator {
  private readonly reportsDir = path.join(process.cwd(), 'verification-reports', 'data-quality');

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger
  ) {}

  /**
   * 验证市场数据质量
   */
  async validateMarketData(
    symbol: string, 
    duration: number = 3600000, // 1小时
    interval: number = 1000 // 1秒
  ): Promise<DataQualityReport> {
    this.logger.info('🔍 开始数据质量验证', { symbol, duration, interval });

    const startTime = Date.now();
    const endTime = startTime + duration;
    const issues: DataQualityIssue[] = [];
    const dataPoints: MarketDataPoint[] = [];

    try {
      // 确保报告目录存在
      await fs.mkdir(this.reportsDir, { recursive: true });

      let checkCount = 0;
      while (Date.now() < endTime) {
        try {
          const data = await this.fetchMarketData(symbol);
          dataPoints.push(data);
          
          // 执行各种质量检查
          const dataIssues = await this.performQualityChecks(data);
          issues.push(...dataIssues);
          
          checkCount++;
          
          // 记录进度
          if (checkCount % 60 === 0) { // 每分钟记录一次
            this.logger.debug('数据质量检查进行中', { 
              symbol, 
              checks: checkCount, 
              issues: issues.length,
              progress: `${Math.round((Date.now() - startTime) / duration * 100)}%`
            });
          }

          await this.sleep(interval);
        } catch (error) {
          issues.push({
            type: 'MISSING_DATA',
            severity: 'HIGH',
            value: null,
            timestamp: new Date(),
            symbol,
            description: `数据获取失败: ${error instanceof Error ? error.message : String(error)}`
          });
        }
      }

      // 生成报告
      const report = await this.generateQualityReport(symbol, dataPoints, issues, Date.now() - startTime);
      
      // 保存报告
      await this.saveReport(report);
      
      this.logger.info('✅ 数据质量验证完成', { 
        symbol, 
        totalChecks: checkCount, 
        issues: issues.length,
        qualityScore: report.qualityScore 
      });

      return report;
    } catch (error) {
      this.logger.error('❌ 数据质量验证失败', { symbol, error });
      throw error;
    }
  }

  /**
   * 获取市场数据（模拟实现，实际应该调用真实服务）
   */
  private async fetchMarketData(symbol: string): Promise<MarketDataPoint> {
    // 这里应该调用真实的市场数据服务
    // 暂时返回模拟数据，实际实现时需要替换为真实的服务调用
    
    // 模拟不同的数据源
    const sources = ['Binance', 'OKX', 'CoinGecko'];
    const source = sources[Math.floor(Math.random() * sources.length)];
    
    // 模拟价格数据（基于BTC价格范围）
    const basePrice = symbol === 'BTCUSDT' ? 45000 : 2000;
    const price = basePrice + (Math.random() - 0.5) * basePrice * 0.1;
    
    return {
      symbol,
      price: Math.round(price * 100) / 100,
      volume: Math.round(Math.random() * 1000000 * 100) / 100,
      timestamp: new Date(),
      source,
      bid: price * 0.999,
      ask: price * 1.001,
      high24h: price * 1.05,
      low24h: price * 0.95,
      change24h: (Math.random() - 0.5) * 0.1
    };
  }

  /**
   * 执行数据质量检查
   */
  private async performQualityChecks(data: MarketDataPoint): Promise<DataQualityIssue[]> {
    const issues: DataQualityIssue[] = [];

    // 1. 价格合理性检查
    if (data.price <= 0) {
      issues.push({
        type: 'INVALID_PRICE',
        severity: 'CRITICAL',
        value: data.price,
        expected: '> 0',
        timestamp: new Date(),
        symbol: data.symbol,
        source: data.source,
        description: '价格不能为零或负数'
      });
    }

    // 2. 价格异常值检查（简单的范围检查）
    const expectedRange = this.getExpectedPriceRange(data.symbol);
    if (data.price < expectedRange.min || data.price > expectedRange.max) {
      issues.push({
        type: 'PRICE_OUTLIER',
        severity: 'HIGH',
        value: data.price,
        expected: `${expectedRange.min} - ${expectedRange.max}`,
        timestamp: new Date(),
        symbol: data.symbol,
        source: data.source,
        description: '价格超出预期范围'
      });
    }

    // 3. 成交量检查
    if (data.volume < 0) {
      issues.push({
        type: 'NEGATIVE_VOLUME',
        severity: 'CRITICAL',
        value: data.volume,
        expected: '>= 0',
        timestamp: new Date(),
        symbol: data.symbol,
        source: data.source,
        description: '成交量不能为负数'
      });
    }

    // 4. 数据时效性检查
    const dataAge = Date.now() - data.timestamp.getTime();
    if (dataAge > 60000) { // 超过1分钟
      issues.push({
        type: 'STALE_DATA',
        severity: 'MEDIUM',
        value: dataAge,
        expected: '< 60000ms',
        timestamp: new Date(),
        symbol: data.symbol,
        source: data.source,
        description: '数据过期，可能不是最新数据'
      });
    }

    // 5. 买卖价差检查
    if (data.bid && data.ask && data.bid >= data.ask) {
      issues.push({
        type: 'INVALID_PRICE',
        severity: 'HIGH',
        value: { bid: data.bid, ask: data.ask },
        expected: 'bid < ask',
        timestamp: new Date(),
        symbol: data.symbol,
        source: data.source,
        description: '买价不能大于等于卖价'
      });
    }

    return issues;
  }

  /**
   * 获取预期价格范围
   */
  private getExpectedPriceRange(symbol: string): { min: number; max: number } {
    // 简单的价格范围定义，实际应该基于历史数据动态计算
    const ranges: Record<string, { min: number; max: number }> = {
      'BTCUSDT': { min: 20000, max: 100000 },
      'ETHUSDT': { min: 1000, max: 10000 },
      'ADAUSDT': { min: 0.1, max: 5 },
      'SOLUSDT': { min: 10, max: 500 }
    };

    return ranges[symbol] || { min: 0.001, max: 1000000 };
  }

  /**
   * 生成质量报告
   */
  private async generateQualityReport(
    symbol: string,
    dataPoints: MarketDataPoint[],
    issues: DataQualityIssue[],
    duration: number
  ): Promise<DataQualityReport> {
    const totalChecks = dataPoints.length;
    const qualityScore = Math.max(0, 1 - (issues.length / totalChecks));

    // 计算统计指标
    const prices = dataPoints.map(d => d.price);
    const volumes = dataPoints.map(d => d.volume);
    
    const averagePrice = prices.reduce((a, b) => a + b, 0) / prices.length;
    const priceStdDev = Math.sqrt(prices.reduce((sum, price) => sum + Math.pow(price - averagePrice, 2), 0) / prices.length);
    
    const averageVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;
    const volumeStdDev = Math.sqrt(volumes.reduce((sum, volume) => sum + Math.pow(volume - averageVolume, 2), 0) / volumes.length);

    // 计算数据新鲜度
    const now = Date.now();
    const dataFreshness = dataPoints.reduce((sum, point) => sum + (now - point.timestamp.getTime()), 0) / dataPoints.length;

    // 计算数据源分布
    const sourceDistribution: Record<string, number> = {};
    dataPoints.forEach(point => {
      sourceDistribution[point.source] = (sourceDistribution[point.source] || 0) + 1;
    });

    // 生成建议
    const recommendations = this.generateRecommendations(issues, qualityScore);

    return {
      symbol,
      testDuration: duration,
      totalChecks,
      issues,
      qualityScore,
      metrics: {
        averagePrice,
        priceStdDev,
        averageVolume,
        volumeStdDev,
        dataFreshness,
        sourceDistribution
      },
      recommendations
    };
  }

  /**
   * 生成改进建议
   */
  private generateRecommendations(issues: DataQualityIssue[], qualityScore: number): string[] {
    const recommendations: string[] = [];

    if (qualityScore < 0.9) {
      recommendations.push('数据质量低于90%，需要立即改进数据源和验证机制');
    }

    const criticalIssues = issues.filter(i => i.severity === 'CRITICAL');
    if (criticalIssues.length > 0) {
      recommendations.push(`发现${criticalIssues.length}个严重问题，需要立即修复`);
    }

    const staleDataIssues = issues.filter(i => i.type === 'STALE_DATA');
    if (staleDataIssues.length > issues.length * 0.1) {
      recommendations.push('超过10%的数据过期，建议优化数据更新频率');
    }

    const priceOutliers = issues.filter(i => i.type === 'PRICE_OUTLIER');
    if (priceOutliers.length > 0) {
      recommendations.push('发现价格异常值，建议加强价格验证和过滤机制');
    }

    if (recommendations.length === 0) {
      recommendations.push('数据质量良好，继续保持当前标准');
    }

    return recommendations;
  }

  /**
   * 保存报告
   */
  private async saveReport(report: DataQualityReport): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `data-quality-${report.symbol}-${timestamp}.json`;
    const filepath = path.join(this.reportsDir, filename);

    await fs.writeFile(filepath, JSON.stringify(report, null, 2));
    this.logger.info('📄 数据质量报告已保存', { filepath });
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  async function main() {
    console.log('🔍 启动数据质量验证器...');
    
    try {
      // 这里需要初始化DI容器
      // const container = getContainer();
      // const validator = container.get<DataQualityValidator>(DataQualityValidator);
      
      // 验证BTCUSDT数据质量（5分钟测试）
      // const report = await validator.validateMarketData('BTCUSDT', 5 * 60 * 1000, 1000);
      
      console.log('✅ 数据质量验证完成！');
    } catch (error) {
      console.error('❌ 数据质量验证失败:', error);
      process.exit(1);
    }
  }

  main();
}

export { DataQualityValidator, DataQualityReport, DataQualityIssue, MarketDataPoint };
