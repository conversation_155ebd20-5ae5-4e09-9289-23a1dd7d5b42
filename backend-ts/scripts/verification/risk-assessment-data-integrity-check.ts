#!/usr/bin/env tsx

/**
 * 风险评估系统数据完整性验证脚本
 * 验证修复后的风险评估系统能否正确访问和使用19万条历史数据
 */

import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';

config();

interface DataIntegrityResult {
  component: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  data?: any;
}

class RiskAssessmentDataIntegrityChecker {
  private readonly prisma = new PrismaClient();
  private results: DataIntegrityResult[] = [];

  async runAllChecks(): Promise<void> {
    console.log('🔍 开始风险评估系统数据完整性检查...');
    console.log('='.repeat(60));

    try {
      // 1. 检查数据库连接和基础数据
      await this.checkDatabaseConnection();
      await this.checkHistoricalDataAvailability();
      
      // 2. 检查风险评估控制器的数据获取
      await this.checkRiskAssessmentControllerDataAccess();
      
      // 3. 检查AI风险分析引擎的数据使用
      await this.checkAIRiskAnalysisEngineDataUsage();
      
      // 4. 检查风险评估应用服务的数据处理
      await this.checkRiskAssessmentApplicationServiceDataProcessing();
      
      // 5. 生成报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 检查过程中发生错误:', error);
      this.addResult('SYSTEM', '整体检查', 'FAIL', `系统错误: ${error}`);
    } finally {
      await this.cleanup();
    }
  }

  private async checkDatabaseConnection(): Promise<void> {
    console.log('\n1️⃣ 检查数据库连接...');
    
    try {
      await this.prisma.$connect();
      this.addResult('DATABASE', '连接测试', 'PASS', '数据库连接成功');
      
      // 检查关键表
      const symbolsCount = await this.prisma.symbols.count();
      const historicalDataCount = await this.prisma.historicalData.count();
      
      this.addResult('DATABASE', '符号表', symbolsCount > 0 ? 'PASS' : 'FAIL', 
        `符号表记录数: ${symbolsCount}`);
      
      this.addResult('DATABASE', '历史数据表', historicalDataCount > 0 ? 'PASS' : 'FAIL', 
        `历史数据记录数: ${historicalDataCount.toLocaleString()}`);
        
    } catch (error) {
      this.addResult('DATABASE', '连接测试', 'FAIL', 
        `数据库连接失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async checkHistoricalDataAvailability(): Promise<void> {
    console.log('\n2️⃣ 检查历史数据可用性...');
    
    try {
      // 检查BTC数据
      const btcSymbol = await this.prisma.symbols.findFirst({
        where: {
          OR: [
            { symbol: 'BTC/USDT' },
            { symbol: 'BTCUSDT' },
            { symbol: 'BTC' }
          ]
        }
      });

      if (!btcSymbol) {
        this.addResult('DATA', 'BTC符号', 'FAIL', 'BTC符号记录不存在');
        return;
      }

      const btcHistoricalData = await this.prisma.historicalData.findMany({
        where: { symbolId: btcSymbol.id },
        orderBy: { timestamp: 'desc' },
        take: 100
      });

      this.addResult('DATA', 'BTC历史数据', btcHistoricalData.length > 0 ? 'PASS' : 'FAIL',
        `BTC历史数据记录数: ${btcHistoricalData.length}`);

      if (btcHistoricalData.length > 0) {
        const latestData = btcHistoricalData[0];
        const oldestData = btcHistoricalData[btcHistoricalData.length - 1];
        
        this.addResult('DATA', '数据时间范围', 'PASS',
          `最新: ${latestData.timestamp.toISOString()}, 最旧: ${oldestData.timestamp.toISOString()}`);
        
        this.addResult('DATA', '价格数据质量',
          Number(latestData.closePrice) > 0 ? 'PASS' : 'FAIL',
          `最新价格: ${latestData.closePrice}`);
      }

    } catch (error) {
      this.addResult('DATA', '历史数据检查', 'FAIL',
        `检查失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async checkRiskAssessmentControllerDataAccess(): Promise<void> {
    console.log('\n3️⃣ 检查风险评估控制器数据访问...');
    
    try {
      // 模拟控制器的数据获取逻辑
      const symbol = 'BTC/USDT';
      
      // 查找符号记录
      const symbolRecord = await this.prisma.symbols.findFirst({
        where: {
          OR: [
            { symbol: symbol },
            { symbol: symbol.replace('/', '') },
            { symbol: symbol.replace('/', 'USDT') },
            { symbol: `${symbol}/USDT` }
          ]
        }
      });

      if (!symbolRecord) {
        this.addResult('CONTROLLER', '符号查找', 'FAIL', '无法找到符号记录');
        return;
      }

      // 获取历史数据
      const historicalData = await this.prisma.historicalData.findMany({
        where: {
          symbolId: symbolRecord.id,
          timeframe: '1h'
        },
        orderBy: { timestamp: 'desc' },
        take: 100
      });

      this.addResult('CONTROLLER', '历史数据获取', 
        historicalData.length > 0 ? 'PASS' : 'FAIL',
        `获取到 ${historicalData.length} 条历史数据`);

      if (historicalData.length > 0) {
        // 验证数据格式
        const priceHistory = historicalData.map(record => ({
          timestamp: record.timestamp,
          price: Number(record.closePrice), // 修正字段名
          volume: Number(record.volume)
        }));

        const validPrices = priceHistory.filter(p => p.price > 0).length;
        const validVolumes = priceHistory.filter(p => p.volume > 0).length;

        this.addResult('CONTROLLER', '价格数据有效性', 
          validPrices === priceHistory.length ? 'PASS' : 'WARNING',
          `有效价格: ${validPrices}/${priceHistory.length}`);

        this.addResult('CONTROLLER', '交易量数据有效性', 
          validVolumes > priceHistory.length * 0.8 ? 'PASS' : 'WARNING',
          `有效交易量: ${validVolumes}/${priceHistory.length}`);
      }

    } catch (error) {
      this.addResult('CONTROLLER', '数据访问测试', 'FAIL',
        `测试失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async checkAIRiskAnalysisEngineDataUsage(): Promise<void> {
    console.log('\n4️⃣ 检查AI风险分析引擎数据使用...');
    
    try {
      // 模拟AI引擎的市场数据输入
      const symbol = 'BTC/USDT';
      const symbolRecord = await this.prisma.symbols.findFirst({
        where: { symbol: { contains: 'BTC' } }
      });

      if (!symbolRecord) {
        this.addResult('AI_ENGINE', '符号查找', 'FAIL', '无法找到BTC符号');
        return;
      }

      const historicalData = await this.prisma.historicalData.findMany({
        where: { symbolId: symbolRecord.id },
        orderBy: { timestamp: 'desc' },
        take: 50
      });

      // 验证市场数据输入结构
      if (historicalData.length > 0) {
        const currentPrice = Number(historicalData[0].closePrice); // 修正字段名
        const volume24h = historicalData.slice(0, 24).reduce((sum, record) => 
          sum + Number(record.volume), 0);
        
        const priceHistory = historicalData.map(record => ({
          timestamp: record.timestamp,
          price: Number(record.closePrice), // 修正字段名
          volume: Number(record.volume)
        }));

        // 估算市值
        const estimatedMarketCap = volume24h * 50;

        this.addResult('AI_ENGINE', '当前价格', currentPrice > 0 ? 'PASS' : 'FAIL',
          `当前价格: ${currentPrice}`);

        this.addResult('AI_ENGINE', '24小时交易量', volume24h > 0 ? 'PASS' : 'FAIL',
          `24h交易量: ${volume24h.toLocaleString()}`);

        this.addResult('AI_ENGINE', '价格历史', priceHistory.length > 0 ? 'PASS' : 'FAIL',
          `价格历史记录数: ${priceHistory.length}`);

        this.addResult('AI_ENGINE', '市值估算', estimatedMarketCap > 0 ? 'PASS' : 'FAIL',
          `估算市值: ${estimatedMarketCap.toLocaleString()}`);
      } else {
        this.addResult('AI_ENGINE', '数据可用性', 'FAIL', '没有可用的历史数据');
      }

    } catch (error) {
      this.addResult('AI_ENGINE', '数据使用测试', 'FAIL',
        `测试失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async checkRiskAssessmentApplicationServiceDataProcessing(): Promise<void> {
    console.log('\n5️⃣ 检查风险评估应用服务数据处理...');
    
    try {
      // 模拟应用服务的数据处理逻辑
      const symbol = 'BTC/USDT';
      
      // 检查是否能获取到足够的数据进行风险计算
      const symbolRecord = await this.prisma.symbols.findFirst({
        where: { symbol: { contains: 'BTC' } }
      });

      if (symbolRecord) {
        const historicalData = await this.prisma.historicalData.findMany({
          where: { symbolId: symbolRecord.id },
          orderBy: { timestamp: 'desc' },
          take: 100
        });

        const priceHistory = historicalData.map(record => ({
          timestamp: record.timestamp,
          price: Number(record.closePrice), // 修正字段名
          volume: Number(record.volume)
        }));

        // 计算平均交易量
        const avgVolume = priceHistory.length > 0 ? 
          priceHistory.reduce((sum, p) => sum + p.volume, 0) / priceHistory.length : 0;

        this.addResult('APP_SERVICE', '数据处理', priceHistory.length > 0 ? 'PASS' : 'FAIL',
          `处理了 ${priceHistory.length} 条数据记录`);

        this.addResult('APP_SERVICE', '交易量计算', avgVolume > 0 ? 'PASS' : 'FAIL',
          `平均交易量: ${avgVolume.toLocaleString()}`);

        this.addResult('APP_SERVICE', '数据质量评估', 
          priceHistory.length > 50 ? 'PASS' : priceHistory.length > 20 ? 'WARNING' : 'FAIL',
          `数据质量: ${priceHistory.length > 50 ? 'HIGH' : priceHistory.length > 20 ? 'MEDIUM' : 'LOW'}`);
      }

    } catch (error) {
      this.addResult('APP_SERVICE', '数据处理测试', 'FAIL',
        `测试失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private addResult(component: string, test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, data?: any): void {
    this.results.push({ component, test, status, message, data });
    
    const statusIcon = status === 'PASS' ? '✅' : status === 'WARNING' ? '⚠️' : '❌';
    console.log(`  ${statusIcon} ${component} - ${test}: ${message}`);
  }

  private generateReport(): void {
    console.log('\n📊 数据完整性检查报告');
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(`总测试数: ${total}`);
    console.log(`✅ 通过: ${passed}`);
    console.log(`⚠️  警告: ${warnings}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ 失败的测试:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(result => {
          console.log(`  • ${result.component} - ${result.test}: ${result.message}`);
        });
    }

    if (warnings > 0) {
      console.log('\n⚠️ 警告的测试:');
      this.results
        .filter(r => r.status === 'WARNING')
        .forEach(result => {
          console.log(`  • ${result.component} - ${result.test}: ${result.message}`);
        });
    }

    // 保存详细报告
    this.saveDetailedReport();
  }

  private saveDetailedReport(): void {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.results.length,
        passed: this.results.filter(r => r.status === 'PASS').length,
        warnings: this.results.filter(r => r.status === 'WARNING').length,
        failed: this.results.filter(r => r.status === 'FAIL').length
      },
      results: this.results
    };

    const fs = require('fs');
    const path = require('path');
    
    const reportsDir = path.join(process.cwd(), 'verification-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const reportPath = path.join(reportsDir, `risk-assessment-data-integrity-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 详细报告已保存: ${reportPath}`);
  }

  private async cleanup(): Promise<void> {
    await this.prisma.$disconnect();
  }
}

// 运行检查
async function main() {
  const checker = new RiskAssessmentDataIntegrityChecker();
  await checker.runAllChecks();
}

if (require.main === module) {
  main().catch(console.error);
}

export { RiskAssessmentDataIntegrityChecker };
