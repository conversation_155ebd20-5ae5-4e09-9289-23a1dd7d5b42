#!/usr/bin/env npx tsx

/**
 * 数据库统一性验证脚本
 * 确保项目中只有一套数据库配置和连接
 */

import { readFileSync, existsSync } from 'fs';
import { execSync } from 'child_process';
import path from 'path';
import { glob } from 'glob';

interface DatabaseIssue {
  type: 'error' | 'warning' | 'info';
  file: string;
  line?: number;
  message: string;
  suggestion?: string;
}

class DatabaseUnityValidator {
  private readonly projectRoot: string;
  private issues: DatabaseIssue[] = [];

  constructor() {
    this.projectRoot = process.cwd();
  }

  async validateDatabaseUnity(): Promise<void> {
    console.log('🔍 验证数据库统一性...\n');

    // 1. 检查PrismaClient实例化
    await this.checkPrismaClientInstances();

    // 2. 检查数据库URL配置
    await this.checkDatabaseUrlConfigurations();

    // 3. 检查数据库连接池配置
    await this.checkConnectionPoolConfigurations();

    // 4. 检查Prisma Schema文件
    await this.checkPrismaSchemas();

    // 5. 检查环境变量配置
    await this.checkEnvironmentConfigurations();

    this.printReport();
  }

  private async checkPrismaClientInstances(): Promise<void> {
    console.log('📋 检查PrismaClient实例化...');

    try {
      // 查找所有TypeScript文件
      const tsFiles = await glob('**/*.ts', {
        cwd: this.projectRoot,
        ignore: ['node_modules/**', 'dist/**', '**/*.d.ts']
      });

      for (const file of tsFiles) {
        const fullPath = path.join(this.projectRoot, file);
        const content = readFileSync(fullPath, 'utf-8');
        const lines = content.split('\n');

        lines.forEach((line, index) => {
          if (line.includes('new PrismaClient()')) {
            const lineNumber = index + 1;
            
            // 检查是否是单例模式中的合法实例化
            if (this.isSingletonImplementation(content, index)) {
              this.issues.push({
                type: 'info',
                file,
                line: lineNumber,
                message: '单例模式中的PrismaClient实例化（合法）',
                suggestion: '确保这是全局唯一实例的创建'
              });
            }
            // 检查是否是允许的文件
            else if (this.isAllowedPrismaClientFile(file)) {
              this.issues.push({
                type: 'info',
                file,
                line: lineNumber,
                message: '独立PrismaClient实例（脚本文件，允许）',
                suggestion: '确保脚本执行完毕后调用$disconnect()'
              });
            } else {
              this.issues.push({
                type: 'error',
                file,
                line: lineNumber,
                message: '违反单例模式：直接创建PrismaClient实例',
                suggestion: '使用getGlobalPrismaClient()替代'
              });
            }
          }
        });
      }
    } catch (error) {
      this.issues.push({
        type: 'error',
        file: 'system',
        message: `检查PrismaClient实例时出错: ${error}`
      });
    }
  }

  private async checkDatabaseUrlConfigurations(): Promise<void> {
    console.log('🔗 检查数据库URL配置...');

    const configFiles = [
      '.env',
      '.env.example',
      '.env.local',
      '.env.development',
      '.env.production',
      '.env.test'
    ];

    const databaseUrls: Array<{ file: string; urls: string[] }> = [];

    for (const configFile of configFiles) {
      const fullPath = path.join(this.projectRoot, configFile);
      if (existsSync(fullPath)) {
        const content = readFileSync(fullPath, 'utf-8');
        const urls = this.extractDatabaseUrls(content);
        if (urls.length > 0) {
          databaseUrls.push({ file: configFile, urls });
        }
      }
    }

    // 分析数据库URL配置
    const allUrls = databaseUrls.flatMap(config => config.urls);
    const uniqueUrls = [...new Set(allUrls)];

    // 分析数据库URL类型
    const mainDbUrls = uniqueUrls.filter(url => !url.includes('test') && !url.includes('shadow'));
    const testDbUrls = uniqueUrls.filter(url => url.includes('test'));
    const shadowDbUrls = uniqueUrls.filter(url => url.includes('shadow'));
    
    this.issues.push({
      type: 'info',
      file: 'configuration',
      message: `数据库配置统计: 主数据库(${mainDbUrls.length}), 测试数据库(${testDbUrls.length}), 影子数据库(${shadowDbUrls.length})`,
      suggestion: '标准配置应包含主数据库、测试数据库和影子数据库'
    });
    
    if (uniqueUrls.length > 5) { // 允许一定的灵活性
      this.issues.push({
        type: 'warning',
        file: 'configuration',
        message: `发现${uniqueUrls.length}个不同的数据库URL配置，可能过多`,
        suggestion: '考虑简化数据库配置，通常只需要主数据库、测试数据库和影子数据库'
      });
    }

    // 检查是否有生产和开发环境使用相同数据库的风险
    const prodUrls = allUrls.filter(url => !url.includes('test') && !url.includes('shadow'));
    if (prodUrls.length > 1) {
      // 检查是否是相同的URL（可能在不同环境文件中重复定义）
      const uniqueProdUrls = [...new Set(prodUrls)];
      if (uniqueProdUrls.length > 1) {
        this.issues.push({
          type: 'warning',
          file: 'configuration',
          message: '发现多个不同的生产数据库URL配置',
          suggestion: '确保生产和开发环境使用不同的数据库'
        });
      } else {
        this.issues.push({
          type: 'info',
          file: 'configuration',
          message: '发现重复的生产数据库URL配置（可能在不同环境文件中）',
          suggestion: '这是正常的，确保不同环境使用适当的配置文件'
        });
      }
    }
  }

  private async checkConnectionPoolConfigurations(): Promise<void> {
    console.log('🏊 检查连接池配置...');

    try {
      const configFiles = await glob('**/*config*.ts', {
        cwd: this.projectRoot,
        ignore: ['node_modules/**', 'dist/**']
      });

      let poolConfigs = 0;
      for (const file of configFiles) {
        const fullPath = path.join(this.projectRoot, file);
        const content = readFileSync(fullPath, 'utf-8');
        
        if (content.includes('connectionPool') || content.includes('pool')) {
          poolConfigs++;
          this.issues.push({
            type: 'info',
            file,
            message: '发现连接池配置',
            suggestion: '确保所有连接池配置指向同一数据库'
          });
        }
      }

      if (poolConfigs === 0) {
        this.issues.push({
          type: 'info',
          file: 'configuration',
          message: '未发现显式连接池配置，使用Prisma默认配置'
        });
      }
    } catch (error) {
      this.issues.push({
        type: 'error',
        file: 'system',
        message: `检查连接池配置时出错: ${error}`
      });
    }
  }

  private async checkPrismaSchemas(): Promise<void> {
    console.log('📄 检查Prisma Schema文件...');

    try {
      const schemaFiles = await glob('**/*.prisma', {
        cwd: this.projectRoot,
        ignore: ['node_modules/**']
      });

      if (schemaFiles.length === 0) {
        this.issues.push({
          type: 'error',
          file: 'prisma',
          message: '未找到Prisma Schema文件'
        });
        return;
      }

      if (schemaFiles.length > 1) {
        // 检查是否有多个主要schema文件
        const mainSchemas = schemaFiles.filter(file => 
          file.includes('schema.prisma') && !file.includes('minimal') && !file.includes('test')
        );
        
        if (mainSchemas.length > 1) {
          this.issues.push({
            type: 'warning',
            file: 'prisma',
            message: `发现${mainSchemas.length}个主要Schema文件: ${mainSchemas.join(', ')}`,
            suggestion: '确保只有一个主要的schema.prisma文件'
          });
        }
      }

      // 检查每个schema文件的数据库配置
      for (const schemaFile of schemaFiles) {
        const fullPath = path.join(this.projectRoot, schemaFile);
        const content = readFileSync(fullPath, 'utf-8');
        
        const datasourceBlocks = content.match(/datasource\s+\w+\s*{[^}]+}/g) || [];
        if (datasourceBlocks.length > 1) {
          this.issues.push({
            type: 'warning',
            file: schemaFile,
            message: '发现多个datasource配置块',
            suggestion: '确保只有一个datasource配置'
          });
        } else if (datasourceBlocks.length === 1) {
          this.issues.push({
            type: 'info',
            file: schemaFile,
            message: '发现标准的datasource配置',
            suggestion: '确保datasource配置正确指向环境变量'
          });
        }
      }
    } catch (error) {
      this.issues.push({
        type: 'error',
        file: 'system',
        message: `检查Prisma Schema时出错: ${error}`
      });
    }
  }

  private async checkEnvironmentConfigurations(): Promise<void> {
    console.log('🌍 检查环境变量配置...');

    const envFiles = ['.env.example'];
    
    for (const envFile of envFiles) {
      const fullPath = path.join(this.projectRoot, envFile);
      if (existsSync(fullPath)) {
        const content = readFileSync(fullPath, 'utf-8');
        
        // 检查数据库相关的环境变量
        const dbVars = content.match(/^[A-Z_]*DATABASE[A-Z_]*=/gm) || [];
        
        this.issues.push({
          type: 'info',
          file: envFile,
          message: `发现${dbVars.length}个数据库环境变量: ${dbVars.join(', ')}`,
          suggestion: '确保变量命名清晰，区分主数据库、测试数据库和影子数据库'
        });
      }
    }
  }

  private extractDatabaseUrls(content: string): string[] {
    const urlPattern = /DATABASE_URL\s*=\s*([^\n\r]+)/g;
    const urls: string[] = [];
    let match;
    
    while ((match = urlPattern.exec(content)) !== null) {
      urls.push(match[1].trim());
    }
    
    return urls;
  }

  private isAllowedPrismaClientFile(filePath: string): boolean {
    const allowedPatterns = [
      /^scripts\//,
      /^prisma\//,
      /seed\.ts$/,
      /migration.*\.ts$/,
      /test.*\.ts$/,
      /\.test\.ts$/,
      /\.spec\.ts$/
    ];
    
    return allowedPatterns.some(pattern => pattern.test(filePath));
  }

  private isSingletonImplementation(content: string, lineIndex: number): boolean {
    // 检查是否在单例模式的实现中
    const lines = content.split('\n');
    const currentLine = lines[lineIndex];
    
    // 检查是否在函数内部且有单例检查逻辑
    const beforeLines = lines.slice(Math.max(0, lineIndex - 10), lineIndex);
    const afterLines = lines.slice(lineIndex, Math.min(lines.length, lineIndex + 5));
    
    const contextLines = [...beforeLines, ...afterLines].join('\n');
    
    // 检查单例模式的特征
    const singletonPatterns = [
      /GLOBAL_PRISMA_CLIENT/,
      /getGlobalPrismaClient/,
      /if\s*\(!.*GLOBAL.*\)/,
      /if\s*\(.*===\s*null\)/,
      /if\s*\(!.*CLIENT.*\)/
    ];
    
    return singletonPatterns.some(pattern => pattern.test(contextLines));
  }

  private printReport(): void {
    console.log('\n' + '='.repeat(80));
    console.log('📊 数据库统一性验证报告');
    console.log('='.repeat(80));

    const errors = this.issues.filter(issue => issue.type === 'error');
    const warnings = this.issues.filter(issue => issue.type === 'warning');
    const infos = this.issues.filter(issue => issue.type === 'info');

    console.log(`\n🔴 错误: ${errors.length} 个`);
    errors.forEach(issue => {
      console.log(`   ❌ ${issue.file}${issue.line ? `:${issue.line}` : ''} - ${issue.message}`);
      if (issue.suggestion) {
        console.log(`      💡 建议: ${issue.suggestion}`);
      }
    });

    console.log(`\n🟡 警告: ${warnings.length} 个`);
    warnings.forEach(issue => {
      console.log(`   ⚠️  ${issue.file}${issue.line ? `:${issue.line}` : ''} - ${issue.message}`);
      if (issue.suggestion) {
        console.log(`      💡 建议: ${issue.suggestion}`);
      }
    });

    console.log(`\n🔵 信息: ${infos.length} 个`);
    infos.forEach(issue => {
      console.log(`   ℹ️  ${issue.file}${issue.line ? `:${issue.line}` : ''} - ${issue.message}`);
      if (issue.suggestion) {
        console.log(`      💡 建议: ${issue.suggestion}`);
      }
    });

    console.log('\n' + '='.repeat(80));
    
    if (errors.length === 0) {
      console.log('✅ 数据库统一性验证通过！');
      console.log('🎯 项目中只有一套数据库配置和连接');
    } else {
      console.log('❌ 发现数据库统一性问题，请修复后重新验证');
    }

    console.log('\n📋 数据库统一性最佳实践:');
    console.log('✅ 1. 使用getGlobalPrismaClient()获取数据库连接');
    console.log('✅ 2. 脚本文件可以使用独立的PrismaClient实例');
    console.log('✅ 3. 确保生产、开发、测试环境使用不同的数据库');
    console.log('✅ 4. 只维护一个主要的schema.prisma文件');
    console.log('✅ 5. 使用环境变量管理数据库连接字符串');
    console.log('✅ 6. 配置影子数据库用于安全的迁移操作');
    console.log('✅ 7. 所有应用代码通过单例模式访问数据库');
  }
}

// 运行验证
if (require.main === module) {
  const validator = new DatabaseUnityValidator();
  validator.validateDatabaseUnity().catch(console.error);
}

export { DatabaseUnityValidator };