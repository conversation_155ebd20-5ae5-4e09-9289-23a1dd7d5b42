#!/usr/bin/env tsx

/**
 * 虚假实现检测器
 * 检测项目中的虚假实现、空实现和模拟代码
 */

import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface FakeImplementation {
  file: string;
  line: number;
  type: 'fake_client' | 'empty_implementation' | 'mock_return' | 'placeholder_comment' | 'fake_success';
  severity: 'critical' | 'high' | 'medium' | 'low';
  description: string;
  code: string;
}

class FakeImplementationDetector {
  private fakeImplementations: FakeImplementation[] = [];
  private scannedFiles: number = 0;
  private readonly srcDir = path.join(process.cwd(), 'src');
  
  // 虚假实现模式
  private readonly patterns = {
    // 虚假客户端创建
    fakeClient: [
      /createFakeClient|createMockClient|FakeRedis|MockRedis/i,
      /虚假.*客户端|模拟.*客户端/,
      /fake.*client|mock.*client/i
    ],
    
    // 空实现
    emptyImplementation: [
      /\/\/ 使用现有的.*基础设施/,
      /\/\/ 这里可以启动项目中已有的/,
      /\/\/ TODO.*实现/,
      /throw new Error\(['"]Not implemented['"]\)/
    ],
    
    // 虚假返回值
    mockReturn: [
      /return true; *\/\/ 虚假/,
      /return 'PONG'; *\/\/ 模拟/,
      /return.*; *\/\/ fake/i,
      /mockResolvedValue|mockReturnValue/
    ],
    
    // 占位符注释
    placeholderComment: [
      /\/\/ 占位符实现/,
      /\/\/ 临时实现/,
      /\/\/ FIXME.*虚假/,
      /\/\/ HACK.*模拟/
    ],
    
    // 虚假成功状态
    fakeSuccess: [
      /logger\.info\(['"](✅|成功).*启动完成['"]/,
      /console\.log\(['"](✅|成功)/,
      /return.*success.*true.*fake/i
    ]
  };
  
  // 排除的文件模式
  private readonly excludePatterns = [
    '**/node_modules/**',
    '**/tests/**',
    '**/test/**',
    '**/*.test.ts',
    '**/*.spec.ts',
    '**/mock/**',
    '**/mocks/**'
  ];

  async detectFakeImplementations(): Promise<void> {
    console.log('🔍 开始检测虚假实现...');
    
    const files = await this.getSourceFiles();
    
    for (const file of files) {
      await this.analyzeFile(file);
    }
    
    this.generateReport();
  }

  private async getSourceFiles(): Promise<string[]> {
    const pattern = path.join(this.srcDir, '**/*.{ts,js}').replace(/\\/g, '/');
    const files = await glob(pattern, {
      ignore: this.excludePatterns
    });
    
    return files.filter(file => {
      // 额外过滤测试相关文件
      const relativePath = path.relative(this.srcDir, file);
      return !relativePath.includes('test') && 
             !relativePath.includes('mock') &&
             !relativePath.includes('spec');
    });
  }

  private async analyzeFile(filePath: string): Promise<void> {
    try {
      const content = await fs.promises.readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      
      // 增加扫描文件计数
      this.scannedFiles++;
      
      lines.forEach((line, index) => {
        this.checkLine(filePath, line, index + 1);
      });
      
      // 检查特殊的虚假实现模式
      this.checkSpecialPatterns(filePath, content);
      
    } catch (error) {
      console.error(`❌ 分析文件失败: ${filePath}`, error);
    }
  }

  private checkLine(filePath: string, line: string, lineNumber: number): void {
    const trimmedLine = line.trim();
    
    // 检查虚假客户端
    for (const pattern of this.patterns.fakeClient) {
      if (pattern.test(trimmedLine)) {
        this.addFakeImplementation({
          file: filePath,
          line: lineNumber,
          type: 'fake_client',
          severity: 'critical',
          description: '检测到虚假客户端实现',
          code: trimmedLine
        });
      }
    }
    
    // 检查空实现
    for (const pattern of this.patterns.emptyImplementation) {
      if (pattern.test(trimmedLine)) {
        this.addFakeImplementation({
          file: filePath,
          line: lineNumber,
          type: 'empty_implementation',
          severity: 'high',
          description: '检测到空实现或占位符代码',
          code: trimmedLine
        });
      }
    }
    
    // 检查虚假返回值
    for (const pattern of this.patterns.mockReturn) {
      if (pattern.test(trimmedLine)) {
        this.addFakeImplementation({
          file: filePath,
          line: lineNumber,
          type: 'mock_return',
          severity: 'medium',
          description: '检测到模拟返回值',
          code: trimmedLine
        });
      }
    }
    
    // 检查占位符注释
    for (const pattern of this.patterns.placeholderComment) {
      if (pattern.test(trimmedLine)) {
        this.addFakeImplementation({
          file: filePath,
          line: lineNumber,
          type: 'placeholder_comment',
          severity: 'medium',
          description: '检测到占位符注释',
          code: trimmedLine
        });
      }
    }
    
    // 检查虚假成功状态
    for (const pattern of this.patterns.fakeSuccess) {
      if (pattern.test(trimmedLine)) {
        this.addFakeImplementation({
          file: filePath,
          line: lineNumber,
          type: 'fake_success',
          severity: 'high',
          description: '检测到虚假成功状态记录',
          code: trimmedLine
        });
      }
    }
  }

  private checkSpecialPatterns(filePath: string, content: string): void {
    // 检查服务启动模块中的空实现
    if (filePath.includes('services-startup.ts')) {
      const emptyServicePattern = /async start\w+Service\(\)[^}]*{[^}]*logger\.info\(['"](✅|成功)[^}]*}(?!.*await|.*\w+\.start\(\))/gs;
      const matches = content.matchAll(emptyServicePattern);
      
      for (const match of matches) {
        const lineNumber = content.substring(0, match.index).split('\n').length;
        this.addFakeImplementation({
          file: filePath,
          line: lineNumber,
          type: 'empty_implementation',
          severity: 'critical',
          description: '检测到服务启动方法中的空实现',
          code: match[0].substring(0, 100) + '...'
        });
      }
    }
    
    // 检查健康检查中的硬编码状态
    const hardcodedHealthPattern = /healthStatus\.[\w]+\s*=\s*['"]healthy['"]/g;
    const healthMatches = content.matchAll(hardcodedHealthPattern);
    
    for (const match of healthMatches) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      this.addFakeImplementation({
        file: filePath,
        line: lineNumber,
        type: 'fake_success',
        severity: 'high',
        description: '检测到硬编码的健康状态',
        code: match[0]
      });
    }
  }

  private addFakeImplementation(fake: FakeImplementation): void {
    this.fakeImplementations.push(fake);
  }

  private generateReport(): void {
    console.log('\n📊 虚假实现检测报告');
    console.log('=' .repeat(50));
    
    if (this.fakeImplementations.length === 0) {
      console.log('✅ 未检测到虚假实现');
      return;
    }
    
    // 按严重程度分组
    const bySeverity = this.groupBySeverity();
    
    // 输出统计信息
    console.log(`\n📈 检测统计:`);
    console.log(`  🔴 严重: ${bySeverity.critical?.length || 0}`);
    console.log(`  🟠 高: ${bySeverity.high?.length || 0}`);
    console.log(`  🟡 中: ${bySeverity.medium?.length || 0}`);
    console.log(`  🟢 低: ${bySeverity.low?.length || 0}`);
    console.log(`  📊 总计: ${this.fakeImplementations.length}`);
    
    // 输出详细信息
    console.log('\n🔍 详细检测结果:');
    
    ['critical', 'high', 'medium', 'low'].forEach(severity => {
      const items = bySeverity[severity as keyof typeof bySeverity];
      if (items && items.length > 0) {
        console.log(`\n${this.getSeverityIcon(severity as any)} ${severity.toUpperCase()} (${items.length}):`);
        items.forEach((item, index) => {
          const relativePath = path.relative(process.cwd(), item.file);
          console.log(`  ${index + 1}. ${relativePath}:${item.line}`);
          console.log(`     类型: ${this.getTypeDescription(item.type)}`);
          console.log(`     描述: ${item.description}`);
          console.log(`     代码: ${item.code}`);
          console.log('');
        });
      }
    });
    
    // 输出修复建议
    this.generateFixSuggestions();
    
    // 保存报告到文件
    this.saveReportToFile();
  }

  private groupBySeverity() {
    return this.fakeImplementations.reduce((acc, item) => {
      if (!acc[item.severity]) {
        acc[item.severity] = [];
      }
      acc[item.severity].push(item);
      return acc;
    }, {} as Record<string, FakeImplementation[]>);
  }

  private getSeverityIcon(severity: string): string {
    const icons = {
      critical: '🔴',
      high: '🟠',
      medium: '🟡',
      low: '🟢'
    };
    return icons[severity as keyof typeof icons] || '⚪';
  }

  private getTypeDescription(type: string): string {
    const descriptions = {
      fake_client: '虚假客户端',
      empty_implementation: '空实现',
      mock_return: '模拟返回值',
      placeholder_comment: '占位符注释',
      fake_success: '虚假成功状态'
    };
    return descriptions[type as keyof typeof descriptions] || type;
  }

  private generateFixSuggestions(): void {
    console.log('\n💡 修复建议:');
    console.log('=' .repeat(30));
    
    const bySeverity = this.groupBySeverity();
    
    if (bySeverity.critical?.length > 0) {
      console.log('🔴 严重问题修复建议:');
      console.log('  1. 移除所有虚假客户端实现，使用真实的服务连接');
      console.log('  2. 实现所有空的服务启动方法，或明确标记为禁用');
      console.log('  3. 确保所有关键服务都有真实的实现');
    }
    
    if (bySeverity.high?.length > 0) {
      console.log('\n🟠 高优先级修复建议:');
      console.log('  1. 替换所有占位符代码为真实实现');
      console.log('  2. 移除虚假的成功状态记录');
      console.log('  3. 实现真实的健康检查逻辑');
    }
    
    if (bySeverity.medium?.length > 0) {
      console.log('\n🟡 中优先级修复建议:');
      console.log('  1. 清理测试代码中的模拟实现');
      console.log('  2. 更新占位符注释为实际的实现计划');
      console.log('  3. 确保返回值反映真实状态');
    }
    
    console.log('\n📋 通用修复原则:');
    console.log('  • 诚实原则: 代码应该诚实地反映实际功能');
    console.log('  • 失败快速: 如果功能未实现，应该明确失败而不是假装成功');
    console.log('  • 明确标记: 如果某个功能被禁用，应该明确标记和文档化');
    console.log('  • 渐进实现: 优先实现核心功能，非核心功能可以明确标记为未实现');
  }

  private saveReportToFile(): void {
    const docsDir = path.join(process.cwd(), 'docs');
    
    // 确保 docs 目录存在
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }
    
    const reportPath = path.join(docsDir, '通用虚假实现检测报告.md');
    const markdownContent = this.generateMarkdownReport();
    
    fs.writeFileSync(reportPath, markdownContent, 'utf-8');
    
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  }

  private generateMarkdownReport(): string {
    const timestamp = new Date().toLocaleString('zh-CN');
    const bySeverity = this.groupBySeverity();
    
    let markdown = `# 通用虚假实现检测报告\n\n`;
    markdown += `**生成时间**: ${timestamp}\n\n`;
    
    // 脚本信息
    markdown += `## 🔧 检测脚本信息\n\n`;
    markdown += `| 属性 | 值 |\n`;
    markdown += `|------|-----|\n`;
    markdown += `| 脚本名称 | detect-fake-implementations.ts |\n`;
    markdown += `| 脚本类型 | 通用虚假实现检测器 |\n`;
    markdown += `| 检测范围 | 全项目代码文件 |\n`;
    markdown += `| 排除目录 | node_modules, .git, dist, build |\n`;
    markdown += `| 排除文件 | 测试文件(*test*, *spec*, *mock*) |\n`;
    markdown += `| 检测模式 | 正则表达式模式匹配 |\n`;
    markdown += `| 扫描文件数 | ${this.scannedFiles} |\n\n`;
    
    // 检测规则说明
    markdown += `## 📋 检测规则说明\n\n`;
    markdown += `### 检测类型\n\n`;
    markdown += `1. **虚假客户端** (fake_client)\n`;
    markdown += `   - 检测模式: \`createFakeClient\`, \`mockClient\`, \`fakeRedisClient\`\n`;
    markdown += `   - 严重程度: 🔴 严重\n`;
    markdown += `   - 描述: 创建虚假或模拟的客户端连接\n\n`;
    
    markdown += `2. **空实现** (empty_implementation)\n`;
    markdown += `   - 检测模式: 空方法体、仅返回空值的方法\n`;
    markdown += `   - 严重程度: 🟠 高危\n`;
    markdown += `   - 描述: 方法体为空或仅包含占位符代码\n\n`;
    
    markdown += `3. **模拟返回值** (mock_return)\n`;
    markdown += `   - 检测模式: \`return { success: true }\`, \`return true\`\n`;
    markdown += `   - 严重程度: 🟡 中等\n`;
    markdown += `   - 描述: 返回硬编码的模拟值\n\n`;
    
    markdown += `4. **占位符注释** (placeholder_comment)\n`;
    markdown += `   - 检测模式: \`TODO\`, \`FIXME\`, \`placeholder\`\n`;
    markdown += `   - 严重程度: 🟡 中等\n`;
    markdown += `   - 描述: 包含占位符或临时实现的注释\n\n`;
    
    markdown += `5. **虚假成功状态** (fake_success)\n`;
    markdown += `   - 检测模式: 虚假的成功日志记录\n`;
    markdown += `   - 严重程度: 🟠 高危\n`;
    markdown += `   - 描述: 记录虚假的成功状态或操作完成\n\n`;
    
    // 概览统计
    markdown += `## 📊 检测概览\n\n`;
    markdown += `| 严重程度 | 数量 |\n`;
    markdown += `|---------|------|\n`;
    markdown += `| 🔴 严重 | ${bySeverity.critical?.length || 0} |\n`;
    markdown += `| 🟠 高危 | ${bySeverity.high?.length || 0} |\n`;
    markdown += `| 🟡 中等 | ${bySeverity.medium?.length || 0} |\n`;
    markdown += `| 🟢 低危 | ${bySeverity.low?.length || 0} |\n`;
    markdown += `| **总计** | **${this.fakeImplementations.length}** |\n\n`;
    
    if (this.fakeImplementations.length === 0) {
      markdown += `## ✅ 检测结果\n\n`;
      markdown += `恭喜！未检测到任何虚假实现。\n\n`;
      return markdown;
    }
    
    // 详细检测结果
    markdown += `## 🔍 详细检测结果\n\n`;
    
    ['critical', 'high', 'medium', 'low'].forEach(severity => {
      const items = bySeverity[severity as keyof typeof bySeverity];
      if (items && items.length > 0) {
        const icon = this.getSeverityIcon(severity as any);
        markdown += `### ${icon} ${severity.toUpperCase()} 级别问题 (${items.length}项)\n\n`;
        
        items.forEach((item, index) => {
          const relativePath = path.relative(process.cwd(), item.file);
          markdown += `#### ${index + 1}. ${relativePath}:${item.line}\n\n`;
          markdown += `- **类型**: ${this.getTypeDescription(item.type)}\n`;
          markdown += `- **描述**: ${item.description}\n`;
          markdown += `- **代码**:\n\n`;
          markdown += `\`\`\`typescript\n${item.code}\n\`\`\`\n\n`;
        });
      }
    });
    
    // 修复建议
    markdown += `## 💡 修复建议\n\n`;
    
    if (bySeverity.critical?.length > 0) {
      markdown += `### 🔴 严重问题修复建议\n\n`;
      markdown += `1. **移除虚假客户端**: 移除所有虚假客户端实现，使用真实的服务连接\n`;
      markdown += `2. **实现空服务**: 实现所有空的服务启动方法，或明确标记为禁用\n`;
      markdown += `3. **确保真实实现**: 确保所有关键服务都有真实的实现\n\n`;
    }
    
    if (bySeverity.high?.length > 0) {
      markdown += `### 🟠 高优先级修复建议\n\n`;
      markdown += `1. **替换占位符**: 替换所有占位符代码为真实实现\n`;
      markdown += `2. **移除虚假状态**: 移除虚假的成功状态记录\n`;
      markdown += `3. **实现健康检查**: 实现真实的健康检查逻辑\n\n`;
    }
    
    if (bySeverity.medium?.length > 0) {
      markdown += `### 🟡 中优先级修复建议\n\n`;
      markdown += `1. **清理测试代码**: 清理测试代码中的模拟实现\n`;
      markdown += `2. **更新注释**: 更新占位符注释为实际的实现计划\n`;
      markdown += `3. **确保真实返回**: 确保返回值反映真实状态\n\n`;
    }
    
    markdown += `### 📋 通用修复原则\n\n`;
    markdown += `- **诚实原则**: 代码应该诚实地反映实际功能\n`;
    markdown += `- **失败快速**: 如果功能未实现，应该明确失败而不是假装成功\n`;
    markdown += `- **明确标记**: 如果某个功能被禁用，应该明确标记和文档化\n`;
    markdown += `- **渐进实现**: 优先实现核心功能，非核心功能可以明确标记为未实现\n\n`;
    
    // 问题类型统计
    const typeStats = this.fakeImplementations.reduce((acc, item) => {
      acc[item.type] = (acc[item.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    markdown += `## 📈 问题类型统计\n\n`;
    markdown += `| 问题类型 | 数量 | 描述 |\n`;
    markdown += `|---------|------|------|\n`;
    Object.entries(typeStats).forEach(([type, count]) => {
      markdown += `| ${this.getTypeDescription(type)} | ${count} | ${this.getTypeDetailDescription(type)} |\n`;
    });
    
    return markdown;
  }

  private getTypeDetailDescription(type: string): string {
    const descriptions = {
      fake_client: '创建虚假或模拟的客户端连接',
      empty_implementation: '方法体为空或仅包含占位符代码',
      mock_return: '返回硬编码的模拟值',
      placeholder_comment: '包含占位符或临时实现的注释',
      fake_success: '记录虚假的成功状态或操作完成'
    };
    return descriptions[type as keyof typeof descriptions] || '未知类型';
  }
}

// 主函数
async function main(): Promise<void> {
  try {
    const detector = new FakeImplementationDetector();
    await detector.detectFakeImplementations();
  } catch (error) {
    console.error('❌ 检测过程中发生错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { FakeImplementationDetector };