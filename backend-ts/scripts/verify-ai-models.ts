#!/usr/bin/env ts-node
/**
 * AI模型可访问性验证脚本
 * 验证所有配置的AI模型是否真实可访问
 */

// 首先加载环境变量
import { config } from 'dotenv';
import path from 'path';

// 加载.env文件
config({ path: path.resolve(__dirname, '../.env') });

import { getEnvironmentManager } from '../src/shared/infrastructure/config/environment/environment-extensions';
import { OpenAIProvider } from '../src/contexts/ai-reasoning/infrastructure/llm-providers/openai-provider';
import { AnthropicProvider } from '../src/contexts/ai-reasoning/infrastructure/llm-providers/anthropic-provider';
import { GeminiProvider } from '../src/contexts/ai-reasoning/infrastructure/llm-providers/gemini-provider';
import { Logger } from 'winston';
import { createLogger, format, transports } from 'winston';

// 创建日志记录器
const logger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp(),
    format.colorize(),
    format.simple()
  ),
  transports: [
    new transports.Console()
  ]
});

// 验证结果接口
interface VerificationResult {
  provider: string;
  model: string;
  isAvailable: boolean;
  isAccessible: boolean;
  error?: string;
  responseTime?: number;
  apiKeyValid: boolean;
  apiKeyFormat: string;
}

// 测试请求
const testRequest = {
  prompt: '请简单回复"测试成功"',
  temperature: 0.1,
  maxTokens: 50
};

class AIModelVerifier {
  private results: VerificationResult[] = [];

  async verifyAllModels(): Promise<void> {
    console.log('🔍 开始验证AI模型可访问性...');
    console.log('=' .repeat(60));

    // 验证环境配置
    await this.verifyEnvironmentConfig();

    // 验证OpenAI模型
    await this.verifyOpenAIModels();

    // 验证Anthropic模型
    await this.verifyAnthropicModels();

    // 验证Gemini模型
    await this.verifyGeminiModels();

    // 生成报告
    this.generateReport();
  }

  private async verifyEnvironmentConfig(): Promise<void> {
    console.log('\n📋 环境配置检查:');
    
    try {
      const envManager = getEnvironmentManager();
      const apiConfig = envManager.getApiKeysConfig();
      
      // 检查OpenAI配置
      const openaiKey = apiConfig.openai.apiKey;
      const openaiUrl = apiConfig.openai.baseUrl;
      console.log(`  OpenAI API Key: ${this.maskApiKey(openaiKey)} (${this.validateOpenAIKey(openaiKey) ? '✅ 有效' : '❌ 无效'})`);
      console.log(`  OpenAI Base URL: ${openaiUrl}`);
      
      // 检查Anthropic配置
      const anthropicKey = apiConfig.anthropic.apiKey;
      const anthropicUrl = apiConfig.anthropic.baseUrl;
      console.log(`  Anthropic API Key: ${this.maskApiKey(anthropicKey)} (${this.validateAnthropicKey(anthropicKey, anthropicUrl) ? '✅ 有效' : '❌ 无效'})`);
      console.log(`  Anthropic Base URL: ${anthropicUrl}`);
      
      // 检查Gemini配置
      const geminiKey = apiConfig.gemini.apiKey;
      console.log(`  Gemini API Key: ${this.maskApiKey(geminiKey)} (${this.validateGeminiKey(geminiKey) ? '✅ 有效' : '❌ 无效'})`);
      
      // 检查默认模型
      const defaultModel = process.env.DEFAULT_LLM_MODEL;
      console.log(`  默认模型: ${defaultModel}`);
      
    } catch (error) {
      console.error('❌ 环境配置检查失败:', error);
    }
  }

  private async verifyOpenAIModels(): Promise<void> {
    console.log('\n🤖 OpenAI模型验证:');
    
    try {
      const provider = new OpenAIProvider(logger);
      const isProviderAvailable = await provider.isAvailable();
      
      console.log(`  提供者可用性: ${isProviderAvailable ? '✅' : '❌'}`);
      
      if (isProviderAvailable) {
        // 测试主要模型
        const modelsToTest = ['gpt-4o-mini', 'gpt-4o', 'gpt-3.5-turbo'];
        
        for (const model of modelsToTest) {
          await this.testModel(provider, 'OpenAI', model);
        }
      } else {
        this.results.push({
          provider: 'OpenAI',
          model: 'all',
          isAvailable: false,
          isAccessible: false,
          error: 'Provider not available',
          apiKeyValid: false,
          apiKeyFormat: 'invalid'
        });
      }
    } catch (error) {
      console.error('❌ OpenAI验证失败:', error);
    }
  }

  private async verifyAnthropicModels(): Promise<void> {
    console.log('\n🧠 Anthropic模型验证:');
    
    try {
      const provider = new AnthropicProvider(logger);
      const isProviderAvailable = await provider.isAvailable();
      
      console.log(`  提供者可用性: ${isProviderAvailable ? '✅' : '❌'}`);
      
      if (isProviderAvailable) {
        // 测试主要模型
        const modelsToTest = ['claude-sonnet-4-20250514', 'claude-3-5-sonnet-20241022', 'claude-3-haiku-20240307'];
        
        for (const model of modelsToTest) {
          await this.testModel(provider, 'Anthropic', model);
        }
      } else {
        this.results.push({
          provider: 'Anthropic',
          model: 'all',
          isAvailable: false,
          isAccessible: false,
          error: 'Provider not available',
          apiKeyValid: false,
          apiKeyFormat: 'invalid'
        });
      }
    } catch (error) {
      console.error('❌ Anthropic验证失败:', error);
    }
  }

  private async verifyGeminiModels(): Promise<void> {
    console.log('\n🌟 Gemini模型验证:');
    
    try {
      const provider = new GeminiProvider(logger);
      const isProviderAvailable = await provider.isAvailable();
      
      console.log(`  提供者可用性: ${isProviderAvailable ? '✅' : '❌'}`);
      
      if (isProviderAvailable) {
        // 测试主要模型
        const modelsToTest = ['gemini-2.5-flash', 'gemini-1.5-pro', 'gemini-1.5-flash'];
        
        for (const model of modelsToTest) {
          await this.testModel(provider, 'Google', model);
        }
      } else {
        this.results.push({
          provider: 'Google',
          model: 'all',
          isAvailable: false,
          isAccessible: false,
          error: 'Provider not available',
          apiKeyValid: false,
          apiKeyFormat: 'invalid'
        });
      }
    } catch (error) {
      console.error('❌ Gemini验证失败:', error);
    }
  }

  private async testModel(provider: any, providerName: string, model: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`    测试模型: ${model}...`);
      
      // 尝试发送测试请求
      const response = await Promise.race([
        provider.reason({ ...testRequest, model }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('请求超时')), 30000)
        )
      ]);
      
      const responseTime = Date.now() - startTime;
      
      if (response && response.content) {
        console.log(`      ✅ 成功 (${responseTime}ms) - 响应: ${response.content.substring(0, 50)}...`);
        
        this.results.push({
          provider: providerName,
          model,
          isAvailable: true,
          isAccessible: true,
          responseTime,
          apiKeyValid: true,
          apiKeyFormat: 'valid'
        });
      } else {
        console.log(`      ❌ 失败 - 空响应`);
        
        this.results.push({
          provider: providerName,
          model,
          isAvailable: true,
          isAccessible: false,
          error: 'Empty response',
          responseTime,
          apiKeyValid: true,
          apiKeyFormat: 'valid'
        });
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      console.log(`      ❌ 失败 (${responseTime}ms) - ${errorMessage}`);
      
      this.results.push({
        provider: providerName,
        model,
        isAvailable: true,
        isAccessible: false,
        error: errorMessage,
        responseTime,
        apiKeyValid: true,
        apiKeyFormat: 'valid'
      });
    }
  }

  private validateOpenAIKey(key: string): boolean {
    return key && key !== 'placeholder-key' && key.startsWith('sk-') && key.length > 40;
  }

  private validateAnthropicKey(key: string, baseUrl: string): boolean {
    if (!key || key === 'placeholder-key') return false;
    
    // 如果是中转服务，使用OpenAI格式检查
    if (baseUrl.includes('gptsapi.net')) {
      return key.startsWith('sk-') && key.length > 40;
    }
    
    // 原生Anthropic API
    return key.startsWith('sk-ant-') && key.length > 40;
  }

  private validateGeminiKey(key: string): boolean {
    return key && key !== 'placeholder-key' && key.startsWith('AIza') && key.length > 30;
  }

  private maskApiKey(key: string): string {
    if (!key || key.length < 8) return '未配置';
    return key.substring(0, 8) + '*'.repeat(Math.max(0, key.length - 12)) + key.substring(Math.max(8, key.length - 4));
  }

  private generateReport(): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 验证报告总结:');
    console.log('='.repeat(60));
    
    const totalModels = this.results.length;
    const accessibleModels = this.results.filter(r => r.isAccessible).length;
    const availableProviders = [...new Set(this.results.filter(r => r.isAvailable).map(r => r.provider))];
    const accessibleProviders = [...new Set(this.results.filter(r => r.isAccessible).map(r => r.provider))];
    
    console.log(`\n📈 总体统计:`);
    console.log(`  总测试模型数: ${totalModels}`);
    console.log(`  可访问模型数: ${accessibleModels}`);
    console.log(`  成功率: ${totalModels > 0 ? ((accessibleModels / totalModels) * 100).toFixed(1) : 0}%`);
    console.log(`  可用提供者: ${availableProviders.join(', ')}`);
    console.log(`  可访问提供者: ${accessibleProviders.join(', ')}`);
    
    console.log(`\n📋 详细结果:`);
    
    // 按提供者分组显示结果
    const groupedResults = this.results.reduce((acc, result) => {
      if (!acc[result.provider]) acc[result.provider] = [];
      acc[result.provider].push(result);
      return acc;
    }, {} as Record<string, VerificationResult[]>);
    
    Object.entries(groupedResults).forEach(([provider, results]) => {
      console.log(`\n  ${provider}:`);
      results.forEach(result => {
        const status = result.isAccessible ? '✅' : '❌';
        const time = result.responseTime ? `${result.responseTime}ms` : 'N/A';
        const error = result.error ? ` (${result.error})` : '';
        console.log(`    ${status} ${result.model} - ${time}${error}`);
      });
    });
    
    console.log(`\n🔧 建议:`);
    
    if (accessibleModels === 0) {
      console.log(`  ❌ 没有可访问的模型，请检查:`);
      console.log(`     1. API密钥是否正确配置`);
      console.log(`     2. 网络连接是否正常`);
      console.log(`     3. API服务是否可用`);
    } else if (accessibleModels < totalModels) {
      console.log(`  ⚠️  部分模型不可访问，建议:`);
      console.log(`     1. 检查失败模型的API密钥`);
      console.log(`     2. 验证模型名称是否正确`);
      console.log(`     3. 确认账户是否有相应模型的访问权限`);
    } else {
      console.log(`  ✅ 所有模型都可正常访问！`);
    }
    
    // 检查默认模型
    const defaultModel = process.env.DEFAULT_LLM_MODEL;
    if (defaultModel) {
      const defaultModelAccessible = this.results.some(r => 
        r.model === defaultModel && r.isAccessible
      );
      
      if (defaultModelAccessible) {
        console.log(`  ✅ 默认模型 ${defaultModel} 可正常访问`);
      } else {
        console.log(`  ❌ 默认模型 ${defaultModel} 不可访问，建议更换`);
      }
    }
    
    console.log('\n' + '='.repeat(60));
  }
}

// 主函数
async function main() {
  try {
    const verifier = new AIModelVerifier();
    await verifier.verifyAllModels();
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { AIModelVerifier };