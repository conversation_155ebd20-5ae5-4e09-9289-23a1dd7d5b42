/**
 * 检查环境变量配置
 */

import * as dotenv from 'dotenv';
import { resolve } from 'path';

// 加载环境变量
dotenv.config({ path: resolve(__dirname, '../../.env') });

console.log('🔍 检查环境变量配置:\n');

const envVars = [
  'OPENAI_API_KEY',
  'OPENAI_BASE_URL', 
  'GEMINI_API_KEY',
  'ANTHROPIC_API_KEY',
  'ANTHROPIC_BASE_URL',
  'DEFAULT_LLM_MODEL'
];

envVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // 对于API密钥，只显示前几位和后几位
    if (varName.includes('API_KEY')) {
      const masked = value.length > 10 
        ? `${value.substring(0, 6)}...${value.substring(value.length - 4)}`
        : '***';
      console.log(`✅ ${varName}: ${masked}`);
    } else {
      console.log(`✅ ${varName}: ${value}`);
    }
  } else {
    console.log(`❌ ${varName}: 未设置`);
  }
});

console.log('\n📋 原始环境变量值:');
console.log('OPENAI_API_KEY length:', process.env.OPENAI_API_KEY?.length || 0);
console.log('GEMINI_API_KEY length:', process.env.GEMINI_API_KEY?.length || 0);
console.log('ANTHROPIC_API_KEY length:', process.env.ANTHROPIC_API_KEY?.length || 0);
