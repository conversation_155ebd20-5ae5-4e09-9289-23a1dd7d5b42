/**
 * AI集成测试脚本
 * 测试OpenAI、Gemini等AI服务的集成状态
 */

import 'reflect-metadata';
import { config } from 'dotenv';
import { Container } from 'inversify';
import { TYPES } from '../../src/shared/infrastructure/di/types/index';
import { getLogger } from '../../src/config/logging';
import { getEnvironmentManager } from '../../src/shared/infrastructure/config/environment/environment-extensions';

// 加载环境变量
config();

// 导入AI相关服务
import { AIReasoningApplicationService } from '../../src/contexts/ai-reasoning/application/services/ai-reasoning-application-service';
import { LLMRouter } from '../../src/contexts/ai-reasoning/infrastructure/llm-providers/llm-router';

const logger = getLogger('ai-integration-test');

async function testAIIntegration() {
  console.log('🤖 开始AI集成测试...\n');

  try {
    // 1. 检查环境变量配置
    console.log('📋 检查环境变量配置:');
    const envManager = getEnvironmentManager();
    const apiKeysConfig = envManager.getApiKeysConfig();
    
    console.log(`   OpenAI API Key: ${apiKeysConfig.openai.apiKey ? '✅ 已配置' : '❌ 未配置'}`);
    console.log(`   OpenAI Base URL: ${apiKeysConfig.openai.baseUrl || 'https://api.openai.com/v1'}`);
    console.log(`   Gemini API Key: ${apiKeysConfig.gemini.apiKey ? '✅ 已配置' : '❌ 未配置'}`);
    console.log(`   Anthropic API Key: ${apiKeysConfig.anthropic.apiKey ? '✅ 已配置' : '❌ 未配置'}\n`);

    // 2. 测试基本AI推理服务
    console.log('🧠 测试AI推理服务:');
    try {
      // 创建一个简单的容器来测试服务
      const container = new Container();
      container.bind(TYPES.Logger).toConstantValue(logger);
      
      // 绑定AI推理服务
      container.bind(TYPES.AIReasoning.AIReasoningApplicationService)
        .to(AIReasoningApplicationService)
        .inSingletonScope();

      const aiService = container.get<AIReasoningApplicationService>(
        TYPES.AIReasoning.AIReasoningApplicationService
      );

      // 执行健康检查
      const healthCheck = await aiService.healthCheck();
      console.log(`   健康检查: ${healthCheck.status === 'healthy' ? '✅' : '❌'} ${healthCheck.message}`);

      // 执行简单推理测试
      const testResult = await aiService.executeReasoning({
        query: '测试AI推理功能是否正常',
        analysisDepth: 'quick'
      });

      console.log(`   推理测试: ${testResult.success ? '✅' : '❌'} ${testResult.message || '推理完成'}`);
      
    } catch (error) {
      console.log(`   AI推理服务: ❌ ${error instanceof Error ? error.message : '未知错误'}`);
    }

    // 3. 测试LLM提供者
    console.log('\n🔌 测试LLM提供者:');
    
    // 测试OpenAI
    try {
      const { OpenAIProvider } = await import('../../src/contexts/ai-reasoning/infrastructure/llm-providers/openai-provider');
      const openAIProvider = new (OpenAIProvider as any)(logger);
      
      const isAvailable = await openAIProvider.isAvailable();
      console.log(`   OpenAI Provider: ${isAvailable ? '✅ 可用' : '❌ 不可用'}`);
      
      if (isAvailable) {
        console.log(`   支持的模型: ${openAIProvider.supportedModels.join(', ')}`);
      }
    } catch (error) {
      console.log(`   OpenAI Provider: ❌ ${error instanceof Error ? error.message : '加载失败'}`);
    }

    // 测试Gemini
    try {
      const { GeminiProvider } = await import('../../src/contexts/ai-reasoning/infrastructure/llm-providers/gemini-provider');
      const geminiProvider = new (GeminiProvider as any)(logger);
      
      const isAvailable = await geminiProvider.isAvailable();
      console.log(`   Gemini Provider: ${isAvailable ? '✅ 可用' : '❌ 不可用'}`);
      
      if (isAvailable) {
        console.log(`   支持的模型: ${geminiProvider.supportedModels.join(', ')}`);
      }
    } catch (error) {
      console.log(`   Gemini Provider: ❌ ${error instanceof Error ? error.message : '加载失败'}`);
    }

    // 4. 测试实际AI调用
    console.log('\n🚀 测试实际AI调用:');
    
    try {
      // 测试简单的AI推理调用
      const testPrompt = '请简单回答：1+1等于多少？';
      
      // 这里可以添加实际的AI调用测试
      console.log(`   测试提示: "${testPrompt}"`);
      console.log('   AI调用测试: ⏳ 跳过实际调用（避免消耗API配额）');
      
    } catch (error) {
      console.log(`   AI调用测试: ❌ ${error instanceof Error ? error.message : '调用失败'}`);
    }

    console.log('\n✅ AI集成测试完成');

  } catch (error) {
    console.error('❌ AI集成测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testAIIntegration().catch(console.error);
}

export { testAIIntegration };
