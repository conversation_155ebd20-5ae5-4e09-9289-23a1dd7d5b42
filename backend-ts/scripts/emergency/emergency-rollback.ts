#!/usr/bin/env tsx

/**
 * 紧急回滚脚本
 * 修复大规模硬编码价格消除器造成的破坏
 */

import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';

interface RollbackAction {
  file: string;
  description: string;
  fixes: Array<{
    oldCode: string;
    newCode: string;
  }>;
}

class EmergencyRollback {
  private actions: RollbackAction[] = [];
  private readonly srcPath = path.join(process.cwd(), 'src');

  async runEmergencyRollback(): Promise<void> {
    console.log('🚨 紧急回滚开始 - 修复大规模消除器造成的破坏...');
    console.log('='.repeat(80));

    try {
      // 1. 修复被错误替换的数字1和2
      await this.fixIncorrectNumberReplacements();
      
      // 2. 修复被破坏的数组定义
      await this.fixBrokenArrayDefinitions();
      
      // 3. 修复被破坏的函数调用
      await this.fixBrokenFunctionCalls();
      
      // 4. 修复被破坏的配置和常量
      await this.fixBrokenConfigurations();
      
      // 5. 应用所有回滚修复
      await this.applyRollbackFixes();
      
      // 6. 生成回滚报告
      this.generateRollbackReport();
      
    } catch (error) {
      console.error('❌ 紧急回滚失败:', error);
    }
  }

  private async fixIncorrectNumberReplacements(): Promise<void> {
    console.log('\n🔧 修复被错误替换的数字1和2...');
    
    const files = this.getAllTSFiles(this.srcPath);
    
    for (const file of files) {
      if (this.isTestFile(file)) continue;
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        const fixes: Array<{oldCode: string; newCode: string}> = [];
        
        // 修复被错误标记的数字1
        const number1Violations = content.match(/🚨 硬编码价格1违规[^}]+/g);
        if (number1Violations) {
          for (const violation of number1Violations) {
            // 提取原始代码
            const match = violation.match(/🚨 硬编码价格1违规 - (.+)/);
            if (match) {
              const originalCode = match[1].trim();
              // 这些很可能是正常的数字1，应该恢复
              if (this.isLegitimateNumber1(originalCode)) {
                fixes.push({
                  oldCode: violation,
                  newCode: originalCode
                });
              }
            }
          }
        }
        
        // 修复被错误标记的数字2
        const number2Violations = content.match(/🚨 硬编码价格2违规[^}]+/g);
        if (number2Violations) {
          for (const violation of number2Violations) {
            const match = violation.match(/🚨 硬编码价格2违规 - (.+)/);
            if (match) {
              const originalCode = match[1].trim();
              if (this.isLegitimateNumber2(originalCode)) {
                fixes.push({
                  oldCode: violation,
                  newCode: originalCode
                });
              }
            }
          }
        }
        
        if (fixes.length > 0) {
          this.actions.push({
            file: path.relative(process.cwd(), file),
            description: `修复${fixes.length}个被错误替换的数字`,
            fixes
          });
        }
        
      } catch (error) {
        console.warn(`⚠️ 处理文件失败: ${file}`);
      }
    }
    
    console.log(`   ✅ 识别了 ${this.actions.length} 个需要修复的文件`);
  }

  private isLegitimateNumber1(code: string): boolean {
    // 检查是否是合法的数字1使用场景
    return (
      code.includes("'1':") ||           // 对象键
      code.includes('"1":') ||           // 对象键
      code.includes('[1]') ||            // 数组索引
      code.includes('version') ||        // 版本号
      code.includes('User-Agent') ||     // 用户代理
      code.includes('feeEstimates') ||   // 费用估算
      code.includes('timeout') ||        // 超时设置
      code.includes('retry') ||          // 重试次数
      code.includes('attempt') ||        // 尝试次数
      code.includes('step') ||           // 步骤
      code.includes('level') ||          // 级别
      code.includes('priority') ||       // 优先级
      /\b1\b/.test(code) && !code.includes('price') && !code.includes('Price')
    );
  }

  private isLegitimateNumber2(code: string): boolean {
    // 检查是否是合法的数字2使用场景
    return (
      code.includes("'2':") ||           // 对象键
      code.includes('"2":') ||           // 对象键
      code.includes('[2]') ||            // 数组索引
      code.includes('* 2') ||            // 数学运算
      code.includes('/ 2') ||            // 数学运算
      code.includes('+ 2') ||            // 数学运算
      code.includes('- 2') ||            // 数学运算
      code.includes('Math') ||           // 数学函数
      code.includes('timeout') ||        // 超时设置
      code.includes('retry') ||          // 重试次数
      code.includes('step') ||           // 步骤
      /\b2\b/.test(code) && !code.includes('price') && !code.includes('Price')
    );
  }

  private async fixBrokenArrayDefinitions(): Promise<void> {
    console.log('\n🔧 修复被破坏的数组定义...');
    
    const files = this.getAllTSFiles(this.srcPath);
    
    for (const file of files) {
      if (this.isTestFile(file)) continue;
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        const fixes: Array<{oldCode: string; newCode: string}> = [];
        
        // 修复commonDefaults数组中的错误
        if (content.includes('/* 拒绝硬编码价格 */ (() => { throw new Error')) {
          fixes.push({
            oldCode: '/* 拒绝硬编码价格 */ (() => { throw new Error("拒绝使用硬编码价格50000，请从真实市场数据源获取"); })()',
            newCode: '50000'
          });
        }
        
        // 修复其他被破坏的数组元素
        const brokenArrayElements = content.match(/\[([^\]]*拒绝硬编码价格[^\]]*)\]/g);
        if (brokenArrayElements) {
          for (const element of brokenArrayElements) {
            // 尝试恢复原始数组
            const restored = element.replace(/\/\* 拒绝硬编码价格 \*\/ \(\(\) => \{ throw new Error\("[^"]+"\); \}\)\(\)/g, '50000');
            if (restored !== element) {
              fixes.push({
                oldCode: element,
                newCode: restored
              });
            }
          }
        }
        
        if (fixes.length > 0) {
          this.actions.push({
            file: path.relative(process.cwd(), file),
            description: `修复${fixes.length}个被破坏的数组定义`,
            fixes
          });
        }
        
      } catch (error) {
        console.warn(`⚠️ 处理文件失败: ${file}`);
      }
    }
  }

  private async fixBrokenFunctionCalls(): Promise<void> {
    console.log('\n🔧 修复被破坏的函数调用...');
    
    const files = this.getAllTSFiles(this.srcPath);
    
    for (const file of files) {
      if (this.isTestFile(file)) continue;
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        const fixes: Array<{oldCode: string; newCode: string}> = [];
        
        // 修复被错误替换的赋值语句
        const brokenAssignments = content.match(/= await this\.getRealPrice\(\); \/\/ 替换硬编码价格\d+/g);
        if (brokenAssignments) {
          for (const assignment of brokenAssignments) {
            // 这些很可能是错误的替换，需要根据上下文恢复
            const priceMatch = assignment.match(/替换硬编码价格(\d+)/);
            if (priceMatch) {
              const originalPrice = priceMatch[1];
              // 如果是明显的非价格数值，恢复原值
              if (['1', '2', '100'].includes(originalPrice)) {
                fixes.push({
                  oldCode: assignment,
                  newCode: `= ${originalPrice};`
                });
              }
            }
          }
        }
        
        if (fixes.length > 0) {
          this.actions.push({
            file: path.relative(process.cwd(), file),
            description: `修复${fixes.length}个被破坏的函数调用`,
            fixes
          });
        }
        
      } catch (error) {
        console.warn(`⚠️ 处理文件失败: ${file}`);
      }
    }
  }

  private async fixBrokenConfigurations(): Promise<void> {
    console.log('\n🔧 修复被破坏的配置和常量...');
    
    // 特别处理一些已知的破坏
    const knownFixes = [
      {
        file: 'src/contexts/trading-signals/infrastructure/validation/data-authenticity-validator.ts',
        description: '修复commonDefaults数组',
        fixes: [{
          oldCode: 'const commonDefaults = [0, 1, 50, 100, 1000, 10000, /* 拒绝硬编码价格 */ (() => { throw new Error("拒绝使用硬编码价格50000，请从真实市场数据源获取"); })(), 100000, 1000000];',
          newCode: 'const commonDefaults = [0, 1, 50, 100, 1000, 10000, 50000, 100000, 1000000];'
        }]
      }
    ];
    
    this.actions.push(...knownFixes);
  }

  private async applyRollbackFixes(): Promise<void> {
    console.log('\n🔧 应用回滚修复...');
    
    let processedFiles = 0;
    let appliedFixes = 0;
    
    for (const action of this.actions) {
      try {
        const fullPath = path.resolve(action.file);
        let content = fs.readFileSync(fullPath, 'utf8');
        let modified = false;
        
        for (const fix of action.fixes) {
          const oldContent = content;
          content = content.replace(fix.oldCode, fix.newCode);
          
          if (content !== oldContent) {
            modified = true;
            appliedFixes++;
          }
        }
        
        if (modified) {
          fs.writeFileSync(fullPath, content, 'utf8');
          processedFiles++;
          console.log(`   ✅ 修复文件: ${action.file} (${action.fixes.length}个修复)`);
        }
        
      } catch (error) {
        console.error(`   ❌ 修复文件失败: ${action.file}`, error);
      }
    }
    
    console.log(`\n📊 回滚完成:`);
    console.log(`   📁 处理文件数: ${processedFiles}`);
    console.log(`   🔄 应用修复数: ${appliedFixes}`);
  }

  private generateRollbackReport(): void {
    console.log('\n📊 紧急回滚报告');
    console.log('='.repeat(80));

    console.log(`总回滚操作: ${this.actions.length}`);
    console.log(`总修复数: ${this.actions.reduce((sum, action) => sum + action.fixes.length, 0)}`);
    
    console.log('\n📋 回滚详情:');
    this.actions.forEach((action, index) => {
      console.log(`${index + 1}. ${action.file}`);
      console.log(`   ${action.description}`);
      console.log(`   修复数: ${action.fixes.length}`);
    });

    // 保存详细报告
    this.saveDetailedRollbackReport();
  }

  private saveDetailedRollbackReport(): void {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalActions: this.actions.length,
        totalFixes: this.actions.reduce((sum, action) => sum + action.fixes.length, 0)
      },
      actions: this.actions
    };

    const reportsDir = path.join(process.cwd(), 'rollback-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const reportPath = path.join(reportsDir, `emergency-rollback-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 详细回滚报告已保存: ${reportPath}`);
  }

  private getAllTSFiles(dir: string): string[] {
    const files: string[] = [];
    
    try {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...this.getAllTSFiles(fullPath));
        } else if (stat.isFile() && item.endsWith('.ts') && !item.endsWith('.d.ts')) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`⚠️ 读取目录失败: ${dir}`);
    }
    
    return files;
  }

  private isTestFile(filePath: string): boolean {
    return filePath.includes('test') ||
           filePath.includes('spec') ||
           filePath.includes('__tests__') ||
           filePath.includes('.test.') ||
           filePath.includes('.spec.');
  }
}

// 运行紧急回滚
async function main() {
  const rollback = new EmergencyRollback();
  await rollback.runEmergencyRollback();
}

if (require.main === module) {
  main().catch(console.error);
}

export { EmergencyRollback };
