#!/usr/bin/env ts-node

/**
 * 系统基线验证脚本
 * 在修复任何测试之前，先验证系统的真实运行状态
 */

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

interface BaselineCheck {
  name: string;
  description: string;
  check: () => Promise<CheckResult>;
}

interface CheckResult {
  success: boolean;
  message: string;
  details?: any;
  critical?: boolean;
}

class SystemBaselineVerifier {
  private checks: BaselineCheck[] = [];
  private results: Map<string, CheckResult> = new Map();

  constructor() {
    this.setupChecks();
  }

  private setupChecks() {
    this.checks = [
      {
        name: 'compilation',
        description: 'TypeScript编译检查',
        check: this.checkCompilation.bind(this)
      },
      {
        name: 'dependencies',
        description: '依赖包完整性检查',
        check: this.checkDependencies.bind(this)
      },
      {
        name: 'database-schema',
        description: '数据库Schema验证',
        check: this.checkDatabaseSchema.bind(this)
      },
      {
        name: 'env-config',
        description: '环境配置验证',
        check: this.checkEnvironmentConfig.bind(this)
      },
      {
        name: 'core-services',
        description: '核心服务可实例化检查',
        check: this.checkCoreServices.bind(this)
      },
      {
        name: 'api-routes',
        description: 'API路由定义检查',
        check: this.checkApiRoutes.bind(this)
      }
    ];
  }

  async runAllChecks(): Promise<void> {
    console.log('🔍 开始系统基线验证...\n');

    for (const check of this.checks) {
      console.log(`⏳ 检查: ${check.description}`);
      try {
        const result = await check.check();
        this.results.set(check.name, result);
        
        const status = result.success ? '✅' : (result.critical ? '🔴' : '🟡');
        console.log(`${status} ${check.description}: ${result.message}`);
        
        if (result.details) {
          console.log(`   详情: ${JSON.stringify(result.details, null, 2)}`);
        }
      } catch (error) {
        const result: CheckResult = {
          success: false,
          message: `检查失败: ${error.message}`,
          critical: true
        };
        this.results.set(check.name, result);
        console.log(`🔴 ${check.description}: ${result.message}`);
      }
      console.log('');
    }

    this.generateReport();
  }

  private async checkCompilation(): Promise<CheckResult> {
    try {
      execSync('npx tsc --noEmit', { stdio: 'pipe' });
      return {
        success: true,
        message: '编译通过'
      };
    } catch (error) {
      const output = error.stdout?.toString() || error.stderr?.toString() || '';
      const errorCount = (output.match(/error TS\d+:/g) || []).length;
      
      return {
        success: false,
        message: `编译失败，发现 ${errorCount} 个错误`,
        details: { errorCount, sample: output.split('\n').slice(0, 10) },
        critical: true
      };
    }
  }

  private async checkDependencies(): Promise<CheckResult> {
    try {
      execSync('npm ls --depth=0', { stdio: 'pipe' });
      return {
        success: true,
        message: '依赖包完整'
      };
    } catch (error) {
      return {
        success: false,
        message: '依赖包缺失或版本冲突',
        details: error.message,
        critical: true
      };
    }
  }

  private async checkDatabaseSchema(): Promise<CheckResult> {
    try {
      const schemaPath = path.join(process.cwd(), 'prisma/schema.prisma');
      if (!fs.existsSync(schemaPath)) {
        return {
          success: false,
          message: 'Prisma schema文件不存在',
          critical: true
        };
      }

      execSync('npx prisma validate', { stdio: 'pipe' });
      return {
        success: true,
        message: 'Prisma schema有效'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Prisma schema验证失败',
        details: error.message,
        critical: true
      };
    }
  }

  private async checkEnvironmentConfig(): Promise<CheckResult> {
    const requiredEnvVars = [
      'DATABASE_URL',
      'JWT_SECRET',
      'NODE_ENV'
    ];

    const missing = requiredEnvVars.filter(env => !process.env[env]);
    
    if (missing.length > 0) {
      return {
        success: false,
        message: `缺少必需的环境变量: ${missing.join(', ')}`,
        critical: true
      };
    }

    return {
      success: true,
      message: '环境配置完整'
    };
  }

  private async checkCoreServices(): Promise<CheckResult> {
    // 这里我们只检查关键文件是否存在，不实际实例化
    const coreFiles = [
      'src/shared/infrastructure/di/modular-container-manager.ts',
      'src/shared/infrastructure/logging/unified-logger.ts',
      'src/shared/infrastructure/database/unified-data-mapper.ts'
    ];

    const missing = coreFiles.filter(file => !fs.existsSync(file));
    
    if (missing.length > 0) {
      return {
        success: false,
        message: `核心服务文件缺失: ${missing.join(', ')}`,
        critical: true
      };
    }

    return {
      success: true,
      message: '核心服务文件存在'
    };
  }

  private async checkApiRoutes(): Promise<CheckResult> {
    const routeFiles = [
      'src/api/routes/index.ts',
      'src/api/controllers'
    ];

    const missing = routeFiles.filter(file => !fs.existsSync(file));
    
    if (missing.length > 0) {
      return {
        success: false,
        message: `API路由文件缺失: ${missing.join(', ')}`,
        critical: false
      };
    }

    return {
      success: true,
      message: 'API路由文件存在'
    };
  }

  private generateReport(): void {
    console.log('\n📊 系统基线验证报告');
    console.log('='.repeat(50));

    const totalChecks = this.results.size;
    const successCount = Array.from(this.results.values()).filter(r => r.success).length;
    const criticalFailures = Array.from(this.results.values()).filter(r => !r.success && r.critical).length;

    console.log(`总检查项: ${totalChecks}`);
    console.log(`通过: ${successCount}`);
    console.log(`失败: ${totalChecks - successCount}`);
    console.log(`严重失败: ${criticalFailures}`);

    if (criticalFailures > 0) {
      console.log('\n🚨 严重问题需要立即修复:');
      for (const [name, result] of this.results) {
        if (!result.success && result.critical) {
          console.log(`  - ${name}: ${result.message}`);
        }
      }
    }

    console.log('\n💡 建议:');
    if (criticalFailures > 0) {
      console.log('  1. 先修复编译错误，再进行测试修复');
      console.log('  2. 确保所有依赖包正确安装');
      console.log('  3. 验证环境配置完整性');
    } else {
      console.log('  1. 系统基线状态良好，可以开始测试修复');
      console.log('  2. 建议先运行端到端验证脚本');
    }

    // 保存报告到文件
    const reportPath = 'baseline-verification-report.json';
    const report = {
      timestamp: new Date().toISOString(),
      summary: { totalChecks, successCount, criticalFailures },
      results: Object.fromEntries(this.results)
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  }
}

// 运行验证
if (require.main === module) {
  const verifier = new SystemBaselineVerifier();
  verifier.runAllChecks().catch(console.error);
}

export { SystemBaselineVerifier };
