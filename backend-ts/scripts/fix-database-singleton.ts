#!/usr/bin/env tsx

/**
 * 数据库单例修复脚本
 * 将所有直接创建PrismaClient实例的代码修改为使用全局单例
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { execSync } from 'child_process';
import path from 'path';

interface FileToFix {
  filePath: string;
  description: string;
  isScript: boolean;
}

// 需要修复的文件列表
const filesToFix: FileToFix[] = [
  // 脚本文件 - 这些可以使用独立的PrismaClient实例
  { filePath: 'scripts/development/configure-real-account.ts', description: '配置真实交易账户', isScript: true },
  { filePath: 'scripts/monitoring/database-monitor.ts', description: '数据库监控脚本', isScript: true },
  { filePath: 'scripts/migration/backfill-short-timeframe-data.ts', description: '数据回填脚本', isScript: true },
  { filePath: 'scripts/development/configure-btc-only-trading.ts', description: 'BTC交易配置', isScript: true },
  { filePath: 'scripts/maintenance/unified-database-analyzer.ts', description: '数据库分析器', isScript: true },
  { filePath: 'scripts/development/generate-price-data.ts', description: '价格数据生成', isScript: true },
  { filePath: 'scripts/migration/optimize-database-structure.ts', description: '数据库结构优化', isScript: true },
  { filePath: 'scripts/maintenance/auto-data-updater.ts', description: '自动数据更新', isScript: true },
  { filePath: 'scripts/remove-wbtc.ts', description: 'WBTC移除脚本', isScript: true },
  { filePath: 'scripts/maintenance/enhance-data-validation.ts', description: '数据验证增强', isScript: true },
  { filePath: 'scripts/migration/sync-real-binance-data.ts', description: 'Binance数据同步', isScript: true },
  { filePath: 'scripts/monitoring/check-predictions.ts', description: '预测检查', isScript: true },
  { filePath: 'scripts/migration/validate-dual-track-migration.ts', description: '双轨迁移验证', isScript: true },
  { filePath: 'scripts/development/generate-initial-risk-assessments.ts', description: '风险评估生成', isScript: true },
  { filePath: 'scripts/migration/sync-real-balance.ts', description: '余额同步', isScript: true },
  { filePath: 'scripts/monitoring/check-short-timeframe-coverage.ts', description: '短期时间框架检查', isScript: true },
  { filePath: 'scripts/maintenance/setup-data-protection.ts', description: '数据保护设置', isScript: true },
  { filePath: 'scripts/migration/manual-price-update.ts', description: '手动价格更新', isScript: true },
  { filePath: 'scripts/check-symbols.ts', description: '符号检查', isScript: true },
  { filePath: 'scripts/development/init-trading-account.ts', description: '交易账户初始化', isScript: true },
  { filePath: 'scripts/simple-config-migration.ts', description: '配置迁移', isScript: true },
  { filePath: 'scripts/check-specific-symbol.ts', description: '特定符号检查', isScript: true },
  { filePath: 'scripts/monitoring/system-readiness-check.ts', description: '系统就绪检查', isScript: true },
  { filePath: 'scripts/monitoring/check-data-status.ts', description: '数据状态检查', isScript: true },
  { filePath: 'scripts/development/inspect-database-structure.ts', description: '数据库结构检查', isScript: true },
  { filePath: 'scripts/maintenance/cleanup-fake-data.ts', description: '虚假数据清理', isScript: true },
  { filePath: 'scripts/development/generate-prediction-report.ts', description: '预测报告生成', isScript: true },
  { filePath: 'scripts/monitoring/check-short-term-service.ts', description: '短期服务检查', isScript: true },
  { filePath: 'scripts/migration/restore-btc-historical-data.ts', description: 'BTC历史数据恢复', isScript: true },
  { filePath: 'scripts/maintenance/apply-data-protection.ts', description: '数据保护应用', isScript: true },
  { filePath: 'scripts/migration/migrate-user-data.ts', description: '用户数据迁移', isScript: true },
  
  // 应用程序文件 - 这些应该使用全局单例
  { filePath: 'scripts/monitoring/continuous-verification.ts', description: '持续验证监控', isScript: false },
  { filePath: 'scripts/maintenance/comprehensive-database-audit.ts', description: '综合数据库审计', isScript: false },
  { filePath: 'scripts/maintenance/comprehensive-honesty-audit.ts', description: '综合诚实审计', isScript: false },
  { filePath: 'check-trend-data.ts', description: '趋势数据检查', isScript: false },
  { filePath: 'tests/integration/test-api-key-system.ts', description: 'API密钥系统测试', isScript: false }
];

class DatabaseSingletonFixer {
  private readonly projectRoot: string;
  private fixedFiles: string[] = [];
  private skippedFiles: string[] = [];
  private errors: Array<{ file: string; error: string }> = [];

  constructor() {
    this.projectRoot = process.cwd();
  }

  async fixAllFiles(): Promise<void> {
    console.log('🔧 开始修复数据库单例违规问题...\n');

    for (const fileInfo of filesToFix) {
      await this.fixFile(fileInfo);
    }

    this.printSummary();
  }

  private async fixFile(fileInfo: FileToFix): Promise<void> {
    const fullPath = path.join(this.projectRoot, fileInfo.filePath);
    
    if (!existsSync(fullPath)) {
      console.log(`⚠️  文件不存在，跳过: ${fileInfo.filePath}`);
      this.skippedFiles.push(fileInfo.filePath);
      return;
    }

    try {
      console.log(`🔍 检查文件: ${fileInfo.filePath}`);
      
      const content = readFileSync(fullPath, 'utf-8');
      
      // 检查是否包含 "const prisma = new PrismaClient()"
      if (!content.includes('const prisma = new PrismaClient()')) {
        console.log(`✅ 文件已符合规范: ${fileInfo.filePath}`);
        return;
      }

      let newContent: string;
      
      if (fileInfo.isScript) {
        // 脚本文件：添加注释说明为什么使用独立实例
        newContent = this.fixScriptFile(content, fileInfo.description);
      } else {
        // 应用程序文件：改为使用全局单例
        newContent = this.fixApplicationFile(content);
      }

      if (newContent !== content) {
        writeFileSync(fullPath, newContent, 'utf-8');
        console.log(`✅ 已修复: ${fileInfo.filePath}`);
        this.fixedFiles.push(fileInfo.filePath);
      }

    } catch (error) {
      console.error(`❌ 修复失败: ${fileInfo.filePath} - ${error}`);
      this.errors.push({ file: fileInfo.filePath, error: String(error) });
    }
  }

  private fixScriptFile(content: string, description: string): string {
    // 为脚本文件添加注释说明
    const comment = `/**
 * ${description}
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */\n\n`;
    
    // 在import语句后添加注释
    const importRegex = /(import.*from.*['"@].*['"];?\s*\n)/g;
    const imports = content.match(importRegex) || [];
    const lastImportIndex = content.lastIndexOf(imports[imports.length - 1] || '') + (imports[imports.length - 1] || '').length;
    
    if (lastImportIndex > 0) {
      return content.slice(0, lastImportIndex) + '\n' + comment + content.slice(lastImportIndex);
    }
    
    return comment + content;
  }

  private fixApplicationFile(content: string): string {
    // 应用程序文件：改为使用全局单例
    let newContent = content;
    
    // 添加全局单例导入
    if (!content.includes('getGlobalPrismaClient')) {
      const prismaImportRegex = /import\s*{[^}]*PrismaClient[^}]*}\s*from\s*['"@]prisma\/client['"]/;
      const match = content.match(prismaImportRegex);
      
      if (match) {
        const importStatement = "import { getGlobalPrismaClient } from '../shared/infrastructure/database/database';";
        newContent = newContent.replace(match[0], match[0] + '\n' + importStatement);
      }
    }
    
    // 替换 PrismaClient 实例化
    newContent = newContent.replace(
      /const\s+prisma\s*=\s*new\s+PrismaClient\(\s*\);?/g,
      'const prisma = getGlobalPrismaClient();'
    );
    
    // 移除 $disconnect 调用（全局单例不需要手动断开）
    newContent = newContent.replace(
      /await\s+prisma\.\$disconnect\(\s*\);?/g,
      '// 全局单例不需要手动断开连接'
    );
    
    return newContent;
  }

  private printSummary(): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 数据库单例修复总结');
    console.log('='.repeat(60));
    
    console.log(`✅ 成功修复: ${this.fixedFiles.length} 个文件`);
    if (this.fixedFiles.length > 0) {
      this.fixedFiles.forEach(file => console.log(`   - ${file}`));
    }
    
    console.log(`⚠️  跳过文件: ${this.skippedFiles.length} 个文件`);
    if (this.skippedFiles.length > 0) {
      this.skippedFiles.forEach(file => console.log(`   - ${file}`));
    }
    
    console.log(`❌ 修复失败: ${this.errors.length} 个文件`);
    if (this.errors.length > 0) {
      this.errors.forEach(({ file, error }) => console.log(`   - ${file}: ${error}`));
    }
    
    console.log('\n🎯 修复完成！');
    console.log('\n📋 后续步骤:');
    console.log('1. 运行 npm run build 检查编译错误');
    console.log('2. 运行 npm test 确保测试通过');
    console.log('3. 运行应用程序验证功能正常');
  }
}

// 运行修复
if (require.main === module) {
  const fixer = new DatabaseSingletonFixer();
  fixer.fixAllFiles().catch(console.error);
}

export { DatabaseSingletonFixer };