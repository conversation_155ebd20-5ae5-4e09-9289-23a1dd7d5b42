#!/usr/bin/env tsx

/**
 * 智能虚假数据清理器
 * 能够区分真正的价格数据和其他数值，进行精准修复
 */

import * as fs from 'fs';
import * as path from 'path';

interface CleanupAction {
  file: string;
  line: number;
  type: 'RESTORE_LOGIC' | 'REMOVE_COMMENT' | 'FIX_BROKEN_CODE';
  description: string;
  oldCode: string;
  newCode: string;
}

class IntelligentFakeDataCleaner {
  private actions: CleanupAction[] = [];
  private readonly srcPath = path.join(process.cwd(), 'src');

  async runIntelligentCleanup(): Promise<void> {
    console.log('🧠 智能虚假数据清理器启动...');
    console.log('='.repeat(80));

    try {
      // 1. 修复被错误注释的逻辑代码
      await this.fixCommentedOutLogic();
      
      // 2. 移除虚假实现注释
      await this.removeFakeImplementationComments();
      
      // 3. 修复被破坏的代码结构
      await this.fixBrokenCodeStructures();
      
      // 4. 应用所有修复
      await this.applyCleanupActions();
      
      // 5. 生成清理报告
      this.generateCleanupReport();
      
    } catch (error) {
      console.error('❌ 智能清理过程失败:', error);
    }
  }

  private async fixCommentedOutLogic(): Promise<void> {
    console.log('\n🔧 修复被错误注释的逻辑代码...');
    
    const files = this.getAllTSFiles(this.srcPath);
    
    for (const file of files) {
      if (this.isTestFile(file)) continue;
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        const lines = content.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          
          // 修复mempool相关的逻辑（这些不是价格数据）
          if (line.includes('🚨 发现硬编码价格') && line.includes('mempoolCount')) {
            const originalLogic = this.extractOriginalMempoolLogic(line);
            if (originalLogic) {
              this.actions.push({
                file: path.relative(process.cwd(), file),
                line: i + 1,
                type: 'RESTORE_LOGIC',
                description: '恢复mempool拥堵判断逻辑',
                oldCode: line,
                newCode: originalLogic
              });
            }
          }
          
          // 修复费用相关的逻辑
          if (line.includes('🚨 发现硬编码价格') && (line.includes('Fee') || line.includes('fee'))) {
            const originalLogic = this.extractOriginalFeeLogic(line);
            if (originalLogic) {
              this.actions.push({
                file: path.relative(process.cwd(), file),
                line: i + 1,
                type: 'RESTORE_LOGIC',
                description: '恢复费用判断逻辑',
                oldCode: line,
                newCode: originalLogic
              });
            }
          }
          
          // 修复技术指标相关的逻辑（RSI、MACD等）
          if (line.includes('🚨 发现硬编码价格') && (line.includes('rsi') || line.includes('RSI') || line.includes('macd'))) {
            const originalLogic = this.extractOriginalIndicatorLogic(line);
            if (originalLogic) {
              this.actions.push({
                file: path.relative(process.cwd(), file),
                line: i + 1,
                type: 'RESTORE_LOGIC',
                description: '恢复技术指标逻辑',
                oldCode: line,
                newCode: originalLogic
              });
            }
          }
          
          // 修复百分比和比率相关的逻辑
          if (line.includes('🚨 发现硬编码价格') && (line.includes('*') || line.includes('/') || line.includes('%'))) {
            const originalLogic = this.extractOriginalRatioLogic(line);
            if (originalLogic) {
              this.actions.push({
                file: path.relative(process.cwd(), file),
                line: i + 1,
                type: 'RESTORE_LOGIC',
                description: '恢复比率计算逻辑',
                oldCode: line,
                newCode: originalLogic
              });
            }
          }
        }
      } catch (error) {
        console.warn(`⚠️ 处理文件失败: ${file}`);
      }
    }
    
    console.log(`   ✅ 识别了 ${this.actions.filter(a => a.type === 'RESTORE_LOGIC').length} 个需要恢复的逻辑`);
  }

  private extractOriginalMempoolLogic(commentedLine: string): string | null {
    // 从注释中提取原始的mempool逻辑
    const match = commentedLine.match(/🚨 发现硬编码价格\d+违规 - (.+)/);
    if (match) {
      const originalCode = match[1];
      // 这些是mempool交易数量阈值，不是价格数据，应该恢复
      if (originalCode.includes('mempoolCount') || originalCode.includes('nextBlockFee')) {
        return originalCode;
      }
    }
    return null;
  }

  private extractOriginalFeeLogic(commentedLine: string): string | null {
    const match = commentedLine.match(/🚨 发现硬编码价格\d+违规 - (.+)/);
    if (match) {
      const originalCode = match[1];
      // 网络费用阈值不是价格数据
      if (originalCode.includes('Fee') || originalCode.includes('fee')) {
        return originalCode;
      }
    }
    return null;
  }

  private extractOriginalIndicatorLogic(commentedLine: string): string | null {
    const match = commentedLine.match(/🚨 发现硬编码价格\d+违规 - (.+)/);
    if (match) {
      const originalCode = match[1];
      // 技术指标的阈值（如RSI的70、30）不是价格数据
      if (originalCode.includes('rsi') || originalCode.includes('RSI') || originalCode.includes('macd')) {
        return originalCode;
      }
    }
    return null;
  }

  private extractOriginalRatioLogic(commentedLine: string): string | null {
    const match = commentedLine.match(/🚨 发现硬编码价格\d+违规 - (.+)/);
    if (match) {
      const originalCode = match[1];
      // 数学计算和比率不是价格数据
      if ((originalCode.includes('*') || originalCode.includes('/')) && 
          !originalCode.includes('price') && !originalCode.includes('Price')) {
        return originalCode;
      }
    }
    return null;
  }

  private async removeFakeImplementationComments(): Promise<void> {
    console.log('\n🧹 移除虚假实现注释...');
    
    const files = this.getAllTSFiles(this.srcPath);
    
    for (const file of files) {
      if (this.isTestFile(file)) continue;
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        const lines = content.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          
          // 移除"🚨 发现硬编码价格X违规"这样的注释行
          if (line.trim().startsWith('// 🚨 发现硬编码价格') && line.includes('违规')) {
            this.actions.push({
              file: path.relative(process.cwd(), file),
              line: i + 1,
              type: 'REMOVE_COMMENT',
              description: '移除虚假实现违规注释',
              oldCode: line,
              newCode: '' // 删除整行
            });
          }
          
          // 移除"替换硬编码价格X"这样的注释
          if (line.includes('替换硬编码价格') || line.includes('拒绝硬编码价格')) {
            // 但保留throw new Error的行，因为这些是有效的错误处理
            if (!line.includes('throw new Error')) {
              this.actions.push({
                file: path.relative(process.cwd(), file),
                line: i + 1,
                type: 'REMOVE_COMMENT',
                description: '移除硬编码价格替换注释',
                oldCode: line,
                newCode: line.replace(/\/\/ 替换硬编码价格\d+/, '').replace(/\/\/ 拒绝硬编码价格\d+/, '').trim()
              });
            }
          }
        }
      } catch (error) {
        console.warn(`⚠️ 处理文件失败: ${file}`);
      }
    }
    
    console.log(`   ✅ 识别了 ${this.actions.filter(a => a.type === 'REMOVE_COMMENT').length} 个需要移除的注释`);
  }

  private async fixBrokenCodeStructures(): Promise<void> {
    console.log('\n🔨 修复被破坏的代码结构...');
    
    const files = this.getAllTSFiles(this.srcPath);
    
    for (const file of files) {
      if (this.isTestFile(file)) continue;
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // 修复被破坏的Math.min调用
        if (content.includes('Math.min(\n        // 🚨')) {
          this.actions.push({
            file: path.relative(process.cwd(), file),
            line: 0,
            type: 'FIX_BROKEN_CODE',
            description: '修复被破坏的Math.min调用',
            oldCode: 'Math.min(\n        // 🚨 发现硬编码价格100违规 - 100,\n        // 🚨 发现硬编码价格2违规 - (stats.mempoolCount / 1000) + (currentFee * 2)\n      );',
            newCode: 'Math.min(100, (stats.mempoolCount / 1000) + (currentFee * 2));'
          });
        }
        
        // 修复被破坏的if语句
        if (content.includes('// 🚨 发现硬编码价格1违规 - // 🚨 发现硬编码价格2违规 - if')) {
          this.actions.push({
            file: path.relative(process.cwd(), file),
            line: 0,
            type: 'FIX_BROKEN_CODE',
            description: '修复被破坏的if语句',
            oldCode: '// 🚨 发现硬编码价格1违规 - // 🚨 发现硬编码价格2违规 - if (currentFee > mediumTermFee * 1.2) {',
            newCode: 'if (currentFee > mediumTermFee * 1.2) {'
          });
        }
        
      } catch (error) {
        console.warn(`⚠️ 处理文件失败: ${file}`);
      }
    }
    
    console.log(`   ✅ 识别了 ${this.actions.filter(a => a.type === 'FIX_BROKEN_CODE').length} 个需要修复的代码结构`);
  }

  private async applyCleanupActions(): Promise<void> {
    console.log('\n🔧 应用智能清理操作...');
    
    // 按文件分组
    const fileGroups = new Map<string, CleanupAction[]>();
    for (const action of this.actions) {
      const fullPath = path.resolve(action.file);
      if (!fileGroups.has(fullPath)) {
        fileGroups.set(fullPath, []);
      }
      fileGroups.get(fullPath)!.push(action);
    }
    
    let processedFiles = 0;
    let appliedActions = 0;
    
    for (const [filePath, fileActions] of fileGroups.entries()) {
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;
        
        // 按行号倒序处理，避免行号偏移
        const sortedActions = fileActions.sort((a, b) => b.line - a.line);
        
        for (const action of sortedActions) {
          const oldContent = content;
          
          if (action.type === 'REMOVE_COMMENT' && action.newCode === '') {
            // 删除整行
            const lines = content.split('\n');
            lines.splice(action.line - 1, 1);
            content = lines.join('\n');
          } else {
            // 替换内容
            content = content.replace(action.oldCode, action.newCode);
          }
          
          if (content !== oldContent) {
            modified = true;
            appliedActions++;
          }
        }
        
        if (modified) {
          fs.writeFileSync(filePath, content, 'utf8');
          processedFiles++;
          console.log(`   ✅ 清理文件: ${path.relative(process.cwd(), filePath)} (${fileActions.length}个操作)`);
        }
        
      } catch (error) {
        console.error(`   ❌ 清理文件失败: ${filePath}`, error);
      }
    }
    
    console.log(`\n📊 清理完成:`);
    console.log(`   📁 处理文件数: ${processedFiles}`);
    console.log(`   🔄 应用操作数: ${appliedActions}`);
  }

  private generateCleanupReport(): void {
    console.log('\n📊 智能清理报告');
    console.log('='.repeat(80));

    const typeStats = this.actions.reduce((acc, action) => {
      acc[action.type] = (acc[action.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log(`总清理操作: ${this.actions.length}`);
    
    console.log('\n🔧 按操作类型统计:');
    Object.entries(typeStats).forEach(([type, count]) => {
      const typeNames = {
        'RESTORE_LOGIC': '🔄 恢复逻辑',
        'REMOVE_COMMENT': '🧹 移除注释',
        'FIX_BROKEN_CODE': '🔨 修复代码'
      };
      console.log(`   ${typeNames[type as keyof typeof typeNames] || type}: ${count}个`);
    });

    // 保存详细报告
    this.saveDetailedCleanupReport();
  }

  private saveDetailedCleanupReport(): void {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.actions.length,
        byType: this.actions.reduce((acc, action) => {
          acc[action.type] = (acc[action.type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      },
      actions: this.actions
    };

    const reportsDir = path.join(process.cwd(), 'cleanup-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const reportPath = path.join(reportsDir, `intelligent-cleanup-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 详细清理报告已保存: ${reportPath}`);
  }

  private getAllTSFiles(dir: string): string[] {
    const files: string[] = [];
    
    try {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...this.getAllTSFiles(fullPath));
        } else if (stat.isFile() && item.endsWith('.ts') && !item.endsWith('.d.ts')) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`⚠️ 读取目录失败: ${dir}`);
    }
    
    return files;
  }

  private isTestFile(filePath: string): boolean {
    return filePath.includes('test') ||
           filePath.includes('spec') ||
           filePath.includes('__tests__') ||
           filePath.includes('.test.') ||
           filePath.includes('.spec.');
  }
}

// 运行智能清理
async function main() {
  const cleaner = new IntelligentFakeDataCleaner();
  await cleaner.runIntelligentCleanup();
}

if (require.main === module) {
  main().catch(console.error);
}

export { IntelligentFakeDataCleaner };
