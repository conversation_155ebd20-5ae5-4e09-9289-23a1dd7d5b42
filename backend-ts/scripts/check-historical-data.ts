/**
 * 检查历史数据脚本
 * 查看数据库中的历史数据情况
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkHistoricalData() {
  try {
    console.log('🔍 检查历史数据...');
    
    // 1. 检查总记录数
    const totalCount = await prisma.historicalData.count();
    console.log(`📊 历史数据总记录数: ${totalCount}`);
    
    // 2. 检查交易对分布
    const symbolDistribution = await prisma.historicalData.groupBy({
      by: ['symbolId'],
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    });
    
    console.log('\n📈 交易对数据分布:');
    for (const item of symbolDistribution) {
      const symbol = await prisma.symbols.findUnique({
        where: { id: item.symbolId }
      });
      console.log(`  ${symbol?.symbol || 'Unknown'}: ${item._count.id} 条记录`);
    }
    
    // 3. 检查时间框架分布
    const timeframeDistribution = await prisma.historicalData.groupBy({
      by: ['timeframe'],
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    });
    
    console.log('\n⏰ 时间框架分布:');
    timeframeDistribution.forEach(item => {
      console.log(`  ${item.timeframe}: ${item._count.id} 条记录`);
    });
    
    // 4. 检查最新数据时间
    const latestData = await prisma.historicalData.findFirst({
      orderBy: {
        timestamp: 'desc'
      },
      include: {
        Symbols: true
      }
    });
    
    if (latestData) {
      console.log('\n🕐 最新数据:');
      console.log(`  交易对: ${latestData.Symbols.symbol}`);
      console.log(`  时间框架: ${latestData.timeframe}`);
      console.log(`  时间戳: ${latestData.timestamp}`);
      console.log(`  价格: ${latestData.closePrice}`);
    }
    
    // 5. 检查最旧数据时间
    const oldestData = await prisma.historicalData.findFirst({
      orderBy: {
        timestamp: 'asc'
      },
      include: {
        Symbols: true
      }
    });
    
    if (oldestData) {
      console.log('\n🕑 最旧数据:');
      console.log(`  交易对: ${oldestData.Symbols.symbol}`);
      console.log(`  时间框架: ${oldestData.timeframe}`);
      console.log(`  时间戳: ${oldestData.timestamp}`);
      console.log(`  价格: ${oldestData.closePrice}`);
    }
    
    // 6. 检查BTCUSDT的具体数据
    const btcSymbol = await prisma.symbols.findFirst({
      where: {
        symbol: {
          contains: 'BTC'
        }
      }
    });
    
    if (btcSymbol) {
      const btcDataCount = await prisma.historicalData.count({
        where: {
          symbolId: btcSymbol.id
        }
      });
      
      console.log(`\n₿ BTC相关数据: ${btcDataCount} 条记录`);
      
      // 检查BTC的1小时数据
      const btcHourlyCount = await prisma.historicalData.count({
        where: {
          symbolId: btcSymbol.id,
          timeframe: '1h'
        }
      });
      
      console.log(`  1小时数据: ${btcHourlyCount} 条记录`);
      
      // 获取最近5条BTC 1小时数据
      const recentBtcData = await prisma.historicalData.findMany({
        where: {
          symbolId: btcSymbol.id,
          timeframe: '1h'
        },
        orderBy: {
          timestamp: 'desc'
        },
        take: 5
      });
      
      console.log('\n📊 最近5条BTC 1小时数据:');
      recentBtcData.forEach((data, index) => {
        console.log(`  ${index + 1}. ${data.timestamp} - 收盘价: ${data.closePrice}`);
      });
    }
    
    // 7. 检查数据库表结构
    console.log('\n🏗️ 检查表结构...');
    const tableInfo = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'HistoricalData' 
      ORDER BY ordinal_position;
    `;
    console.log('HistoricalData表结构:', tableInfo);
    
  } catch (error) {
    console.error('❌ 检查历史数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkHistoricalData();
