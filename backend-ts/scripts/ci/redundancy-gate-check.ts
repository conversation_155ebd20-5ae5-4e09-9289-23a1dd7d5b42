#!/usr/bin/env npx ts-node

/**
 * 重复实现门禁检查器
 * 专门用于CI/CD流程中的重复实现检测
 * 确保没有新的重复实现被合并到代码库中
 */

import { execSync } from 'child_process';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

interface RedundancyCheckResult {
  timestamp: string;
  status: 'PASSED' | 'FAILED';
  totalViolations: number;
  criticalViolations: number;
  warningViolations: number;
  details: {
    redundancyDetection: {
      passed: boolean;
      violationCount: number;
      details: string;
    };
    knownDuplicationsValidation: {
      passed: boolean;
      violationCount: number;
      details: string;
    };
  };
  summary: string;
}

class RedundancyGateChecker {
  private projectRoot: string;
  private reportDir: string;
  private timestamp: string;

  constructor() {
    this.projectRoot = process.cwd();
    this.reportDir = join(this.projectRoot, 'reports');
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // 确保报告目录存在
    if (!existsSync(this.reportDir)) {
      mkdirSync(this.reportDir, { recursive: true });
    }
  }

  /**
   * 执行重复实现检测
   */
  private async runRedundancyDetection(): Promise<{ passed: boolean; violationCount: number; details: string }> {
    try {
      console.log('🔍 执行重复实现检测...');
      
      const output = execSync('npm run detect:redundant', { 
        encoding: 'utf8',
        cwd: this.projectRoot,
        timeout: 120000 // 2分钟超时
      });
      
      // 分析输出结果
      const lines = output.split('\n');
      const violationLines = lines.filter(line => 
        line.includes('发现') && line.includes('个') && 
        (line.includes('重复') || line.includes('冗余'))
      );
      
      let totalViolations = 0;
      for (const line of violationLines) {
        const match = line.match(/发现\s*(\d+)\s*个/);
        if (match) {
          totalViolations += parseInt(match[1], 10);
        }
      }
      
      const passed = totalViolations === 0;
      
      return {
        passed,
        violationCount: totalViolations,
        details: passed ? '✅ 未发现新的重复实现' : `❌ 发现 ${totalViolations} 个重复实现违规`
      };
      
    } catch (error) {
      return {
        passed: false,
        violationCount: -1,
        details: `❌ 重复实现检测执行失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 执行已知重复实现验证
   */
  private async runKnownDuplicationsValidation(): Promise<{ passed: boolean; violationCount: number; details: string }> {
    try {
      console.log('🔍 执行已知重复实现验证...');
      
      const output = execSync('npm run validate:known-duplications', { 
        encoding: 'utf8',
        cwd: this.projectRoot,
        timeout: 120000 // 2分钟超时
      });
      
      // 分析输出结果
      const lines = output.split('\n');
      const violationLines = lines.filter(line => 
        line.includes('违规') || line.includes('重新出现') || line.includes('回归')
      );
      
      const totalViolations = violationLines.length;
      const passed = totalViolations === 0;
      
      return {
        passed,
        violationCount: totalViolations,
        details: passed ? '✅ 已知重复实现验证通过' : `❌ 发现 ${totalViolations} 个已知重复实现回归`
      };
      
    } catch (error) {
      return {
        passed: false,
        violationCount: -1,
        details: `❌ 已知重复实现验证执行失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 执行完整的重复实现门禁检查
   */
  async runGateCheck(): Promise<RedundancyCheckResult> {
    console.log('🚀 开始重复实现门禁检查...');
    console.log(`时间: ${new Date().toISOString()}`);
    console.log(`项目根目录: ${this.projectRoot}`);
    console.log('');

    // 执行检查
    const redundancyResult = await this.runRedundancyDetection();
    const knownDuplicationsResult = await this.runKnownDuplicationsValidation();

    // 计算总体结果
    const allPassed = redundancyResult.passed && knownDuplicationsResult.passed;
    const totalViolations = Math.max(0, redundancyResult.violationCount) + Math.max(0, knownDuplicationsResult.violationCount);
    
    // 分类违规严重程度
    const criticalViolations = knownDuplicationsResult.violationCount > 0 ? knownDuplicationsResult.violationCount : 0;
    const warningViolations = redundancyResult.violationCount > 0 ? redundancyResult.violationCount : 0;

    const result: RedundancyCheckResult = {
      timestamp: new Date().toISOString(),
      status: allPassed ? 'PASSED' : 'FAILED',
      totalViolations,
      criticalViolations,
      warningViolations,
      details: {
        redundancyDetection: redundancyResult,
        knownDuplicationsValidation: knownDuplicationsResult
      },
      summary: allPassed 
        ? '🎉 重复实现门禁检查通过，代码质量良好！'
        : `⚠️ 重复实现门禁检查失败，发现 ${totalViolations} 个违规项，请修复后重新提交。`
    };

    // 保存报告
    this.saveReport(result);
    
    // 输出结果
    this.printResult(result);
    
    return result;
  }

  /**
   * 保存检查报告
   */
  private saveReport(result: RedundancyCheckResult): void {
    const reportPath = join(this.reportDir, `redundancy-gate-${this.timestamp}.json`);
    writeFileSync(reportPath, JSON.stringify(result, null, 2));
    console.log(`📄 报告已保存到: ${reportPath}`);
  }

  /**
   * 打印检查结果
   */
  private printResult(result: RedundancyCheckResult): void {
    console.log('\n' + '='.repeat(60));
    console.log('🏁 重复实现门禁检查结果');
    console.log('='.repeat(60));
    
    const statusIcon = result.status === 'PASSED' ? '✅' : '❌';
    console.log(`${statusIcon} 状态: ${result.status}`);
    console.log(`📊 总违规数: ${result.totalViolations}`);
    console.log(`🚨 严重违规: ${result.criticalViolations}`);
    console.log(`⚠️  警告违规: ${result.warningViolations}`);
    console.log(`⏰ 检查时间: ${result.timestamp}`);
    
    console.log('\n📋 详细结果:');
    console.log(`  重复实现检测: ${result.details.redundancyDetection.details}`);
    console.log(`  已知重复验证: ${result.details.knownDuplicationsValidation.details}`);
    
    console.log(`\n💬 ${result.summary}`);
    console.log('='.repeat(60));
  }
}

// 主执行逻辑
async function main() {
  const checker = new RedundancyGateChecker();
  
  try {
    const result = await checker.runGateCheck();
    
    // 根据结果设置退出码
    if (result.status === 'FAILED') {
      console.error('\n❌ 重复实现门禁检查失败');
      process.exit(1);
    } else {
      console.log('\n✅ 重复实现门禁检查通过');
      process.exit(0);
    }
    
  } catch (error) {
    console.error('❌ 重复实现门禁检查执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { RedundancyGateChecker, RedundancyCheckResult };
