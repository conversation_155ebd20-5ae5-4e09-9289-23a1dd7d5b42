#!/bin/bash

# 质量门禁检查脚本
# 用于CI/CD流水线中的自动化质量检查
# 确保代码不包含降级逻辑、模拟数据等违反用户要求的实现

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
PROJECT_ROOT="."
REPORT_DIR="reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo -e "${BLUE}🚀 开始质量门禁检查...${NC}"
echo "时间: $(date)"
echo "项目根目录: $PROJECT_ROOT"
echo ""

# 创建报告目录
mkdir -p "$REPORT_DIR"

# 检查计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
run_check() {
    local check_name="$1"
    local command="$2"
    local fail_on_error="${3:-true}"
    
    echo -e "${BLUE}📋 执行检查: $check_name${NC}"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$command"; then
        echo -e "${GREEN}✅ $check_name: 通过${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌ $check_name: 失败${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        
        if [ "$fail_on_error" = "true" ]; then
            echo -e "${RED}💥 质量门禁检查失败，停止构建${NC}"
            exit 1
        fi
        return 1
    fi
}

# 1. 重复实现检测
echo -e "${YELLOW}🔍 1. 重复实现检测${NC}"
run_check "重复实现检测" "npm run detect:redundant > $REPORT_DIR/redundancy-check-$TIMESTAMP.log 2>&1"

# 2. 已知重复实现验证
echo -e "${YELLOW}🔍 2. 已知重复实现验证${NC}"
run_check "已知重复实现验证" "npm run validate:known-duplications > $REPORT_DIR/known-duplications-$TIMESTAMP.log 2>&1"

# 3. 反降级逻辑检测
echo -e "${YELLOW}🔍 3. 反降级逻辑检测${NC}"
run_check "反降级逻辑检测" "npx ts-node scripts/quality-assurance/anti-degradation-detector.ts . $REPORT_DIR/anti-degradation-$TIMESTAMP.txt"

# 4. 检查是否存在多个PrismaClient实例
echo -e "${YELLOW}🔍 4. PrismaClient实例检查${NC}"
check_prisma_instances() {
    local instances=$(grep -r "new PrismaClient" src/ --include="*.ts" | wc -l)
    if [ "$instances" -gt 1 ]; then
        echo "发现 $instances 个 PrismaClient 实例，违反单例模式要求"
        grep -r "new PrismaClient" src/ --include="*.ts"
        return 1
    fi
    echo "PrismaClient实例检查通过"
    return 0
}
run_check "PrismaClient单例检查" "check_prisma_instances"

# 5. 检查getDefault方法
echo -e "${YELLOW}🔍 5. getDefault方法检查${NC}"
check_get_default_methods() {
    local methods=$(grep -r "getDefault[A-Z]" src/ --include="*.ts" | wc -l)
    if [ "$methods" -gt 0 ]; then
        echo "发现 $methods 个 getDefault 方法，违反零容忍默认值要求"
        grep -r "getDefault[A-Z]" src/ --include="*.ts"
        return 1
    fi
    echo "getDefault方法检查通过"
    return 0
}
run_check "getDefault方法检查" "check_get_default_methods"

# 4. 检查模拟数据
echo -e "${YELLOW}🔍 4. 模拟数据检查${NC}"
check_mock_data() {
    local mock_count=$(grep -r -i "mock\|fake\|stub\|simulate\|模拟" src/ --include="*.ts" | grep -v "test\|spec" | wc -l)
    if [ "$mock_count" -gt 0 ]; then
        echo "发现 $mock_count 处模拟数据，违反真实数据要求"
        grep -r -i "mock\|fake\|stub\|simulate\|模拟" src/ --include="*.ts" | grep -v "test\|spec" | head -10
        return 1
    fi
    echo "模拟数据检查通过"
    return 0
}
run_check "模拟数据检查" "check_mock_data"

# 5. 检查降级逻辑关键词
echo -e "${YELLOW}🔍 5. 降级逻辑关键词检查${NC}"
check_degradation_keywords() {
    local degradation_count=$(grep -r -i "降级\|fallback\|degraded\|backup.*logic" src/ --include="*.ts" | wc -l)
    if [ "$degradation_count" -gt 0 ]; then
        echo "发现 $degradation_count 处降级逻辑关键词"
        grep -r -i "降级\|fallback\|degraded\|backup.*logic" src/ --include="*.ts" | head -5
        return 1
    fi
    echo "降级逻辑关键词检查通过"
    return 0
}
run_check "降级逻辑关键词检查" "check_degradation_keywords"

# 6. 检查TODO实现
echo -e "${YELLOW}🔍 6. TODO实现检查${NC}"
check_todo_implementations() {
    local todo_count=$(grep -r "TODO.*implement\|FIXME.*implement\|待实现\|需要实现" src/ --include="*.ts" | wc -l)
    if [ "$todo_count" -gt 5 ]; then  # 允许少量TODO，但不能太多
        echo "发现 $todo_count 处待实现功能，超过允许阈值(5)"
        grep -r "TODO.*implement\|FIXME.*implement\|待实现\|需要实现" src/ --include="*.ts" | head -10
        return 1
    fi
    echo "TODO实现检查通过 ($todo_count/5)"
    return 0
}
run_check "TODO实现检查" "check_todo_implementations" "false"  # 非阻塞检查

# 7. 检查硬编码配置
echo -e "${YELLOW}🔍 7. 硬编码配置检查${NC}"
check_hardcoded_config() {
    local hardcoded_count=$(grep -r "localhost\|127.0.0.1\|hardcoded\|硬编码" src/ --include="*.ts" | grep -v "test\|spec\|example" | wc -l)
    if [ "$hardcoded_count" -gt 3 ]; then  # 允许少量硬编码
        echo "发现 $hardcoded_count 处硬编码配置，超过允许阈值(3)"
        grep -r "localhost\|127.0.0.1\|hardcoded\|硬编码" src/ --include="*.ts" | grep -v "test\|spec\|example" | head -5
        return 1
    fi
    echo "硬编码配置检查通过 ($hardcoded_count/3)"
    return 0
}
run_check "硬编码配置检查" "check_hardcoded_config" "false"  # 非阻塞检查

# 8. TypeScript编译检查
echo -e "${YELLOW}🔍 8. TypeScript编译检查${NC}"
run_check "TypeScript编译" "npx tsc --noEmit" "false"  # 非阻塞检查

# 9. ESLint检查
echo -e "${YELLOW}🔍 9. ESLint代码质量检查${NC}"
run_check "ESLint检查" "npx eslint src/ --ext .ts --max-warnings 10" "false"  # 非阻塞检查

# 生成综合报告
echo ""
echo -e "${BLUE}📊 质量门禁检查总结${NC}"
echo "=================================="
echo "总检查项: $TOTAL_CHECKS"
echo -e "通过检查: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "失败检查: ${RED}$FAILED_CHECKS${NC}"
echo "检查时间: $(date)"

# 生成JSON格式报告
cat > "$REPORT_DIR/quality-gate-summary-$TIMESTAMP.json" << EOF
{
  "timestamp": "$(date -Iseconds)",
  "total_checks": $TOTAL_CHECKS,
  "passed_checks": $PASSED_CHECKS,
  "failed_checks": $FAILED_CHECKS,
  "success_rate": $(echo "scale=2; $PASSED_CHECKS * 100 / $TOTAL_CHECKS" | bc -l),
  "status": "$([ $FAILED_CHECKS -eq 0 ] && echo "PASSED" || echo "FAILED")"
}
EOF

# 最终结果
if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有质量门禁检查通过！${NC}"
    echo -e "${GREEN}✅ 代码质量符合要求，可以继续构建和部署${NC}"
    exit 0
else
    echo -e "${RED}💥 质量门禁检查失败！${NC}"
    echo -e "${RED}❌ 发现 $FAILED_CHECKS 个问题需要修复${NC}"
    echo -e "${YELLOW}📄 详细报告已保存到 $REPORT_DIR/ 目录${NC}"
    exit 1
fi
