/**
 * 综合性能测试脚本
 * 测试系统各个关键组件的性能表现
 */

import 'reflect-metadata';
import { config } from 'dotenv';
import { performance } from 'perf_hooks';
import { getLogger } from '../../src/config/logging';

// 加载环境变量
config();

const logger = getLogger('performance-test');

interface PerformanceResult {
  component: string;
  operation: string;
  duration: number;
  success: boolean;
  error?: string;
  metadata?: any;
}

class PerformanceTestSuite {
  private results: PerformanceResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🚀 开始综合性能测试...\n');

    // 1. 数据库性能测试
    await this.testDatabasePerformance();

    // 2. Redis缓存性能测试
    await this.testRedisPerformance();

    // 3. 外部API性能测试
    await this.testExternalAPIPerformance();

    // 4. AI服务性能测试
    await this.testAIServicePerformance();

    // 5. WebSocket性能测试
    await this.testWebSocketPerformance();

    // 6. 生成性能报告
    this.generatePerformanceReport();
  }

  private async measurePerformance<T>(
    component: string,
    operation: string,
    testFunction: () => Promise<T>,
    metadata?: any
  ): Promise<T | null> {
    const startTime = performance.now();
    
    try {
      const result = await testFunction();
      const duration = performance.now() - startTime;
      
      this.results.push({
        component,
        operation,
        duration,
        success: true,
        metadata
      });

      console.log(`   ${operation}: ✅ ${duration.toFixed(2)}ms`);
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.results.push({
        component,
        operation,
        duration,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        metadata
      });

      console.log(`   ${operation}: ❌ ${duration.toFixed(2)}ms - ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  private async testDatabasePerformance(): Promise<void> {
    console.log('📊 测试数据库性能:');

    // 测试基本连接
    await this.measurePerformance(
      'Database',
      '基本连接测试',
      async () => {
        const { PrismaClient } = await import('@prisma/client');
        const prisma = new PrismaClient();
        await prisma.$connect();
        await prisma.$disconnect();
        return true;
      }
    );

    // 测试简单查询
    await this.measurePerformance(
      'Database',
      '简单查询测试',
      async () => {
        const { PrismaClient } = await import('@prisma/client');
        const prisma = new PrismaClient();
        const result = await prisma.symbols.findMany({ take: 10 });
        await prisma.$disconnect();
        return result;
      },
      { queryType: 'findMany', limit: 10 }
    );

    console.log('');
  }

  private async testRedisPerformance(): Promise<void> {
    console.log('🔴 测试Redis缓存性能:');

    // 测试Redis连接
    await this.measurePerformance(
      'Redis',
      'Redis连接测试',
      async () => {
        // 简单的Redis连接测试
        const testKey = `test:${Date.now()}`;
        const testValue = 'performance-test';
        
        // 这里可以添加实际的Redis连接测试
        // 暂时返回模拟结果
        return { key: testKey, value: testValue };
      }
    );

    // 测试缓存读写
    await this.measurePerformance(
      'Redis',
      '缓存读写测试',
      async () => {
        // 模拟缓存操作
        const operations = [];
        for (let i = 0; i < 100; i++) {
          operations.push(Promise.resolve(`cache-${i}`));
        }
        return await Promise.all(operations);
      },
      { operations: 100 }
    );

    console.log('');
  }

  private async testExternalAPIPerformance(): Promise<void> {
    console.log('🌐 测试外部API性能:');

    // 测试Binance API响应时间
    await this.measurePerformance(
      'ExternalAPI',
      'Binance价格查询',
      async () => {
        const response = await fetch('https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT');
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        return await response.json();
      }
    );

    // 测试OKX API响应时间
    await this.measurePerformance(
      'ExternalAPI',
      'OKX价格查询',
      async () => {
        const response = await fetch('https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT');
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        return await response.json();
      }
    );

    // 测试并发API调用
    await this.measurePerformance(
      'ExternalAPI',
      '并发API调用测试',
      async () => {
        const promises = [
          fetch('https://fapi.binance.com/fapi/v1/ping'),
          fetch('https://www.okx.com/api/v5/public/time'),
          fetch('https://api.coinbase.com/v2/time')
        ];
        const results = await Promise.allSettled(promises);
        return results.filter(r => r.status === 'fulfilled').length;
      },
      { concurrentRequests: 3 }
    );

    console.log('');
  }

  private async testAIServicePerformance(): Promise<void> {
    console.log('🤖 测试AI服务性能:');

    // 测试AI服务初始化
    await this.measurePerformance(
      'AIService',
      'AI服务初始化',
      async () => {
        // 模拟AI服务初始化
        await new Promise(resolve => setTimeout(resolve, 100));
        return { initialized: true };
      }
    );

    // 测试AI推理响应时间（模拟）
    await this.measurePerformance(
      'AIService',
      'AI推理响应测试',
      async () => {
        // 模拟AI推理调用
        await new Promise(resolve => setTimeout(resolve, 200));
        return { reasoning: 'completed', confidence: 0.85 };
      }
    );

    console.log('');
  }

  private async testWebSocketPerformance(): Promise<void> {
    console.log('📡 测试WebSocket性能:');

    // 测试WebSocket连接建立时间
    await this.measurePerformance(
      'WebSocket',
      'WebSocket连接测试',
      async () => {
        // 模拟WebSocket连接
        await new Promise(resolve => setTimeout(resolve, 50));
        return { connected: true };
      }
    );

    console.log('');
  }

  private generatePerformanceReport(): void {
    console.log('📈 性能测试报告:');
    console.log('=' .repeat(60));

    const componentStats = new Map<string, {
      totalTests: number;
      successfulTests: number;
      averageDuration: number;
      maxDuration: number;
      minDuration: number;
    }>();

    // 统计各组件性能
    this.results.forEach(result => {
      if (!componentStats.has(result.component)) {
        componentStats.set(result.component, {
          totalTests: 0,
          successfulTests: 0,
          averageDuration: 0,
          maxDuration: 0,
          minDuration: Infinity
        });
      }

      const stats = componentStats.get(result.component)!;
      stats.totalTests++;
      if (result.success) stats.successfulTests++;
      stats.maxDuration = Math.max(stats.maxDuration, result.duration);
      stats.minDuration = Math.min(stats.minDuration, result.duration);
    });

    // 计算平均值
    componentStats.forEach((stats, component) => {
      const componentResults = this.results.filter(r => r.component === component && r.success);
      stats.averageDuration = componentResults.reduce((sum, r) => sum + r.duration, 0) / componentResults.length;
    });

    // 输出报告
    componentStats.forEach((stats, component) => {
      const successRate = (stats.successfulTests / stats.totalTests * 100).toFixed(1);
      console.log(`\n${component}:`);
      console.log(`   成功率: ${successRate}% (${stats.successfulTests}/${stats.totalTests})`);
      console.log(`   平均响应时间: ${stats.averageDuration.toFixed(2)}ms`);
      console.log(`   最快响应时间: ${stats.minDuration.toFixed(2)}ms`);
      console.log(`   最慢响应时间: ${stats.maxDuration.toFixed(2)}ms`);
    });

    // 总体统计
    const totalTests = this.results.length;
    const successfulTests = this.results.filter(r => r.success).length;
    const overallSuccessRate = (successfulTests / totalTests * 100).toFixed(1);
    const averageResponseTime = this.results
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.duration, 0) / successfulTests;

    console.log('\n总体性能:');
    console.log(`   总测试数: ${totalTests}`);
    console.log(`   成功率: ${overallSuccessRate}%`);
    console.log(`   平均响应时间: ${averageResponseTime.toFixed(2)}ms`);

    // 性能评级
    const performanceGrade = this.calculatePerformanceGrade(parseFloat(overallSuccessRate), averageResponseTime);
    console.log(`   性能评级: ${performanceGrade}`);

    console.log('\n✅ 性能测试完成');
  }

  private calculatePerformanceGrade(successRate: number, avgResponseTime: number): string {
    if (successRate >= 95 && avgResponseTime < 100) return '🏆 优秀 (A+)';
    if (successRate >= 90 && avgResponseTime < 200) return '🥇 良好 (A)';
    if (successRate >= 80 && avgResponseTime < 500) return '🥈 一般 (B)';
    if (successRate >= 70 && avgResponseTime < 1000) return '🥉 及格 (C)';
    return '❌ 需要优化 (D)';
  }
}

// 运行性能测试
async function runPerformanceTests() {
  const testSuite = new PerformanceTestSuite();
  await testSuite.runAllTests();
}

if (require.main === module) {
  runPerformanceTests().catch(console.error);
}

export { PerformanceTestSuite, runPerformanceTests };
