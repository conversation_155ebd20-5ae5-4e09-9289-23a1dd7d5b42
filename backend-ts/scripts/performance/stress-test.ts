/**
 * 系统压力测试脚本
 * 测试系统在高负载下的表现
 */

import 'reflect-metadata';
import { config } from 'dotenv';
import { performance } from 'perf_hooks';
import { getLogger } from '../../src/config/logging';

// 加载环境变量
config();

const logger = getLogger('stress-test');

interface StressTestConfig {
  concurrentUsers: number;
  requestsPerUser: number;
  testDurationMs: number;
}

interface StressTestResult {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
}

class StressTestSuite {
  private config: StressTestConfig = {
    concurrentUsers: 10,
    requestsPerUser: 20,
    testDurationMs: 30000 // 30秒
  };

  async runStressTests(): Promise<void> {
    console.log('💪 开始系统压力测试...\n');
    console.log(`配置: ${this.config.concurrentUsers}个并发用户，每用户${this.config.requestsPerUser}个请求\n`);

    // 1. API端点压力测试
    await this.testAPIEndpointStress();

    // 2. 数据库连接池压力测试
    await this.testDatabaseStress();

    // 3. 外部API调用压力测试
    await this.testExternalAPIStress();

    // 4. 内存使用监控
    await this.monitorMemoryUsage();

    console.log('\n✅ 压力测试完成');
  }

  private async testAPIEndpointStress(): Promise<void> {
    console.log('🌐 API端点压力测试:');

    const results = await this.runConcurrentRequests(
      'API健康检查',
      async () => {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
        if (Math.random() > 0.95) throw new Error('模拟API错误');
        return { status: 'ok' };
      }
    );

    this.printStressTestResults('API端点', results);
  }

  private async testDatabaseStress(): Promise<void> {
    console.log('\n📊 数据库压力测试:');

    const results = await this.runConcurrentRequests(
      '数据库查询',
      async () => {
        // 模拟数据库查询
        await new Promise(resolve => setTimeout(resolve, Math.random() * 200 + 100));
        if (Math.random() > 0.98) throw new Error('模拟数据库连接超时');
        return { rows: Math.floor(Math.random() * 100) };
      }
    );

    this.printStressTestResults('数据库', results);
  }

  private async testExternalAPIStress(): Promise<void> {
    console.log('\n🔗 外部API压力测试:');

    const results = await this.runConcurrentRequests(
      '外部API调用',
      async () => {
        // 实际的外部API调用
        const apis = [
          'https://fapi.binance.com/fapi/v1/ping',
          'https://www.okx.com/api/v5/public/time',
          'https://api.coinbase.com/v2/time'
        ];
        
        const randomAPI = apis[Math.floor(Math.random() * apis.length)];
        const response = await fetch(randomAPI);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }
        
        return await response.json();
      }
    );

    this.printStressTestResults('外部API', results);
  }

  private async runConcurrentRequests(
    testName: string,
    requestFunction: () => Promise<any>
  ): Promise<StressTestResult> {
    const startTime = performance.now();
    const responseTimes: number[] = [];
    let successfulRequests = 0;
    let failedRequests = 0;

    // 创建并发用户
    const userPromises: Promise<void>[] = [];

    for (let user = 0; user < this.config.concurrentUsers; user++) {
      const userPromise = this.simulateUser(user, requestFunction, responseTimes, (success) => {
        if (success) {
          successfulRequests++;
        } else {
          failedRequests++;
        }
      });
      userPromises.push(userPromise);
    }

    // 等待所有用户完成
    await Promise.allSettled(userPromises);

    const endTime = performance.now();
    const totalDuration = endTime - startTime;
    const totalRequests = successfulRequests + failedRequests;

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime: responseTimes.length > 0 ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0,
      minResponseTime: responseTimes.length > 0 ? Math.min(...responseTimes) : 0,
      maxResponseTime: responseTimes.length > 0 ? Math.max(...responseTimes) : 0,
      requestsPerSecond: totalRequests / (totalDuration / 1000),
      errorRate: (failedRequests / totalRequests) * 100
    };
  }

  private async simulateUser(
    userId: number,
    requestFunction: () => Promise<any>,
    responseTimes: number[],
    onComplete: (success: boolean) => void
  ): Promise<void> {
    for (let i = 0; i < this.config.requestsPerUser; i++) {
      const requestStart = performance.now();
      
      try {
        await requestFunction();
        const responseTime = performance.now() - requestStart;
        responseTimes.push(responseTime);
        onComplete(true);
      } catch (error) {
        const responseTime = performance.now() - requestStart;
        responseTimes.push(responseTime);
        onComplete(false);
      }

      // 用户请求间隔
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
    }
  }

  private printStressTestResults(testName: string, results: StressTestResult): void {
    console.log(`   ${testName}结果:`);
    console.log(`     总请求数: ${results.totalRequests}`);
    console.log(`     成功请求: ${results.successfulRequests}`);
    console.log(`     失败请求: ${results.failedRequests}`);
    console.log(`     成功率: ${((results.successfulRequests / results.totalRequests) * 100).toFixed(2)}%`);
    console.log(`     错误率: ${results.errorRate.toFixed(2)}%`);
    console.log(`     平均响应时间: ${results.averageResponseTime.toFixed(2)}ms`);
    console.log(`     最快响应: ${results.minResponseTime.toFixed(2)}ms`);
    console.log(`     最慢响应: ${results.maxResponseTime.toFixed(2)}ms`);
    console.log(`     吞吐量: ${results.requestsPerSecond.toFixed(2)} 请求/秒`);
    
    // 性能评级
    const grade = this.calculateStressTestGrade(results);
    console.log(`     压力测试评级: ${grade}`);
  }

  private calculateStressTestGrade(results: StressTestResult): string {
    const successRate = (results.successfulRequests / results.totalRequests) * 100;
    const avgResponseTime = results.averageResponseTime;
    const throughput = results.requestsPerSecond;

    if (successRate >= 99 && avgResponseTime < 200 && throughput > 50) {
      return '🏆 优秀 (A+)';
    } else if (successRate >= 95 && avgResponseTime < 500 && throughput > 30) {
      return '🥇 良好 (A)';
    } else if (successRate >= 90 && avgResponseTime < 1000 && throughput > 20) {
      return '🥈 一般 (B)';
    } else if (successRate >= 80 && avgResponseTime < 2000 && throughput > 10) {
      return '🥉 及格 (C)';
    } else {
      return '❌ 需要优化 (D)';
    }
  }

  private async monitorMemoryUsage(): Promise<void> {
    console.log('\n💾 内存使用监控:');

    const initialMemory = process.memoryUsage();
    console.log(`   初始内存使用:`);
    console.log(`     RSS: ${(initialMemory.rss / 1024 / 1024).toFixed(2)} MB`);
    console.log(`     Heap Used: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    console.log(`     Heap Total: ${(initialMemory.heapTotal / 1024 / 1024).toFixed(2)} MB`);

    // 模拟一些内存使用
    const testData: any[] = [];
    for (let i = 0; i < 10000; i++) {
      testData.push({
        id: i,
        data: `test-data-${i}`,
        timestamp: new Date(),
        randomData: Math.random().toString(36)
      });
    }

    const peakMemory = process.memoryUsage();
    console.log(`   峰值内存使用:`);
    console.log(`     RSS: ${(peakMemory.rss / 1024 / 1024).toFixed(2)} MB`);
    console.log(`     Heap Used: ${(peakMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    console.log(`     Heap Total: ${(peakMemory.heapTotal / 1024 / 1024).toFixed(2)} MB`);

    // 清理测试数据
    testData.length = 0;
    
    // 强制垃圾回收（如果可用）
    if (global.gc) {
      global.gc();
    }

    const finalMemory = process.memoryUsage();
    console.log(`   清理后内存使用:`);
    console.log(`     RSS: ${(finalMemory.rss / 1024 / 1024).toFixed(2)} MB`);
    console.log(`     Heap Used: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    console.log(`     Heap Total: ${(finalMemory.heapTotal / 1024 / 1024).toFixed(2)} MB`);

    const memoryIncrease = (finalMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024;
    console.log(`   内存增长: ${memoryIncrease.toFixed(2)} MB`);
    
    if (memoryIncrease < 10) {
      console.log(`   内存管理: ✅ 良好`);
    } else if (memoryIncrease < 50) {
      console.log(`   内存管理: ⚠️ 一般`);
    } else {
      console.log(`   内存管理: ❌ 需要优化`);
    }
  }
}

// 运行压力测试
async function runStressTests() {
  const testSuite = new StressTestSuite();
  await testSuite.runStressTests();
}

if (require.main === module) {
  runStressTests().catch(console.error);
}

export { StressTestSuite, runStressTests };
