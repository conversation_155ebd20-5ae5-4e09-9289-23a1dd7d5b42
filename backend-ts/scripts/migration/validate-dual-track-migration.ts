/**
 * 验证双轨制数据库迁移脚本
 * 运行命令: npx ts-node scripts/validate-dual-track-migration.ts
 */

import { PrismaClient } from '@prisma/client';


/**
 * 双轨迁移验证
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

const prisma = new PrismaClient();

interface ValidationResult {
  checkName: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  details: string;
  suggestions?: string[];
}

class DualTrackMigrationValidator {
  private results: ValidationResult[] = [];

  async validateMigration(): Promise<ValidationResult[]> {
    console.log('🔍 开始验证双轨制数据库迁移...\n');

    try {
      await this.checkTableStructure();
      await this.checkIndexes();
      await this.checkConstraints();
      await this.checkViews();
      await this.checkFunctions();
      await this.checkDefaultData();
      await this.checkDataConsistency();
    } catch (error) {
      this.addResult({
        checkName: 'Database Connection',
        status: 'FAIL',
        details: `数据库连接失败: ${error instanceof Error ? error.message : String(error)}`,
        suggestions: ['检查数据库连接配置', '确保数据库服务正在运行']
      });
    } finally {
      await prisma.$disconnect();
    }

    return this.results;
  }

  private async checkTableStructure(): Promise<void> {
    console.log('📋 检查表结构...');

    // 检查 TradingAccounts 表的新字段
    try {
      const accountColumns = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'TradingAccounts' 
        AND column_name IN ('accountType', 'executionEngine', 'apiCredentials', 'syncSettings', 'parentAccountId')
        ORDER BY column_name;
      `;

      const expectedColumns = ['accountType', 'executionEngine', 'apiCredentials', 'syncSettings', 'parentAccountId'];
      const actualColumns = (accountColumns as any[]).map(col => col.column_name);
      const missingColumns = expectedColumns.filter(col => !actualColumns.includes(col));

      if (missingColumns.length === 0) {
        this.addResult({
          checkName: 'TradingAccounts Table Structure',
          status: 'PASS',
          details: '所有双轨制字段已成功添加到 TradingAccounts 表'
        });
      } else {
        this.addResult({
          checkName: 'TradingAccounts Table Structure',
          status: 'FAIL',
          details: `缺少字段: ${missingColumns.join(', ')}`,
          suggestions: ['运行数据库迁移脚本', '检查迁移脚本是否正确执行']
        });
      }
    } catch (error) {
      this.addResult({
        checkName: 'TradingAccounts Table Structure',
        status: 'FAIL',
        details: `检查失败: ${error instanceof Error ? error.message : String(error)}`
      });
    }

    // 检查 TradingOrders 表的新字段
    try {
      const orderColumns = await prisma.$queryRaw`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'TradingOrders' 
        AND column_name IN ('executionSource', 'engineMetadata');
      `;

      const expectedOrderColumns = ['executionSource', 'engineMetadata'];
      const actualOrderColumns = (orderColumns as any[]).map(col => col.column_name);
      const missingOrderColumns = expectedOrderColumns.filter(col => !actualOrderColumns.includes(col));

      if (missingOrderColumns.length === 0) {
        this.addResult({
          checkName: 'TradingOrders Table Structure',
          status: 'PASS',
          details: '所有双轨制字段已成功添加到 TradingOrders 表'
        });
      } else {
        this.addResult({
          checkName: 'TradingOrders Table Structure',
          status: 'FAIL',
          details: `缺少字段: ${missingOrderColumns.join(', ')}`
        });
      }
    } catch (error) {
      this.addResult({
        checkName: 'TradingOrders Table Structure',
        status: 'FAIL',
        details: `检查失败: ${error instanceof Error ? error.message : String(error)}`
      });
    }

    // 检查 TradingPositions 表的新字段
    try {
      const positionColumns = await prisma.$queryRaw`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'TradingPositions' 
        AND column_name IN ('executionSource', 'syncedFromId');
      `;

      const expectedPositionColumns = ['executionSource', 'syncedFromId'];
      const actualPositionColumns = (positionColumns as any[]).map(col => col.column_name);
      const missingPositionColumns = expectedPositionColumns.filter(col => !actualPositionColumns.includes(col));

      if (missingPositionColumns.length === 0) {
        this.addResult({
          checkName: 'TradingPositions Table Structure',
          status: 'PASS',
          details: '所有双轨制字段已成功添加到 TradingPositions 表'
        });
      } else {
        this.addResult({
          checkName: 'TradingPositions Table Structure',
          status: 'FAIL',
          details: `缺少字段: ${missingPositionColumns.join(', ')}`
        });
      }
    } catch (error) {
      this.addResult({
        checkName: 'TradingPositions Table Structure',
        status: 'FAIL',
        details: `检查失败: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  }

  private async checkIndexes(): Promise<void> {
    console.log('📇 检查索引...');

    try {
      const indexes = await prisma.$queryRaw`
        SELECT indexname 
        FROM pg_indexes 
        WHERE tablename IN ('TradingAccounts', 'TradingOrders', 'TradingPositions')
        AND indexname LIKE '%account_type%' 
        OR indexname LIKE '%execution_source%' 
        OR indexname LIKE '%execution_engine%';
      `;

      const indexCount = (indexes as any[]).length;
      
      if (indexCount >= 5) { // 预期至少5个双轨制相关索引
        this.addResult({
          checkName: 'Dual Track Indexes',
          status: 'PASS',
          details: `找到 ${indexCount} 个双轨制相关索引`
        });
      } else {
        this.addResult({
          checkName: 'Dual Track Indexes',
          status: 'WARNING',
          details: `只找到 ${indexCount} 个双轨制相关索引，可能缺少一些索引`,
          suggestions: ['检查索引创建脚本', '手动创建缺失的索引']
        });
      }
    } catch (error) {
      this.addResult({
        checkName: 'Dual Track Indexes',
        status: 'FAIL',
        details: `检查索引失败: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  }

  private async checkConstraints(): Promise<void> {
    console.log('🔒 检查约束...');

    try {
      const constraints = await prisma.$queryRaw`
        SELECT constraint_name, table_name 
        FROM information_schema.table_constraints 
        WHERE constraint_type = 'CHECK' 
        AND (constraint_name LIKE '%account_type%' 
             OR constraint_name LIKE '%execution_source%');
      `;

      const constraintCount = (constraints as any[]).length;
      
      if (constraintCount >= 3) { // 预期至少3个检查约束
        this.addResult({
          checkName: 'Check Constraints',
          status: 'PASS',
          details: `找到 ${constraintCount} 个双轨制检查约束`
        });
      } else {
        this.addResult({
          checkName: 'Check Constraints',
          status: 'WARNING',
          details: `只找到 ${constraintCount} 个检查约束，可能缺少一些约束`
        });
      }
    } catch (error) {
      this.addResult({
        checkName: 'Check Constraints',
        status: 'FAIL',
        details: `检查约束失败: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  }

  private async checkViews(): Promise<void> {
    console.log('👁️ 检查视图...');

    try {
      const views = await prisma.$queryRaw`
        SELECT viewname 
        FROM pg_views 
        WHERE viewname = 'TradingPerformanceComparison';
      `;

      if ((views as any[]).length > 0) {
        this.addResult({
          checkName: 'Performance Comparison View',
          status: 'PASS',
          details: 'TradingPerformanceComparison 视图已创建'
        });
      } else {
        this.addResult({
          checkName: 'Performance Comparison View',
          status: 'FAIL',
          details: '缺少 TradingPerformanceComparison 视图',
          suggestions: ['运行视图创建脚本']
        });
      }
    } catch (error) {
      this.addResult({
        checkName: 'Performance Comparison View',
        status: 'FAIL',
        details: `检查视图失败: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  }

  private async checkFunctions(): Promise<void> {
    console.log('⚙️ 检查函数...');

    try {
      const functions = await prisma.$queryRaw`
        SELECT proname 
        FROM pg_proc 
        WHERE proname IN ('get_dual_track_statistics', 'validate_dual_track_consistency');
      `;

      const functionCount = (functions as any[]).length;
      
      if (functionCount >= 2) {
        this.addResult({
          checkName: 'Dual Track Functions',
          status: 'PASS',
          details: '双轨制相关函数已创建'
        });
      } else {
        this.addResult({
          checkName: 'Dual Track Functions',
          status: 'WARNING',
          details: `只找到 ${functionCount} 个函数，可能缺少一些函数`
        });
      }
    } catch (error) {
      this.addResult({
        checkName: 'Dual Track Functions',
        status: 'FAIL',
        details: `检查函数失败: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  }

  private async checkDefaultData(): Promise<void> {
    console.log('📊 检查默认数据...');

    try {
      const configCount = await prisma.$queryRaw`
        SELECT COUNT(*) as count 
        FROM "DualTrackConfigs" 
        WHERE "isActive" = true;
      `;

      const count = (configCount as any[])[0]?.count || 0;
      
      if (count >= 10) {
        this.addResult({
          checkName: 'Default Configuration Data',
          status: 'PASS',
          details: `找到 ${count} 条默认配置数据`
        });
      } else {
        this.addResult({
          checkName: 'Default Configuration Data',
          status: 'WARNING',
          details: `只找到 ${count} 条配置数据，可能缺少默认配置`
        });
      }
    } catch (error) {
      this.addResult({
        checkName: 'Default Configuration Data',
        status: 'FAIL',
        details: `检查默认数据失败: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  }

  private async checkDataConsistency(): Promise<void> {
    console.log('🔍 检查数据一致性...');

    try {
      // 运行一致性检查函数
      const consistencyResults = await prisma.$queryRaw`
        SELECT * FROM validate_dual_track_consistency();
      `;

      const results = consistencyResults as any[];
      let allPassed = true;

      for (const result of results) {
        if (result.status !== 'PASS') {
          allPassed = false;
          this.addResult({
            checkName: `Data Consistency: ${result.check_name}`,
            status: 'FAIL',
            details: result.details,
            suggestions: ['检查数据迁移脚本', '手动修复不一致的数据']
          });
        }
      }

      if (allPassed) {
        this.addResult({
          checkName: 'Data Consistency',
          status: 'PASS',
          details: '所有数据一致性检查通过'
        });
      }
    } catch (error) {
      this.addResult({
        checkName: 'Data Consistency',
        status: 'FAIL',
        details: `一致性检查失败: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  }

  private addResult(result: ValidationResult): void {
    this.results.push(result);
    
    const statusIcon = {
      'PASS': '✅',
      'FAIL': '❌',
      'WARNING': '⚠️'
    }[result.status];

    console.log(`${statusIcon} ${result.checkName}: ${result.details}`);
    
    if (result.suggestions) {
      result.suggestions.forEach(suggestion => {
        console.log(`   💡 建议: ${suggestion}`);
      });
    }
    console.log();
  }

  printSummary(): void {
    const passCount = this.results.filter(r => r.status === 'PASS').length;
    const failCount = this.results.filter(r => r.status === 'FAIL').length;
    const warningCount = this.results.filter(r => r.status === 'WARNING').length;

    console.log('📋 验证总结:');
    console.log(`✅ 通过: ${passCount}`);
    console.log(`❌ 失败: ${failCount}`);
    console.log(`⚠️ 警告: ${warningCount}`);
    console.log(`📊 总计: ${this.results.length}`);

    if (failCount === 0) {
      console.log('\n🎉 双轨制数据库迁移验证成功！');
    } else {
      console.log('\n🚨 发现问题，请检查失败的项目并修复。');
    }
  }
}

// 运行验证
async function main() {
  const validator = new DualTrackMigrationValidator();
  await validator.validateMigration();
  validator.printSummary();
}

if (require.main === module) {
  main().catch(console.error);
}

export { DualTrackMigrationValidator };
