#!/usr/bin/env tsx

/**
 * Schema规范化主执行脚本
 * 
 * 统一执行所有Schema规范化操作：
 * 1. 添加@map注解
 * 2. 优化索引策略
 * 3. 验证Schema完整性
 * 4. 生成迁移脚本
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { SchemaNormalizer } from './schema-normalization';
import { addMapAnnotations, analyzeSchema } from './generate-map-annotations';
import { generateSQLIndexes, updateSchemaIndexes } from './generate-optimized-indexes';

interface NormalizationOptions {
  dryRun?: boolean;
  skipBackup?: boolean;
  skipValidation?: boolean;
  generateMigration?: boolean;
  applyChanges?: boolean;
}

class SchemaManager {
  private readonly schemaPath = 'prisma/schema.prisma';
  private readonly backupDir = 'prisma/backups';
  private readonly migrationsDir = 'prisma/migrations';

  constructor() {
    this.ensureDirectories();
  }

  /**
   * 确保必要目录存在
   */
  private ensureDirectories(): void {
    [this.backupDir, this.migrationsDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  /**
   * 执行完整的Schema规范化
   */
  async runNormalization(options: NormalizationOptions = {}): Promise<void> {
    console.log('🚀 开始Schema规范化流程...\n');
    console.log('配置选项:', options);
    console.log('='.repeat(60));

    try {
      // 1. 分析当前状态
      await this.analyzeCurrentState();

      // 2. 创建备份
      if (!options.skipBackup) {
        await this.createBackup();
      }

      // 3. 执行规范化（干运行模式）
      if (options.dryRun) {
        await this.performDryRun();
        return;
      }

      // 4. 添加@map注解
      await this.addMapAnnotations();

      // 5. 优化索引
      await this.optimizeIndexes();

      // 6. 验证Schema
      if (!options.skipValidation) {
        await this.validateSchema();
      }

      // 7. 生成迁移脚本
      if (options.generateMigration) {
        await this.generateMigration();
      }

      // 8. 应用更改
      if (options.applyChanges) {
        await this.applyChanges();
      }

      console.log('\n✅ Schema规范化完成！');
      this.printNextSteps(options);

    } catch (error) {
      console.error('❌ Schema规范化失败:', error);
      
      if (!options.skipBackup) {
        console.log('🔄 正在恢复备份...');
        await this.restoreLatestBackup();
      }
      
      throw error;
    }
  }

  /**
   * 分析当前Schema状态
   */
  private async analyzeCurrentState(): Promise<void> {
    console.log('📊 分析当前Schema状态...\n');
    
    const schemaContent = fs.readFileSync(this.schemaPath, 'utf-8');
    
    // 统计基本信息
    const models = (schemaContent.match(/model\s+\w+\s*{/g) || []).length;
    const mapAnnotations = (schemaContent.match(/@@map\(/g) || []).length;
    const indexes = (schemaContent.match(/@@index\(/g) || []).length;
    const uniqueConstraints = (schemaContent.match(/@@unique\(/g) || []).length;
    
    console.log(`📋 模型总数: ${models}`);
    console.log(`🏷️  @map注解: ${mapAnnotations}/${models} (${Math.round(mapAnnotations/models*100)}%)`);
    console.log(`📊 索引数量: ${indexes}`);
    console.log(`🔑 唯一约束: ${uniqueConstraints}`);
    
    // 详细分析
    analyzeSchema();
    
    console.log('\n' + '='.repeat(60));
  }

  /**
   * 创建备份
   */
  private async createBackup(): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.backupDir, `schema_${timestamp}.prisma`);
    
    fs.copyFileSync(this.schemaPath, backupPath);
    console.log(`📁 Schema备份已创建: ${backupPath}`);
    
    return backupPath;
  }

  /**
   * 执行干运行
   */
  private async performDryRun(): Promise<void> {
    console.log('🔍 执行干运行模式（不会修改文件）...\n');
    
    const schemaContent = fs.readFileSync(this.schemaPath, 'utf-8');
    
    // 模拟添加@map注解
    const withMapAnnotations = addMapAnnotations(schemaContent);
    const mapChanges = withMapAnnotations !== schemaContent;
    
    console.log(`📝 @map注解更改: ${mapChanges ? '是' : '否'}`);
    
    if (mapChanges) {
      const originalMaps = (schemaContent.match(/@@map\(/g) || []).length;
      const newMaps = (withMapAnnotations.match(/@@map\(/g) || []).length;
      console.log(`   将添加 ${newMaps - originalMaps} 个@map注解`);
    }
    
    // 模拟索引优化
    console.log('📊 索引优化预览:');
    console.log('   - 将为核心表添加性能优化索引');
    console.log('   - 将为JSON字段添加GIN索引');
    console.log('   - 将添加复合索引以提高查询性能');
    
    console.log('\n💡 要执行实际更改，请使用 --apply 参数');
  }

  /**
   * 添加@map注解
   */
  private async addMapAnnotations(): Promise<void> {
    console.log('🔧 添加@map注解...');
    
    const schemaContent = fs.readFileSync(this.schemaPath, 'utf-8');
    const updatedContent = addMapAnnotations(schemaContent);
    
    fs.writeFileSync(this.schemaPath, updatedContent);
    console.log('✅ @map注解添加完成');
  }

  /**
   * 优化索引
   */
  private async optimizeIndexes(): Promise<void> {
    console.log('📊 优化索引策略...');
    
    // 更新Schema中的索引
    updateSchemaIndexes();
    
    // 生成SQL索引脚本
    const sqlScript = generateSQLIndexes();
    const sqlPath = path.join(this.migrationsDir, 'optimize_indexes.sql');
    fs.writeFileSync(sqlPath, sqlScript);
    
    console.log('✅ 索引优化完成');
    console.log(`📄 SQL脚本已生成: ${sqlPath}`);
  }

  /**
   * 验证Schema
   */
  private async validateSchema(): Promise<void> {
    console.log('🔍 验证Schema完整性...');
    
    try {
      execSync('npx prisma validate', { stdio: 'pipe' });
      console.log('✅ Schema验证通过');
    } catch (error) {
      throw new Error(`Schema验证失败: ${error}`);
    }
  }

  /**
   * 生成迁移脚本
   */
  private async generateMigration(): Promise<void> {
    console.log('📝 生成迁移脚本...');
    
    try {
      const migrationName = `schema_normalization_${Date.now()}`;
      execSync(`npx prisma migrate dev --name ${migrationName}`, { stdio: 'inherit' });
      console.log('✅ 迁移脚本生成成功');
    } catch (error) {
      throw new Error(`迁移脚本生成失败: ${error}`);
    }
  }

  /**
   * 应用更改
   */
  private async applyChanges(): Promise<void> {
    console.log('🚀 应用Schema更改...');
    
    try {
      // 生成Prisma客户端
      execSync('npx prisma generate', { stdio: 'pipe' });
      console.log('✅ Prisma客户端已更新');
      
      // 推送到数据库
      execSync('npx prisma db push', { stdio: 'inherit' });
      console.log('✅ 数据库Schema已更新');
      
    } catch (error) {
      throw new Error(`应用更改失败: ${error}`);
    }
  }

  /**
   * 恢复最新备份
   */
  private async restoreLatestBackup(): Promise<void> {
    const backupFiles = fs.readdirSync(this.backupDir)
      .filter(file => file.startsWith('schema_') && file.endsWith('.prisma'))
      .sort()
      .reverse();

    if (backupFiles.length === 0) {
      throw new Error('没有找到备份文件');
    }

    const latestBackup = path.join(this.backupDir, backupFiles[0]);
    fs.copyFileSync(latestBackup, this.schemaPath);
    console.log(`🔄 已恢复备份: ${latestBackup}`);
  }

  /**
   * 打印后续步骤
   */
  private printNextSteps(options: NormalizationOptions): void {
    console.log('\n📋 后续步骤:');
    
    if (!options.applyChanges) {
      console.log('1. 检查Schema更改是否符合预期');
      console.log('2. 运行 npm run db:generate 更新Prisma客户端');
      console.log('3. 运行 npm run db:push 应用到数据库');
    }
    
    if (!options.generateMigration) {
      console.log('4. 考虑生成迁移脚本用于生产环境');
    }
    
    console.log('5. 执行生成的SQL索引脚本以优化性能');
    console.log('6. 使用性能分析脚本监控优化效果');
    console.log('7. 运行测试确保应用程序正常工作');
  }
}

/**
 * 解析命令行参数
 */
function parseArgs(): NormalizationOptions {
  const args = process.argv.slice(2);
  
  return {
    dryRun: args.includes('--dry-run'),
    skipBackup: args.includes('--skip-backup'),
    skipValidation: args.includes('--skip-validation'),
    generateMigration: args.includes('--generate-migration'),
    applyChanges: args.includes('--apply')
  };
}

/**
 * 显示帮助信息
 */
function showHelp(): void {
  console.log(`
Schema规范化工具

用法: tsx run-schema-normalization.ts [选项]

选项:
  --dry-run              只分析不修改文件
  --skip-backup          跳过备份创建
  --skip-validation      跳过Schema验证
  --generate-migration   生成迁移脚本
  --apply               应用所有更改到数据库
  --help                显示此帮助信息

示例:
  tsx run-schema-normalization.ts --dry-run
  tsx run-schema-normalization.ts --apply --generate-migration
`);
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    showHelp();
    return;
  }
  
  const options = parseArgs();
  const manager = new SchemaManager();
  
  try {
    await manager.runNormalization(options);
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { SchemaManager };
