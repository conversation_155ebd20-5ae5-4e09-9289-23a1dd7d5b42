/**
 * 同步真实币安数据到数据库
 * 从币安API获取真实的账户余额和持仓数据，并更新到数据库
 */

import * as dotenv from 'dotenv';
import Binance from 'binance-api-node';
import { PrismaClient } from '@prisma/client';
import { getEnvironment } from '../src/config/environment';


/**
 * Binance数据同步
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

// 加载环境变量
dotenv.config();

async function syncRealBinanceData() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🚀 开始同步真实币安数据到数据库...\n');

    const env = getEnvironment();
    
    if (!env.BINANCE_API_KEY || !env.BINANCE_SECRET_KEY) {
      throw new Error('缺少币安API凭证环境变量');
    }

    // 创建币安客户端
    const client = Binance({
      apiKey: env.BINANCE_API_KEY,
      apiSecret: env.BINANCE_SECRET_KEY
    });

    // 查找真实交易账户
    const realAccount = await prisma.tradingAccounts.findFirst({
      where: {
        accountType: 'LIVE',
        executionEngine: 'binance'
      }
    });

    if (!realAccount) {
      console.log('❌ 未找到真实交易账户');
      return;
    }

    console.log(`✅ 找到真实交易账户: ${realAccount.name} (${realAccount.id})`);

    // 1. 获取期货账户信息
    console.log('\n1️⃣ 获取期货账户信息...');
    const futuresAccountInfo = await client.futuresAccountInfo();
    
    console.log(`   总余额: ${futuresAccountInfo.totalWalletBalance} USDT`);
    console.log(`   可用余额: ${futuresAccountInfo.availableBalance} USDT`);
    console.log(`   总未实现盈亏: ${futuresAccountInfo.totalUnrealizedProfit} USDT`);

    // 更新账户余额
    await prisma.tradingAccounts.update({
      where: { id: realAccount.id },
      data: {
        currentBalance: parseFloat(futuresAccountInfo.totalWalletBalance),
        availableBalance: parseFloat(futuresAccountInfo.availableBalance),
        totalPnl: parseFloat(futuresAccountInfo.totalUnrealizedProfit),
        updatedAt: new Date()
      }
    });

    console.log('✅ 账户余额已更新到数据库');

    // 2. 获取期货持仓信息
    console.log('\n2️⃣ 获取期货持仓信息...');
    const positions = await client.futuresPositionRisk();
    const activePositions = positions.filter(p => parseFloat(p.positionAmt) !== 0);
    
    console.log(`   总持仓数: ${positions.length}`);
    console.log(`   活跃持仓数: ${activePositions.length}`);

    // 清理现有持仓数据
    await prisma.tradingPositions.deleteMany({
      where: { accountId: realAccount.id }
    });
    console.log('🗑️ 清理了现有持仓数据');

    // 3. 同步活跃持仓到数据库
    if (activePositions.length > 0) {
      console.log('\n3️⃣ 同步活跃持仓到数据库...');
      
      for (const position of activePositions) {
        console.log(`\n   处理持仓: ${position.symbol}`);
        console.log(`     持仓数量: ${position.positionAmt}`);
        console.log(`     入场价格: ${position.entryPrice}`);
        console.log(`     未实现盈亏: ${position.unRealizedProfit}`);

        // 查找或创建交易对
        let symbol = await prisma.symbols.findFirst({
          where: { symbol: position.symbol }
        });

        if (!symbol) {
          // 创建新的交易对
          symbol = await prisma.symbols.create({
            data: {
              symbol: position.symbol,
              baseAsset: position.symbol.replace('USDT', ''),
              quoteAsset: 'USDT',
              isActive: true,
              tickSize: 0.01,
              stepSize: 0.001,
              minNotional: 5.0
            }
          });
          console.log(`     ✅ 创建了新交易对: ${symbol.symbol}`);
        }

        // 创建持仓记录
        const positionAmt = parseFloat(position.positionAmt);
        const entryPrice = parseFloat(position.entryPrice);
        const markPrice = parseFloat(position.markPrice);
        const unrealizedPnl = parseFloat(position.unRealizedProfit);

        await prisma.tradingPositions.create({
          data: {
            accountId: realAccount.id,
            symbolId: symbol.id,
            side: positionAmt > 0 ? 'LONG' : 'SHORT',
            entryPrice: entryPrice,
            quantity: Math.abs(positionAmt),
            leverage: parseFloat(position.leverage),
            marginUsed: Math.abs(positionAmt) * entryPrice / parseFloat(position.leverage),
            currentPrice: markPrice,
            unrealizedPnl: unrealizedPnl,
            realizedPnl: 0,
            status: 'OPEN',
            pyramidLevel: 1,
            atrValue: Math.abs(markPrice - entryPrice),
            openedAt: new Date(),
            executionSource: 'BINANCE_LIVE'
          }
        });

        console.log(`     ✅ 持仓已同步到数据库`);
      }

      console.log(`\n✅ 成功同步了 ${activePositions.length} 个活跃持仓`);
    } else {
      console.log('\n   当前没有活跃持仓');
    }

    // 4. 创建或更新统计数据
    console.log('\n4️⃣ 更新交易统计数据...');
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 删除今天的统计数据
    await prisma.tradingStatistics.deleteMany({
      where: {
        accountId: realAccount.id,
        date: today
      }
    });

    // 创建今天的统计数据
    await prisma.tradingStatistics.create({
      data: {
        accountId: realAccount.id,
        date: today,
        totalTrades: activePositions.length,
        winningTrades: activePositions.filter(p => parseFloat(p.unRealizedProfit) > 0).length,
        losingTrades: activePositions.filter(p => parseFloat(p.unRealizedProfit) < 0).length,
        totalPnl: parseFloat(futuresAccountInfo.totalUnrealizedProfit),
        grossProfit: activePositions
          .filter(p => parseFloat(p.unRealizedProfit) > 0)
          .reduce((sum, p) => sum + parseFloat(p.unRealizedProfit), 0),
        grossLoss: Math.abs(activePositions
          .filter(p => parseFloat(p.unRealizedProfit) < 0)
          .reduce((sum, p) => sum + parseFloat(p.unRealizedProfit), 0)),
        maxDrawdown: 0,
        winRate: activePositions.length > 0 ? 
          activePositions.filter(p => parseFloat(p.unRealizedProfit) > 0).length / activePositions.length : 0,
        profitFactor: 1.0
      }
    });

    console.log('✅ 交易统计数据已更新');

    console.log('\n🎉 数据同步完成！');
    console.log('\n📊 同步结果汇总:');
    console.log(`   账户余额: ${futuresAccountInfo.totalWalletBalance} USDT`);
    console.log(`   活跃持仓: ${activePositions.length} 个`);
    console.log(`   未实现盈亏: ${futuresAccountInfo.totalUnrealizedProfit} USDT`);

  } catch (error) {
    console.error('❌ 数据同步失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  syncRealBinanceData();
}
