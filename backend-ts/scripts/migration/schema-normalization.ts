#!/usr/bin/env tsx

/**
 * 数据库Schema规范化脚本
 * 
 * 功能：
 * 1. 修复49个表的@map注解
 * 2. 统一命名规范
 * 3. 优化索引策略
 * 4. 验证Schema完整性
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

interface TableMapping {
  modelName: string;
  tableName: string;
  needsMap: boolean;
}

interface IndexOptimization {
  table: string;
  indexes: string[];
  reason: string;
}

/**
 * 标准表名映射 - 统一使用snake_case命名
 */
const TABLE_MAPPINGS: TableMapping[] = [
  { modelName: 'AiCallLogs', tableName: 'ai_call_logs', needsMap: true },
  { modelName: 'AiOptimizationSuggestions', tableName: 'ai_optimization_suggestions', needsMap: true },
  { modelName: 'AiReasoningChains', tableName: 'ai_reasoning_chains', needsMap: true },
  { modelName: 'ApiKeys', tableName: 'api_keys', needsMap: true },
  { modelName: 'ApiPerformanceMetrics', tableName: 'api_performance_metrics', needsMap: true },
  { modelName: 'CacheConfigurations', tableName: 'cache_configurations', needsMap: true },
  { modelName: 'CachePerformanceMetrics', tableName: 'cache_performance_metrics', needsMap: true },
  { modelName: 'ChartDataCache', tableName: 'chart_data_cache', needsMap: true },
  { modelName: 'ConfigItems', tableName: 'config_items', needsMap: true },
  { modelName: 'CrossTimeframeInsights', tableName: 'cross_timeframe_insights', needsMap: true },
  { modelName: 'DataQualityMetrics', tableName: 'data_quality_metrics', needsMap: true },
  { modelName: 'DecisionOutcomes', tableName: 'decision_outcomes', needsMap: true },
  { modelName: 'HistoricalData', tableName: 'historical_data', needsMap: true },
  { modelName: 'InvestmentDecisions', tableName: 'investment_decisions', needsMap: true },
  { modelName: 'InvitationCodes', tableName: 'invitation_codes', needsMap: true },
  { modelName: 'KnowledgeEntities', tableName: 'knowledge_entities', needsMap: true },
  { modelName: 'KnowledgeRelations', tableName: 'knowledge_relations', needsMap: true },
  { modelName: 'KnowledgeSearchIndices', tableName: 'knowledge_search_indices', needsMap: true },
  { modelName: 'LearningEffectivenessLog', tableName: 'learning_effectiveness_log', needsMap: true },
  { modelName: 'LearningKnowledgeBase', tableName: 'learning_knowledge_base', needsMap: true },
  { modelName: 'LongCycleMetrics', tableName: 'long_cycle_metrics', needsMap: true },
  { modelName: 'LongCyclePredictions', tableName: 'long_cycle_predictions', needsMap: true },
  { modelName: 'MacroCycleMetrics', tableName: 'macro_cycle_metrics', needsMap: true },
  { modelName: 'MacroCyclePredictions', tableName: 'macro_cycle_predictions', needsMap: true },
  { modelName: 'MacroLearningKnowledge', tableName: 'macro_learning_knowledge', needsMap: true },
  { modelName: 'MacroPredictionValidations', tableName: 'macro_prediction_validations', needsMap: true },
  { modelName: 'MarketSentiment', tableName: 'market_sentiment', needsMap: true },
  { modelName: 'ModelPerformanceMetrics', tableName: 'model_performance_metrics', needsMap: true },
  { modelName: 'OptimizationRecords', tableName: 'optimization_records', needsMap: true },
  { modelName: 'ParameterAdjustmentHistory', tableName: 'parameter_adjustment_history', needsMap: true },
  { modelName: 'ParameterConfigs', tableName: 'parameter_configs', needsMap: true },
  { modelName: 'ParameterOptimizations', tableName: 'parameter_optimizations', needsMap: true },
  { modelName: 'PriceData', tableName: 'price_data', needsMap: true },
  { modelName: 'PromptTemplates', tableName: 'prompt_templates', needsMap: true },
  { modelName: 'RiskAlerts', tableName: 'risk_alerts', needsMap: true },
  { modelName: 'RiskAssessments', tableName: 'risk_assessments', needsMap: true },
  { modelName: 'RiskEvents', tableName: 'risk_events', needsMap: true },
  { modelName: 'ShortCycleMetrics', tableName: 'short_cycle_metrics', needsMap: true },
  { modelName: 'ShortCyclePredictions', tableName: 'short_cycle_predictions', needsMap: true },
  { modelName: 'Symbols', tableName: 'symbols', needsMap: true },
  { modelName: 'TimeframeConsistencyLogs', tableName: 'timeframe_consistency_logs', needsMap: true },
  { modelName: 'TimeframeParameterConfigs', tableName: 'timeframe_parameter_configs', needsMap: true },
  { modelName: 'TimeframeParameterHistory', tableName: 'timeframe_parameter_history', needsMap: true },
  { modelName: 'TradingAccounts', tableName: 'trading_accounts', needsMap: true },
  { modelName: 'TradingOrders', tableName: 'trading_orders', needsMap: true },
  { modelName: 'TradingPositions', tableName: 'trading_positions', needsMap: true },
  { modelName: 'TradingSignals', tableName: 'trading_signals', needsMap: true },
  { modelName: 'TradingStatistics', tableName: 'trading_statistics', needsMap: true },
  { modelName: 'TrendAnalyses', tableName: 'trend_analyses', needsMap: true },
  { modelName: 'UserLLMConfig', tableName: 'user_llm_config', needsMap: true },
  { modelName: 'UserModelPreferences', tableName: 'user_model_preferences', needsMap: true },
  { modelName: 'Users', tableName: 'users', needsMap: true },
  { modelName: 'UserSessions', tableName: 'user_sessions', needsMap: true },
  { modelName: 'WebsocketMessages', tableName: 'websocket_messages', needsMap: true }
];

/**
 * 索引优化建议
 */
const INDEX_OPTIMIZATIONS: IndexOptimization[] = [
  {
    table: 'ai_call_logs',
    indexes: [
      '@@index([user_id, created_at])',
      '@@index([model_name, call_type, created_at])',
      '@@index([cache_hit, created_at])',
      '@@index([cost_usd, created_at])'
    ],
    reason: '优化AI调用查询和成本分析'
  },
  {
    table: 'trading_signals',
    indexes: [
      '@@index([symbol_id, signal_type, created_at])',
      '@@index([confidence, created_at])',
      '@@index([is_active, created_at])',
      '@@index([strategy_type, symbol_id])'
    ],
    reason: '优化交易信号查询性能'
  },
  {
    table: 'price_data',
    indexes: [
      '@@index([symbol_id, timestamp])',
      '@@index([timestamp, data_quality])',
      '@@index([cache_expires_at])'
    ],
    reason: '优化价格数据时序查询'
  },
  {
    table: 'historical_data',
    indexes: [
      '@@index([symbol_id, timeframe, timestamp])',
      '@@index([timestamp, data_quality])',
      '@@index([data_source, timestamp])'
    ],
    reason: '优化历史数据查询'
  },
  {
    table: 'risk_assessments',
    indexes: [
      '@@index([symbol_id, risk_level, created_at])',
      '@@index([overall_risk_score, created_at])',
      '@@index([is_active, created_at])'
    ],
    reason: '优化风险评估查询'
  }
];

class SchemaNormalizer {
  private readonly schemaPath = 'prisma/schema.prisma';
  private readonly backupDir = 'prisma/backups';

  constructor() {
    this.ensureBackupDir();
  }

  /**
   * 执行完整的Schema规范化
   */
  async normalize(): Promise<void> {
    console.log('🚀 开始数据库Schema规范化...\n');

    try {
      // 1. 创建备份
      await this.createBackup();

      // 2. 修复@map注解
      await this.fixMapAnnotations();

      // 3. 优化索引
      await this.optimizeIndexes();

      // 4. 验证Schema
      await this.validateSchema();

      // 5. 生成Prisma客户端
      await this.generatePrismaClient();

      console.log('\n✅ Schema规范化完成！');
      console.log('\n📋 后续步骤:');
      console.log('1. 检查生成的migration文件');
      console.log('2. 运行 npm run db:push 应用更改');
      console.log('3. 验证应用程序正常运行');

    } catch (error) {
      console.error('❌ Schema规范化失败:', error);
      console.log('\n🔄 正在恢复备份...');
      await this.restoreBackup();
      throw error;
    }
  }

  /**
   * 确保备份目录存在
   */
  private ensureBackupDir(): void {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  /**
   * 创建Schema备份
   */
  private async createBackup(): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.backupDir, `schema_${timestamp}.prisma`);
    
    fs.copyFileSync(this.schemaPath, backupPath);
    console.log(`📁 Schema备份已创建: ${backupPath}`);
  }

  /**
   * 修复@map注解
   */
  private async fixMapAnnotations(): Promise<void> {
    console.log('🔧 修复@map注解...');
    
    let schemaContent = fs.readFileSync(this.schemaPath, 'utf-8');
    let fixCount = 0;

    for (const mapping of TABLE_MAPPINGS) {
      if (!mapping.needsMap) continue;

      // 查找模型定义
      const modelRegex = new RegExp(
        `(model\\s+${mapping.modelName}\\s*{[^}]*?)(\\n\\s*@@\\w+[^}]*?)*(\\n})`,
        'gs'
      );

      schemaContent = schemaContent.replace(modelRegex, (match, modelBody, existingAnnotations, closingBrace) => {
        // 检查是否已有@map注解
        if (modelBody.includes('@@map(')) {
          return match;
        }

        fixCount++;
        const mapAnnotation = `\n  @@map("${mapping.tableName}")`;
        
        if (existingAnnotations) {
          return `${modelBody}${mapAnnotation}${existingAnnotations}${closingBrace}`;
        } else {
          return `${modelBody}${mapAnnotation}${closingBrace}`;
        }
      });
    }

    fs.writeFileSync(this.schemaPath, schemaContent);
    console.log(`✅ 已添加 ${fixCount} 个@map注解`);
  }
