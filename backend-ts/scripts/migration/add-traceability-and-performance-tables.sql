-- 添加AI推理可追溯性和性能监控相关表的迁移脚本
-- 执行前请备份数据库

-- ==================== AI推理可追溯性表 ====================

-- 推理会话表
CREATE TABLE IF NOT EXISTS "ReasoningSessions" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "sessionId" VARCHAR(100) UNIQUE NOT NULL,
    "requestType" VARCHAR(50) NOT NULL,
    "requestData" JSONB NOT NULL,
    "status" VARCHAR(20) DEFAULT 'active' NOT NULL,
    "startTime" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "endTime" TIMESTAMP(3),
    "totalDuration" INTEGER,
    "userId" VARCHAR(255),
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 推理步骤表
CREATE TABLE IF NOT EXISTS "ReasoningSteps" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "sessionId" VARCHAR(100) NOT NULL,
    "stepId" VARCHAR(100) NOT NULL,
    "stepType" VARCHAR(50) NOT NULL,
    "stepName" VARCHAR(200) NOT NULL,
    "description" TEXT,
    "inputData" JSONB NOT NULL,
    "outputData" JSONB,
    "startTime" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "endTime" TIMESTAMP(3),
    "duration" INTEGER,
    "status" VARCHAR(20) DEFAULT 'running' NOT NULL,
    "errorMessage" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    FOREIGN KEY ("sessionId") REFERENCES "ReasoningSessions"("sessionId") ON DELETE CASCADE
);

-- 决策点表
CREATE TABLE IF NOT EXISTS "DecisionPoints" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "sessionId" VARCHAR(100) NOT NULL,
    "pointId" VARCHAR(100) NOT NULL,
    "pointType" VARCHAR(50) NOT NULL,
    "description" TEXT NOT NULL,
    "options" JSONB NOT NULL,
    "selectedOption" JSONB,
    "reasoning" TEXT,
    "confidence" DECIMAL(3,2),
    "timestamp" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    FOREIGN KEY ("sessionId") REFERENCES "ReasoningSessions"("sessionId") ON DELETE CASCADE
);

-- 模型调用表
CREATE TABLE IF NOT EXISTS "ModelInvocations" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "sessionId" VARCHAR(100) NOT NULL,
    "invocationId" VARCHAR(100) NOT NULL,
    "modelName" VARCHAR(100) NOT NULL,
    "modelVersion" VARCHAR(50),
    "prompt" TEXT NOT NULL,
    "response" TEXT,
    "tokenUsage" JSONB,
    "cost" DECIMAL(10,6),
    "startTime" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "endTime" TIMESTAMP(3),
    "duration" INTEGER,
    "status" VARCHAR(20) DEFAULT 'pending' NOT NULL,
    "errorMessage" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    FOREIGN KEY ("sessionId") REFERENCES "ReasoningSessions"("sessionId") ON DELETE CASCADE
);

-- ==================== 配置管理表 ====================

-- 配置历史表
CREATE TABLE IF NOT EXISTS "ConfigHistory" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "key" VARCHAR(100) NOT NULL,
    "oldValue" TEXT,
    "newValue" TEXT NOT NULL,
    "changeType" VARCHAR(20) NOT NULL,
    "changedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "changedBy" VARCHAR(255),
    "source" VARCHAR(50) NOT NULL,
    "reason" TEXT,
    "version" INTEGER DEFAULT 1 NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 配置快照表
CREATE TABLE IF NOT EXISTS "ConfigSnapshots" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "description" TEXT NOT NULL,
    "configs" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "createdBy" VARCHAR(255),
    "metadata" TEXT
);

-- 配置回滚历史表
CREATE TABLE IF NOT EXISTS "ConfigRollbackHistory" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "versionId" VARCHAR(100),
    "preRollbackSnapshotId" UUID NOT NULL,
    "success" BOOLEAN NOT NULL,
    "rolledBackConfigs" TEXT NOT NULL,
    "failedConfigs" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "performedBy" VARCHAR(255)
);

-- 配置快照回滚历史表
CREATE TABLE IF NOT EXISTS "ConfigSnapshotRollbackHistory" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "snapshotId" UUID NOT NULL,
    "preRollbackSnapshotId" UUID NOT NULL,
    "success" BOOLEAN NOT NULL,
    "rolledBackConfigs" TEXT NOT NULL,
    "failedConfigs" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "performedBy" VARCHAR(255)
);

-- ==================== 性能监控表 ====================

-- 执行延迟记录表
CREATE TABLE IF NOT EXISTS "ExecutionLatencyRecords" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "executionId" VARCHAR(100) UNIQUE NOT NULL,
    "executionType" VARCHAR(50) NOT NULL,
    "symbol" VARCHAR(20),
    "accountId" UUID,
    "orderType" VARCHAR(20),
    "quantity" DECIMAL(18,8),
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3),
    "duration" INTEGER,
    "success" BOOLEAN,
    "errorMessage" TEXT,
    "responseSize" INTEGER,
    "networkLatency" INTEGER,
    "processingTime" INTEGER,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 滑点记录表
CREATE TABLE IF NOT EXISTS "SlippageRecords" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "orderId" VARCHAR(100) UNIQUE NOT NULL,
    "symbol" VARCHAR(20) NOT NULL,
    "orderType" VARCHAR(20) NOT NULL,
    "side" VARCHAR(10) NOT NULL,
    "requestedPrice" DECIMAL(18,8) NOT NULL,
    "executedPrice" DECIMAL(18,8) NOT NULL,
    "slippage" DECIMAL(10,6) NOT NULL,
    "slippageBps" DECIMAL(10,2) NOT NULL,
    "quantity" DECIMAL(18,8) NOT NULL,
    "volatility" DECIMAL(10,6),
    "spread" DECIMAL(18,8),
    "volume" DECIMAL(18,8),
    "liquidity" VARCHAR(20),
    "timeOfDay" VARCHAR(10),
    "timestamp" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 吞吐量记录表
CREATE TABLE IF NOT EXISTS "ThroughputRecords" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "operationId" VARCHAR(100) NOT NULL,
    "operationType" VARCHAR(50) NOT NULL,
    "timestamp" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "duration" INTEGER,
    "success" BOOLEAN NOT NULL,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 性能告警表
CREATE TABLE IF NOT EXISTS "PerformanceAlerts" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "ruleId" VARCHAR(100) NOT NULL,
    "title" VARCHAR(200) NOT NULL,
    "description" TEXT NOT NULL,
    "severity" VARCHAR(20) NOT NULL,
    "status" VARCHAR(20) DEFAULT 'active' NOT NULL,
    "triggeredAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "acknowledgedAt" TIMESTAMP(3),
    "acknowledgedBy" VARCHAR(255),
    "resolvedAt" TIMESTAMP(3),
    "value" DECIMAL(18,8) NOT NULL,
    "threshold" DECIMAL(18,8) NOT NULL,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 性能报告表
CREATE TABLE IF NOT EXISTS "PerformanceReports" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "reportId" VARCHAR(100) UNIQUE NOT NULL,
    "timeRangeStart" TIMESTAMP(3) NOT NULL,
    "timeRangeEnd" TIMESTAMP(3) NOT NULL,
    "summary" JSONB NOT NULL,
    "detailedMetrics" JSONB NOT NULL,
    "anomalies" JSONB NOT NULL,
    "recommendations" JSONB NOT NULL,
    "attachments" JSONB,
    "generatedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- ==================== 创建索引 ====================

-- 推理会话索引
CREATE INDEX IF NOT EXISTS "idxReasoningSessionsSessionId" ON "ReasoningSessions"("sessionId");
CREATE INDEX IF NOT EXISTS "idxReasoningSessionsRequestTypeStartTime" ON "ReasoningSessions"("requestType", "startTime");
CREATE INDEX IF NOT EXISTS "idxReasoningSessionsStatusStartTime" ON "ReasoningSessions"("status", "startTime");
CREATE INDEX IF NOT EXISTS "idxReasoningSessionsUserStartTime" ON "ReasoningSessions"("userId", "startTime");

-- 推理步骤索引
CREATE INDEX IF NOT EXISTS "idxReasoningStepsSessionStartTime" ON "ReasoningSteps"("sessionId", "startTime");
CREATE INDEX IF NOT EXISTS "idxReasoningStepsTypeStartTime" ON "ReasoningSteps"("stepType", "startTime");
CREATE INDEX IF NOT EXISTS "idxReasoningStepsStatusStartTime" ON "ReasoningSteps"("status", "startTime");

-- 决策点索引
CREATE INDEX IF NOT EXISTS "idxDecisionPointsSessionTimestamp" ON "DecisionPoints"("sessionId", "timestamp");
CREATE INDEX IF NOT EXISTS "idxDecisionPointsTypeTimestamp" ON "DecisionPoints"("pointType", "timestamp");
CREATE INDEX IF NOT EXISTS "idxDecisionPointsConfidence" ON "DecisionPoints"("confidence");

-- 模型调用索引
CREATE INDEX IF NOT EXISTS "idxModelInvocationsSessionStartTime" ON "ModelInvocations"("sessionId", "startTime");
CREATE INDEX IF NOT EXISTS "idxModelInvocationsModelStartTime" ON "ModelInvocations"("modelName", "startTime");
CREATE INDEX IF NOT EXISTS "idxModelInvocationsStatusStartTime" ON "ModelInvocations"("status", "startTime");
CREATE INDEX IF NOT EXISTS "idxModelInvocationsCost" ON "ModelInvocations"("cost");

-- 配置历史索引
CREATE INDEX IF NOT EXISTS "idxConfigHistoryKeyChangedAt" ON "ConfigHistory"("key", "changedAt");
CREATE INDEX IF NOT EXISTS "idxConfigHistoryChangeTypeChangedAt" ON "ConfigHistory"("changeType", "changedAt");
CREATE INDEX IF NOT EXISTS "idxConfigHistoryChangedByChangedAt" ON "ConfigHistory"("changedBy", "changedAt");
CREATE INDEX IF NOT EXISTS "idxConfigHistorySourceChangedAt" ON "ConfigHistory"("source", "changedAt");

-- 配置快照索引
CREATE INDEX IF NOT EXISTS "idxConfigSnapshotsCreatedAt" ON "ConfigSnapshots"("createdAt");
CREATE INDEX IF NOT EXISTS "idxConfigSnapshotsCreatedBy" ON "ConfigSnapshots"("createdBy");

-- 配置回滚历史索引
CREATE INDEX IF NOT EXISTS "idxConfigRollbackHistoryTimestamp" ON "ConfigRollbackHistory"("timestamp");
CREATE INDEX IF NOT EXISTS "idxConfigRollbackHistorySuccessTimestamp" ON "ConfigRollbackHistory"("success", "timestamp");

-- 执行延迟记录索引
CREATE INDEX IF NOT EXISTS "idxExecutionLatencyTypeStartTime" ON "ExecutionLatencyRecords"("executionType", "startTime");
CREATE INDEX IF NOT EXISTS "idxExecutionLatencySymbolStartTime" ON "ExecutionLatencyRecords"("symbol", "startTime");
CREATE INDEX IF NOT EXISTS "idxExecutionLatencyDuration" ON "ExecutionLatencyRecords"("duration");
CREATE INDEX IF NOT EXISTS "idxExecutionLatencySuccessStartTime" ON "ExecutionLatencyRecords"("success", "startTime");

-- 滑点记录索引
CREATE INDEX IF NOT EXISTS "idxSlippageRecordsSymbolTimestamp" ON "SlippageRecords"("symbol", "timestamp");
CREATE INDEX IF NOT EXISTS "idxSlippageRecordsOrderTypeTimestamp" ON "SlippageRecords"("orderType", "timestamp");
CREATE INDEX IF NOT EXISTS "idxSlippageRecordsSlippageBps" ON "SlippageRecords"("slippageBps");
CREATE INDEX IF NOT EXISTS "idxSlippageRecordsTimestamp" ON "SlippageRecords"("timestamp");

-- 吞吐量记录索引
CREATE INDEX IF NOT EXISTS "idxThroughputRecordsOperationTypeTimestamp" ON "ThroughputRecords"("operationType", "timestamp");
CREATE INDEX IF NOT EXISTS "idxThroughputRecordsSuccessTimestamp" ON "ThroughputRecords"("success", "timestamp");
CREATE INDEX IF NOT EXISTS "idxThroughputRecordsTimestamp" ON "ThroughputRecords"("timestamp");

-- 性能告警索引
CREATE INDEX IF NOT EXISTS "idxPerformanceAlertsRuleTriggeredAt" ON "PerformanceAlerts"("ruleId", "triggeredAt");
CREATE INDEX IF NOT EXISTS "idxPerformanceAlertsSeverityStatus" ON "PerformanceAlerts"("severity", "status");
CREATE INDEX IF NOT EXISTS "idxPerformanceAlertsStatusTriggeredAt" ON "PerformanceAlerts"("status", "triggeredAt");

-- 性能报告索引
CREATE INDEX IF NOT EXISTS "idxPerformanceReportsTimeRange" ON "PerformanceReports"("timeRangeStart", "timeRangeEnd");
CREATE INDEX IF NOT EXISTS "idxPerformanceReportsGeneratedAt" ON "PerformanceReports"("generatedAt");

-- 完成提示
SELECT 'AI推理可追溯性和性能监控表创建完成' as status;
