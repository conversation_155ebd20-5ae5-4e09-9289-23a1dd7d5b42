/**
 * 同步真实交易账户的币安余额
 */

import { PrismaClient } from '@prisma/client';
import Binance from 'binance-api-node';


/**
 * 余额同步
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

async function syncRealAccountBalance() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔄 同步真实交易账户余额...\n');
    
    // 查找真实交易账户
    const realAccount = await prisma.tradingAccounts.findFirst({
      where: {
        accountType: 'LIVE',
        executionEngine: 'binance'
      }
    });
    
    if (!realAccount || !realAccount.apiCredentials) {
      console.log('❌ 未找到配置了API凭证的真实交易账户');
      return;
    }
    
    console.log(`✅ 找到真实交易账户: ${realAccount.name}`);
    console.log(`  当前数据库余额: ${realAccount.currentBalance} USDT (虚假)`);
    
    // 获取API凭证
    const credentials = realAccount.apiCredentials as any;
    
    // 创建币安客户端
    const client = Binance({
      apiKey: credentials.apiKey,
      apiSecret: credentials.secretKey
    });
    
    console.log('✅ 连接到币安API...');
    
    // 获取真实的合约账户信息
    const accountInfo = await client.futuresAccountInfo();
    
    console.log('\n📊 币安合约账户真实余额:');
    console.log(`  总钱包余额: ${accountInfo.totalWalletBalance} USDT`);
    console.log(`  可用余额: ${accountInfo.availableBalance} USDT`);
    console.log(`  总未实现盈亏: ${accountInfo.totalUnrealizedProfit} USDT`);
    console.log(`  总保证金余额: ${accountInfo.totalMarginBalance} USDT`);
    
    // 获取活跃仓位信息
    const positions = await client.futuresPositionRisk();
    const activePositions = positions.filter((pos: any) => parseFloat(pos.positionAmt) !== 0);
    
    console.log(`\n📈 活跃仓位: ${activePositions.length} 个`);
    if (activePositions.length > 0) {
      activePositions.forEach((pos: any) => {
        console.log(`  ${pos.symbol}: ${pos.positionAmt} (未实现盈亏: ${pos.unRealizedProfit} USDT)`);
      });
    }
    
    // 计算总的未实现盈亏
    const totalUnrealizedPnl = parseFloat(accountInfo.totalUnrealizedProfit);
    
    // 更新数据库中的账户余额
    const updatedAccount = await prisma.tradingAccounts.update({
      where: { id: realAccount.id },
      data: {
        currentBalance: parseFloat(accountInfo.totalWalletBalance),
        availableBalance: parseFloat(accountInfo.availableBalance),
        totalPnl: totalUnrealizedPnl, // 使用未实现盈亏作为总盈亏
        updatedAt: new Date()
      }
    });
    
    console.log('\n✅ 数据库余额已更新:');
    console.log(`  当前余额: ${updatedAccount.currentBalance} USDT (真实)`);
    console.log(`  可用余额: ${updatedAccount.availableBalance} USDT (真实)`);
    console.log(`  总盈亏: ${updatedAccount.totalPnl} USDT (真实)`);
    console.log(`  更新时间: ${updatedAccount.updatedAt}`);
    
    console.log('\n🎯 余额同步完成:');
    console.log('✅ 已从虚假的100 USDT更新为真实的币安余额');
    console.log('✅ 数据库现在显示真实的合约账户余额');
    console.log('✅ 包含了未实现盈亏信息');
    console.log('✅ 反映了实际的可用余额');
    
    // 显示对比
    console.log('\n📊 更新前后对比:');
    console.log(`  更新前: ${realAccount.currentBalance} USDT (虚假设置)`);
    console.log(`  更新后: ${updatedAccount.currentBalance} USDT (币安真实余额)`);
    console.log(`  差异: ${parseFloat(updatedAccount.currentBalance.toString()) - parseFloat(realAccount.currentBalance.toString())} USDT`);
    
  } catch (error) {
    console.error('❌ 同步失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行同步
syncRealAccountBalance().catch(console.error);
