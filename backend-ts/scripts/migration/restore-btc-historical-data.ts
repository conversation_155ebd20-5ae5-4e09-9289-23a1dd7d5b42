import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import { config } from 'dotenv';


/**
 * BTC历史数据恢复
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

// 加载环境变量
config();

const prisma = new PrismaClient();

interface BinanceKlineData {
  openTime: number;
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
  closeTime: number;
  quoteAssetVolume: string;
  numberOfTrades: number;
  takerBuyBaseAssetVolume: string;
  takerBuyQuoteAssetVolume: string;
  ignore: string;
}

class HistoricalDataRestorer {
  private readonly BINANCE_API_URL = 'https://api.binance.com/api/v3/klines';
  private readonly RATE_LIMIT_DELAY = 100; // 100ms between requests
  private readonly MAX_RETRIES = 3;
  private readonly BATCH_SIZE = 1000; // Binance limit is 1000

  private readonly timeframes = [
    { binance: '15m', internal: '15m' },
    { binance: '1h', internal: '1h' },
    { binance: '2h', internal: '2h' },
    { binance: '4h', internal: '4h' },
    { binance: '8h', internal: '8h' },
    { binance: '1d', internal: '1d' },
    { binance: '3d', internal: '3d' }
  ];

  async restoreAllData() {
    console.log('🚀 开始恢复BTC历史数据...');
    console.log('📅 时间范围: 2017-01-01 至今');
    console.log('⏰ 时间框架:', this.timeframes.map(tf => tf.internal).join(', '));

    try {
      // 确保BTC符号存在
      const btcSymbol = await this.ensureBTCSymbol();
      console.log(`✅ BTC符号确认: ${btcSymbol.symbol} (ID: ${btcSymbol.id})`);

      // 为每个时间框架恢复数据
      for (const timeframe of this.timeframes) {
        console.log(`\n📊 开始恢复 ${timeframe.internal} 数据...`);
        await this.restoreTimeframeData(btcSymbol.id, timeframe);
      }

      console.log('\n🎉 所有历史数据恢复完成！');
      await this.printDataSummary(btcSymbol.id);

    } catch (error) {
      console.error('❌ 数据恢复失败:', error);
      throw error;
    }
  }

  private async ensureBTCSymbol() {
    let symbol = await prisma.symbol.findFirst({
      where: { symbol: 'BTC/USDT' }
    });

    if (!symbol) {
      console.log('📝 创建BTC/USDT符号...');
      symbol = await prisma.symbol.create({
        data: {
          symbol: 'BTC/USDT',
          baseAsset: 'BTC',
          quoteAsset: 'USDT',
          status: 'ACTIVE',
          isActive: true,
          exchange: 'binance',
          metadata: {
            description: 'Bitcoin to Tether',
            category: 'spot',
            isProtected: true
          },
          tradingRules: {
            minQty: '0.00001',
            maxQty: '9000',
            stepSize: '0.00001',
            minNotional: '10'
          }
        }
      });
    }

    return symbol;
  }

  private async restoreTimeframeData(symbolId: string, timeframe: { binance: string; internal: string }) {
    const startTime = new Date('2017-01-01').getTime();
    const endTime = Date.now();

    // 检查已有数据
    const existingCount = await prisma.historicalData.count({
      where: {
        symbolId,
        timeframe: timeframe.internal
      }
    });

    console.log(`📈 ${timeframe.internal} - 现有数据: ${existingCount} 条`);

    // 计算预期的数据量
    const expectedCount = this.calculateExpectedRecords(startTime, endTime, timeframe.internal);
    console.log(`📊 预期数据量: ${expectedCount} 条`);

    // 如果现有数据少于预期的80%，则重新获取所有数据
    if (existingCount < expectedCount * 0.8) {
      console.log(`⚠️  数据不完整 (${existingCount}/${expectedCount})，开始完整恢复...`);

      // 删除现有的不完整数据（仅限非保护数据）
      const deleteResult = await prisma.historicalData.deleteMany({
        where: {
          symbolId,
          timeframe: timeframe.internal,
          isProtected: false
        }
      });
      console.log(`🗑️  清理不完整数据: ${deleteResult.count} 条`);

    } else if (existingCount > 0) {
      // 找到最新数据的时间，从那里继续
      const latestData = await prisma.historicalData.findFirst({
        where: {
          symbolId,
          timeframe: timeframe.internal
        },
        orderBy: { timestamp: 'desc' }
      });

      if (latestData) {
        const latestTime = latestData.timestamp.getTime();
        const timeDiff = endTime - latestTime;
        const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

        console.log(`⏰ 最新数据: ${latestData.timestamp.toISOString()}`);
        console.log(`📅 数据缺口: ${daysDiff.toFixed(1)} 天`);

        if (daysDiff < 1) {
          console.log(`✅ ${timeframe.internal} 数据已是最新，跳过`);
          return;
        }
      }
    }

    // 分批获取数据
    let currentStartTime = startTime;
    let totalInserted = 0;
    let batchCount = 0;

    while (currentStartTime < endTime) {
      batchCount++;
      console.log(`📦 批次 ${batchCount} - 获取从 ${new Date(currentStartTime).toISOString()} 开始的数据...`);

      try {
        const klineData = await this.fetchKlineData('BTCUSDT', timeframe.binance, currentStartTime, endTime);
        
        if (klineData.length === 0) {
          console.log('📭 没有更多数据，结束获取');
          break;
        }

        const insertedCount = await this.insertKlineData(symbolId, timeframe.internal, klineData);
        totalInserted += insertedCount;

        console.log(`✅ 批次 ${batchCount} 完成: 插入 ${insertedCount} 条数据`);

        // 更新下次开始时间
        const lastKline = klineData[klineData.length - 1];
        currentStartTime = lastKline.closeTime + 1;

        // 如果返回的数据少于批次大小，说明已经到最新数据
        if (klineData.length < this.BATCH_SIZE) {
          break;
        }

        // 速率限制
        await this.delay(this.RATE_LIMIT_DELAY);

      } catch (error) {
        console.error(`❌ 批次 ${batchCount} 失败:`, error);
        
        // 重试机制
        let retryCount = 0;
        while (retryCount < this.MAX_RETRIES) {
          retryCount++;
          console.log(`🔄 重试 ${retryCount}/${this.MAX_RETRIES}...`);
          
          try {
            await this.delay(1000 * retryCount); // 递增延迟
            const klineData = await this.fetchKlineData('BTCUSDT', timeframe.binance, currentStartTime, endTime);
            const insertedCount = await this.insertKlineData(symbolId, timeframe.internal, klineData);
            totalInserted += insertedCount;
            console.log(`✅ 重试成功: 插入 ${insertedCount} 条数据`);
            
            const lastKline = klineData[klineData.length - 1];
            currentStartTime = lastKline.closeTime + 1;
            break;
            
          } catch (retryError) {
            console.error(`❌ 重试 ${retryCount} 失败:`, retryError);
            if (retryCount === this.MAX_RETRIES) {
              throw new Error(`批次 ${batchCount} 重试 ${this.MAX_RETRIES} 次后仍然失败`);
            }
          }
        }
      }
    }

    console.log(`🎯 ${timeframe.internal} 恢复完成: 总共插入 ${totalInserted} 条数据`);
  }

  private async fetchKlineData(symbol: string, interval: string, startTime: number, endTime: number): Promise<BinanceKlineData[]> {
    const params = {
      symbol,
      interval,
      startTime,
      endTime,
      limit: this.BATCH_SIZE
    };

    const response = await axios.get(this.BINANCE_API_URL, { 
      params,
      timeout: 10000 
    });

    return response.data.map((kline: any[]) => ({
      openTime: kline[0],
      open: kline[1],
      high: kline[2],
      low: kline[3],
      close: kline[4],
      volume: kline[5],
      closeTime: kline[6],
      quoteAssetVolume: kline[7],
      numberOfTrades: kline[8],
      takerBuyBaseAssetVolume: kline[9],
      takerBuyQuoteAssetVolume: kline[10],
      ignore: kline[11]
    }));
  }

  private async insertKlineData(symbolId: string, timeframe: string, klineData: BinanceKlineData[]): Promise<number> {
    const dataToInsert = klineData.map(kline => ({
      symbolId,
      timeframe,
      timestamp: new Date(kline.openTime),
      openPrice: parseFloat(kline.open),
      highPrice: parseFloat(kline.high),
      lowPrice: parseFloat(kline.low),
      closePrice: parseFloat(kline.close),
      volume: parseFloat(kline.volume),
      trades: kline.numberOfTrades,
      quoteVolume: parseFloat(kline.quoteAssetVolume),
      dataQuality: 1.0,
      isProtected: true, // 标记为受保护数据
      dataSource: 'binance'
    }));

    // 使用 createMany 批量插入，忽略重复数据
    const result = await prisma.historicalData.createMany({
      data: dataToInsert,
      skipDuplicates: true
    });

    return result.count;
  }

  private async printDataSummary(symbolId: string) {
    console.log('\n📊 数据恢复总结:');
    console.log('─'.repeat(60));

    for (const timeframe of this.timeframes) {
      const count = await prisma.historicalData.count({
        where: {
          symbolId,
          timeframe: timeframe.internal
        }
      });

      const latest = await prisma.historicalData.findFirst({
        where: {
          symbolId,
          timeframe: timeframe.internal
        },
        orderBy: { timestamp: 'desc' }
      });

      const earliest = await prisma.historicalData.findFirst({
        where: {
          symbolId,
          timeframe: timeframe.internal
        },
        orderBy: { timestamp: 'asc' }
      });

      console.log(`📈 ${timeframe.internal.padEnd(4)} | ${count.toString().padStart(8)} 条 | ${earliest?.timestamp.toISOString().split('T')[0]} 至 ${latest?.timestamp.toISOString().split('T')[0]}`);
    }

    const totalCount = await prisma.historicalData.count({
      where: { symbolId }
    });

    console.log('─'.repeat(60));
    console.log(`📊 总计: ${totalCount} 条历史数据`);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private calculateExpectedRecords(startTime: number, endTime: number, timeframe: string): number {
    const timeDiff = endTime - startTime;

    switch (timeframe) {
      case '15m':
        return Math.floor(timeDiff / (15 * 60 * 1000));
      case '1h':
        return Math.floor(timeDiff / (60 * 60 * 1000));
      case '2h':
        return Math.floor(timeDiff / (2 * 60 * 60 * 1000));
      case '4h':
        return Math.floor(timeDiff / (4 * 60 * 60 * 1000));
      case '8h':
        return Math.floor(timeDiff / (8 * 60 * 60 * 1000));
      case '1d':
        return Math.floor(timeDiff / (24 * 60 * 60 * 1000));
      case '3d':
        return Math.floor(timeDiff / (3 * 24 * 60 * 60 * 1000));
      default:
        return 0;
    }
  }
}

// 运行数据恢复
async function main() {
  const restorer = new HistoricalDataRestorer();
  
  try {
    await restorer.restoreAllData();
    console.log('\n🎉 BTC历史数据恢复任务完成！');
  } catch (error) {
    console.error('\n❌ 数据恢复失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { HistoricalDataRestorer };
