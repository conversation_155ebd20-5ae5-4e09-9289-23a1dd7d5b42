#!/bin/bash

# API端点测试脚本
# 测试所有主要的API端点功能

BASE_URL="http://localhost:3001"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "🚀 开始API端点测试..."
echo "基础URL: $BASE_URL"
echo "=================================="

# 测试健康检查
echo -e "${YELLOW}测试健康检查端点...${NC}"
response=$(curl -s "$BASE_URL/health")
if echo "$response" | jq -e '.success == true' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 健康检查通过${NC}"
    echo "$response" | jq '.data.status'
else
    echo -e "${RED}❌ 健康检查失败${NC}"
    echo "$response"
fi
echo ""

# 测试市场数据 - 交易对列表
echo -e "${YELLOW}测试市场数据 - 交易对列表...${NC}"
response=$(curl -s "$BASE_URL/api/v1/market-data/symbols")
if echo "$response" | jq -e '.success == true' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 交易对列表获取成功${NC}"
    count=$(echo "$response" | jq '.data | length')
    echo "交易对数量: $count"
else
    echo -e "${RED}❌ 交易对列表获取失败${NC}"
    echo "$response"
fi
echo ""

# 测试市场数据 - 价格数据
echo -e "${YELLOW}测试市场数据 - BTCUSDT价格...${NC}"
response=$(curl -s "$BASE_URL/api/v1/market-data/price/BTCUSDT")
if echo "$response" | jq -e '.success == true' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 价格数据获取成功${NC}"
    price=$(echo "$response" | jq '.data.price')
    echo "当前价格: $price"
else
    echo -e "${RED}❌ 价格数据获取失败${NC}"
    echo "$response"
fi
echo ""

# 测试市场数据 - 历史K线数据
echo -e "${YELLOW}测试市场数据 - BTCUSDT历史K线...${NC}"
response=$(curl -s "$BASE_URL/api/v1/market-data/klines?symbol=BTCUSDT&timeframe=1h&limit=5")
if echo "$response" | jq -e '.success == true' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 历史K线数据获取成功${NC}"
    count=$(echo "$response" | jq '.data.data | length')
    echo "K线数据条数: $count"
else
    echo -e "${RED}❌ 历史K线数据获取失败${NC}"
    echo "$response"
fi
echo ""

# 测试趋势分析
echo -e "${YELLOW}测试趋势分析...${NC}"
response=$(curl -s -X POST "$BASE_URL/api/v1/trend-analysis/analysis" \
    -H "Content-Type: application/json" \
    -d '{"symbol":"BTCUSDT","timeframe":"1h"}')
if echo "$response" | jq -e '.success == true' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 趋势分析成功${NC}"
else
    echo -e "${RED}❌ 趋势分析失败${NC}"
    echo "$response"
fi
echo ""

# 测试风险评估（使用完整参数）
echo -e "${YELLOW}测试风险评估...${NC}"
response=$(curl -s -X POST "$BASE_URL/api/v1/risk-assessment/assessment" \
    -H "Content-Type: application/json" \
    -d '{
        "portfolioId": "550e8400-e29b-41d4-a716-446655440000",
        "position": {
            "symbol": "BTCUSDT",
            "quantity": 0.1,
            "entryPrice": 100000,
            "side": "LONG"
        }
    }')
if echo "$response" | jq -e '.success == true' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 风险评估成功${NC}"
else
    echo -e "${RED}❌ 风险评估失败${NC}"
    echo "$response"
fi
echo ""

echo "=================================="
echo -e "${GREEN}🎉 API端点测试完成！${NC}"
