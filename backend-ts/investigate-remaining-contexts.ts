/**
 * 剩余上下文模块快速调查
 * 调查重点：快速评估开发完成度
 */

import 'reflect-metadata';
import { TYPES } from './src/shared/infrastructure/di/types/index';

interface QuickAssessment {
  name: string;
  completionPercentage: number;
  status: 'EXCELLENT' | 'GOOD' | 'NEEDS_IMPROVEMENT' | 'POOR';
  keyFindings: string[];
  existingComponents: number;
  totalComponents: number;
}

async function quickCheckModule(
  moduleName: string,
  checkPaths: string[]
): Promise<QuickAssessment> {
  
  let existingComponents = 0;
  const keyFindings: string[] = [];

  for (const path of checkPaths) {
    try {
      await import(path);
      existingComponents++;
    } catch (error) {
      // 组件不存在或有问题
      if (error instanceof Error && !error.message.includes('Cannot find module')) {
        keyFindings.push(`编译错误: ${path.split('/').pop()}`);
      }
    }
  }

  const completionPercentage = Math.round((existingComponents / checkPaths.length) * 100);
  
  let status: 'EXCELLENT' | 'GOOD' | 'NEEDS_IMPROVEMENT' | 'POOR';
  if (completionPercentage >= 90) {
    status = 'EXCELLENT';
  } else if (completionPercentage >= 70) {
    status = 'GOOD';
  } else if (completionPercentage >= 30) {
    status = 'NEEDS_IMPROVEMENT';
  } else {
    status = 'POOR';
  }

  return {
    name: moduleName,
    completionPercentage,
    status,
    keyFindings,
    existingComponents,
    totalComponents: checkPaths.length
  };
}

async function investigateRemainingContexts() {
  console.log('🔍 开始剩余上下文模块快速调查...\n');

  const modules = [
    {
      name: 'Trend Analysis',
      paths: [
        './src/contexts/trend-analysis/application/services/trend-analysis-application-service',
        './src/contexts/trend-analysis/infrastructure/services/trend-analysis-engine',
        './src/contexts/trend-analysis/domain/entities/trend-analysis',
        './src/contexts/trend-analysis/domain/value-objects/trend-direction',
        './src/contexts/trend-analysis/domain/value-objects/trend-strength',
        './src/contexts/trend-analysis/infrastructure/repositories/prisma-trend-analysis-repository'
      ]
    },
    {
      name: 'AI Reasoning',
      paths: [
        './src/contexts/ai-reasoning/application/services/ai-reasoning-application-service',
        './src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine',
        './src/contexts/ai-reasoning/infrastructure/services/unified-learning-engine',
        './src/contexts/ai-reasoning/domain/entities/prediction-result',
        './src/contexts/ai-reasoning/domain/value-objects/confidence-level',
        './src/contexts/ai-reasoning/infrastructure/repositories/prediction-repository'
      ]
    },
    {
      name: 'Risk Management',
      paths: [
        './src/contexts/risk-management/application/services/risk-assessment-application-service',
        './src/contexts/risk-management/domain/entities/risk-assessment',
        './src/contexts/risk-management/domain/value-objects/risk-level',
        './src/contexts/risk-management/infrastructure/repositories/unified-risk-assessment-repository',
        './src/contexts/risk-management/infrastructure/services/risk-calculation-service'
      ]
    },
    {
      name: 'Trading Execution',
      paths: [
        './src/contexts/trading-execution/application/services/trading-execution-application-service',
        './src/contexts/trading-execution/domain/entities/trade-order',
        './src/contexts/trading-execution/domain/value-objects/order-type',
        './src/contexts/trading-execution/infrastructure/repositories/trade-order-repository',
        './src/contexts/trading-execution/infrastructure/services/order-execution-service'
      ]
    },
    {
      name: 'User Management',
      paths: [
        './src/contexts/user-management/application/services/user-management-application-service',
        './src/contexts/user-management/domain/entities/user',
        './src/contexts/user-management/domain/value-objects/user-id',
        './src/contexts/user-management/infrastructure/repositories/UnifiedUserRepository',
        './src/contexts/user-management/infrastructure/services/user-authentication-service'
      ]
    },
    {
      name: 'User Config',
      paths: [
        './src/contexts/user-config/application/services/user-config-application-service',
        './src/contexts/user-config/domain/entities/user-config',
        './src/contexts/user-config/domain/value-objects/config-key',
        './src/contexts/user-config/infrastructure/repositories/user-config-repository'
      ]
    }
  ];

  const results: QuickAssessment[] = [];

  for (const module of modules) {
    console.log(`🔍 快速检查 ${module.name}...`);
    const assessment = await quickCheckModule(module.name, module.paths);
    results.push(assessment);
    
    const statusIcon = {
      'EXCELLENT': '✅',
      'GOOD': '⚠️',
      'NEEDS_IMPROVEMENT': '❌',
      'POOR': '💀'
    }[assessment.status];
    
    console.log(`${statusIcon} ${module.name}: ${assessment.status} (${assessment.completionPercentage}% - ${assessment.existingComponents}/${assessment.totalComponents})`);
    
    if (assessment.keyFindings.length > 0) {
      assessment.keyFindings.forEach(finding => {
        console.log(`    📝 ${finding}`);
      });
    }
  }

  // 生成总结
  console.log('\n📊 快速调查结果总结:');
  console.log('=' .repeat(80));
  
  results.forEach(result => {
    const statusIcon = {
      'EXCELLENT': '✅',
      'GOOD': '⚠️',
      'NEEDS_IMPROVEMENT': '❌',
      'POOR': '💀'
    }[result.status];
    
    console.log(`${statusIcon} ${result.name.padEnd(20)} | ${result.status.padEnd(18)} | ${result.completionPercentage.toString().padStart(3)}% | ${result.existingComponents}/${result.totalComponents}`);
  });

  // 统计分析
  const statusCounts = results.reduce((acc, result) => {
    acc[result.status] = (acc[result.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const totalComponents = results.reduce((sum, r) => sum + r.totalComponents, 0);
  const existingComponents = results.reduce((sum, r) => sum + r.existingComponents, 0);
  const overallCompletion = Math.round((existingComponents / totalComponents) * 100);

  console.log('\n📈 整体统计:');
  console.log(`✅ 优秀 (EXCELLENT): ${statusCounts.EXCELLENT || 0}`);
  console.log(`⚠️ 良好 (GOOD): ${statusCounts.GOOD || 0}`);
  console.log(`❌ 需改进 (NEEDS_IMPROVEMENT): ${statusCounts.NEEDS_IMPROVEMENT || 0}`);
  console.log(`💀 极差 (POOR): ${statusCounts.POOR || 0}`);
  console.log(`\n🎯 整体完成度: ${overallCompletion}% (${existingComponents}/${totalComponents})`);

  // 检查统一组件使用情况
  console.log('\n🔍 统一组件类型定义检查:');
  const unifiedComponentTypes = [
    'UnifiedTechnicalIndicatorCalculator',
    'PatternRecognitionService',
    'DynamicWeightingService',
    'MultiTimeframeService',
    'UnifiedAnalysisServiceManager',
    'FinancialMetricsService'
  ];

  let definedTypes = 0;
  unifiedComponentTypes.forEach(typeName => {
    const isDefined = !!(TYPES.Shared as any)?.[typeName];
    if (isDefined) {
      definedTypes++;
      console.log(`  ✅ TYPES.Shared.${typeName} 已定义`);
    } else {
      console.log(`  ❌ TYPES.Shared.${typeName} 未定义`);
    }
  });

  const typesCompletion = Math.round((definedTypes / unifiedComponentTypes.length) * 100);
  console.log(`\n📊 统一组件类型定义完整性: ${typesCompletion}% (${definedTypes}/${unifiedComponentTypes.length})`);

  console.log('\n🎉 剩余上下文模块快速调查完成！');
  
  return results;
}

// 运行调查
if (require.main === module) {
  investigateRemainingContexts()
    .then(results => {
      console.log('\n详细结果已生成，可用于更新调查报告。');
    })
    .catch(console.error);
}
