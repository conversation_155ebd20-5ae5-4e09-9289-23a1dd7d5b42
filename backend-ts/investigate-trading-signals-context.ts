/**
 * Trading Signals 上下文模块详细调查
 * 调查重点：开发完成度 + 统一组件使用情况
 */

import 'reflect-metadata';
import { TYPES } from './src/shared/infrastructure/di/types/index';

async function investigateTradingSignalsContext() {
  console.log('🔍 开始 Trading Signals 上下文模块详细调查...\n');

  let totalChecks = 0;
  let passedChecks = 0;
  let unifiedComponentViolations: string[] = [];

  // 1. 检查应用服务
  console.log('1️⃣ 检查应用服务...');
  const applicationServices = [
    {
      name: 'SignalGenerationApplicationService',
      path: './src/contexts/trading-signals/application/services/signal-generation-application-service'
    },
    {
      name: 'TradingSignalApplicationService',
      path: './src/contexts/trading-signals/application/services/trading-signal-application-service'
    }
  ];

  for (const service of applicationServices) {
    totalChecks++;
    try {
      const module = await import(service.path);
      if (module[service.name]) {
        passedChecks++;
        console.log(`  ✅ ${service.name} 存在且编译正常`);
      } else {
        console.log(`  ❌ ${service.name} 模块存在但类未导出`);
      }
    } catch (error) {
      console.log(`  ❌ ${service.name} 不存在或编译失败`);
      if (error instanceof Error && error.message.includes('Cannot find module')) {
        console.log(`    📝 模块文件不存在`);
      } else {
        console.log(`    📝 编译错误: ${error instanceof Error ? error.message.substring(0, 100) + '...' : String(error)}`);
      }
    }
  }

  // 2. 检查领域实体
  console.log('2️⃣ 检查领域实体...');
  const domainEntities = [
    {
      name: 'TradingSignal',
      path: './src/contexts/trading-signals/domain/entities/trading-signal'
    },
    {
      name: 'SignalStrategy',
      path: './src/contexts/trading-signals/domain/entities/signal-strategy'
    }
  ];

  for (const entity of domainEntities) {
    totalChecks++;
    try {
      const module = await import(entity.path);
      if (module[entity.name]) {
        passedChecks++;
        console.log(`  ✅ ${entity.name} 实体存在`);
      } else {
        console.log(`  ❌ ${entity.name} 模块存在但实体未导出`);
      }
    } catch (error) {
      console.log(`  ❌ ${entity.name} 实体不存在`);
    }
  }

  // 3. 检查值对象
  console.log('3️⃣ 检查值对象...');
  const valueObjects = [
    {
      name: 'SignalStrength',
      path: './src/contexts/trading-signals/domain/value-objects/signal-strength'
    },
    {
      name: 'SignalType',
      path: './src/contexts/trading-signals/domain/value-objects/signal-type'
    },
    {
      name: 'SignalConfidence',
      path: './src/contexts/trading-signals/domain/value-objects/signal-confidence'
    }
  ];

  for (const vo of valueObjects) {
    totalChecks++;
    try {
      const module = await import(vo.path);
      if (module[vo.name]) {
        passedChecks++;
        console.log(`  ✅ ${vo.name} 值对象存在`);
      } else {
        console.log(`  ❌ ${vo.name} 模块存在但值对象未导出`);
      }
    } catch (error) {
      console.log(`  ❌ ${vo.name} 值对象不存在`);
    }
  }

  // 4. 检查仓储
  console.log('4️⃣ 检查仓储...');
  const repositories = [
    {
      name: 'TradingSignalRepository',
      interfacePath: './src/contexts/trading-signals/domain/repositories/trading-signal-repository',
      implementationPath: './src/contexts/trading-signals/infrastructure/repositories/prisma-trading-signal-repository'
    },
    {
      name: 'StrategyRepository',
      interfacePath: './src/contexts/trading-signals/domain/repositories/strategy-repository',
      implementationPath: './src/contexts/trading-signals/infrastructure/repositories/prisma-strategy-repository'
    }
  ];

  for (const repo of repositories) {
    // 检查接口
    totalChecks++;
    try {
      await import(repo.interfacePath);
      passedChecks++;
      console.log(`  ✅ ${repo.name} 接口存在`);
    } catch (error) {
      console.log(`  ❌ ${repo.name} 接口不存在`);
    }

    // 检查实现
    totalChecks++;
    try {
      await import(repo.implementationPath);
      passedChecks++;
      console.log(`  ✅ ${repo.name} 实现存在`);
    } catch (error) {
      console.log(`  ❌ ${repo.name} 实现不存在`);
    }
  }

  // 5. 检查策略模式实现
  console.log('5️⃣ 检查策略模式实现...');
  const strategies = [
    {
      name: 'TrendFollowingStrategy',
      path: './src/contexts/trading-signals/domain/strategies/trend-following-strategy'
    },
    {
      name: 'MeanReversionStrategy',
      path: './src/contexts/trading-signals/domain/strategies/mean-reversion-strategy'
    },
    {
      name: 'StrategyFactory',
      path: './src/contexts/trading-signals/domain/strategies/strategy-factory'
    }
  ];

  for (const strategy of strategies) {
    totalChecks++;
    try {
      const module = await import(strategy.path);
      if (module[strategy.name]) {
        passedChecks++;
        console.log(`  ✅ ${strategy.name} 策略存在`);
      } else {
        console.log(`  ❌ ${strategy.name} 模块存在但策略未导出`);
      }
    } catch (error) {
      console.log(`  ❌ ${strategy.name} 策略不存在`);
    }
  }

  // 6. 检查基础设施实现
  console.log('6️⃣ 检查基础设施实现...');
  const infrastructure = [
    {
      name: 'SignalCalculationService',
      path: './src/contexts/trading-signals/infrastructure/services/signal-calculation-service'
    },
    {
      name: 'DegradationManagerService',
      path: './src/contexts/trading-signals/infrastructure/services/degradation-manager-service'
    }
  ];

  for (const infra of infrastructure) {
    totalChecks++;
    try {
      const module = await import(infra.path);
      if (module[infra.name]) {
        passedChecks++;
        console.log(`  ✅ ${infra.name} 存在`);
      } else {
        console.log(`  ❌ ${infra.name} 模块存在但服务未导出`);
      }
    } catch (error) {
      console.log(`  ❌ ${infra.name} 不存在`);
    }
  }

  // 7. 检查依赖注入配置
  console.log('7️⃣ 检查依赖注入配置...');
  totalChecks++;

  // 检查是否有专门的trading-signals容器模块
  let containerModuleExists = false;
  const possibleContainerModules = [
    './src/shared/infrastructure/di/modules/trading-signals-container-module',
    './src/contexts/trading-signals/infrastructure/di/container-module'
  ];

  for (const modulePath of possibleContainerModules) {
    try {
      await import(modulePath);
      containerModuleExists = true;
      console.log(`  ✅ 容器模块存在: ${modulePath}`);
      break;
    } catch (error) {
      // 继续检查下一个可能的路径
    }
  }

  if (containerModuleExists) {
    passedChecks++;
  } else {
    console.log(`  ❌ 未找到trading-signals容器模块`);
  }

  // 8. 检查统一组件使用情况
  console.log('8️⃣ 检查统一组件使用情况...');
  
  // 检查是否有违反统一组件使用的情况
  const potentialViolations = [
    {
      description: '直接使用技术指标库而非UnifiedTechnicalIndicatorCalculator',
      checkPaths: [
        './src/contexts/trading-signals/infrastructure/services/technical-indicator-service'
      ]
    },
    {
      description: '直接使用HTTP库而非HttpClientFactory',
      checkPaths: [
        './src/contexts/trading-signals/infrastructure/services/external-signal-service'
      ]
    }
  ];

  for (const violation of potentialViolations) {
    for (const path of violation.checkPaths) {
      try {
        await import(path);
        unifiedComponentViolations.push(`⚠️ 可能违规: ${violation.description} (${path})`);
        console.log(`  ⚠️ 发现可能的统一组件使用违规: ${path}`);
      } catch (error) {
        // 文件不存在，不算违规
      }
    }
  }

  // 9. 检查TYPES定义
  console.log('9️⃣ 检查TYPES定义...');
  const typesCheck = {
    SignalGenerationApplicationService: !!TYPES.TradingSignals?.SignalGenerationApplicationService,
    TradingSignalApplicationService: !!TYPES.TradingSignals?.TradingSignalApplicationService,
    StrategyFactory: !!TYPES.TradingSignals?.StrategyFactory,
    DegradationManagerService: !!TYPES.TradingSignals?.DegradationManagerService
  };

  let typesScore = 0;
  const totalTypes = Object.keys(typesCheck).length;
  
  for (const [typeName, exists] of Object.entries(typesCheck)) {
    if (exists) {
      typesScore++;
      console.log(`  ✅ TYPES.TradingSignals.${typeName} 已定义`);
    } else {
      console.log(`  ❌ TYPES.TradingSignals.${typeName} 未定义`);
    }
  }

  // 10. 生成总体评估
  console.log('\n📊 Trading Signals 模块总体评估:');
  
  const completionPercentage = Math.round((passedChecks / totalChecks) * 100);
  const typesPercentage = Math.round((typesScore / totalTypes) * 100);
  const hasViolations = unifiedComponentViolations.length > 0;
  
  console.log(`  完成度: ${completionPercentage}% (${passedChecks}/${totalChecks})`);
  console.log(`  类型定义完整性: ${typesPercentage}% (${typesScore}/${totalTypes})`);
  console.log(`  统一组件合规性: ${hasViolations ? '有违规' : '无明显违规'}`);

  let status: string;
  if (completionPercentage >= 90 && typesPercentage >= 90 && !hasViolations) {
    status = 'EXCELLENT';
  } else if (completionPercentage >= 70 && typesPercentage >= 70) {
    status = 'GOOD';
  } else if (completionPercentage >= 50) {
    status = 'NEEDS_IMPROVEMENT';
  } else {
    status = 'POOR';
  }

  console.log(`  状态: ${status}`);

  if (unifiedComponentViolations.length > 0) {
    console.log('\n⚠️ 统一组件使用违规:');
    unifiedComponentViolations.forEach(violation => console.log(`  ${violation}`));
  }

  console.log('\n🎉 Trading Signals 上下文调查完成！');
  
  return {
    completionPercentage,
    typesPercentage,
    status,
    violations: unifiedComponentViolations,
    passedChecks,
    totalChecks
  };
}

// 运行调查
if (require.main === module) {
  investigateTradingSignalsContext()
    .then(result => {
      console.log('\n详细结果已生成，可用于更新调查报告。');
    })
    .catch(console.error);
}
