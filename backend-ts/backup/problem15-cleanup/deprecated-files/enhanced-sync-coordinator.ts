/**
 * ⚠️ 已废弃：增强数据同步协调器
 *
 * 此文件已被标记为废弃，功能已迁移到 unified-data-sync-coordinator.ts
 *
 * 迁移原因：
 * 1. 避免重复实现 - unified-data-sync-coordinator.ts 提供更完整的功能
 * 2. 统一架构 - 使用已建立的统一错误处理、缓存、监控系统
 * 3. 减少维护成本 - 单一数据同步协调器实现
 *
 * 替代方案：
 * - 使用 UnifiedDataSyncCoordinator 替代 EnhancedSyncCoordinator
 * - 参考 unified-data-sync-coordinator.ts 获取完整API
 *
 * @deprecated 请使用 unified-data-sync-coordinator.ts
 */

import { injectable } from 'inversify';
import { EventEmitter } from 'events';
import { createLogger } from '../shared/infrastructure/logging/logger-factory';
import { RealTimeSyncService, SyncData, SyncResult } from './real-time-sync-service';
import { RealWebSocketManager, MarketData } from '../contexts/market-data/infrastructure/websocket/real-websocket-manager';
import { HighFrequencyDataProcessor } from '../contexts/market-data/infrastructure/processing/high-frequency-data-processor';

export interface SyncCoordinatorConfig {
  enableRealTimeSync: boolean;
  enableWebSocketIntegration: boolean;
  enableDataProcessing: boolean;
  syncBatchSize: number;
  syncInterval: number;
  maxConcurrentSyncs: number;
  retryAttempts: number;
}

export interface SyncStats {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  averageSyncTime: number;
  throughput: number;
  lastSyncTime?: Date;
  activeConnections: number;
  processedMessages: number;
}

/**
 * @deprecated 此类已废弃，请使用 UnifiedDataSyncCoordinator
 *
 * 迁移指南：
 * 1. 导入 UnifiedDataSyncCoordinator 替代此类
 * 2. 使用 unified-data-sync-coordinator.ts 中的API
 * 3. 参考统一架构文档进行迁移
 */
@injectable()
export class EnhancedSyncCoordinator extends EventEmitter {
  private readonly logger = createLogger('EnhancedSyncCoordinator');
  
  // 核心服务
  private realTimeSyncService: RealTimeSyncService;
  private webSocketManager: RealWebSocketManager;
  private dataProcessor: HighFrequencyDataProcessor;
  
  // 状态管理
  private isRunning = false;
  private syncInterval: NodeJS.Timeout | null = null;
  
  // 配置
  private config: SyncCoordinatorConfig = {
    enableRealTimeSync: true,
    enableWebSocketIntegration: true,
    enableDataProcessing: true,
    syncBatchSize: 100,
    syncInterval: 30000, // 30秒
    maxConcurrentSyncs: 10,
    retryAttempts: 3
  };
  
  // 统计信息
  private stats: SyncStats = {
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    averageSyncTime: 0,
    throughput: 0,
    activeConnections: 0,
    processedMessages: 0
  };
  
  // 数据缓冲
  private dataBuffer: MarketData[] = [];
  private syncTimes: number[] = [];

  constructor() {
    super();
    this.initializeServices();
  }

  /**
   * 初始化服务
   */
  private initializeServices(): void {
    // 初始化实时同步服务
    this.realTimeSyncService = new RealTimeSyncService();
    
    // 初始化WebSocket管理器
    this.webSocketManager = new RealWebSocketManager();
    
    // 初始化数据处理器
    this.dataProcessor = new HighFrequencyDataProcessor();
    
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // WebSocket事件
    this.webSocketManager.on('marketData', (data: MarketData) => {
      this.handleMarketData(data);
    });
    
    this.webSocketManager.on('connected', (exchange: string) => {
      this.logger.info('WebSocket连接成功', { exchange });
      this.updateConnectionStats();
    });
    
    this.webSocketManager.on('disconnected', (exchange: string) => {
      this.logger.warn('WebSocket连接断开', { exchange });
      this.updateConnectionStats();
    });
    
    // 数据处理器事件
    this.dataProcessor.on('dataProcessed', (data: any) => {
      this.handleProcessedData(data);
    });
    
    this.dataProcessor.on('batchProcessed', (event: any) => {
      this.stats.processedMessages += event.batchSize;
      this.emit('batchProcessed', event);
    });
    
    // 同步服务事件
    this.realTimeSyncService.on('marketDataSynced', (event: any) => {
      this.stats.successfulSyncs++;
      this.emit('dataSynced', event);
    });
    
    this.realTimeSyncService.on('jobFailed', (event: any) => {
      this.stats.failedSyncs++;
      this.emit('syncFailed', event);
    });
  }

  /**
   * 启动协调器
   */
  async start(config?: Partial<SyncCoordinatorConfig>): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('同步协调器已在运行');
      return;
    }

    // 更新配置
    if (config) {
      this.config = { ...this.config, ...config };
    }

    this.isRunning = true;
    
    try {
      // 启动实时同步服务
      if (this.config.enableRealTimeSync) {
        await this.realTimeSyncService.start();
        this.logger.info('实时同步服务已启动');
      }
      
      // 启动数据处理器
      if (this.config.enableDataProcessing) {
        await this.dataProcessor.startProcessing({
          batchSize: this.config.syncBatchSize,
          batchInterval: this.config.syncInterval / 2,
          maxBufferSize: this.config.syncBatchSize * 10
        });
        this.logger.info('数据处理器已启动');
      }
      
      // 启动WebSocket连接
      if (this.config.enableWebSocketIntegration) {
        await this.webSocketManager.connectToAllExchanges();
        this.logger.info('WebSocket连接已建立');
      }
      
      // 启动定期同步
      this.startPeriodicSync();
      
      this.logger.info('增强同步协调器启动成功', {
        config: this.config
      });
      
      this.emit('started', this.config);
    } catch (error) {
      this.isRunning = false;
      this.logger.error('启动同步协调器失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 停止协调器
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    try {
      // 停止定期同步
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = null;
      }
      
      // 停止服务
      if (this.config.enableRealTimeSync) {
        await this.realTimeSyncService.stop();
      }
      
      if (this.config.enableDataProcessing) {
        await this.dataProcessor.stopProcessing();
      }
      
      if (this.config.enableWebSocketIntegration) {
        this.webSocketManager.disconnect();
      }
      
      this.logger.info('增强同步协调器已停止');
      this.emit('stopped');
    } catch (error) {
      this.logger.error('停止同步协调器失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 同步数据
   */
  async syncData(data: SyncData): Promise<SyncResult> {
    if (!this.isRunning) {
      throw new Error('同步协调器未运行');
    }
    
    const startTime = Date.now();
    this.stats.totalSyncs++;
    
    try {
      const result = await this.realTimeSyncService.syncDataInRealTime(data);
      
      const syncTime = Date.now() - startTime;
      this.updateSyncTimeStats(syncTime);
      
      return result;
    } catch (error) {
      this.stats.failedSyncs++;
      throw error;
    }
  }

  /**
   * 批量同步数据
   */
  async batchSyncData(dataList: SyncData[]): Promise<SyncResult[]> {
    if (!this.isRunning) {
      throw new Error('同步协调器未运行');
    }
    
    const startTime = Date.now();
    
    try {
      const results = await this.realTimeSyncService.batchSyncData(dataList);
      
      const syncTime = Date.now() - startTime;
      this.updateSyncTimeStats(syncTime);
      
      return results;
    } catch (error) {
      this.logger.error('批量同步失败', {
        error: error instanceof Error ? error.message : String(error),
        dataCount: dataList.length
      });
      throw error;
    }
  }

  /**
   * 处理市场数据
   */
  private async handleMarketData(data: MarketData): Promise<void> {
    try {
      // 添加到数据处理器
      if (this.config.enableDataProcessing) {
        await this.dataProcessor.addData(data);
      }
      
      // 直接同步关键数据
      if (this.shouldSyncImmediately(data)) {
        const syncData: SyncData = {
          type: 'marketData',
          payload: data,
          source: data.exchange,
          metadata: {
            timestamp: data.timestamp,
            version: '1.0'
          }
        };
        
        await this.syncData(syncData);
      } else {
        // 添加到缓冲区
        this.dataBuffer.push(data);
      }
    } catch (error) {
      this.logger.error('处理市场数据失败', {
        error: error instanceof Error ? error.message : String(error),
        symbol: data.symbol,
        exchange: data.exchange
      });
    }
  }

  /**
   * 处理已处理的数据
   */
  private async handleProcessedData(data: any): Promise<void> {
    try {
      const syncData: SyncData = {
        type: 'marketData',
        payload: data,
        source: 'dataProcessor',
        metadata: {
          timestamp: new Date(),
          version: '1.0'
        }
      };
      
      await this.syncData(syncData);
    } catch (error) {
      this.logger.error('处理已处理数据失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 判断是否需要立即同步
   */
  private shouldSyncImmediately(data: MarketData): boolean {
    // 价格变化超过阈值
    const priceChangeThreshold = 0.01; // 1%
    if (data.change24h && Math.abs(data.change24h) > priceChangeThreshold) {
      return true;
    }
    
    // 交易量异常
    const volumeThreshold = 1000000; // 100万
    if (data.volume > volumeThreshold) {
      return true;
    }
    
    return false;
  }

  /**
   * 启动定期同步
   */
  private startPeriodicSync(): void {
    this.syncInterval = setInterval(async () => {
      await this.performPeriodicSync();
    }, this.config.syncInterval);
  }

  /**
   * 执行定期同步
   */
  private async performPeriodicSync(): Promise<void> {
    if (this.dataBuffer.length === 0) {
      return;
    }
    
    try {
      // 批量处理缓冲的数据
      const batchSize = Math.min(this.config.syncBatchSize, this.dataBuffer.length);
      const batch = this.dataBuffer.splice(0, batchSize);
      
      const syncDataList: SyncData[] = batch.map(data => ({
        type: 'marketData',
        payload: data,
        source: data.exchange,
        metadata: {
          timestamp: data.timestamp,
          version: '1.0'
        }
      }));
      
      await this.batchSyncData(syncDataList);
      
      this.logger.debug('定期同步完成', {
        batchSize,
        remainingBuffer: this.dataBuffer.length
      });
    } catch (error) {
      this.logger.error('定期同步失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 更新连接统计
   */
  private updateConnectionStats(): void {
    const healthStatus = this.webSocketManager.getHealthStatus();
    this.stats.activeConnections = healthStatus.activeConnections;
  }

  /**
   * 更新同步时间统计
   */
  private updateSyncTimeStats(time: number): void {
    this.syncTimes.push(time);
    
    // 保持最近1000次的同步时间
    if (this.syncTimes.length > 1000) {
      this.syncTimes = this.syncTimes.slice(-1000);
    }
    
    // 计算平均同步时间
    this.stats.averageSyncTime = 
      this.syncTimes.reduce((sum, t) => sum + t, 0) / this.syncTimes.length;
    
    // 计算吞吐量
    const timeWindow = this.config.syncInterval / 1000; // 转换为秒
    this.stats.throughput = this.stats.successfulSyncs / timeWindow;
    this.stats.lastSyncTime = new Date();
  }

  /**
   * 获取统计信息
   */
  getStats(): SyncStats {
    return { ...this.stats };
  }

  /**
   * 获取配置
   */
  getConfig(): SyncCoordinatorConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SyncCoordinatorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.info('配置已更新', { config: this.config });
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取健康状态
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: Record<string, boolean>;
    stats: SyncStats;
    uptime: number;
  }> {
    const services = {
      realTimeSync: this.config.enableRealTimeSync,
      webSocketManager: this.config.enableWebSocketIntegration && this.stats.activeConnections > 0,
      dataProcessor: this.config.enableDataProcessing
    };
    
    const healthyServices = Object.values(services).filter(Boolean).length;
    const totalServices = Object.keys(services).length;
    
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (healthyServices === totalServices) {
      status = 'healthy';
    } else if (healthyServices > 0) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }
    
    return {
      status,
      services,
      stats: this.stats,
      uptime: Date.now() - (this.stats.lastSyncTime?.getTime() ?? Date.now())
    };
  }
}
