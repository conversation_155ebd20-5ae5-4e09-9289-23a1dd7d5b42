/**
 * ⚠️ 已废弃：增强信号控制器
 *
 * 此文件已被标记为废弃，功能已合并到 production-signal-controller.ts
 *
 * 迁移原因：
 * 1. 避免重复实现 - 合并到统一的生产信号控制器
 * 2. 统一架构 - 使用已建立的统一错误处理和HTTP客户端
 * 3. 减少维护成本 - 单一信号控制器实现
 * 4. 向后兼容 - DI容器中的别名确保现有代码继续工作
 *
 * 替代方案：
 * - 使用 ProductionSignalController.generateEnhancedSignal() 方法
 * - 参考 production-signal-controller.ts 获取完整API
 *
 * @deprecated 请使用 production-signal-controller.ts
 */

import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { TYPES } from '@shared/infrastructure/di/types';
import {
  EnhancedSignalFusionAdapter,
  EnhancedFusedSignalRequest,
  EnhancedFusedTradingSignal
} from '@contexts/trend-analysis/infrastructure/adapters/signal-fusion-adapter';
import { TradingSymbol } from '@contexts/market-data/domain/value-objects/trading-symbol';
import { Timeframe } from '@contexts/market-data/domain/value-objects/timeframe';

/**
 * 增强信号控制器
 */
@injectable()
export class EnhancedSignalController {
  constructor(
    @inject(TYPES.Logger) private readonly logger: Logger,
    @inject(TYPES.TradingAnalysis.EnhancedSignalFusionCoordinator)
    private readonly enhancedCoordinator: EnhancedSignalFusionAdapter
  ) {}

  /**
   * 生成账户感知的增强交易信号
   * POST /api/v2/signals/enhanced
   */
  public generateEnhancedSignal = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        symbol,
        timeframe = '1h',
        analysisDepth = 'standard',
        accountInfo,
        collaborativeOptions
      } = req.body;

      // 验证必要参数
      if (!symbol) {
        res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: '交易对符号不能为空'
        });
        return;
      }

      // 构造请求
      const request: EnhancedFusedSignalRequest = {
        symbol: TradingSymbol.create(symbol),
        timeframe: Timeframe.create(timeframe),
        analysisDepth: analysisDepth === 'basic' ? 'quick' : analysisDepth as 'standard' | 'comprehensive',
        accountInfo,
        collaborativeOptions
      };

      this.logger.info('开始生成增强交易信号', {
        symbol,
        timeframe,
        analysisDepth,
        hasAccountInfo: !!accountInfo,
        collaborativeOptions
      });

      // 生成增强信号
      const enhancedSignal = await this.enhancedCoordinator.generateAccountAwareSignal(request);

      // 构造响应
      const response = {
        success: true,
        data: {
          ...enhancedSignal,
          apiVersion: 'v2',
          systemType: 'ENHANCED_FOUR_DIMENSIONAL_FUSION',
          upgradePhase: 'PHASE_1_ACCOUNT_AWARE',
          features: {
            accountAware: !!accountInfo,
            collaborativeAnalysis: !!collaborativeOptions,
            realTimeRiskConstraints: !!collaborativeOptions?.enableRealTimeRisk,
            accountConstraints: !!collaborativeOptions?.enableAccountConstraints
          }
        },
        message: '增强交易信号生成成功'
      };

      this.logger.info('增强交易信号生成完成', {
        symbol,
        signal: enhancedSignal.signal,
        strength: enhancedSignal.strength,
        confidence: enhancedSignal.confidence,
        trendAlignment: enhancedSignal.collaborativeInsights?.trendAlignment,
        riskAssessment: enhancedSignal.collaborativeInsights?.riskAssessment,
        marketConditions: enhancedSignal.collaborativeInsights?.marketConditions
      });

      res.json(response);
    } catch (error) {
      this.logger.error('增强交易信号生成失败', {
        error: error instanceof Error ? error.message : String(error),
        body: req.body
      });

      res.status(500).json({
        success: false,
        error: 'Internal Server Error',
        message: '增强交易信号生成失败'
      });
    }
  };

  /**
   * 获取增强信号配置
   * GET /api/v2/signals/enhanced/config
   */
  public getEnhancedConfig = async (req: Request, res: Response): Promise<void> => {
    try {
      const configManager = this.enhancedCoordinator.getConfigManager();
      const config = await configManager.getCollaborativeDecisionConfig();

      res.json({
        success: true,
        data: {
          config,
          isValid: true, // UnifiedConfigManager已内置验证
          apiVersion: 'v2',
          upgradePhase: 'PHASE_1_ACCOUNT_AWARE'
        },
        message: '增强信号配置获取成功'
      });
    } catch (error) {
      this.logger.error('获取增强信号配置失败', { error });
      res.status(500).json({
        success: false,
        error: 'Internal Server Error',
        message: '获取增强信号配置失败'
      });
    }
  };

  /**
   * 更新增强信号配置
   * PUT /api/v2/signals/enhanced/config
   */
  public updateEnhancedConfig = async (req: Request, res: Response): Promise<void> => {
    try {
      const { config } = req.body;

      if (!config) {
        res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: '配置数据不能为空'
        });
        return;
      }

      const configManager = this.enhancedCoordinator.getConfigManager();
      // 使用UnifiedConfigManager的批量设置方法
      await configManager.setBatch(
        Object.entries(config).map(([key, value]) => ({
          key,
          value: value as string | number | boolean | object
        })),
        {},
        'enhanced-signal-controller'
      );

      // UnifiedConfigManager已内置验证，无需额外验证
      const isValid = true;
      if (!isValid) {
        res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: '配置数据无效'
        });
        return;
      }

      this.logger.info('增强信号配置更新成功', { config });

      res.json({
        success: true,
        data: {
          config: await configManager.getCollaborativeDecisionConfig(),
          isValid: true
        },
        message: '增强信号配置更新成功'
      });
    } catch (error) {
      this.logger.error('更新增强信号配置失败', { error, body: req.body });
      res.status(500).json({
        success: false,
        error: 'Internal Server Error',
        message: '更新增强信号配置失败'
      });
    }
  };

  /**
   * 获取系统状态
   * GET /api/v2/signals/enhanced/status
   */
  public getSystemStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const configManager = this.enhancedCoordinator.getConfigManager();
      const errorHandler = this.enhancedCoordinator.getErrorHandler();

      const status = {
        systemType: 'ENHANCED_FOUR_DIMENSIONAL_FUSION',
        upgradePhase: 'PHASE_1_ACCOUNT_AWARE',
        timestamp: new Date().toISOString(),
        components: {
          enhancedCoordinator: 'OPERATIONAL',
          configManager: 'OPERATIONAL', // UnifiedConfigManager已内置验证
          errorHandler: 'OPERATIONAL',
          riskAssessmentService: 'OPTIONAL',
          trendAnalysisService: 'OPTIONAL'
        },
        features: {
          accountAwareSignals: true,
          collaborativeAnalysis: true,
          realTimeRiskConstraints: true,
          dynamicWeightAdjustment: true,
          enhancedFusionAlgorithm: true,
          configurationManagement: true,
          errorRecovery: true
        },
        capabilities: {
          backwardCompatible: true,
          gracefulDegradation: true,
          errorRecovery: true,
          configValidation: true
        }
      };

      res.json({
        success: true,
        data: status,
        message: '系统状态获取成功'
      });
    } catch (error) {
      this.logger.error('获取系统状态失败', { error });
      res.status(500).json({
        success: false,
        error: 'Internal Server Error',
        message: '获取系统状态失败'
      });
    }
  };

  /**
   * 健康检查
   * GET /api/v2/signals/enhanced/health
   */
  public healthCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      const startTime = Date.now();

      // 执行基本的健康检查
      const configManager = this.enhancedCoordinator.getConfigManager();
      const isConfigValid = true; // UnifiedConfigManager已内置验证

      // 测试基本信号生成（不包含账户信息）
      const testRequest: EnhancedFusedSignalRequest = {
        symbol: TradingSymbol.create('BTCUSDT'),
        timeframe: Timeframe.create('1h'),
        analysisDepth: 'quick'
      };

      const testSignal = await this.enhancedCoordinator.generateAccountAwareSignal(testRequest);
      const responseTime = Date.now() - startTime;

      const healthStatus = {
        status: 'HEALTHY',
        timestamp: new Date().toISOString(),
        responseTime,
        systemType: 'ENHANCED_FOUR_DIMENSIONAL_FUSION',
        upgradePhase: 'PHASE_1_ACCOUNT_AWARE',
        checks: {
          configValidation: isConfigValid ? 'PASS' : 'FAIL',
          signalGeneration: testSignal ? 'PASS' : 'FAIL',
          errorHandling: 'PASS',
          apiResponse: responseTime < 5000 ? 'PASS' : 'SLOW'
        },
        testSignal: {
          signal: testSignal.signal,
          strength: testSignal.strength,
          confidence: testSignal.confidence,
          hasCollaborativeInsights: !!testSignal.collaborativeInsights,
          hasAccountConstraints: !!testSignal.accountConstraints
        }
      };

      res.json({
        success: true,
        data: healthStatus,
        message: '健康检查完成'
      });
    } catch (error) {
      this.logger.error('健康检查失败', { error });
      res.status(503).json({
        success: false,
        error: 'Service Unavailable',
        message: '健康检查失败',
        data: {
          status: 'UNHEALTHY',
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : String(error)
        }
      });
    }
  };
}
