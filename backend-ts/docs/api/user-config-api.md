# 用户配置API文档

## 概述

用户配置API提供了完整的用户LLM配置管理、模型偏好设置和使用统计功能。

## 基础信息

- **基础URL**: `/api/v1/user`
- **认证方式**: 用户ID通过Header `x-user-id` 或查询参数 `userId` 传递
- **响应格式**: JSON

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {...},
  "message": "操作成功信息"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误信息"
}
```

## 1. LLM配置管理API

### 1.1 获取用户配置列表

**GET** `/llm-configs`

获取用户的所有LLM配置。

**请求参数**
- `userId` (string, 可选): 用户ID，也可通过Header传递

**响应示例**
```json
{
  "success": true,
  "data": [
    {
      "id": "config-123",
      "userId": "user-456",
      "providerName": "openai",
      "isActive": true,
      "configMetadata": {
        "model": "gpt-4-turbo",
        "temperature": 0.7
      },
      "validationStatus": "valid",
      "totalCalls": 150,
      "totalCost": 12.50,
      "lastUsed": "2024-01-15T10:30:00Z",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 1.2 获取活跃配置

**GET** `/llm-configs/active`

获取用户的活跃LLM配置。

### 1.3 创建或更新配置

**POST** `/llm-configs`

创建或更新用户的LLM配置。

**请求体**
```json
{
  "providerName": "openai",
  "apiKey": "sk-...",
  "configMetadata": {
    "model": "gpt-4-turbo",
    "temperature": 0.7,
    "maxTokens": 4096
  }
}
```

### 1.4 更新特定提供者配置

**PUT** `/llm-configs/{provider}`

更新特定提供者的配置。

**路径参数**
- `provider` (string): 提供者名称 (openai, anthropic, gemini)

**请求体**
```json
{
  "apiKey": "new-api-key",
  "isActive": true,
  "configMetadata": {
    "temperature": 0.8
  }
}
```

### 1.5 删除配置

**DELETE** `/llm-configs/{provider}`

删除特定提供者的配置。

### 1.6 测试API密钥

**POST** `/llm-configs/{provider}/test`

测试API密钥的有效性。

**请求体**
```json
{
  "apiKey": "sk-..."
}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "isValid": true,
    "responseTime": 1250
  }
}
```

### 1.7 获取支持的提供者

**GET** `/llm-configs/providers`

获取系统支持的LLM提供者列表。

**响应示例**
```json
{
  "success": true,
  "data": [
    {
      "name": "openai",
      "displayName": "OpenAI",
      "description": "GPT-4, GPT-3.5 Turbo等模型",
      "models": ["gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"],
      "apiKeyFormat": "sk-...",
      "testEndpoint": "https://api.openai.com/v1/models",
      "documentation": "https://platform.openai.com/docs"
    }
  ]
}
```

### 1.8 批量操作

**PATCH** `/llm-configs/batch`

批量更新配置状态。

**请求体**
```json
{
  "operations": [
    {
      "action": "activate",
      "providerName": "openai"
    },
    {
      "action": "deactivate", 
      "providerName": "anthropic"
    }
  ]
}
```

### 1.9 验证所有配置

**POST** `/llm-configs/validate`

验证用户所有配置的有效性。

### 1.10 导出配置

**GET** `/llm-configs/export`

导出用户配置（不包含API密钥）。

### 1.11 导入配置

**POST** `/llm-configs/import`

导入用户配置。

**请求体**
```json
{
  "configs": [
    {
      "providerName": "openai",
      "isActive": true,
      "configMetadata": {...}
    }
  ],
  "overwrite": false
}
```

## 2. 模型偏好配置API

### 2.1 获取可用场景

**GET** `/model-preferences/scenarios`

获取系统支持的场景列表。

**响应示例**
```json
{
  "success": true,
  "data": [
    {
      "id": "trading_signal",
      "name": "交易信号生成",
      "description": "生成买入/卖出/持有信号",
      "requiredCapabilities": ["REASONING", "ANALYSIS"],
      "priority": "high",
      "typicalLatency": 3000,
      "typicalCost": 0.005
    }
  ]
}
```

### 2.2 获取场景推荐模型

**GET** `/model-preferences/scenarios/{scenario}/recommendations`

获取特定场景的推荐模型。

### 2.3 选择最优模型

**POST** `/model-preferences/select`

基于场景和要求选择最优模型。

**请求体**
```json
{
  "scenario": "trading_signal",
  "requirements": {
    "maxLatency": 5000,
    "maxCost": 0.01
  },
  "urgency": "high"
}
```

**响应示例**
```json
{
  "success": true,
  "data": {
    "selectedModel": "gpt-4-turbo",
    "selectedProvider": "openai",
    "confidence": 0.95,
    "reasoning": "适合交易信号场景，高质量模型，响应速度快",
    "fallbackModels": [
      {
        "model": "claude-3-sonnet",
        "provider": "anthropic",
        "priority": 1
      }
    ],
    "estimatedLatency": 3000,
    "estimatedCost": 0.005
  }
}
```

### 2.4 获取用户偏好列表

**GET** `/model-preferences`

获取用户的模型偏好列表。

### 2.5 获取特定场景偏好

**GET** `/model-preferences/{scenario}`

获取特定场景的模型偏好。

### 2.6 创建或更新偏好

**POST** `/model-preferences`

创建或更新模型偏好。

**请求体**
```json
{
  "scenario": "trading_signal",
  "preferredProvider": "openai",
  "preferredModel": "gpt-4-turbo",
  "fallbackProviders": [
    {
      "provider": "anthropic",
      "model": "claude-3-sonnet",
      "priority": 1
    }
  ],
  "metadata": {
    "temperature": 0.7,
    "maxTokens": 2048
  },
  "isActive": true
}
```

### 2.7 更新特定场景偏好

**PUT** `/model-preferences/{scenario}`

更新特定场景的模型偏好。

### 2.8 删除偏好

**DELETE** `/model-preferences/{scenario}`

删除特定场景的模型偏好。

### 2.9 获取偏好统计

**GET** `/model-preferences/stats`

获取用户模型偏好统计信息。

## 3. 统计和监控API

### 3.1 使用概览

**GET** `/statistics/overview`

获取用户使用统计概览。

**响应示例**
```json
{
  "success": true,
  "data": {
    "user": {
      "userId": "user-123",
      "lastActivity": "2024-01-15T10:30:00Z"
    },
    "configurations": {
      "total": 3,
      "active": 2,
      "valid": 2
    },
    "preferences": {
      "total": 5,
      "active": 4,
      "scenarios": 3
    },
    "usage": {
      "totalCalls": 1250,
      "totalCost": 45.67,
      "averageCostPerCall": 0.0365
    },
    "performance": {
      "cacheHitRate": 0.85,
      "averageResponseTime": 1200
    }
  }
}
```

### 3.2 调用统计

**GET** `/statistics/calls`

获取调用次数统计。

**查询参数**
- `timeRange` (string): 时间范围 (7d, 30d)
- `groupBy` (string): 分组方式 (day, hour)

### 3.3 成本分析

**GET** `/statistics/costs`

获取成本分析数据。

### 3.4 性能监控

**GET** `/statistics/performance`

获取性能监控数据。

### 3.5 热门模型

**GET** `/statistics/popular-models`

获取热门模型统计。

**查询参数**
- `scenario` (string, 可选): 特定场景
- `limit` (number, 可选): 返回数量限制，默认10

## 错误代码

| 状态码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### 完整配置流程

1. **获取支持的提供者**
```bash
curl -X GET "/api/v1/user/llm-configs/providers"
```

2. **创建配置**
```bash
curl -X POST "/api/v1/user/llm-configs" \
  -H "x-user-id: user-123" \
  -H "Content-Type: application/json" \
  -d '{
    "providerName": "openai",
    "apiKey": "sk-...",
    "configMetadata": {
      "model": "gpt-4-turbo"
    }
  }'
```

3. **设置模型偏好**
```bash
curl -X POST "/api/v1/user/model-preferences" \
  -H "x-user-id: user-123" \
  -H "Content-Type: application/json" \
  -d '{
    "scenario": "trading_signal",
    "preferredProvider": "openai",
    "preferredModel": "gpt-4-turbo"
  }'
```

4. **选择最优模型**
```bash
curl -X POST "/api/v1/user/model-preferences/select" \
  -H "x-user-id: user-123" \
  -H "Content-Type: application/json" \
  -d '{
    "scenario": "trading_signal",
    "urgency": "high"
  }'
```
