# API健康检查报告

**生成时间**: 2025/7/19 10:08:55
**检查耗时**: 10027ms
**平均响应时间**: 391.36ms

## 📊 检查概览

| 指标 | 数值 |
|------|------|
| 总端点数 | 260 |
| 成功端点 | 41 |
| 失败端点 | 219 |
| 成功率 | 15.77% |

**整体健康状态**: 🔴 需要关注

## 📋 分模块检查结果

### ❌ 用户管理

成功率: 22.2% (10/45)

- 🟢 **get-api-v1-admin-users**: 成功 (25ms)
- 🟢 **get-api-v1-admin-users-userId**: 成功 (37ms)
- 🟢 **patch-api-v1-admin-users-userId-status**: 成功 (35ms)
- 🟢 **patch-api-v1-admin-users-userId-role**: 成功 (35ms)
- 🟢 **post-api-v1-admin-users-userId-revoke-sessions**: 成功 (35ms)
- 🔴 **post-api-v1-user-userId-llm-configs**: 失败 (38ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-user-userId-llm-configs**: 失败 (38ms) - Status: 404, Valid: true
- 🔴 **put-api-v1-user-userId-llm-configs-provider**: 失败 (38ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-user-userId-llm-configs-active**: 失败 (38ms) - Status: 404, Valid: true
- 🔴 **delete-api-v1-user-userId-llm-configs-provider**: 失败 (39ms) - Status: 404, Valid: true
- 🟢 **get-api-v1-admin-users-userId**: 成功 (54ms)
- 🔴 **get-api-v1-user-config-users-userId-profile-strategy-compatibility**: 失败 (61ms) - Status: 400, Valid: false
- 🔴 **get-api-v1-user-management**: 失败 (61ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-user-management-userId**: 失败 (61ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-user-management-profile**: 失败 (62ms) - Status: 500, Valid: false
- 🟢 **patch-api-v1-admin-users-userId-status**: 成功 (63ms)
- 🟢 **patch-api-v1-admin-users-userId-role**: 成功 (63ms)
- 🔴 **patch-api-v1-user-management-userId-status**: 失败 (63ms) - Status: 500, Valid: false
- 🟢 **post-api-v1-admin-users-userId-revoke-sessions**: 成功 (62ms)
- 🔴 **patch-api-v1-user-management-userId-role**: 失败 (65ms) - Status: 500, Valid: false
- 🔴 **delete-api-v1-user-config-users-userId-profile**: 失败 (82ms) - Status: 400, Valid: false
- 🔴 **delete-api-v1-user-config-users-userId-preferences**: 失败 (82ms) - Status: 400, Valid: false
- 🔴 **get-api-v1-user-config-notifications-notificationType-users**: 失败 (84ms) - Status: 400, Valid: false
- 🟢 **get-api-v1-user-config-profiles-statistics**: 成功 (84ms)
- 🔴 **get-api-v1-user-config-users-userId-preferences-localization**: 失败 (84ms) - Status: 400, Valid: false
- 🔴 **get-api-v1-user-config-users-userId-preferences**: 失败 (86ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-user-config-users-userId-profile**: 失败 (85ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-user-config-users-userId-preferences-validate**: 失败 (86ms) - Status: 400, Valid: false
- 🔴 **get-api-v1-user-config-users-userId-profile-strategy-adjustments**: 失败 (86ms) - Status: 400, Valid: false
- 🔴 **get-api-v1-user-config-users-userId-profile-validate**: 失败 (86ms) - Status: 400, Valid: false
- 🔴 **get-api-v1-user-config-preferences-statistics**: 失败 (92ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-user-config-users-userId-preferences**: 失败 (98ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-user-config-users-userId-profile**: 失败 (98ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-user-config-users-userId-preferences-reset**: 失败 (99ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-user-config-users-preferences-batch**: 失败 (99ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-user-management-logout**: 失败 (100ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-user-management-login**: 失败 (100ms) - Status: 500, Valid: true
- 🔴 **post-api-v1-user-management-refresh-token**: 失败 (100ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-user-management-register**: 失败 (100ms) - Status: 500, Valid: false
- 🔴 **put-api-v1-user-config-users-userId-preferences**: 失败 (101ms) - Status: 400, Valid: false
- 🔴 **put-api-v1-user-config-users-userId-preferences-display**: 失败 (102ms) - Status: 400, Valid: false
- 🔴 **put-api-v1-user-config-users-userId-preferences-notifications**: 失败 (102ms) - Status: 400, Valid: false
- 🔴 **put-api-v1-user-config-users-userId-profile**: 失败 (103ms) - Status: 400, Valid: false
- 🔴 **put-api-v1-user-management-profile**: 失败 (103ms) - Status: 500, Valid: false
- 🔴 **put-api-v1-user-management-password**: 失败 (104ms) - Status: 500, Valid: false

### ⚠️ 系统管理

成功率: 80.0% (4/5)

- 🟢 **post-api-v1-admin-invitation-codes**: 成功 (36ms)
- 🟢 **get-api-v1-admin-invitation-codes**: 成功 (36ms)
- 🟢 **get-api-v1-admin-invitation-codes-stats**: 成功 (36ms)
- 🟢 **delete-api-v1-admin-invitation-codes-cleanup**: 成功 (35ms)
- 🔴 **get-api-v1-api-keys-test-admin**: 失败 (56ms) - Status: 500, Valid: false

### ❌ AI推理

成功率: 10.3% (3/29)

- 🔴 **get-api-ai-analytics-cost-statistics**: 失败 (35ms) - Status: 404, Valid: true
- 🔴 **get-api-ai-analytics-calls**: 失败 (35ms) - Status: 404, Valid: true
- 🔴 **get-api-ai-analytics-cache-statistics**: 失败 (35ms) - Status: 404, Valid: true
- 🔴 **get-api-ai-analytics-quality-statistics**: 失败 (35ms) - Status: 404, Valid: true
- 🔴 **get-api-ai-analytics-report**: 失败 (35ms) - Status: 404, Valid: true
- 🔴 **get-api-ai-analytics-recommendations**: 失败 (35ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-ai-decision-symbol**: 失败 (35ms) - Status: 500, Valid: true
- 🔴 **get-api-ai-analytics-models**: 失败 (35ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-ai-analyze**: 失败 (35ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-ai-reasoning**: 失败 (35ms) - Status: 400, Valid: true
- 🔴 **get-api-v1-ai-market-context**: 失败 (35ms) - Status: 400, Valid: true
- 🔴 **get-api-v1-ai-predictions-long-term**: 失败 (35ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-ai-predictions-short-term**: 失败 (36ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-ai-predictions-stats**: 失败 (35ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-ai-decision-symbol**: 失败 (54ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-ai-unified-learning-diagnostics**: 失败 (54ms) - Status: 503, Valid: true
- 🔴 **get-api-v1-ai-unified-learning-metrics**: 失败 (54ms) - Status: 503, Valid: true
- 🔴 **get-api-v1-ai-unified-learning-status**: 失败 (54ms) - Status: 503, Valid: true
- 🟢 **get-api-v1-dual-layer-reasoning-transparency-analysisId**: 成功 (56ms)
- 🟢 **get-api-v1-dual-layer-reasoning-learning-status**: 成功 (56ms)
- 🔴 **get-api-v1-trading-execution-dual-track-account-pairing**: 失败 (59ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-ai-unified-learning-predict-comprehensive**: 失败 (62ms) - Status: 500, Valid: true
- 🔴 **post-api-v1-ai-unified-learning-predict-meso**: 失败 (86ms) - Status: 500, Valid: true
- 🔴 **post-api-v1-ai-unified-learning-predict-macro**: 失败 (91ms) - Status: 500, Valid: true
- 🔴 **post-api-v1-ai-unified-learning-predict-micro**: 失败 (92ms) - Status: 500, Valid: true
- 🔴 **post-api-v1-ai-unified-learning-restart**: 失败 (93ms) - Status: 503, Valid: true
- 🔴 **post-api-v1-dual-layer-reasoning-analyze**: 失败 (94ms) - Status: 500, Valid: true
- 🔴 **post-api-v1-dual-layer-reasoning-compare**: 失败 (94ms) - Status: 500, Valid: true
- 🟢 **post-api-v1-dual-layer-reasoning-pure-ai**: 成功 (94ms)

### ❌ 系统健康检查

成功率: 35.7% (5/14)

- 🟢 **get-api-v1-ai-health**: 成功 (35ms)
- 🔴 **get-api-v2-trading-signals-health**: 失败 (45ms) - Status: 404, Valid: true
- 🔴 **get-api-health**: 失败 (50ms) - Status: 404, Valid: true
- 🔴 **get-api-health-quick**: 失败 (51ms) - Status: 404, Valid: true
- 🔴 **get-api-health-metrics**: 失败 (51ms) - Status: 404, Valid: true
- 🔴 **get-api-health-service-serviceName**: 失败 (51ms) - Status: 404, Valid: true
- 🔴 **get-api-health-ready**: 失败 (51ms) - Status: 404, Valid: true
- 🔴 **get-api-health-live**: 失败 (52ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-ai-unified-learning-health**: 失败 (54ms) - Status: 503, Valid: true
- 🟢 **get-api-v1-api-keys-health**: 成功 (56ms)
- 🟢 **get-api-v1-auth-api-keys-health**: 成功 (56ms)
- 🟢 **get-health**: 成功 (63ms)
- 🟢 **get-api-v1-signals-signals-health**: 成功 (4450ms)
- 🔴 **get-api-v1-trading-signals-health**: 失败 (10011ms) - timeout of 10000ms exceeded

### ❌ 认证授权

成功率: 11.1% (2/18)

- 🟢 **post-api-v1-auth-register**: 成功 (35ms)
- 🔴 **post-api-v1-auth-logout**: 失败 (35ms) - Status: 401, Valid: true
- 🟢 **post-api-v1-auth-login**: 成功 (35ms)
- 🔴 **post-api-v1-auth-refresh**: 失败 (35ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-auth-validate-invitation**: 失败 (36ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-auth-change-password**: 失败 (36ms) - Status: 401, Valid: true
- 🔴 **get-api-v1-auth-me**: 失败 (36ms) - Status: 401, Valid: true
- 🔴 **post-api-v1-auth-revoke-all-sessions**: 失败 (35ms) - Status: 401, Valid: true
- 🔴 **delete-api-v1-auth-api-keys-keyId**: 失败 (69ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-auth-api-keys**: 失败 (67ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-auth-api-keys-test-admin**: 失败 (67ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-auth-api-keys-test-market**: 失败 (67ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-auth-api-keys-test-high-frequency**: 失败 (68ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-auth-api-keys-test-signal**: 失败 (68ms) - Status: 500, Valid: true
- 🔴 **post-api-v1-auth-api-keys**: 失败 (94ms) - Status: 500, Valid: true
- 🔴 **post-api-v1-auth-api-keys-test-trading**: 失败 (93ms) - Status: 500, Valid: true
- 🔴 **post-api-v1-auth-api-keys-validate**: 失败 (94ms) - Status: 500, Valid: true
- 🔴 **put-api-v1-auth-api-keys-keyId**: 失败 (101ms) - Status: 500, Valid: true

### ❌ 其他接口

成功率: 3.2% (2/63)

- 🔴 **get-api-v1-config-key**: 失败 (35ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-configs**: 失败 (35ms) - Status: 404, Valid: true
- 🔴 **put-api-v1-config-key**: 失败 (36ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-config-key-reset**: 失败 (36ms) - Status: 404, Valid: true
- 🔴 **put-api-v1-configs**: 失败 (36ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-configs-history**: 失败 (36ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-config-key-history**: 失败 (37ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-configs-validate**: 失败 (38ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-configs-stats**: 失败 (38ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-config-key-validation-rule**: 失败 (37ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-configs-validation-rules**: 失败 (39ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-monitoring-dashboard-accountId**: 失败 (39ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-monitoring-metrics**: 失败 (39ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-monitoring-anomalies-accountId**: 失败 (39ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-monitoring-performance-comparison**: 失败 (39ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-monitoring-trigger-alert**: 失败 (40ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-monitoring-webhooks**: 失败 (40ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-monitoring-webhooks**: 失败 (40ms) - Status: 404, Valid: true
- 🔴 **delete-api-v1-monitoring-webhooks-id**: 失败 (39ms) - Status: 404, Valid: true
- 🔴 **put-api-v1-monitoring-webhooks-id**: 失败 (40ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-monitoring-webhooks-id-statistics**: 失败 (40ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-monitoring-webhooks-id-test**: 失败 (40ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-model-preferences-scenarios-scenario-recommendations**: 失败 (42ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-model-preferences-scenarios**: 失败 (43ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-model-preferences-select**: 失败 (43ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-model-preferences-stats**: 失败 (43ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-model-preferences**: 失败 (44ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-model-preferences**: 失败 (44ms) - Status: 404, Valid: true
- 🔴 **put-api-v1-model-preferences-scenario**: 失败 (43ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-model-preferences-scenario**: 失败 (43ms) - Status: 404, Valid: true
- 🔴 **delete-api-v1-model-preferences-scenario**: 失败 (44ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-strategy-sync-sync**: 失败 (47ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-strategy-sync-validate**: 失败 (47ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-strategy-sync-status-syncId**: 失败 (46ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-strategy-sync-cancel-syncId**: 失败 (46ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-strategy-sync-history-accountId**: 失败 (47ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-strategy-sync-config-recommendations-accountId**: 失败 (47ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-strategy-sync-auto-sync-check**: 失败 (47ms) - Status: 404, Valid: true
- 🔴 **get-api-performance-stats**: 失败 (52ms) - Status: 404, Valid: true
- 🔴 **get-api-performance-recommendations**: 失败 (52ms) - Status: 404, Valid: true
- 🔴 **get-api-monitoring-metrics**: 失败 (52ms) - Status: 404, Valid: true
- 🔴 **post-api-performance-cache-clear**: 失败 (52ms) - Status: 404, Valid: true
- 🔴 **delete-api-v1-api-keys-keyId**: 失败 (53ms) - Status: 500, Valid: false
- 🟢 **get-**: 成功 (53ms)
- 🟢 **get-api**: 成功 (53ms)
- 🔴 **get-api-docs-json**: 失败 (53ms) - Status: 200, Valid: false
- 🔴 **get-api-v1-api-keys**: 失败 (55ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-api-keys-test-high-frequency**: 失败 (56ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-api-keys-test-signal**: 失败 (56ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-api-keys-test-market**: 失败 (56ms) - Status: 500, Valid: false
- 🔴 **get-dev-routes**: 失败 (62ms) - Status: 200, Valid: false
- 🔴 **get-api-docs**: 失败 (97ms) - Status: 200, Valid: false
- 🔴 **get-documentation**: 失败 (92ms) - Status: 200, Valid: false
- 🔴 **get-docs**: 失败 (92ms) - Status: 200, Valid: false
- 🔴 **get-swagger**: 失败 (92ms) - Status: 200, Valid: false
- 🔴 **post-api-v1-api-keys**: 失败 (93ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-api-keys-validate**: 失败 (94ms) - Status: 500, Valid: false
- 🔴 **put-api-v1-api-keys-keyId**: 失败 (100ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-statistics-statistics-calls**: 失败 (10010ms) - timeout of 10000ms exceeded
- 🔴 **get-api-v1-statistics-statistics-costs**: 失败 (10011ms) - timeout of 10000ms exceeded
- 🔴 **get-api-v1-statistics-statistics-overview**: 失败 (10011ms) - timeout of 10000ms exceeded
- 🔴 **get-api-v1-statistics-statistics-performance**: 失败 (10011ms) - timeout of 10000ms exceeded
- 🔴 **get-api-v1-statistics-statistics-popular-models**: 失败 (10011ms) - timeout of 10000ms exceeded

### ❌ 市场数据

成功率: 37.5% (3/8)

- 🔴 **get-api-v1-market-data-prices-symbol**: 失败 (40ms) - Status: 404, Valid: true
- 🟢 **get-api-v1-market-data-overview**: 成功 (41ms)
- 🔴 **get-api-v1-market-data-klines**: 失败 (41ms) - Status: 400, Valid: true
- 🟢 **get-api-v1-market-data-symbols**: 成功 (47ms)
- 🔴 **get-api-v1-market-data-prices-symbol**: 失败 (56ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-market-data-price-symbol**: 失败 (56ms) - Status: 404, Valid: true
- 🟢 **get-api-v1-market-data-prices**: 成功 (99ms)
- 🔴 **post-api-v1-market-data-refresh**: 失败 (921ms) - Status: 500, Valid: true

### ❌ 交易执行

成功率: 2.4% (1/41)

- 🔴 **post-api-v2-trading-signals-generate**: 失败 (44ms) - Status: 404, Valid: true
- 🔴 **get-api-v2-trading-signals-symbol-latest**: 失败 (44ms) - Status: 404, Valid: true
- 🔴 **post-api-v2-trading-signals-batch**: 失败 (44ms) - Status: 404, Valid: true
- 🔴 **get-api-v2-trading-signals-symbol-validate**: 失败 (45ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-trading-execution-accounts**: 失败 (48ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-trading-execution-dual-track-disable**: 失败 (48ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-trading-execution-dual-track-enable**: 失败 (48ms) - Status: 400, Valid: false
- 🔴 **put-api-v1-trading-execution-dual-track-settings**: 失败 (48ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-trading-execution-dual-track-sync-strategy**: 失败 (48ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-trading-execution-toggle-auto-trading**: 失败 (48ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-trading-execution-execute**: 失败 (48ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-trading-execution-accounts-accountId-manage-positions**: 失败 (49ms) - Status: 400, Valid: false
- 🔴 **get-api-v1-trading-execution-status**: 失败 (49ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-trading-execution-credentials**: 失败 (49ms) - Status: 400, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts-default**: 失败 (59ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-dual-track-accounts**: 失败 (59ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-dual-track-metrics**: 失败 (60ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-dual-track-statistics**: 失败 (60ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-dual-track-status**: 失败 (60ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-dual-track-sync-status**: 失败 (61ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts**: 失败 (77ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts-accountId**: 失败 (78ms) - Status: 404, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts-accountId-statistics**: 失败 (78ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts-accountId-balance**: 失败 (79ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts-accountId-positions**: 失败 (78ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts-accountId-orders**: 失败 (79ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-trading-execution-accounts-accountId-sync-binance-history**: 失败 (80ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-risk-events**: 失败 (81ms) - Status: 500, Valid: false
- 🟢 **get-api-v1-trading-execution-credentials**: 成功 (82ms)
- 🔴 **get-api-v1-trading-execution-accounts-accountId**: 失败 (82ms) - Status: 404, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts-accountId-balance**: 失败 (82ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts-accountId-orders**: 失败 (83ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts-accountId-positions**: 失败 (82ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-accounts-accountId-statistics**: 失败 (83ms) - Status: 500, Valid: false
- 🔴 **get-api-v1-trading-execution-dual-track-performance-comparison**: 失败 (83ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-api-keys-test-trading**: 失败 (93ms) - Status: 500, Valid: false
- 🔴 **post-api-v1-trading-execution-accounts-accountId-manage-positions**: 失败 (95ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-trading-execution-accounts-accountId-sync-binance-history**: 失败 (96ms) - Status: 500, Valid: false
- 🔴 **put-api-v1-user-config-users-userId-preferences-trading**: 失败 (104ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-trading-signals-generate**: 失败 (10008ms) - timeout of 10000ms exceeded
- 🔴 **post-api-v1-trading-signals-generate-batch**: 失败 (10008ms) - timeout of 10000ms exceeded

### ❌ 风险管理

成功率: 0.0% (0/10)

- 🔴 **post-api-v1-risk-assessment-assessment**: 失败 (45ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-risk-assessment-assess**: 失败 (45ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-risk-assessment-stress-test**: 失败 (46ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-risk-assessment-monitoring**: 失败 (46ms) - Status: 400, Valid: true
- 🔴 **get-api-v1-risk-assessment-comprehensive-analysis-accountId**: 失败 (76ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-risk-assessment-assessment-symbol**: 失败 (78ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-risk-assessment-assessment-symbol**: 失败 (81ms) - Status: 500, Valid: true
- 🔴 **get-api-v1-risk-assessment-comprehensive-analysis-accountId**: 失败 (82ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-user-config-users-userId-profile-risk-level**: 失败 (86ms) - Status: 400, Valid: false
- 🔴 **post-api-v1-user-config-users-profiles-batch-risk-levels**: 失败 (99ms) - Status: 400, Valid: false

### ❌ 趋势分析

成功率: 12.5% (2/16)

- 🔴 **post-api-v1-trend-analysis**: 失败 (50ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-trend-prediction-symbol**: 失败 (50ms) - Status: 404, Valid: true
- 🔴 **get-api-v1-trend-quick-symbol-timeframe**: 失败 (51ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-trend-fundamental-analysis**: 失败 (51ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-trend-comprehensive-analysis**: 失败 (51ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-trend-patterns**: 失败 (50ms) - Status: 404, Valid: true
- 🔴 **post-api-v1-trend-indicators**: 失败 (51ms) - Status: 404, Valid: true
- 🟢 **get-api-v1-trend-analysis-quick-symbol-timeframe**: 成功 (61ms)
- 🟢 **get-api-v1-trend-analysis-prediction-symbol**: 成功 (61ms)
- 🔴 **post-api-v1-trend-analysis-analysis**: 失败 (96ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-trend-analysis-comprehensive-analysis**: 失败 (97ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-trend-analysis-fundamental-analysis**: 失败 (97ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-trend-analysis-indicators**: 失败 (97ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-trend-analysis-multi-timeframe**: 失败 (97ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-trend-analysis-patterns**: 失败 (97ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-trend-analysis-timeframes**: 失败 (97ms) - Status: 400, Valid: true

### ✅ 数据分析

成功率: 100.0% (7/7)

- 🟢 **get-api-v1-analytics-calls**: 成功 (82ms)
- 🟢 **get-api-v1-analytics-models**: 成功 (94ms)
- 🟢 **get-api-v1-analytics-cost-statistics**: 成功 (94ms)
- 🟢 **get-api-v1-analytics-quality-statistics**: 成功 (97ms)
- 🟢 **get-api-v1-analytics-cache-statistics**: 成功 (97ms)
- 🟢 **get-api-v1-analytics-recommendations**: 成功 (98ms)
- 🟢 **get-api-v1-analytics-report**: 成功 (98ms)

### ❌ 交易信号

成功率: 50.0% (2/4)

- 🔴 **post-api-v1-signals-signals-batch**: 失败 (95ms) - Status: 400, Valid: true
- 🔴 **post-api-v1-signals-signals-generate**: 失败 (95ms) - Status: 400, Valid: true
- 🟢 **get-api-v1-signals-signals-symbol-validate**: 成功 (455ms)
- 🟢 **get-api-v1-signals-signals-symbol-latest**: 成功 (457ms)

## ❌ 失败详情

### get-api-ai-analytics-cost-statistics

- **状态码**: 404
- **响应时间**: 35ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-ai-analytics-calls

- **状态码**: 404
- **响应时间**: 35ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-ai-analytics-cache-statistics

- **状态码**: 404
- **响应时间**: 35ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-ai-analytics-quality-statistics

- **状态码**: 404
- **响应时间**: 35ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-ai-analytics-report

- **状态码**: 404
- **响应时间**: 35ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-ai-analytics-recommendations

- **状态码**: 404
- **响应时间**: 35ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-ai-decision-symbol

- **状态码**: 500
- **响应时间**: 35ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-ai-analytics-models

- **状态码**: 404
- **响应时间**: 35ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-ai-analyze

- **状态码**: 400
- **响应时间**: 35ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-ai-reasoning

- **状态码**: 400
- **响应时间**: 35ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-ai-market-context

- **状态码**: 400
- **响应时间**: 35ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-ai-predictions-long-term

- **状态码**: 500
- **响应时间**: 35ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-ai-predictions-short-term

- **状态码**: 500
- **响应时间**: 36ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-ai-predictions-stats

- **状态码**: 500
- **响应时间**: 35ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-auth-logout

- **状态码**: 401
- **响应时间**: 35ms
- **错误信息**: Status: 401, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-auth-refresh

- **状态码**: 400
- **响应时间**: 35ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-auth-validate-invitation

- **状态码**: 400
- **响应时间**: 36ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-auth-change-password

- **状态码**: 401
- **响应时间**: 36ms
- **错误信息**: Status: 401, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-auth-me

- **状态码**: 401
- **响应时间**: 36ms
- **错误信息**: Status: 401, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-config-key

- **状态码**: 404
- **响应时间**: 35ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-auth-revoke-all-sessions

- **状态码**: 401
- **响应时间**: 35ms
- **错误信息**: Status: 401, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-configs

- **状态码**: 404
- **响应时间**: 35ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-config-key

- **状态码**: 404
- **响应时间**: 36ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-config-key-reset

- **状态码**: 404
- **响应时间**: 36ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-configs

- **状态码**: 404
- **响应时间**: 36ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-configs-history

- **状态码**: 404
- **响应时间**: 36ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-config-key-history

- **状态码**: 404
- **响应时间**: 37ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-configs-validate

- **状态码**: 404
- **响应时间**: 38ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-configs-stats

- **状态码**: 404
- **响应时间**: 38ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-config-key-validation-rule

- **状态码**: 404
- **响应时间**: 37ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-configs-validation-rules

- **状态码**: 404
- **响应时间**: 39ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-user-userId-llm-configs

- **状态码**: 404
- **响应时间**: 38ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-userId-llm-configs

- **状态码**: 404
- **响应时间**: 38ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-user-userId-llm-configs-provider

- **状态码**: 404
- **响应时间**: 38ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-userId-llm-configs-active

- **状态码**: 404
- **响应时间**: 38ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-monitoring-dashboard-accountId

- **状态码**: 404
- **响应时间**: 39ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### delete-api-v1-user-userId-llm-configs-provider

- **状态码**: 404
- **响应时间**: 39ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-monitoring-metrics

- **状态码**: 404
- **响应时间**: 39ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-monitoring-anomalies-accountId

- **状态码**: 404
- **响应时间**: 39ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-monitoring-performance-comparison

- **状态码**: 404
- **响应时间**: 39ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-monitoring-trigger-alert

- **状态码**: 404
- **响应时间**: 40ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-monitoring-webhooks

- **状态码**: 404
- **响应时间**: 40ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-monitoring-webhooks

- **状态码**: 404
- **响应时间**: 40ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### delete-api-v1-monitoring-webhooks-id

- **状态码**: 404
- **响应时间**: 39ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-monitoring-webhooks-id

- **状态码**: 404
- **响应时间**: 40ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-monitoring-webhooks-id-statistics

- **状态码**: 404
- **响应时间**: 40ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-monitoring-webhooks-id-test

- **状态码**: 404
- **响应时间**: 40ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-market-data-prices-symbol

- **状态码**: 404
- **响应时间**: 40ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-market-data-klines

- **状态码**: 400
- **响应时间**: 41ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-model-preferences-scenarios-scenario-recommendations

- **状态码**: 404
- **响应时间**: 42ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-model-preferences-scenarios

- **状态码**: 404
- **响应时间**: 43ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-model-preferences-select

- **状态码**: 404
- **响应时间**: 43ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-model-preferences-stats

- **状态码**: 404
- **响应时间**: 43ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-model-preferences

- **状态码**: 404
- **响应时间**: 44ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-model-preferences

- **状态码**: 404
- **响应时间**: 44ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-model-preferences-scenario

- **状态码**: 404
- **响应时间**: 43ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-model-preferences-scenario

- **状态码**: 404
- **响应时间**: 43ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v2-trading-signals-generate

- **状态码**: 404
- **响应时间**: 44ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### delete-api-v1-model-preferences-scenario

- **状态码**: 404
- **响应时间**: 44ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v2-trading-signals-symbol-latest

- **状态码**: 404
- **响应时间**: 44ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v2-trading-signals-batch

- **状态码**: 404
- **响应时间**: 44ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v2-trading-signals-health

- **状态码**: 404
- **响应时间**: 45ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v2-trading-signals-symbol-validate

- **状态码**: 404
- **响应时间**: 45ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-risk-assessment-assessment

- **状态码**: 400
- **响应时间**: 45ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-risk-assessment-assess

- **状态码**: 400
- **响应时间**: 45ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-risk-assessment-stress-test

- **状态码**: 400
- **响应时间**: 46ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-risk-assessment-monitoring

- **状态码**: 400
- **响应时间**: 46ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-strategy-sync-sync

- **状态码**: 404
- **响应时间**: 47ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-strategy-sync-validate

- **状态码**: 404
- **响应时间**: 47ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-strategy-sync-status-syncId

- **状态码**: 404
- **响应时间**: 46ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-strategy-sync-cancel-syncId

- **状态码**: 404
- **响应时间**: 46ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-strategy-sync-history-accountId

- **状态码**: 404
- **响应时间**: 47ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-strategy-sync-config-recommendations-accountId

- **状态码**: 404
- **响应时间**: 47ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-strategy-sync-auto-sync-check

- **状态码**: 404
- **响应时间**: 47ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-accounts

- **状态码**: 400
- **响应时间**: 48ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-dual-track-disable

- **状态码**: 500
- **响应时间**: 48ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-dual-track-enable

- **状态码**: 400
- **响应时间**: 48ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-trading-execution-dual-track-settings

- **状态码**: 500
- **响应时间**: 48ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-dual-track-sync-strategy

- **状态码**: 400
- **响应时间**: 48ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-toggle-auto-trading

- **状态码**: 400
- **响应时间**: 48ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-execute

- **状态码**: 400
- **响应时间**: 48ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-accounts-accountId-manage-positions

- **状态码**: 400
- **响应时间**: 49ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-status

- **状态码**: 500
- **响应时间**: 49ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-credentials

- **状态码**: 400
- **响应时间**: 49ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-analysis

- **状态码**: 404
- **响应时间**: 50ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trend-prediction-symbol

- **状态码**: 404
- **响应时间**: 50ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trend-quick-symbol-timeframe

- **状态码**: 404
- **响应时间**: 51ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-fundamental-analysis

- **状态码**: 404
- **响应时间**: 51ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-comprehensive-analysis

- **状态码**: 404
- **响应时间**: 51ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-patterns

- **状态码**: 404
- **响应时间**: 50ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-indicators

- **状态码**: 404
- **响应时间**: 51ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-health

- **状态码**: 404
- **响应时间**: 50ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-health-quick

- **状态码**: 404
- **响应时间**: 51ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-health-metrics

- **状态码**: 404
- **响应时间**: 51ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-health-service-serviceName

- **状态码**: 404
- **响应时间**: 51ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-health-ready

- **状态码**: 404
- **响应时间**: 51ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-health-live

- **状态码**: 404
- **响应时间**: 52ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-performance-stats

- **状态码**: 404
- **响应时间**: 52ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-performance-recommendations

- **状态码**: 404
- **响应时间**: 52ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-monitoring-metrics

- **状态码**: 404
- **响应时间**: 52ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-performance-cache-clear

- **状态码**: 404
- **响应时间**: 52ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### delete-api-v1-api-keys-keyId

- **状态码**: 500
- **响应时间**: 53ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-docs-json

- **状态码**: 200
- **响应时间**: 53ms
- **错误信息**: Status: 200, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-ai-decision-symbol

- **状态码**: 500
- **响应时间**: 54ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-ai-unified-learning-diagnostics

- **状态码**: 503
- **响应时间**: 54ms
- **错误信息**: Status: 503, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-ai-unified-learning-health

- **状态码**: 503
- **响应时间**: 54ms
- **错误信息**: Status: 503, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-ai-unified-learning-metrics

- **状态码**: 503
- **响应时间**: 54ms
- **错误信息**: Status: 503, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-ai-unified-learning-status

- **状态码**: 503
- **响应时间**: 54ms
- **错误信息**: Status: 503, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-api-keys

- **状态码**: 500
- **响应时间**: 55ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-api-keys-test-high-frequency

- **状态码**: 500
- **响应时间**: 56ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-api-keys-test-admin

- **状态码**: 500
- **响应时间**: 56ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-api-keys-test-signal

- **状态码**: 500
- **响应时间**: 56ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-api-keys-test-market

- **状态码**: 500
- **响应时间**: 56ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-market-data-prices-symbol

- **状态码**: 404
- **响应时间**: 56ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-market-data-price-symbol

- **状态码**: 404
- **响应时间**: 56ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-default

- **状态码**: 500
- **响应时间**: 59ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-dual-track-accounts

- **状态码**: 500
- **响应时间**: 59ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-dual-track-account-pairing

- **状态码**: 500
- **响应时间**: 59ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-dual-track-metrics

- **状态码**: 500
- **响应时间**: 60ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-dual-track-statistics

- **状态码**: 500
- **响应时间**: 60ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-dual-track-status

- **状态码**: 500
- **响应时间**: 60ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-dual-track-sync-status

- **状态码**: 500
- **响应时间**: 61ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-config-users-userId-profile-strategy-compatibility

- **状态码**: 400
- **响应时间**: 61ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-management

- **状态码**: 500
- **响应时间**: 61ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-management-userId

- **状态码**: 500
- **响应时间**: 61ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-management-profile

- **状态码**: 500
- **响应时间**: 62ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-dev-routes

- **状态码**: 200
- **响应时间**: 62ms
- **错误信息**: Status: 200, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### patch-api-v1-user-management-userId-status

- **状态码**: 500
- **响应时间**: 63ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-ai-unified-learning-predict-comprehensive

- **状态码**: 500
- **响应时间**: 62ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### delete-api-v1-auth-api-keys-keyId

- **状态码**: 500
- **响应时间**: 69ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-auth-api-keys

- **状态码**: 500
- **响应时间**: 67ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-auth-api-keys-test-admin

- **状态码**: 500
- **响应时间**: 67ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-auth-api-keys-test-market

- **状态码**: 500
- **响应时间**: 67ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-auth-api-keys-test-high-frequency

- **状态码**: 500
- **响应时间**: 68ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-auth-api-keys-test-signal

- **状态码**: 500
- **响应时间**: 68ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### patch-api-v1-user-management-userId-role

- **状态码**: 500
- **响应时间**: 65ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-risk-assessment-comprehensive-analysis-accountId

- **状态码**: 404
- **响应时间**: 76ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-risk-assessment-assessment-symbol

- **状态码**: 500
- **响应时间**: 78ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts

- **状态码**: 500
- **响应时间**: 77ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-accountId

- **状态码**: 404
- **响应时间**: 78ms
- **错误信息**: Status: 404, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-accountId-statistics

- **状态码**: 500
- **响应时间**: 78ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-accountId-balance

- **状态码**: 500
- **响应时间**: 79ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-accountId-positions

- **状态码**: 500
- **响应时间**: 78ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-accountId-orders

- **状态码**: 500
- **响应时间**: 79ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-accounts-accountId-sync-binance-history

- **状态码**: 500
- **响应时间**: 80ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-risk-events

- **状态码**: 500
- **响应时间**: 81ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### delete-api-v1-user-config-users-userId-profile

- **状态码**: 400
- **响应时间**: 82ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### delete-api-v1-user-config-users-userId-preferences

- **状态码**: 400
- **响应时间**: 82ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-risk-assessment-assessment-symbol

- **状态码**: 500
- **响应时间**: 81ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-risk-assessment-comprehensive-analysis-accountId

- **状态码**: 404
- **响应时间**: 82ms
- **错误信息**: Status: 404, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-accountId

- **状态码**: 404
- **响应时间**: 82ms
- **错误信息**: Status: 404, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-accountId-balance

- **状态码**: 500
- **响应时间**: 82ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-accountId-orders

- **状态码**: 500
- **响应时间**: 83ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-accountId-positions

- **状态码**: 500
- **响应时间**: 82ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-accounts-accountId-statistics

- **状态码**: 500
- **响应时间**: 83ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-trading-execution-dual-track-performance-comparison

- **状态码**: 500
- **响应时间**: 83ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-config-notifications-notificationType-users

- **状态码**: 400
- **响应时间**: 84ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-config-users-userId-preferences-localization

- **状态码**: 400
- **响应时间**: 84ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-config-users-userId-preferences

- **状态码**: 500
- **响应时间**: 86ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-config-users-userId-profile

- **状态码**: 500
- **响应时间**: 85ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-config-users-userId-preferences-validate

- **状态码**: 400
- **响应时间**: 86ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-config-users-userId-profile-strategy-adjustments

- **状态码**: 400
- **响应时间**: 86ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-config-users-userId-profile-risk-level

- **状态码**: 400
- **响应时间**: 86ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-config-users-userId-profile-validate

- **状态码**: 400
- **响应时间**: 86ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-ai-unified-learning-predict-meso

- **状态码**: 500
- **响应时间**: 86ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-v1-user-config-preferences-statistics

- **状态码**: 500
- **响应时间**: 92ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-ai-unified-learning-predict-macro

- **状态码**: 500
- **响应时间**: 91ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### get-api-docs

- **状态码**: 200
- **响应时间**: 97ms
- **错误信息**: Status: 200, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-documentation

- **状态码**: 200
- **响应时间**: 92ms
- **错误信息**: Status: 200, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-docs

- **状态码**: 200
- **响应时间**: 92ms
- **错误信息**: Status: 200, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### get-swagger

- **状态码**: 200
- **响应时间**: 92ms
- **错误信息**: Status: 200, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-ai-unified-learning-predict-micro

- **状态码**: 500
- **响应时间**: 92ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-ai-unified-learning-restart

- **状态码**: 503
- **响应时间**: 93ms
- **错误信息**: Status: 503, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-api-keys

- **状态码**: 500
- **响应时间**: 93ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-api-keys-test-trading

- **状态码**: 500
- **响应时间**: 93ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-api-keys-validate

- **状态码**: 500
- **响应时间**: 94ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-auth-api-keys

- **状态码**: 500
- **响应时间**: 94ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-auth-api-keys-test-trading

- **状态码**: 500
- **响应时间**: 93ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-auth-api-keys-validate

- **状态码**: 500
- **响应时间**: 94ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-dual-layer-reasoning-analyze

- **状态码**: 500
- **响应时间**: 94ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-dual-layer-reasoning-compare

- **状态码**: 500
- **响应时间**: 94ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-signals-signals-batch

- **状态码**: 400
- **响应时间**: 95ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-signals-signals-generate

- **状态码**: 400
- **响应时间**: 95ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-accounts-accountId-manage-positions

- **状态码**: 400
- **响应时间**: 95ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trading-execution-accounts-accountId-sync-binance-history

- **状态码**: 500
- **响应时间**: 96ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-analysis-analysis

- **状态码**: 400
- **响应时间**: 96ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-analysis-comprehensive-analysis

- **状态码**: 400
- **响应时间**: 97ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-analysis-fundamental-analysis

- **状态码**: 400
- **响应时间**: 97ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-analysis-indicators

- **状态码**: 400
- **响应时间**: 97ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-analysis-multi-timeframe

- **状态码**: 400
- **响应时间**: 97ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-analysis-patterns

- **状态码**: 400
- **响应时间**: 97ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-trend-analysis-timeframes

- **状态码**: 400
- **响应时间**: 97ms
- **错误信息**: Status: 400, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-user-config-users-userId-preferences

- **状态码**: 400
- **响应时间**: 98ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-user-config-users-userId-profile

- **状态码**: 400
- **响应时间**: 98ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-user-config-users-profiles-batch-risk-levels

- **状态码**: 400
- **响应时间**: 99ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-user-config-users-userId-preferences-reset

- **状态码**: 400
- **响应时间**: 99ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-user-config-users-preferences-batch

- **状态码**: 400
- **响应时间**: 99ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-user-management-logout

- **状态码**: 500
- **响应时间**: 100ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-user-management-login

- **状态码**: 500
- **响应时间**: 100ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-user-management-refresh-token

- **状态码**: 500
- **响应时间**: 100ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-api-keys-keyId

- **状态码**: 500
- **响应时间**: 100ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-user-management-register

- **状态码**: 500
- **响应时间**: 100ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-auth-api-keys-keyId

- **状态码**: 500
- **响应时间**: 101ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-user-config-users-userId-preferences

- **状态码**: 400
- **响应时间**: 101ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-user-config-users-userId-preferences-display

- **状态码**: 400
- **响应时间**: 102ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-user-config-users-userId-preferences-notifications

- **状态码**: 400
- **响应时间**: 102ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-user-config-users-userId-profile

- **状态码**: 400
- **响应时间**: 103ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-user-management-profile

- **状态码**: 500
- **响应时间**: 103ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-user-config-users-userId-preferences-trading

- **状态码**: 400
- **响应时间**: 104ms
- **错误信息**: Status: 400, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### put-api-v1-user-management-password

- **状态码**: 500
- **响应时间**: 104ms
- **错误信息**: Status: 500, Valid: false
- **检查时间**: 2025/7/19 10:08:45

### post-api-v1-market-data-refresh

- **状态码**: 500
- **响应时间**: 921ms
- **错误信息**: Status: 500, Valid: true
- **检查时间**: 2025/7/19 10:08:46

### get-api-v1-statistics-statistics-calls

- **状态码**: N/A
- **响应时间**: 10010ms
- **错误信息**: timeout of 10000ms exceeded
- **检查时间**: 2025/7/19 10:08:55

### get-api-v1-statistics-statistics-costs

- **状态码**: N/A
- **响应时间**: 10011ms
- **错误信息**: timeout of 10000ms exceeded
- **检查时间**: 2025/7/19 10:08:55

### get-api-v1-statistics-statistics-overview

- **状态码**: N/A
- **响应时间**: 10011ms
- **错误信息**: timeout of 10000ms exceeded
- **检查时间**: 2025/7/19 10:08:55

### get-api-v1-statistics-statistics-performance

- **状态码**: N/A
- **响应时间**: 10011ms
- **错误信息**: timeout of 10000ms exceeded
- **检查时间**: 2025/7/19 10:08:55

### get-api-v1-statistics-statistics-popular-models

- **状态码**: N/A
- **响应时间**: 10011ms
- **错误信息**: timeout of 10000ms exceeded
- **检查时间**: 2025/7/19 10:08:55

### get-api-v1-trading-signals-health

- **状态码**: N/A
- **响应时间**: 10011ms
- **错误信息**: timeout of 10000ms exceeded
- **检查时间**: 2025/7/19 10:08:55

### post-api-v1-trading-signals-generate

- **状态码**: N/A
- **响应时间**: 10008ms
- **错误信息**: timeout of 10000ms exceeded
- **检查时间**: 2025/7/19 10:08:55

### post-api-v1-trading-signals-generate-batch

- **状态码**: N/A
- **响应时间**: 10008ms
- **错误信息**: timeout of 10000ms exceeded
- **检查时间**: 2025/7/19 10:08:55

## 💡 建议

⚠️ 多个API端点存在问题，建议立即检查和修复。

---
*报告由API健康检查系统自动生成*
