openapi: 3.0.3
info:
  title: 用户配置API
  description: 用户LLM配置管理、模型偏好设置和使用统计API
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:3000/api/v1/user
    description: 开发环境
  - url: https://api.example.com/api/v1/user
    description: 生产环境

security:
  - UserIdHeader: []
  - UserIdQuery: []

paths:
  /llm-configs:
    get:
      summary: 获取用户配置列表
      tags: [LLM配置管理]
      responses:
        '200':
          description: 成功返回配置列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'
    
    post:
      summary: 创建或更新配置
      tags: [LLM配置管理]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConfigRequest'
      responses:
        '200':
          description: 配置创建/更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /llm-configs/providers:
    get:
      summary: 获取支持的提供者列表
      tags: [LLM配置管理]
      responses:
        '200':
          description: 成功返回提供者列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProvidersResponse'

  /llm-configs/{provider}:
    put:
      summary: 更新特定提供者配置
      tags: [LLM配置管理]
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            type: string
            enum: [openai, anthropic, gemini]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateConfigRequest'
      responses:
        '200':
          description: 配置更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigResponse'
    
    delete:
      summary: 删除配置
      tags: [LLM配置管理]
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 配置删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /llm-configs/{provider}/test:
    post:
      summary: 测试API密钥
      tags: [LLM配置管理]
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                apiKey:
                  type: string
                  description: 要测试的API密钥
              required: [apiKey]
      responses:
        '200':
          description: 测试结果
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestKeyResponse'

  /model-preferences/scenarios:
    get:
      summary: 获取可用场景列表
      tags: [模型偏好]
      responses:
        '200':
          description: 成功返回场景列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScenariosResponse'

  /model-preferences/select:
    post:
      summary: 选择最优模型
      tags: [模型偏好]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ModelSelectionRequest'
      responses:
        '200':
          description: 模型选择结果
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ModelSelectionResponse'

  /statistics/overview:
    get:
      summary: 获取使用统计概览
      tags: [统计监控]
      responses:
        '200':
          description: 统计概览数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OverviewResponse'

components:
  securitySchemes:
    UserIdHeader:
      type: apiKey
      in: header
      name: x-user-id
    UserIdQuery:
      type: apiKey
      in: query
      name: userId

  schemas:
    BaseResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required: [success]

    SuccessResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            success:
              type: boolean
              example: true

    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            success:
              type: boolean
              example: false
            error:
              type: string
              description: 错误信息

    LLMConfig:
      type: object
      properties:
        id:
          type: string
        userId:
          type: string
        providerName:
          type: string
          enum: [openai, anthropic, gemini]
        isActive:
          type: boolean
        configMetadata:
          type: object
        validationStatus:
          type: string
          enum: [valid, invalid, pending]
        totalCalls:
          type: integer
        totalCost:
          type: number
        lastUsed:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CreateConfigRequest:
      type: object
      properties:
        providerName:
          type: string
          enum: [openai, anthropic, gemini]
        apiKey:
          type: string
        configMetadata:
          type: object
      required: [providerName, apiKey]

    UpdateConfigRequest:
      type: object
      properties:
        apiKey:
          type: string
        isActive:
          type: boolean
        configMetadata:
          type: object

    ConfigResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/LLMConfig'

    ConfigListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/LLMConfig'

    Provider:
      type: object
      properties:
        name:
          type: string
        displayName:
          type: string
        description:
          type: string
        models:
          type: array
          items:
            type: string
        apiKeyFormat:
          type: string
        testEndpoint:
          type: string
        documentation:
          type: string

    ProvidersResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Provider'

    TestKeyResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                isValid:
                  type: boolean
                errorMessage:
                  type: string
                responseTime:
                  type: number

    Scenario:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        requiredCapabilities:
          type: array
          items:
            type: string
        priority:
          type: string
          enum: [low, medium, high, critical]
        typicalLatency:
          type: number
        typicalCost:
          type: number

    ScenariosResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Scenario'

    ModelSelectionRequest:
      type: object
      properties:
        scenario:
          type: string
        requirements:
          type: object
          properties:
            maxLatency:
              type: number
            maxCost:
              type: number
            minAccuracy:
              type: number
        context:
          type: object
        urgency:
          type: string
          enum: [low, medium, high]
      required: [scenario]

    ModelSelectionResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                selectedModel:
                  type: string
                selectedProvider:
                  type: string
                confidence:
                  type: number
                reasoning:
                  type: string
                fallbackModels:
                  type: array
                  items:
                    type: object
                    properties:
                      model:
                        type: string
                      provider:
                        type: string
                      priority:
                        type: integer
                estimatedLatency:
                  type: number
                estimatedCost:
                  type: number

    OverviewResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    userId:
                      type: string
                    lastActivity:
                      type: string
                      format: date-time
                configurations:
                  type: object
                  properties:
                    total:
                      type: integer
                    active:
                      type: integer
                    valid:
                      type: integer
                preferences:
                  type: object
                  properties:
                    total:
                      type: integer
                    active:
                      type: integer
                    scenarios:
                      type: integer
                usage:
                  type: object
                  properties:
                    totalCalls:
                      type: integer
                    totalCost:
                      type: number
                    averageCostPerCall:
                      type: number
                performance:
                  type: object
                  properties:
                    cacheHitRate:
                      type: number
                    averageResponseTime:
                      type: number

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    
    InternalError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

tags:
  - name: LLM配置管理
    description: 用户LLM配置的增删改查操作
  - name: 模型偏好
    description: 模型偏好设置和智能选择
  - name: 统计监控
    description: 使用统计和性能监控
