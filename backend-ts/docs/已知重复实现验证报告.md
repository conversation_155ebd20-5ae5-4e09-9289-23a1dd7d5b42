# 已知重复实现验证报告

## 脚本说明

本报告由 **已知重复实现验证器** (known-duplications-validator.ts) 自动生成。该脚本基于 `/docs/目前发现的重复实现.md` 文件中记录的20个重复实现问题，检查这些问题是否再次出现，确保已解决的重复实现不会重新引入。

**验证范围**:
- 技术指标计算重复实现
- HTTP客户端重复实现  
- WebSocket连接管理重复实现
- 缓存系统重复实现
- 错误处理重复实现
- 应用服务编排模式重复
- DTO映射逻辑重复
- 健康检查逻辑重复
- 其他已知重复实现问题

---

## 验证结果概览

**生成时间**: 2025/7/18 21:33:49  
**验证器版本**: 1.0.0

| 指标 | 数值 |
|------|------|
| 总检查项 | 8 |
| 安全检查 | 1 |
| 警告检查 | 0 |
| 违规检查 | 7 |

**总结**: ❌ 发现 7 个严重违规，0 个警告

## 详细验证结果

### 1. [VIOLATION] 技术指标计算重复实现

**风险级别**: CRITICAL
**违规数量**: 4

**违规详情**:
1. **文件**: `src/contexts/trend-analysis/infrastructure/services/key-level-analysis-engine.ts`
   **行号**: 649
   **描述**: 发现可能的重复实现: private calculateBollingerBands
   **代码片段**:
   ```typescript
      * 计算布林带 - 使用统一技术指标计算器
   */
  private calculateBollingerBands(klines: KlineDataPoint[], timeframe: Timeframe): DynamicKeyLevel | null {
    return KeyLevelHelpers.calculateBollingerBands(klines, timeframe, this.technicalCalculator);
  }
   ```

2. **文件**: `src/contexts/trading-signals/infrastructure/strategies/mean-reversion-strategy.ts`
   **行号**: 731
   **描述**: 发现可能的重复实现: private async calculateRSI
   **代码片段**:
   ```typescript
      * 计算相对强弱指数 (使用统一技术指标计算器)
   */
  private async calculateRSI(symbol: string, period: number = 14): Promise<number> {
    try {
      const historicalPrices = await this.getHistoricalPriceData(symbol, period + 10);
   ```

3. **文件**: `src/contexts/trend-analysis/infrastructure/modules/indicators/indicators-module.ts`
   **描述**: 发现可能的重复实现文件

4. **文件**: `src/contexts/trend-analysis/infrastructure/modules/indicators/basic-indicators-module.ts`
   **描述**: 发现可能的重复实现文件

**建议**: ⚠️ 发现 4 个潜在违规，需要检查是否重新引入了重复实现
建议使用统一解决方案: UnifiedTechnicalIndicatorCalculator
- 所有技术指标计算必须通过UnifiedTechnicalIndicatorCalculator
- 禁止在业务模块中直接实现指标计算逻辑
- 新增指标必须添加到统一计算器中

### 2. [VIOLATION] HTTP客户端和API调用重复实现

**风险级别**: HIGH
**违规数量**: 5

**违规详情**:
1. **文件**: `scripts/verify-http-refactor.ts`
   **行号**: 61
   **描述**: 发现可能的重复实现: import axios') || content.includes('from \'axios\'') || content.includes('from "axios"
   **代码片段**:
   ```typescript
       
    // 检查1: 不应该有axios导入
    if (content.includes('import axios') || content.includes('from \'axios\'') || content.includes('from "axios"')) {
      issues.push('仍然导入了axios');
    }
   ```

2. **文件**: `scripts/monitoring/diagnose-binance-connection.ts`
   **行号**: 5
   **描述**: 发现可能的重复实现: import axios from 'axios'
   **代码片段**:
   ```typescript
    */

import axios from 'axios';
import Binance from 'binance-api-node';
import * as dotenv from 'dotenv';
   ```

3. **文件**: `scripts/maintenance/auto-data-updater.ts`
   **行号**: 2
   **描述**: 发现可能的重复实现: import axios from 'axios'
   **代码片段**:
   ```typescript
   import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import cron from 'node-cron';

   ```

4. **文件**: `scripts/migration/restore-btc-historical-data.ts`
   **行号**: 2
   **描述**: 发现可能的重复实现: import axios from 'axios'
   **代码片段**:
   ```typescript
   import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import { config } from 'dotenv';

   ```

5. **文件**: `src/shared/application/interfaces/external-service.ts`
   **行号**: 5
   **描述**: 发现可能的重复实现: import axios, { AxiosError } from 'axios'
   **代码片段**:
   ```typescript
    */

import axios, { AxiosError } from 'axios';
import { BaseHttpClient } from '../../../shared/infrastructure/http/base-http-client';
import { HttpClientFactory, HttpClientType } from '../../../shared/infrastructure/http/http-client-factory';
   ```

**建议**: ⚠️ 发现 5 个潜在违规，需要检查是否重新引入了重复实现
建议使用统一解决方案: BaseHttpClient和工厂模式
- 所有HTTP请求必须通过BaseHttpClient或其工厂方法
- 禁止直接使用axios.create()创建实例
- 外部API适配器必须继承BaseHttpClient

### 3. [SAFE] WebSocket连接管理重复实现

**风险级别**: HIGH
**违规数量**: 0

**建议**: ✅ 该重复实现问题已得到有效控制，继续使用 BaseWebSocketAdapter

### 4. [VIOLATION] 缓存系统重复实现

**风险级别**: HIGH
**违规数量**: 4

**违规详情**:
1. **文件**: `src/shared/infrastructure/cache/cache-performance-monitor.ts`
   **描述**: 发现可能的重复实现文件

2. **文件**: `src/shared/infrastructure/cache/cache-consistency-manager.ts`
   **描述**: 发现可能的重复实现文件

3. **文件**: `src/shared/application/interfaces/application-service.ts`
   **行号**: 168
   **描述**: 发现可能的重复实现: interface ICacheService {
   **代码片段**:
   ```typescript
    * 缓存接口
 */
export interface ICacheService {
  /**
   * 获取缓存值
   ```

4. **文件**: `src/shared/infrastructure/performance/cache-manager.ts`
   **行号**: 33
   **描述**: 发现可能的重复实现: class EnhancedCacheManager 
   **代码片段**:
   ```typescript
    */
@injectable()
export class EnhancedCacheManager implements ICacheService {
  private readonly cache: Map<string, CacheItem> = new Map();
  private readonly accessOrder: string[] = []; // LRU 访问顺序
   ```

**建议**: ⚠️ 发现 4 个潜在违规，需要检查是否重新引入了重复实现
建议使用统一解决方案: MultiTierCacheService
- 所有缓存操作必须通过MultiTierCacheService
- 禁止创建新的缓存实现
- 缓存配置必须统一管理

### 5. [VIOLATION] 错误处理重复实现

**风险级别**: MEDIUM
**违规数量**: 4

**违规详情**:
1. **文件**: `src/api/middleware/express-middleware.ts`
   **行号**: 137
   **描述**: 发现可能的重复实现: public handleError
   **代码片段**:
   ```typescript
   
  // 错误处理中间件方法
  public handleError() {
    this.initializeMiddlewares();
    return this.errorHandlingMiddleware.handleError();
   ```

2. **文件**: `src/shared/infrastructure/logging/unified-logger.ts`
   **行号**: 299
   **描述**: 发现可能的重复实现: private formatError
   **代码片段**:
   ```typescript
      * 格式化错误信息
   */
  private formatError(error: Error | unknown): any {
    if (!error) return undefined;

   ```

3. **文件**: `src/contexts/trading-execution/infrastructure/services/binance-websocket-service.ts`
   **行号**: 396
   **描述**: 发现可能的重复实现: private handleError
   **代码片段**:
   ```typescript
      * 处理错误
   */
  private handleError(error: Error): void {
    this.logger.error('币安WebSocket错误', { error });
    this.emit('error', error);
   ```

4. **文件**: `src/contexts/market-data/infrastructure/external/millisecond-data-stream-processor.ts`
   **行号**: 535
   **描述**: 发现可能的重复实现: private handleError
   **代码片段**:
   ```typescript
      * 处理错误
   */
  private handleError(error: Error, message?: HighFrequencyMessage): void {
    this.logger.error('处理消息时发生错误', {
      error: error.message,
   ```

**建议**: ⚠️ 发现 4 个潜在违规，需要检查是否重新引入了重复实现
建议使用统一解决方案: UnifiedErrorHandler
- 所有错误处理必须通过UnifiedErrorHandler
- 禁止在业务模块中实现错误处理逻辑
- 错误分类和格式化必须统一

### 6. [VIOLATION] 应用服务中的"请求编排"模式重复

**风险级别**: HIGH
**违规数量**: 14

**违规详情**:
1. **文件**: `src/shared/application/application-service-interfaces.ts`
   **描述**: 发现可能的重复实现文件

2. **文件**: `src/shared/application/interfaces/application-service.ts`
   **描述**: 发现可能的重复实现文件

3. **文件**: `src/contexts/trend-analysis/application/trend-analysis-application.service.ts`
   **描述**: 发现可能的重复实现文件

4. **文件**: `src/contexts/user-management/application/services/UserManagementApplicationService.ts`
   **描述**: 发现可能的重复实现文件

5. **文件**: `src/contexts/user-config/application/services/user-profile-application-service.ts`
   **描述**: 发现可能的重复实现文件

6. **文件**: `src/contexts/user-config/application/services/user-preferences-application-service.ts`
   **描述**: 发现可能的重复实现文件

7. **文件**: `src/contexts/user-config/application/services/UserConfigApplicationService.ts`
   **描述**: 发现可能的重复实现文件

8. **文件**: `src/contexts/trend-analysis/infrastructure/di/application-services-bindings.ts`
   **描述**: 发现可能的重复实现文件

9. **文件**: `src/contexts/trading-signals/application/services/signal-generation-application-service.ts`
   **描述**: 发现可能的重复实现文件

10. **文件**: `src/contexts/risk-management/application/services/risk-assessment-application-service.ts`
   **描述**: 发现可能的重复实现文件

11. **文件**: `src/contexts/risk-management/domain/interfaces/risk-assessment-application-service.interface.ts`
   **描述**: 发现可能的重复实现文件

12. **文件**: `src/contexts/trading-execution/application/services/trading-execution-application-service.ts`
   **描述**: 发现可能的重复实现文件

13. **文件**: `src/contexts/market-data/application/services/market-data-application-service.ts`
   **描述**: 发现可能的重复实现文件

14. **文件**: `src/contexts/ai-reasoning/application/services/ai-reasoning-application-service.ts`
   **描述**: 发现可能的重复实现文件

**建议**: ⚠️ 发现 14 个潜在违规，需要检查是否重新引入了重复实现
建议使用统一解决方案: BaseApplicationService基类
- 所有应用服务必须继承BaseApplicationService
- 禁止重复实现请求编排逻辑
- 统一使用模板方法模式

### 7. [VIOLATION] "实体到DTO"的手动映射逻辑重复

**风险级别**: MEDIUM
**违规数量**: 57

**违规详情**:
1. **文件**: `src/shared/infrastructure/data-processing/executors/mapping-stage-executor.ts`
   **行号**: 127
   **描述**: 发现可能的重复实现: private async mapToBusinessObjects
   **代码片段**:
   ```typescript
      * 映射到业务对象
   */
  private async mapToBusinessObjects(data: any, context: ProcessingContext): Promise<any> {
    const businessObjects = [];

   ```

2. **文件**: `src/contexts/user-config/application/services/user-profile-application-service.ts`
   **行号**: 264
   **描述**: 发现可能的重复实现: private mapToResponse
   **代码片段**:
   ```typescript
     }

  private mapToResponse(profile: UserProfile): UserProfileResponse {
    return {
      userId: profile.userId,
   ```

3. **文件**: `src/contexts/user-config/application/services/user-preferences-application-service.ts`
   **行号**: 270
   **描述**: 发现可能的重复实现: private mapToResponse
   **代码片段**:
   ```typescript
     }

  private mapToResponse(preferences: UserPreferences): UserPreferencesResponse {
    return {
      userId: preferences.userId,
   ```

4. **文件**: `src/contexts/trend-analysis/infrastructure/services/trend-prediction.ts`
   **行号**: 1168
   **描述**: 发现可能的重复实现: private mapToTurningPointType
   **代码片段**:
   ```typescript
     }

  private mapToTurningPointType(typeString: string | null): TurningPointType {
    if (!typeString) {
      return TurningPointType.TREND_REVERSAL;
   ```

5. **文件**: `src/contexts/risk-management/application/services/risk-assessment-application-service.ts`
   **行号**: 1724
   **描述**: 发现可能的重复实现: private mapToRealTimeRiskLevel
   **代码片段**:
   ```typescript
      * 映射风险等级到实时监控格式
   */
  private mapToRealTimeRiskLevel(level: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | 'EMERGENCY' {
    switch (level.toUpperCase()) {
      case 'LOW': return 'LOW';
   ```

6. **文件**: `src/contexts/trading-execution/domain/services/webhook-alert-service.ts`
   **行号**: 495
   **描述**: 发现可能的重复实现: private mapToWebhookConfig
   **代码片段**:
   ```typescript
     }

  private mapToWebhookConfig(webhook: any): WebhookConfig {
    return {
      id: webhook.id,
   ```

7. **文件**: `src/contexts/trading-execution/domain/services/strategy-sync-service.ts`
   **行号**: 613
   **描述**: 发现可能的重复实现: private mapToStrategySyncRecord
   **代码片段**:
   ```typescript
     }

  private mapToStrategySyncRecord(record: any): StrategySyncRecord {
    return {
      id: record.id,
   ```

8. **文件**: `src/shared/infrastructure/health/unified-health-service.ts`
   **行号**: 489
   **描述**: 发现可能的重复实现: private mapCheckStatusToHealthStatus(
   **代码片段**:
   ```typescript
      * 映射检查状态到健康状态
   */
  private mapCheckStatusToHealthStatus(status: 'pass' | 'fail' | 'warn'): HealthStatus {
    switch (status) {
      case 'pass':
   ```

9. **文件**: `src/shared/infrastructure/monitoring/services/unified-health-check-service.ts`
   **行号**: 305
   **描述**: 发现可能的重复实现: private mapComponentStatusToHealthStatus(
   **代码片段**:
   ```typescript
      * 映射组件状态到健康状态
   */
  private mapComponentStatusToHealthStatus(status: ComponentHealthStatus): HealthStatus {
    switch (status) {
      case ComponentHealthStatus.PASS:
   ```

10. **文件**: `src/shared/infrastructure/di/base/di-configuration-optimizer.ts`
   **行号**: 408
   **描述**: 发现可能的重复实现: private mapScopeToInversify(
   **代码片段**:
   ```typescript
      * 映射生命周期到Inversify格式
   */
  private mapScopeToInversify(scope: LifecycleScope): string {
    switch (scope) {
      case LifecycleScope.SINGLETON:
   ```

11. **文件**: `src/shared/infrastructure/data-processing/executors/sync-stage-executor.ts`
   **行号**: 400
   **描述**: 发现可能的重复实现: private mapBusinessSystemToSyncType(
   **代码片段**:
   ```typescript
      * 映射业务系统到同步类型
   */
  private mapBusinessSystemToSyncType(businessSystem: string): any {
    const mapping: Record<string, any> = {
      'market-data': 'MARKET_DATA',
   ```

12. **文件**: `src/shared/infrastructure/data-processing/executors/sync-stage-executor.ts`
   **行号**: 412
   **描述**: 发现可能的重复实现: private mapTargetSystemToSyncType(
   **代码片段**:
   ```typescript
      * 映射目标系统到同步类型
   */
  private mapTargetSystemToSyncType(targetSystem: string): any {
    const mapping: Record<string, any> = {
      'database': 'DATABASE',
   ```

13. **文件**: `src/shared/infrastructure/data-processing/executors/sync-stage-executor.ts`
   **行号**: 428
   **描述**: 发现可能的重复实现: private mapDataTypeToEventType(
   **代码片段**:
   ```typescript
      * 映射数据类型到事件类型
   */
  private mapDataTypeToEventType(dataType: string): any {
    const mapping: Record<string, any> = {
      'price': 'PRICE_UPDATE',
   ```

14. **文件**: `src/shared/infrastructure/data-processing/executors/mapping-stage-executor.ts`
   **行号**: 127
   **描述**: 发现可能的重复实现: private async mapToBusinessObjects(
   **代码片段**:
   ```typescript
      * 映射到业务对象
   */
  private async mapToBusinessObjects(data: any, context: ProcessingContext): Promise<any> {
    const businessObjects = [];

   ```

15. **文件**: `src/contexts/user-config/application/services/user-profile-application-service.ts`
   **行号**: 264
   **描述**: 发现可能的重复实现: private mapToResponse(
   **代码片段**:
   ```typescript
     }

  private mapToResponse(profile: UserProfile): UserProfileResponse {
    return {
      userId: profile.userId,
   ```

16. **文件**: `src/contexts/user-config/application/services/user-preferences-application-service.ts`
   **行号**: 270
   **描述**: 发现可能的重复实现: private mapToResponse(
   **代码片段**:
   ```typescript
     }

  private mapToResponse(preferences: UserPreferences): UserPreferencesResponse {
    return {
      userId: preferences.userId,
   ```

17. **文件**: `src/contexts/trend-analysis/infrastructure/services/trend-prediction.ts`
   **行号**: 1168
   **描述**: 发现可能的重复实现: private mapToTurningPointType(
   **代码片段**:
   ```typescript
     }

  private mapToTurningPointType(typeString: string | null): TurningPointType {
    if (!typeString) {
      return TurningPointType.TREND_REVERSAL;
   ```

18. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1030
   **描述**: 发现可能的重复实现: private mapPatternTypeToDirection(
   **代码片段**:
   ```typescript
      * 映射形态类型到趋势方向
   */
  private mapPatternTypeToDirection(patternType: string): 'bullish' | 'bearish' | 'neutral' {
    if (patternType.includes('bullish') || patternType.includes('ascending') || patternType.includes('rising')) {
      return 'bullish';
   ```

19. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1042
   **描述**: 发现可能的重复实现: private mapPatternTypeToStrength(
   **代码片段**:
   ```typescript
      * 映射形态类型到强度
   */
  private mapPatternTypeToStrength(patternType: string): number {
    if (patternType.includes('elliottWave')) return 8.0;
    if (patternType.includes('harmonic')) return 7.5;
   ```

20. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1053
   **描述**: 发现可能的重复实现: private mapPatternTypeToMomentum(
   **代码片段**:
   ```typescript
      * 映射形态类型到动量
   */
  private mapPatternTypeToMomentum(patternType: string): number {
    if (patternType.includes('breakout')) return 0.8;
    if (patternType.includes('reversal')) return -0.6;
   ```

21. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1063
   **描述**: 发现可能的重复实现: private mapMarketConditionToDirection(
   **代码片段**:
   ```typescript
      * 映射市场条件到趋势方向
   */
  private mapMarketConditionToDirection(marketCondition?: 'BULL' | 'BEAR' | 'SIDEWAYS'): 'bullish' | 'bearish' | 'neutral' {
    switch (marketCondition) {
      case 'BULL': return 'bullish';
   ```

22. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1074
   **描述**: 发现可能的重复实现: private mapMarketConditionToStrength(
   **代码片段**:
   ```typescript
      * 映射市场条件到强度
   */
  private mapMarketConditionToStrength(marketCondition?: 'BULL' | 'BEAR' | 'SIDEWAYS'): number {
    switch (marketCondition) {
      case 'BULL': return 7.0;
   ```

23. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1086
   **描述**: 发现可能的重复实现: private mapMarketConditionToMomentum(
   **代码片段**:
   ```typescript
      * 映射市场条件到动量
   */
  private mapMarketConditionToMomentum(marketCondition?: 'BULL' | 'BEAR' | 'SIDEWAYS'): number {
    switch (marketCondition) {
      case 'BULL': return 0.6;
   ```

24. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1097
   **描述**: 发现可能的重复实现: private mapTimeframeToDirection(
   **代码片段**:
   ```typescript
      * 映射时间框架到趋势方向
   */
  private mapTimeframeToDirection(timeframe: string): 'bullish' | 'bearish' | 'neutral' {
    // 短时间框架倾向于更多噪音，长时间框架更稳定
    if (['1m', '5m', '15m'].includes(timeframe)) return 'neutral';
   ```

25. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1107
   **描述**: 发现可能的重复实现: private mapTimeframeToStrength(
   **代码片段**:
   ```typescript
      * 映射时间框架到强度
   */
  private mapTimeframeToStrength(timeframe: string): number {
    if (['1m', '5m'].includes(timeframe)) return 3.0;
    if (['15m', '30m'].includes(timeframe)) return 4.0;
   ```

26. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1118
   **描述**: 发现可能的重复实现: private mapTimeframeToMomentum(
   **代码片段**:
   ```typescript
      * 映射时间框架到动量
   */
  private mapTimeframeToMomentum(timeframe: string): number {
    if (['1m', '5m'].includes(timeframe)) return 0.2;
    if (['15m', '30m'].includes(timeframe)) return 0.3;
   ```

27. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1128
   **描述**: 发现可能的重复实现: private mapMarketConditionToType(
   **代码片段**:
   ```typescript
      * 映射市场条件到类型
   */
  private mapMarketConditionToType(marketCondition?: 'BULL' | 'BEAR' | 'SIDEWAYS'): 'HIGH_VOLATILITY' | 'LOW_VOLATILITY' | 'TRENDING' | 'RANGING' {
    switch (marketCondition) {
      case 'BULL':
   ```

28. **文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
   **行号**: 1158
   **描述**: 发现可能的重复实现: private mapMarketConditionToTrend(
   **代码片段**:
   ```typescript
      * 映射市场条件到趋势
   */
  private mapMarketConditionToTrend(marketCondition?: 'BULL' | 'BEAR' | 'SIDEWAYS'): string {
    switch (marketCondition) {
      case 'BULL': return 'uptrend';
   ```

29. **文件**: `src/contexts/trend-analysis/infrastructure/services/collaboration-service.ts`
   **行号**: 560
   **描述**: 发现可能的重复实现: private mapUrgencyToPriority(
   **代码片段**:
   ```typescript
     }

  private mapUrgencyToPriority(urgency: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    switch (urgency) {
      case 'HIGH': return 'HIGH';
   ```

30. **文件**: `src/contexts/trading-execution/domain/services/webhook-alert-service.ts`
   **行号**: 495
   **描述**: 发现可能的重复实现: private mapToWebhookConfig(
   **代码片段**:
   ```typescript
     }

  private mapToWebhookConfig(webhook: any): WebhookConfig {
    return {
      id: webhook.id,
   ```

31. **文件**: `src/contexts/trading-execution/domain/services/strategy-sync-service.ts`
   **行号**: 613
   **描述**: 发现可能的重复实现: private mapToStrategySyncRecord(
   **代码片段**:
   ```typescript
     }

  private mapToStrategySyncRecord(record: any): StrategySyncRecord {
    return {
      id: record.id,
   ```

32. **文件**: `src/contexts/trading-execution/domain/services/execution-engine-router.ts`
   **行号**: 249
   **描述**: 发现可能的重复实现: private mapAccountTypeToEngineType(
   **代码片段**:
   ```typescript
      * 将账户类型映射到执行引擎类型
   */
  private mapAccountTypeToEngineType(accountType: AccountType | string): ExecutionEngineType {
    switch (accountType) {
      case AccountType.SIMULATION:
   ```

33. **文件**: `src/contexts/trading-signals/presentation/controllers/trading-signals.controller.ts`
   **行号**: 434
   **描述**: 发现可能的重复实现: private mapSignalToResponse(
   **代码片段**:
   ```typescript
      * 将信号实体映射为响应DTO
   */
  private mapSignalToResponse(signal: TradingSignalEntity): any {
    return {
      id: signal.id,
   ```

34. **文件**: `src/contexts/market-data/infrastructure/external/senticrypt-adapter.ts`
   **行号**: 596
   **描述**: 发现可能的重复实现: private mapSentimentLevelFromMean(
   **代码片段**:
   ```typescript
     }

  private mapSentimentLevelFromMean(mean: number): 'Very Negative' | 'Negative' | 'Neutral' | 'Positive' | 'Very Positive' {
    if (mean >= 0.5) return 'Very Positive';
    if (mean >= 0.2) return 'Positive';
   ```

35. **文件**: `src/contexts/market-data/infrastructure/external/okx-websocket-adapter.ts`
   **行号**: 248
   **描述**: 发现可能的重复实现: private mapTimeframeToOKX(
   **代码片段**:
   ```typescript
     }

  private mapTimeframeToOKX(timeframe: Timeframe): string {
    const mapping: Record<string, string> = {
      '1m': '1m',
   ```

36. **文件**: `src/contexts/market-data/infrastructure/external/binance-websocket-adapter.ts`
   **行号**: 431
   **描述**: 发现可能的重复实现: private mapTimeframeToBinanceInterval(
   **代码片段**:
   ```typescript
      * 映射时间框架到Binance间隔
   */
  private mapTimeframeToBinanceInterval(timeframe: string): string {
    const mapping: Record<string, string> = {
      '1m': '1m',
   ```

37. **文件**: `src/contexts/ai-reasoning/presentation/http/dual-layer-reasoning-controller.ts`
   **行号**: 311
   **描述**: 发现可能的重复实现: private mapDirectionToAction(
   **代码片段**:
   ```typescript
     }

  private mapDirectionToAction(direction: string): string {
    switch (direction) {
      case 'BUY': return 'BUY';
   ```

38. **文件**: `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
   **行号**: 947
   **描述**: 发现可能的重复实现: private mapPerformanceToDirection(
   **代码片段**:
   ```typescript
      * 将性能映射到趋势方向
   */
  private mapPerformanceToDirection(performance: number): string {
    if (performance > 0.6) return 'bullish';
    if (performance < 0.4) return 'bearish';
   ```

39. **文件**: `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
   **行号**: 956
   **描述**: 发现可能的重复实现: private mapPerformanceToStrength(
   **代码片段**:
   ```typescript
      * 将性能映射到强度
   */
  private mapPerformanceToStrength(performance: number): number {
    return Math.max(1, Math.min(10, performance * 10));
  }
   ```

40. **文件**: `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
   **行号**: 963
   **描述**: 发现可能的重复实现: private mapPerformanceToConfidence(
   **代码片段**:
   ```typescript
      * 将性能映射到置信度
   */
  private mapPerformanceToConfidence(performance: number): number {
    return Math.max(0.1, Math.min(1.0, performance));
  }
   ```

41. **文件**: `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
   **行号**: 970
   **描述**: 发现可能的重复实现: private mapPerformanceToMomentum(
   **代码片段**:
   ```typescript
      * 将性能映射到动量
   */
  private mapPerformanceToMomentum(performance: number): number {
    return Math.max(-1, Math.min(1, (performance - 0.5) * 2));
  }
   ```

42. **文件**: `src/contexts/ai-reasoning/infrastructure/optimization/model-selector.ts`
   **行号**: 541
   **描述**: 发现可能的重复实现: private mapUrgencyToDirection(
   **代码片段**:
   ```typescript
      * 将紧急度映射到趋势方向
   */
  private mapUrgencyToDirection(urgency: string): string {
    switch (urgency) {
      case 'high':
   ```

43. **文件**: `src/contexts/ai-reasoning/infrastructure/optimization/model-selector.ts`
   **行号**: 555
   **描述**: 发现可能的重复实现: private mapUrgencyToStrength(
   **代码片段**:
   ```typescript
      * 将紧急度映射到强度
   */
  private mapUrgencyToStrength(urgency: string): number {
    switch (urgency) {
      case 'high':
   ```

44. **文件**: `src/contexts/ai-reasoning/infrastructure/optimization/model-selector.ts`
   **行号**: 571
   **描述**: 发现可能的重复实现: private mapUrgencyToMomentum(
   **代码片段**:
   ```typescript
      * 将紧急度映射到动量
   */
  private mapUrgencyToMomentum(urgency: string): number {
    switch (urgency) {
      case 'high':
   ```

45. **文件**: `src/contexts/ai-reasoning/infrastructure/optimization/model-selector.ts`
   **行号**: 587
   **描述**: 发现可能的重复实现: private mapQualityToDirection(
   **代码片段**:
   ```typescript
      * 将质量要求映射到趋势方向
   */
  private mapQualityToDirection(qualityRequirement: string): string {
    switch (qualityRequirement) {
      case 'premium':
   ```

46. **文件**: `src/contexts/ai-reasoning/infrastructure/optimization/model-selector.ts`
   **行号**: 601
   **描述**: 发现可能的重复实现: private mapQualityToStrength(
   **代码片段**:
   ```typescript
      * 将质量要求映射到强度
   */
  private mapQualityToStrength(qualityRequirement: string): number {
    switch (qualityRequirement) {
      case 'premium':
   ```

47. **文件**: `src/contexts/ai-reasoning/infrastructure/optimization/model-selector.ts`
   **行号**: 617
   **描述**: 发现可能的重复实现: private mapQualityToMomentum(
   **代码片段**:
   ```typescript
      * 将质量要求映射到动量
   */
  private mapQualityToMomentum(qualityRequirement: string): number {
    switch (qualityRequirement) {
      case 'premium':
   ```

48. **文件**: `src/contexts/ai-reasoning/infrastructure/optimization/model-selector.ts`
   **行号**: 633
   **描述**: 发现可能的重复实现: private mapCostToDirection(
   **代码片段**:
   ```typescript
      * 将最大成本映射到趋势方向
   */
  private mapCostToDirection(maxCost?: number): string {
    if (!maxCost) return 'neutral';

   ```

49. **文件**: `src/contexts/ai-reasoning/infrastructure/optimization/model-selector.ts`
   **行号**: 648
   **描述**: 发现可能的重复实现: private mapCostToStrength(
   **代码片段**:
   ```typescript
      * 将最大成本映射到强度
   */
  private mapCostToStrength(maxCost?: number): number {
    if (!maxCost) return 4;

   ```

50. **文件**: `src/contexts/ai-reasoning/infrastructure/optimization/model-selector.ts`
   **行号**: 663
   **描述**: 发现可能的重复实现: private mapCostToMomentum(
   **代码片段**:
   ```typescript
      * 将最大成本映射到动量
   */
  private mapCostToMomentum(maxCost?: number): number {
    if (!maxCost) return 0;

   ```

51. **文件**: `src/contexts/ai-reasoning/infrastructure/llm-providers/llm-router.ts`
   **行号**: 272
   **描述**: 发现可能的重复实现: private mapDomainToQualityImportance(
   **代码片段**:
   ```typescript
      * 映射领域到质量重要性
   */
  private mapDomainToQualityImportance(domain?: string): number {
    switch (domain) {
      case 'tradingSignal': return 8;
   ```

52. **文件**: `src/contexts/ai-reasoning/infrastructure/llm-providers/llm-router.ts`
   **行号**: 284
   **描述**: 发现可能的重复实现: private mapDomainToLatencyImportance(
   **代码片段**:
   ```typescript
      * 映射领域到延迟重要性
   */
  private mapDomainToLatencyImportance(domain?: string): number {
    switch (domain) {
      case 'tradingSignal': return 7;
   ```

53. **文件**: `src/contexts/ai-reasoning/infrastructure/llm-providers/llm-router.ts`
   **行号**: 296
   **描述**: 发现可能的重复实现: private mapDomainToCostImportance(
   **代码片段**:
   ```typescript
      * 映射领域到成本重要性
   */
  private mapDomainToCostImportance(domain?: string): number {
    switch (domain) {
      case 'tradingSignal': return 3;
   ```

54. **文件**: `src/contexts/ai-reasoning/infrastructure/llm-providers/llm-router.ts`
   **行号**: 308
   **描述**: 发现可能的重复实现: private mapDomainToReliabilityImportance(
   **代码片段**:
   ```typescript
      * 映射领域到可靠性重要性
   */
  private mapDomainToReliabilityImportance(domain?: string): number {
    switch (domain) {
      case 'tradingSignal': return 2;
   ```

55. **文件**: `src/contexts/risk-management/application/services/risk-assessment-application-service.ts`
   **行号**: 1724
   **描述**: 发现可能的重复实现: private mapToRealTimeRiskLevel(
   **代码片段**:
   ```typescript
      * 映射风险等级到实时监控格式
   */
  private mapToRealTimeRiskLevel(level: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | 'EMERGENCY' {
    switch (level.toUpperCase()) {
      case 'LOW': return 'LOW';
   ```

56. **文件**: `src/contexts/trend-analysis/infrastructure/modules/fusion/four-dimension-fusion-coordinator.ts`
   **行号**: 825
   **描述**: 发现可能的重复实现: private mapSignalToDirection(
   **代码片段**:
   ```typescript
      * 映射信号到趋势方向
   */
  private mapSignalToDirection(signal: string): string {
    switch (signal) {
      case 'BULLISH':
   ```

57. **文件**: `src/contexts/trend-analysis/infrastructure/modules/fusion/four-dimension-fusion-coordinator.ts`
   **行号**: 839
   **描述**: 发现可能的重复实现: private mapQuantitativeSignalToDirection(
   **代码片段**:
   ```typescript
      * 映射量化信号到趋势方向
   */
  private mapQuantitativeSignalToDirection(signal: string): string {
    switch (signal) {
      case 'BUY':
   ```

**建议**: ⚠️ 发现 57 个潜在违规，需要检查是否重新引入了重复实现
建议使用统一解决方案: UnifiedDtoMapperRegistry
- 所有DTO映射必须通过UnifiedDtoMapperRegistry
- 禁止在服务中实现私有映射方法
- 映射逻辑必须集中管理

### 8. [VIOLATION] 分散的健康检查逻辑重复

**风险级别**: MEDIUM
**违规数量**: 27

**违规详情**:
1. **文件**: `scripts/verify-monitoring-unification.ts`
   **行号**: 140
   **描述**: 发现可能的重复实现: private async checkHealth
   **代码片段**:
   ```typescript
      * 检查健康检查提供者
   */
  private async checkHealthCheckProviders(): Promise<string[]> {
    const providers: string[] = [];
    
   ```

2. **文件**: `src/api/controllers/dual-track-monitoring-controller.ts`
   **行号**: 195
   **描述**: 发现可能的重复实现: public healthCheck
   **代码片段**:
   ```typescript
      * GET /api/v1/monitoring/health
   */
  public healthCheck = async (_req: Request, res: Response): Promise<void> => {
    try {
      const healthStatus = await this.monitoringService.performHealthCheck();
   ```

3. **文件**: `src/api/routes/market-data-router.ts`
   **行号**: 289
   **描述**: 发现可能的重复实现: private async healthCheck
   **代码片段**:
   ```typescript
      * GET /api/v1/market-data/health
   */
  private async healthCheck(_req: Request, res: Response): Promise<void> {
    try {
      const marketDataService = this.getMarketDataService();
   ```

4. **文件**: `backup/problem15-cleanup/deprecated-files/enhanced-signal-controller.ts`
   **行号**: 273
   **描述**: 发现可能的重复实现: public healthCheck
   **代码片段**:
   ```typescript
      * GET /api/v2/signals/enhanced/health
   */
  public healthCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      const startTime = Date.now();
   ```

5. **文件**: `src/shared/infrastructure/health/unified-health-service.ts`
   **行号**: 166
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     private readonly healthChecks: Map<string, () => Promise<ComponentHealthCheck>> = new Map();
  private lastHealthCheck: HealthCheckResult | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  // 服务器状态管理
   ```

6. **文件**: `src/shared/infrastructure/controllers/base-controller.ts`
   **行号**: 295
   **描述**: 发现可能的重复实现: public healthCheck
   **代码片段**:
   ```typescript
      * 健康检查端点
   */
  public healthCheck = this.asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const context = this.createRequestContext(req);
    
   ```

7. **文件**: `src/application/sync/system/system-status-manager.ts`
   **行号**: 16
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     private readonly systems: Map<SystemType, SystemStatus> = new Map();
  private readonly heartbeatTimeout = 60000; // 60秒心跳超时
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(private readonly logger: ILogger) {
   ```

8. **文件**: `src/application/real-time-sync/redis/redis-connection-manager.ts`
   **行号**: 19
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 10;
  private healthCheckInterval?: NodeJS.Timeout;

  constructor(
   ```

9. **文件**: `src/contexts/market-data/infrastructure/services/multi-exchange-data-service.ts`
   **行号**: 51
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     private readonly config: FailoverConfig;
  private readonly dataValidator: RealDataValidator;
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(
   ```

10. **文件**: `src/contexts/market-data/infrastructure/external/multi-exchange-websocket-manager.ts`
   **行号**: 91
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     private readonly adapterFactory: IExchangeAdapterFactory;

  private healthCheckInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private restFallbackIntervals: Map<string, NodeJS.Timeout> = new Map();
   ```

11. **文件**: `src/contexts/market-data/infrastructure/external/high-performance-data-distribution-network.ts`
   **行号**: 226
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     
  // 定时器
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private metricsInterval: NodeJS.Timeout | null = null;
  private queueProcessorInterval: NodeJS.Timeout | null = null;
   ```

12. **文件**: `src/contexts/market-data/infrastructure/external/exchange-router.ts`
   **行号**: 179
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     // 故障检测和恢复
  private readonly failureStates: Map<string, ExchangeFailureState> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;

  // 路由统计
   ```

13. **文件**: `src/contexts/market-data/infrastructure/external/dynamic-load-balancer.ts`
   **行号**: 126
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     
  // 定时器
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private weightUpdateInterval: NodeJS.Timeout | null = null;
  
   ```

14. **文件**: `src/shared/infrastructure/monitoring/services/unified-health-check-service.ts`
   **行号**: 31
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     private readonly healthCheckProviders = new Map<string, IHealthCheckProvider>();
  private readonly startTime = new Date();
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private lastHealthCheck: SystemHealthCheck | null = null;

   ```

15. **文件**: `src/contexts/market-data/infrastructure/websocket/manager/websocket-connection-manager.ts`
   **行号**: 48
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     private readonly loadBalanceConfig: WebSocketLoadBalanceConfig;
  
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private roundRobinIndex = 0;
   ```

16. **文件**: `src/contexts/market-data/infrastructure/websocket/monitoring/websocket-health-checker.ts`
   **行号**: 66
   **描述**: 发现可能的重复实现: private healthCheck
   **代码片段**:
   ```typescript
     private readonly lastResults: Map<string, HealthCheckResult> = new Map();
  
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

   ```

17. **文件**: `src/express-main-simple.ts`
   **行号**: 27
   **描述**: 发现可能的重复实现: app.get('/health
   **代码片段**:
   ```typescript
   
      // 添加健康检查端点
      this.app.get('/health', (req, res) => {
        res.json({
          status: 'OK',
   ```

18. **文件**: `src/express-app.ts`
   **行号**: 162
   **描述**: 发现可能的重复实现: app.get('/health
   **代码片段**:
   ```typescript
   
    // 健康检查
    this.app.get('/health', (req: any, res: any) => {
      res.success({
        status: 'healthy',
   ```

19. **文件**: `src/api/routes/trading-signals.routes.ts`
   **行号**: 106
   **描述**: 发现可能的重复实现: router.get('/health
   **代码片段**:
   ```typescript
    * @access Public
 */
router.get('/health', async (req, res) => {
  const controller = getTradingSignalsController();
  await controller.healthCheck(req, res);
   ```

20. **文件**: `src/api/routes/market-data-router.ts`
   **行号**: 56
   **描述**: 发现可能的重复实现: router.get('/health
   **代码片段**:
   ```typescript
       
    // 健康检查
    this.router.get('/health', this.healthCheck.bind(this));
  }

   ```

21. **文件**: `src/api/routes/dual-track-monitoring-routes.ts`
   **行号**: 150
   **描述**: 发现可能的重复实现: router.get('/health
   **代码片段**:
   ```typescript
    *         description: 系统不健康
 */
router.get('/health', (req, res) => getController().healthCheck(req, res));

/**
   ```

22. **文件**: `src/api/routes/ai-reasoning-router.ts`
   **行号**: 226
   **描述**: 发现可能的重复实现: router.get('/health
   **代码片段**:
   ```typescript
        *         description: 健康检查结果
     */
    this.router.get('/health', this.asyncHandler(this.getHealthStatus.bind(this)));
  }

   ```

23. **文件**: `src/express-app/modules/middleware-setup.ts`
   **行号**: 156
   **描述**: 发现可能的重复实现: app.get('/health
   **代码片段**:
   ```typescript
         try {
        // 健康检查端点
        app.get('/health', (req, res) => {
          const healthStatus = {
            status: 'healthy',
   ```

24. **文件**: `src/contexts/ai-reasoning/presentation/http/unified-learning-system-routes.ts`
   **行号**: 60
   **描述**: 发现可能的重复实现: router.get('/health
   **代码片段**:
   ```typescript
      * GET /api/v3/ai/unified-learning/health
   */
  router.get('/health', async (req: Request, res: Response): Promise<void> => {
    try {
      const unifiedSystem = getUnifiedSystem();
   ```

25. **文件**: `src/api/routes/trend-analysis/routes/health-routes.ts`
   **行号**: 34
   **描述**: 发现可能的重复实现: router.get('/health
   **代码片段**:
   ```typescript
      *         description: 健康检查结果
   */
  router.get('/health', asyncHandler(async (req: Request, res: Response) => {
    try {
      // 检查趋势分析服务状态
   ```

26. **文件**: `src/api/routes/risk-assessment/routes/health-check-routes.ts`
   **行号**: 69
   **描述**: 发现可能的重复实现: router.get('/health
   **代码片段**:
   ```typescript
      *                       type: object
   */
  router.get('/health', asyncHandler(async (req: Request, res: Response) => {
    try {
      const startTime = Date.now();
   ```

27. **文件**: `src/contexts/user-management/presentation/http/routes/api-key-routes.ts`
   **行号**: 160
   **描述**: 发现可能的重复实现: router.get('/health
   **代码片段**:
   ```typescript
   
// 健康检查端点（无需认证）
router.get('/health',
  (req, res) => {
    res.json({
   ```

**建议**: ⚠️ 发现 27 个潜在违规，需要检查是否重新引入了重复实现
建议使用统一解决方案: HealthCheckAggregator
- 所有健康检查必须通过HealthCheckAggregator
- 禁止在业务服务中实现健康检查逻辑
- 健康检查必须使用统一的提供者模式


## 改进建议

1. 立即处理发现的违规问题，防止重复实现扩散
2. 定期运行此验证脚本，确保重复实现不会重新引入
3. 在CI/CD流程中集成此验证，阻止违规代码合并
4. 为开发团队提供统一组件使用指南
5. 建立代码审查检查清单，重点关注重复实现

---

**报告生成完成** - 如需重新验证，请运行: `npm run validate:known-duplications`
