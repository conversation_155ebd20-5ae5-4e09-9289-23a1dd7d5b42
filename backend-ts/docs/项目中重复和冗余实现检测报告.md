# 项目中重复和冗余实现检测报告

## 脚本说明

本报告由 **重复实现检测器** (redundant-implementation-detector.ts) 自动生成。该脚本专门用于检测项目中的重复实现问题，包括：

- **命名模式问题**: 检测带有问题前缀的文件（如 enhanced-, optimized-, unified- 等）
- **功能重复**: 识别相同功能的多个实现版本
- **依赖注入重复**: 分析DI配置中的重复Symbol定义
- **类和接口重复**: 检测重复的类和接口定义
- **服务层重复**: 识别服务层的功能重叠

检测器基于之前分析的47+个重复实现问题的根源模式，能够系统性地发现和报告代码库中的重复实现问题。

---

## 检测结果概览

**生成时间**: 2025/7/20 19:57:48  
**检测器版本**: 1.0.0

| 指标 | 数值 |
|------|------|
| 总问题数 | 209 |
| 严重问题 | 1 |
| 高优先级 | 2 |
| 中优先级 | 206 |
| 低优先级 | 0 |
| 受影响文件 | 132 |
| 预计清理时间 | 222小时 |

## 问题分类统计

- **接口定义重复**: 107个问题
- **相似文件名**: 87个问题
- **类定义重复**: 6个问题
- **服务层重复**: 6个问题
- **命名模式问题**: 1个问题
- **分析服务重复**: 1个问题
- **配置管理重复**: 1个问题

## 严重问题详情

### 1. 发现同一功能的多个版本: environment-manager.ts

**文件**: `src/shared/infrastructure/config/environment/unified-environment-manager.ts`
**相关文件**:
- `src/shared/infrastructure/config/environment/environment-manager.ts`
**建议**: 合并重复实现，保留最优版本，建立统一接口
**预计工作量**: 2-4小时


## 高优先级问题详情

### 1. 发现6个分析服务重复相关文件

**文件**: `src/tests/integration/core-analysis-services-integration.test.ts`
**相关文件**:
- `src/shared/infrastructure/analysis/unified-analysis-service-manager.ts`
- `src/contexts/trend-analysis/application/trend-analysis-application.service.ts`
- `src/shared/infrastructure/analysis/di/AnalysisServiceBindings.ts`
- `scripts/verify-core-analysis-services-migration.ts`
- `src/tests/integration/core-analysis-services-integration.test.ts`
**建议**: 分析功能重叠，合并相似实现，建立统一接口
**预计工作量**: 4-8小时

### 2. 发现5个配置管理重复相关文件

**文件**: `src/shared/infrastructure/testing/unified-test-config-manager.ts`
**相关文件**:
- `src/shared/infrastructure/risk/risk-enforcement-config-manager.ts`
- `src/shared/infrastructure/config/unified-config-manager.ts`
- `src/shared/infrastructure/config/dynamic/dynamic-config-manager.ts`
- `src/shared/infrastructure/config/dynamic/config-version-manager.ts`
**建议**: 分析功能重叠，合并相似实现，建立统一接口
**预计工作量**: 4-8小时


## 改进建议

1. 建立严格的命名规范，禁用问题前缀（enhanced-, optimized-, unified-等）
2. 实施代码审查检查清单，重点关注重复实现
3. 建立自动化重复检测机制，集成到CI/CD流程
4. 重构优先的开发文化，避免增量式重写
5. 建立架构治理机制，统一接口标准
6. 定期进行重复实现清理，建立技术债务管理流程

## 受影响文件列表

- `src/express-main.ts`
- `src/application/short-term-learning-service.ts`
- `src/contexts/trading-execution/test-data-service-trading.ts`
- `src/api/routes/unified-health-routes.ts`
- `src/api/controllers/unified-health-controller.ts`
- `src/api/controllers/trend-analysis-controller.ts`
- `src/api/controllers/config-controller.ts`
- `src/shared/infrastructure/types/unified-service-interfaces.ts`
- `src/shared/infrastructure/messaging/test-redis-event-bus.ts`
- `src/shared/infrastructure/logging/unified-logger.ts`
- `src/shared/infrastructure/di/modular-container-manager.ts`
- `src/shared/infrastructure/database/repository-base-service.ts`
- `src/shared/infrastructure/config/unified-config-manager.ts`
- `src/shared/infrastructure/config/risk-config.service.ts`
- `src/shared/infrastructure/ai/vector-service.ts`
- `src/shared/infrastructure/ai/scheduled-ai-call-decorator.ts`
- `src/shared/infrastructure/ai/multi-tier-cache-service.ts`
- `src/shared/infrastructure/ai/dynamic-cache-strategy.ts`
- `src/contexts/trend-analysis/tests/unified-pattern-recognition-integration.test.ts`
- `src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts`
- `src/contexts/trend-analysis/tests/simple-performance-benchmark.test.ts`
- `src/contexts/risk-management/tests/risk-management-integration.test.ts`
- `src/api/middleware/modules/validation-middleware.ts`
- `src/api/middleware/modules/core-middleware.ts`
- `src/shared/infrastructure/di/modules/user-management-container-module.ts`
- `src/shared/infrastructure/di/modules/trend-analysis-container-module.ts`
- `src/shared/infrastructure/di/modules/dynamic-config-container-module.ts`
- `src/shared/infrastructure/di/modules/data-processing-container-module.ts`
- `src/shared/infrastructure/config/interfaces/risk-config-validation.interface.ts`
- `src/shared/infrastructure/config/environment/unified-environment-manager.ts`
- `src/shared/infrastructure/data-processing/executors/validation-stage-executor.ts`
- `src/shared/infrastructure/data-processing/executors/sync-stage-executor.ts`
- `src/shared/infrastructure/data-processing/executors/receive-stage-executor.ts`
- `src/shared/infrastructure/data-processing/strategies/strategy-monitor.ts`
- `src/shared/infrastructure/analysis/algorithms/HarmonicPatternAlgorithms.ts`
- `src/contexts/user-management/infrastructure/services/TotpService.ts`
- `src/contexts/user-management/infrastructure/services/AuthorizationService.ts`
- `src/contexts/user-management/infrastructure/repositories/PrismaMfaDeviceRepository.ts`
- `src/contexts/user-config/infrastructure/repositories/prisma-user-profile-repository.ts`
- `src/contexts/user-config/presentation/controllers/user-profile-controller.ts`
- `src/contexts/user-config/domain/repositories/user-profile-repository.interface.ts`
- `src/contexts/user-config/application/services/user-profile-application-service.ts`
- `src/contexts/user-management/domain/value-objects/TrustScore.ts`
- `src/contexts/user-management/domain/value-objects/SecurityEventType.ts`
- `src/contexts/user-management/domain/services/IAuthorizationService.ts`
- `src/contexts/user-management/domain/repositories/IUserRepository.ts`
- `src/contexts/trend-analysis/infrastructure/di/presentation-bindings.ts`
- `src/contexts/trend-analysis/infrastructure/di/monitoring-services-bindings.ts`
- `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
- `src/contexts/trend-analysis/infrastructure/services/trend-analysis-engine.ts`
- `src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`
- `src/contexts/ai-reasoning/presentation/http/dual-layer-reasoning-routes.ts`
- `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
- `src/api/routes/trend-analysis/routes/fundamental-analysis-routes.ts`
- `src/api/routes/trend-analysis/routes/comprehensive-trend-analysis-routes.ts`
- `src/contexts/market-data/infrastructure/repositories/prisma-price-data-repository.ts`
- `src/contexts/market-data/infrastructure/external/websocket-data-stream-processor.ts`
- `src/contexts/market-data/infrastructure/external/tokenmetrics-adapter.ts`
- `src/contexts/market-data/infrastructure/external/robust-websocket-adapter.ts`
- `src/contexts/market-data/infrastructure/external/coingecko-adapter.ts`
- `src/contexts/ai-reasoning/domain/services/unified-prediction-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts`
- `src/contexts/ai-reasoning/domain/services/pure-ai-analysis.interface.ts`
- `src/contexts/ai-reasoning/domain/services/llm-service.interface.ts`
- `src/contexts/market-data/domain/repositories/price-data-repository.ts`
- `src/contexts/user-management/presentation/http/controllers/UserController.ts`
- `src/shared/infrastructure/data-processing/strategies/sentiment/sentiment-validation-stage.ts`
- `src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
- `src/shared/infrastructure/data-processing/strategies/interfaces/strategy-validation.interface.ts`
- `src/shared/infrastructure/analysis/interfaces/pattern-recognition/pattern-validation.interface.ts`
- `src/contexts/user-management/domain/services/mfa/IMfaVerificationService.ts`
- `src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.ts`
- `src/contexts/trend-analysis/infrastructure/modules/indicators/indicators-module.ts`
- `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-starter.test.ts`
- `src/contexts/market-data/infrastructure/external/__tests__/binance-websocket-adapter.test.ts`
- `scripts/verify-problem-16-solution.ts`
- `scripts/verify-logger-fix.ts`
- `scripts/validate-config-migration.ts`
- `scripts/fix-all-fake-implementations.ts`
- `scripts/comprehensive-duplication-analysis.ts`
- `scripts/migration/schema-normalization.ts`
- `scripts/maintenance/setup-data-protection.ts`
- `scripts/database/unified-database-verification.ts`
- `scripts/cleanup/clean-test-fake-implementations.ts`
- `tests/utils/test-data-validator.ts`
- `tests/integration/binance-integration.test.ts`
- `tests/unit/contexts/user-management/presentation/UserController.test.ts`
- `src/tests/integration/core-analysis-services-integration.test.ts`
- `src/shared/infrastructure/testing/unified-test-config-manager.ts`
- `src/application/version-manager/clock/vector-clock-manager.ts`
- `src/contexts/user-management/domain/value-objects/RiskLevel.ts`
- `src/contexts/user-config/infrastructure/services/EnhancedLLMRouter.ts`
- `src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts`
- `src/shared/application/application-service-interfaces.ts`
- `src/tests/integration/real-performance.test.ts`
- `src/tests/integration/learning-system-integration-test.ts`
- `src/application/state-sync/StateDataValidator.ts`
- `src/application/sync/sync-types.ts`
- `src/shared/domain/repositories/base-repository.interface.ts`
- `src/shared/core/interfaces/ILogger.ts`
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/testing/unified-integration-test-base.ts`
- `src/shared/infrastructure/monitoring/enhanced-trading-execution-monitor.ts`
- `src/shared/infrastructure/data-quality/unified-data-quality-monitor.ts`
- `src/shared/infrastructure/data-quality/data-quality-dashboard.ts`
- `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
- `src/contexts/user-management/domain/services/IThreatDetectionService.ts`
- `src/contexts/user-management/domain/services/IMfaService.ts`
- `src/contexts/trend-analysis/domain/services/trend-analysis-engine.interface.ts`
- `src/contexts/trend-analysis/domain/value-objects/trend-analysis.ts`
- `src/contexts/trend-analysis/infrastructure/services/professional-pivot-detector.ts`
- `src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts`
- `src/contexts/user-config/application/services/UserConfigService.ts`
- `src/contexts/trading-execution/domain/types/dual-track.types.ts`
- `src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts`
- `src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts`
- `src/contexts/risk-management/domain/value-objects/account-info.ts`
- `src/contexts/learning/infrastructure/analyzers/trend-consistency-analyzer.ts`
- `src/contexts/learning/infrastructure/analyzers/signal-quality-analyzer.ts`
- `src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts`
- `src/contexts/market-data/domain/services/high-performance-data-distribution-network.interface.ts`
- `src/contexts/ai-reasoning/domain/services/unified-learning-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
- `src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/llm-provider.interface.ts`
- `scripts/duplication-cleanup-plan.ts`
- `scripts/detect-specific-duplications.ts`
- `scripts/verification/verification-reporter.ts`
- `src/contexts/trend-analysis/infrastructure/services/real-market-data-provider.ts`
- `src/contexts/user-config/domain/services/user-config-validator.ts`
- `src/contexts/trading-signals/application/services/signal-generation-application-service.ts`

## 详细检测结果

### 1. [MEDIUM] 相似文件名

**描述**: 目录 src 中发现相似文件
**文件**: `src/express-main.ts`
**相关文件**:
- `src/express-main.ts`
- `src/express-main-simple.ts`
- `src/express-main-minimal.ts`
- `src/express-main-debug.ts`
- `src/express-app.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 2. [MEDIUM] 相似文件名

**描述**: 目录 src/application 中发现相似文件
**文件**: `src/application/short-term-learning-service.ts`
**相关文件**:
- `src/application/short-term-learning-service.ts`
- `src/application/long-term-learning-service.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 3. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/trading-execution 中发现相似文件
**文件**: `src/contexts/trading-execution/test-data-service-trading.ts`
**相关文件**:
- `src/contexts/trading-execution/test-data-service-trading.ts`
- `src/contexts/trading-execution/test-data-service-integration.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 4. [MEDIUM] 相似文件名

**描述**: 目录 src/api/routes 中发现相似文件
**文件**: `src/api/routes/unified-health-routes.ts`
**相关文件**:
- `src/api/routes/unified-health-routes.ts`
- `src/api/routes/auth-routes.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 5. [MEDIUM] 相似文件名

**描述**: 目录 src/api/controllers 中发现相似文件
**文件**: `src/api/controllers/unified-health-controller.ts`
**相关文件**:
- `src/api/controllers/unified-health-controller.ts`
- `src/api/controllers/auth-controller.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 6. [MEDIUM] 相似文件名

**描述**: 目录 src/api/controllers 中发现相似文件
**文件**: `src/api/controllers/trend-analysis-controller.ts`
**相关文件**:
- `src/api/controllers/trend-analysis-controller.ts`
- `src/api/controllers/ai-analytics-controller.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 7. [MEDIUM] 相似文件名

**描述**: 目录 src/api/controllers 中发现相似文件
**文件**: `src/api/controllers/config-controller.ts`
**相关文件**:
- `src/api/controllers/config-controller.ts`
- `src/api/controllers/admin-controller.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 8. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/types 中发现相似文件
**文件**: `src/shared/infrastructure/types/unified-service-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-service-interfaces.ts`
- `src/shared/infrastructure/types/unified-interfaces.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 9. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/messaging 中发现相似文件
**文件**: `src/shared/infrastructure/messaging/test-redis-event-bus.ts`
**相关文件**:
- `src/shared/infrastructure/messaging/test-redis-event-bus.ts`
- `src/shared/infrastructure/messaging/redis.ts`
- `src/shared/infrastructure/messaging/event-bus.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 10. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/logging 中发现相似文件
**文件**: `src/shared/infrastructure/logging/unified-logger.ts`
**相关文件**:
- `src/shared/infrastructure/logging/unified-logger.ts`
- `src/shared/infrastructure/logging/logger.interface.ts`
- `src/shared/infrastructure/logging/logger-factory.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 11. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/di 中发现相似文件
**文件**: `src/shared/infrastructure/di/modular-container-manager.ts`
**相关文件**:
- `src/shared/infrastructure/di/modular-container-manager.ts`
- `src/shared/infrastructure/di/container.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 12. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/database 中发现相似文件
**文件**: `src/shared/infrastructure/database/repository-base-service.ts`
**相关文件**:
- `src/shared/infrastructure/database/repository-base-service.ts`
- `src/shared/infrastructure/database/repository-base-service.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 13. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/config 中发现相似文件
**文件**: `src/shared/infrastructure/config/unified-config-manager.ts`
**相关文件**:
- `src/shared/infrastructure/config/unified-config-manager.ts`
- `src/shared/infrastructure/config/config-monitor.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 14. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/config 中发现相似文件
**文件**: `src/shared/infrastructure/config/risk-config.service.ts`
**相关文件**:
- `src/shared/infrastructure/config/risk-config.service.ts`
- `src/shared/infrastructure/config/risk-config.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 15. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/ai 中发现相似文件
**文件**: `src/shared/infrastructure/ai/vector-service.ts`
**相关文件**:
- `src/shared/infrastructure/ai/vector-service.ts`
- `src/shared/infrastructure/ai/vector-service.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 16. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/ai 中发现相似文件
**文件**: `src/shared/infrastructure/ai/scheduled-ai-call-decorator.ts`
**相关文件**:
- `src/shared/infrastructure/ai/scheduled-ai-call-decorator.ts`
- `src/shared/infrastructure/ai/cached-ai-call-decorator.ts`
- `src/shared/infrastructure/ai/ai-call-decorator.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 17. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/ai 中发现相似文件
**文件**: `src/shared/infrastructure/ai/multi-tier-cache-service.ts`
**相关文件**:
- `src/shared/infrastructure/ai/multi-tier-cache-service.ts`
- `src/shared/infrastructure/ai/multi-tier-cache-service.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 18. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/ai 中发现相似文件
**文件**: `src/shared/infrastructure/ai/dynamic-cache-strategy.ts`
**相关文件**:
- `src/shared/infrastructure/ai/dynamic-cache-strategy.ts`
- `src/shared/infrastructure/ai/dynamic-cache-strategy.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 19. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/trend-analysis/tests 中发现相似文件
**文件**: `src/contexts/trend-analysis/tests/unified-pattern-recognition-integration.test.ts`
**相关文件**:
- `src/contexts/trend-analysis/tests/unified-pattern-recognition-integration.test.ts`
- `src/contexts/trend-analysis/tests/pattern-recognition-expansion.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 20. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/trend-analysis/tests 中发现相似文件
**文件**: `src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts`
**相关文件**:
- `src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts`
- `src/contexts/trend-analysis/tests/trend-analysis-integration-simple.test.ts`
- `src/contexts/trend-analysis/tests/trend-analysis-engine.test.ts`
- `src/contexts/trend-analysis/tests/trend-analysis-api.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 21. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/trend-analysis/tests 中发现相似文件
**文件**: `src/contexts/trend-analysis/tests/simple-performance-benchmark.test.ts`
**相关文件**:
- `src/contexts/trend-analysis/tests/simple-performance-benchmark.test.ts`
- `src/contexts/trend-analysis/tests/performance-benchmark.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 22. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/risk-management/tests 中发现相似文件
**文件**: `src/contexts/risk-management/tests/risk-management-integration.test.ts`
**相关文件**:
- `src/contexts/risk-management/tests/risk-management-integration.test.ts`
- `src/contexts/risk-management/tests/risk-management-api.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 23. [MEDIUM] 相似文件名

**描述**: 目录 src/api/middleware/modules 中发现相似文件
**文件**: `src/api/middleware/modules/validation-middleware.ts`
**相关文件**:
- `src/api/middleware/modules/validation-middleware.ts`
- `src/api/middleware/modules/authorization-middleware.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 24. [MEDIUM] 相似文件名

**描述**: 目录 src/api/middleware/modules 中发现相似文件
**文件**: `src/api/middleware/modules/core-middleware.ts`
**相关文件**:
- `src/api/middleware/modules/core-middleware.ts`
- `src/api/middleware/modules/cache-middleware.ts`
- `src/api/middleware/modules/auth-middleware.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 25. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/di/modules 中发现相似文件
**文件**: `src/shared/infrastructure/di/modules/user-management-container-module.ts`
**相关文件**:
- `src/shared/infrastructure/di/modules/user-management-container-module.ts`
- `src/shared/infrastructure/di/modules/user-config-container-module.ts`
- `src/shared/infrastructure/di/modules/risk-management-container-module.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 26. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/di/modules 中发现相似文件
**文件**: `src/shared/infrastructure/di/modules/trend-analysis-container-module.ts`
**相关文件**:
- `src/shared/infrastructure/di/modules/trend-analysis-container-module.ts`
- `src/shared/infrastructure/di/modules/trading-analysis-container-module.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 27. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/di/modules 中发现相似文件
**文件**: `src/shared/infrastructure/di/modules/dynamic-config-container-module.ts`
**相关文件**:
- `src/shared/infrastructure/di/modules/dynamic-config-container-module.ts`
- `src/shared/infrastructure/di/modules/config-container-module.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 28. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/di/modules 中发现相似文件
**文件**: `src/shared/infrastructure/di/modules/data-processing-container-module.ts`
**相关文件**:
- `src/shared/infrastructure/di/modules/data-processing-container-module.ts`
- `src/shared/infrastructure/di/modules/ai-reasoning-container-module.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 29. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/config/interfaces 中发现相似文件
**文件**: `src/shared/infrastructure/config/interfaces/risk-config-validation.interface.ts`
**相关文件**:
- `src/shared/infrastructure/config/interfaces/risk-config-validation.interface.ts`
- `src/shared/infrastructure/config/interfaces/risk-config-mutation.interface.ts`
- `src/shared/infrastructure/config/interfaces/risk-config-data-access.interface.ts`
- `src/shared/infrastructure/config/interfaces/config-validation.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 30. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/config/environment 中发现相似文件
**文件**: `src/shared/infrastructure/config/environment/unified-environment-manager.ts`
**相关文件**:
- `src/shared/infrastructure/config/environment/unified-environment-manager.ts`
- `src/shared/infrastructure/config/environment/environment-manager.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 31. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/data-processing/executors 中发现相似文件
**文件**: `src/shared/infrastructure/data-processing/executors/validation-stage-executor.ts`
**相关文件**:
- `src/shared/infrastructure/data-processing/executors/validation-stage-executor.ts`
- `src/shared/infrastructure/data-processing/executors/cleaning-stage-executor.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 32. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/data-processing/executors 中发现相似文件
**文件**: `src/shared/infrastructure/data-processing/executors/sync-stage-executor.ts`
**相关文件**:
- `src/shared/infrastructure/data-processing/executors/sync-stage-executor.ts`
- `src/shared/infrastructure/data-processing/executors/mapping-stage-executor.ts`
- `src/shared/infrastructure/data-processing/executors/base-stage-executor.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 33. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/data-processing/executors 中发现相似文件
**文件**: `src/shared/infrastructure/data-processing/executors/receive-stage-executor.ts`
**相关文件**:
- `src/shared/infrastructure/data-processing/executors/receive-stage-executor.ts`
- `src/shared/infrastructure/data-processing/executors/processing-stage-executor.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 34. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/data-processing/strategies 中发现相似文件
**文件**: `src/shared/infrastructure/data-processing/strategies/strategy-monitor.ts`
**相关文件**:
- `src/shared/infrastructure/data-processing/strategies/strategy-monitor.ts`
- `src/shared/infrastructure/data-processing/strategies/strategy-manager.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 35. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/analysis/algorithms 中发现相似文件
**文件**: `src/shared/infrastructure/analysis/algorithms/HarmonicPatternAlgorithms.ts`
**相关文件**:
- `src/shared/infrastructure/analysis/algorithms/HarmonicPatternAlgorithms.ts`
- `src/shared/infrastructure/analysis/algorithms/GannPatternAlgorithms.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 36. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-management/infrastructure/services 中发现相似文件
**文件**: `src/contexts/user-management/infrastructure/services/TotpService.ts`
**相关文件**:
- `src/contexts/user-management/infrastructure/services/TotpService.ts`
- `src/contexts/user-management/infrastructure/services/JwtService.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 37. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-management/infrastructure/services 中发现相似文件
**文件**: `src/contexts/user-management/infrastructure/services/AuthorizationService.ts`
**相关文件**:
- `src/contexts/user-management/infrastructure/services/AuthorizationService.ts`
- `src/contexts/user-management/infrastructure/services/AuthenticationService.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 38. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-management/infrastructure/repositories 中发现相似文件
**文件**: `src/contexts/user-management/infrastructure/repositories/PrismaMfaDeviceRepository.ts`
**相关文件**:
- `src/contexts/user-management/infrastructure/repositories/PrismaMfaDeviceRepository.ts`
- `src/contexts/user-management/infrastructure/repositories/PrismaMfaAttemptRepository.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 39. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-config/infrastructure/repositories 中发现相似文件
**文件**: `src/contexts/user-config/infrastructure/repositories/prisma-user-profile-repository.ts`
**相关文件**:
- `src/contexts/user-config/infrastructure/repositories/prisma-user-profile-repository.ts`
- `src/contexts/user-config/infrastructure/repositories/prisma-user-preferences-repository.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 40. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-config/presentation/controllers 中发现相似文件
**文件**: `src/contexts/user-config/presentation/controllers/user-profile-controller.ts`
**相关文件**:
- `src/contexts/user-config/presentation/controllers/user-profile-controller.ts`
- `src/contexts/user-config/presentation/controllers/user-preferences-controller.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 41. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-config/domain/repositories 中发现相似文件
**文件**: `src/contexts/user-config/domain/repositories/user-profile-repository.interface.ts`
**相关文件**:
- `src/contexts/user-config/domain/repositories/user-profile-repository.interface.ts`
- `src/contexts/user-config/domain/repositories/user-preferences-repository.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 42. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-config/application/services 中发现相似文件
**文件**: `src/contexts/user-config/application/services/user-profile-application-service.ts`
**相关文件**:
- `src/contexts/user-config/application/services/user-profile-application-service.ts`
- `src/contexts/user-config/application/services/user-preferences-application-service.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 43. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-management/domain/value-objects 中发现相似文件
**文件**: `src/contexts/user-management/domain/value-objects/TrustScore.ts`
**相关文件**:
- `src/contexts/user-management/domain/value-objects/TrustScore.ts`
- `src/contexts/user-management/domain/value-objects/ThreatScore.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 44. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-management/domain/value-objects 中发现相似文件
**文件**: `src/contexts/user-management/domain/value-objects/SecurityEventType.ts`
**相关文件**:
- `src/contexts/user-management/domain/value-objects/SecurityEventType.ts`
- `src/contexts/user-management/domain/value-objects/SecurityEventId.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 45. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-management/domain/services 中发现相似文件
**文件**: `src/contexts/user-management/domain/services/IAuthorizationService.ts`
**相关文件**:
- `src/contexts/user-management/domain/services/IAuthorizationService.ts`
- `src/contexts/user-management/domain/services/IAuthenticationService.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 46. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-management/domain/repositories 中发现相似文件
**文件**: `src/contexts/user-management/domain/repositories/IUserRepository.ts`
**相关文件**:
- `src/contexts/user-management/domain/repositories/IUserRepository.ts`
- `src/contexts/user-management/domain/repositories/IApiKeyRepository.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 47. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/trend-analysis/infrastructure/di 中发现相似文件
**文件**: `src/contexts/trend-analysis/infrastructure/di/presentation-bindings.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/di/presentation-bindings.ts`
- `src/contexts/trend-analysis/infrastructure/di/bindings.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 48. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/trend-analysis/infrastructure/di 中发现相似文件
**文件**: `src/contexts/trend-analysis/infrastructure/di/monitoring-services-bindings.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/di/monitoring-services-bindings.ts`
- `src/contexts/trend-analysis/infrastructure/di/domain-services-bindings.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 49. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/trend-analysis/infrastructure/services 中发现相似文件
**文件**: `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
- `src/contexts/trend-analysis/infrastructure/services/collaboration-service.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 50. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/trend-analysis/infrastructure/services 中发现相似文件
**文件**: `src/contexts/trend-analysis/infrastructure/services/trend-analysis-engine.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/services/trend-analysis-engine.ts`
- `src/contexts/trend-analysis/infrastructure/services/pure-ai-trend-analysis-engine.ts`
- `src/contexts/trend-analysis/infrastructure/services/base-trend-analysis-engine.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 51. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/risk-management/infrastructure/services 中发现相似文件
**文件**: `src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`
**相关文件**:
- `src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`
- `src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 52. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/ai-reasoning/presentation/http 中发现相似文件
**文件**: `src/contexts/ai-reasoning/presentation/http/dual-layer-reasoning-routes.ts`
**相关文件**:
- `src/contexts/ai-reasoning/presentation/http/dual-layer-reasoning-routes.ts`
- `src/contexts/ai-reasoning/presentation/http/dual-layer-reasoning-controller.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 53. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/ai-reasoning/infrastructure/services 中发现相似文件
**文件**: `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
**相关文件**:
- `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
- `src/contexts/ai-reasoning/infrastructure/services/multi-timeframe-learning-coordinator.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 54. [MEDIUM] 相似文件名

**描述**: 目录 src/api/routes/trend-analysis/routes 中发现相似文件
**文件**: `src/api/routes/trend-analysis/routes/fundamental-analysis-routes.ts`
**相关文件**:
- `src/api/routes/trend-analysis/routes/fundamental-analysis-routes.ts`
- `src/api/routes/trend-analysis/routes/comprehensive-analysis-routes.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 55. [MEDIUM] 相似文件名

**描述**: 目录 src/api/routes/trend-analysis/routes 中发现相似文件
**文件**: `src/api/routes/trend-analysis/routes/comprehensive-trend-analysis-routes.ts`
**相关文件**:
- `src/api/routes/trend-analysis/routes/comprehensive-trend-analysis-routes.ts`
- `src/api/routes/trend-analysis/routes/basic-analysis-routes.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 56. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/market-data/infrastructure/repositories 中发现相似文件
**文件**: `src/contexts/market-data/infrastructure/repositories/prisma-price-data-repository.ts`
**相关文件**:
- `src/contexts/market-data/infrastructure/repositories/prisma-price-data-repository.ts`
- `src/contexts/market-data/infrastructure/repositories/prisma-historical-data-repository.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 57. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/market-data/infrastructure/external 中发现相似文件
**文件**: `src/contexts/market-data/infrastructure/external/websocket-data-stream-processor.ts`
**相关文件**:
- `src/contexts/market-data/infrastructure/external/websocket-data-stream-processor.ts`
- `src/contexts/market-data/infrastructure/external/millisecond-data-stream-processor.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 58. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/market-data/infrastructure/external 中发现相似文件
**文件**: `src/contexts/market-data/infrastructure/external/tokenmetrics-adapter.ts`
**相关文件**:
- `src/contexts/market-data/infrastructure/external/tokenmetrics-adapter.ts`
- `src/contexts/market-data/infrastructure/external/coinmetrics-adapter.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 59. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/market-data/infrastructure/external 中发现相似文件
**文件**: `src/contexts/market-data/infrastructure/external/robust-websocket-adapter.ts`
**相关文件**:
- `src/contexts/market-data/infrastructure/external/robust-websocket-adapter.ts`
- `src/contexts/market-data/infrastructure/external/okx-websocket-adapter.ts`
- `src/contexts/market-data/infrastructure/external/coinbase-websocket-adapter.ts`
- `src/contexts/market-data/infrastructure/external/binance-websocket-adapter.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 60. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/market-data/infrastructure/external 中发现相似文件
**文件**: `src/contexts/market-data/infrastructure/external/coingecko-adapter.ts`
**相关文件**:
- `src/contexts/market-data/infrastructure/external/coingecko-adapter.ts`
- `src/contexts/market-data/infrastructure/external/coinbase-adapter.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 61. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/ai-reasoning/domain/services 中发现相似文件
**文件**: `src/contexts/ai-reasoning/domain/services/unified-prediction-engine.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/unified-prediction-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/unified-learning-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 62. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/ai-reasoning/domain/services 中发现相似文件
**文件**: `src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 63. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/ai-reasoning/domain/services 中发现相似文件
**文件**: `src/contexts/ai-reasoning/domain/services/pure-ai-analysis.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/pure-ai-analysis.interface.ts`
- `src/contexts/ai-reasoning/domain/services/learning-analysis.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 64. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/ai-reasoning/domain/services 中发现相似文件
**文件**: `src/contexts/ai-reasoning/domain/services/llm-service.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/llm-service.interface.ts`
- `src/contexts/ai-reasoning/domain/services/llm-provider.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 65. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/market-data/domain/repositories 中发现相似文件
**文件**: `src/contexts/market-data/domain/repositories/price-data-repository.ts`
**相关文件**:
- `src/contexts/market-data/domain/repositories/price-data-repository.ts`
- `src/contexts/market-data/domain/repositories/historical-data-repository.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 66. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-management/presentation/http/controllers 中发现相似文件
**文件**: `src/contexts/user-management/presentation/http/controllers/UserController.ts`
**相关文件**:
- `src/contexts/user-management/presentation/http/controllers/UserController.ts`
- `src/contexts/user-management/presentation/http/controllers/AuthController.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 67. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/data-processing/strategies/sentiment 中发现相似文件
**文件**: `src/shared/infrastructure/data-processing/strategies/sentiment/sentiment-validation-stage.ts`
**相关文件**:
- `src/shared/infrastructure/data-processing/strategies/sentiment/sentiment-validation-stage.ts`
- `src/shared/infrastructure/data-processing/strategies/sentiment/sentiment-calculation-stage.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 68. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/analysis/__tests__/services 中发现相似文件
**文件**: `src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
**相关文件**:
- `src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
- `src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.standard.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 69. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/data-processing/strategies/interfaces 中发现相似文件
**文件**: `src/shared/infrastructure/data-processing/strategies/interfaces/strategy-validation.interface.ts`
**相关文件**:
- `src/shared/infrastructure/data-processing/strategies/interfaces/strategy-validation.interface.ts`
- `src/shared/infrastructure/data-processing/strategies/interfaces/strategy-registry.interface.ts`
- `src/shared/infrastructure/data-processing/strategies/interfaces/strategy-creation.interface.ts`
- `src/shared/infrastructure/data-processing/strategies/interfaces/strategy-configuration.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 70. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/analysis/interfaces/pattern-recognition 中发现相似文件
**文件**: `src/shared/infrastructure/analysis/interfaces/pattern-recognition/pattern-validation.interface.ts`
**相关文件**:
- `src/shared/infrastructure/analysis/interfaces/pattern-recognition/pattern-validation.interface.ts`
- `src/shared/infrastructure/analysis/interfaces/pattern-recognition/pattern-monitoring.interface.ts`
- `src/shared/infrastructure/analysis/interfaces/pattern-recognition/pattern-detection.interface.ts`
- `src/shared/infrastructure/analysis/interfaces/pattern-recognition/pattern-configuration.interface.ts`
- `src/shared/infrastructure/analysis/interfaces/pattern-recognition/pattern-calculation.interface.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 71. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/user-management/domain/services/mfa 中发现相似文件
**文件**: `src/contexts/user-management/domain/services/mfa/IMfaVerificationService.ts`
**相关文件**:
- `src/contexts/user-management/domain/services/mfa/IMfaVerificationService.ts`
- `src/contexts/user-management/domain/services/mfa/IMfaConfigurationService.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 72. [MEDIUM] 相似文件名

**描述**: 目录 src/shared/infrastructure/data-processing/strategies/risk 中发现相似文件
**文件**: `src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.ts`
**相关文件**:
- `src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.ts`
- `src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 73. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/trend-analysis/infrastructure/modules/indicators 中发现相似文件
**文件**: `src/contexts/trend-analysis/infrastructure/modules/indicators/indicators-module.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/modules/indicators/indicators-module.ts`
- `src/contexts/trend-analysis/infrastructure/modules/indicators/basic-indicators-module.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 74. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/ai-reasoning/infrastructure/services/__tests__ 中发现相似文件
**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-starter.test.ts`
**相关文件**:
- `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-starter.test.ts`
- `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-integration.test.ts`
- `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-api.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 75. [MEDIUM] 相似文件名

**描述**: 目录 src/contexts/market-data/infrastructure/external/__tests__ 中发现相似文件
**文件**: `src/contexts/market-data/infrastructure/external/__tests__/binance-websocket-adapter.test.ts`
**相关文件**:
- `src/contexts/market-data/infrastructure/external/__tests__/binance-websocket-adapter.test.ts`
- `src/contexts/market-data/infrastructure/external/__tests__/binance-websocket-adapter-refactored.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 76. [MEDIUM] 相似文件名

**描述**: 目录 scripts 中发现相似文件
**文件**: `scripts/verify-problem-16-solution.ts`
**相关文件**:
- `scripts/verify-problem-16-solution.ts`
- `scripts/verify-problem-12-solution.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 77. [MEDIUM] 相似文件名

**描述**: 目录 scripts 中发现相似文件
**文件**: `scripts/verify-logger-fix.ts`
**相关文件**:
- `scripts/verify-logger-fix.ts`
- `scripts/comprehensive-logger-fix.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 78. [MEDIUM] 相似文件名

**描述**: 目录 scripts 中发现相似文件
**文件**: `scripts/validate-config-migration.ts`
**相关文件**:
- `scripts/validate-config-migration.ts`
- `scripts/simple-config-migration.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 79. [MEDIUM] 相似文件名

**描述**: 目录 scripts 中发现相似文件
**文件**: `scripts/fix-all-fake-implementations.ts`
**相关文件**:
- `scripts/fix-all-fake-implementations.ts`
- `scripts/detect-fake-implementations.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 80. [MEDIUM] 相似文件名

**描述**: 目录 scripts 中发现相似文件
**文件**: `scripts/comprehensive-duplication-analysis.ts`
**相关文件**:
- `scripts/comprehensive-duplication-analysis.ts`
- `scripts/advanced-duplication-analyzer.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 81. [MEDIUM] 相似文件名

**描述**: 目录 scripts/migration 中发现相似文件
**文件**: `scripts/migration/schema-normalization.ts`
**相关文件**:
- `scripts/migration/schema-normalization.ts`
- `scripts/migration/run-schema-normalization.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 82. [MEDIUM] 相似文件名

**描述**: 目录 scripts/maintenance 中发现相似文件
**文件**: `scripts/maintenance/setup-data-protection.ts`
**相关文件**:
- `scripts/maintenance/setup-data-protection.ts`
- `scripts/maintenance/apply-data-protection.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 83. [MEDIUM] 相似文件名

**描述**: 目录 scripts/database 中发现相似文件
**文件**: `scripts/database/unified-database-verification.ts`
**相关文件**:
- `scripts/database/unified-database-verification.ts`
- `scripts/database/final-verification.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 84. [MEDIUM] 相似文件名

**描述**: 目录 scripts/cleanup 中发现相似文件
**文件**: `scripts/cleanup/clean-test-fake-implementations.ts`
**相关文件**:
- `scripts/cleanup/clean-test-fake-implementations.ts`
- `scripts/cleanup/clean-production-fake-implementations.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 85. [MEDIUM] 相似文件名

**描述**: 目录 tests/utils 中发现相似文件
**文件**: `tests/utils/test-data-validator.ts`
**相关文件**:
- `tests/utils/test-data-validator.ts`
- `tests/utils/test-data-selector.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 86. [MEDIUM] 相似文件名

**描述**: 目录 tests/integration 中发现相似文件
**文件**: `tests/integration/binance-integration.test.ts`
**相关文件**:
- `tests/integration/binance-integration.test.ts`
- `tests/integration/ai-modules-integration.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 87. [MEDIUM] 相似文件名

**描述**: 目录 tests/unit/contexts/user-management/presentation 中发现相似文件
**文件**: `tests/unit/contexts/user-management/presentation/UserController.test.ts`
**相关文件**:
- `tests/unit/contexts/user-management/presentation/UserController.test.ts`
- `tests/unit/contexts/user-management/presentation/AuthController.test.ts`
**建议**: 检查文件功能是否重复，考虑合并或重命名
**预计工作量**: 1小时

### 88. [CRITICAL] 命名模式问题

**描述**: 发现同一功能的多个版本: environment-manager.ts
**文件**: `src/shared/infrastructure/config/environment/unified-environment-manager.ts`
**相关文件**:
- `src/shared/infrastructure/config/environment/unified-environment-manager.ts`
- `src/shared/infrastructure/config/environment/environment-manager.ts`
**建议**: 合并重复实现，保留最优版本，建立统一接口
**预计工作量**: 2-4小时

### 89. [HIGH] 分析服务重复

**描述**: 发现6个分析服务重复相关文件
**文件**: `src/tests/integration/core-analysis-services-integration.test.ts`
**相关文件**:
- `src/tests/integration/core-analysis-services-integration.test.ts`
- `src/shared/infrastructure/analysis/unified-analysis-service-manager.ts`
- `src/contexts/trend-analysis/application/trend-analysis-application.service.ts`
- `src/shared/infrastructure/analysis/di/AnalysisServiceBindings.ts`
- `scripts/verify-core-analysis-services-migration.ts`
- `src/tests/integration/core-analysis-services-integration.test.ts`
**建议**: 分析功能重叠，合并相似实现，建立统一接口
**预计工作量**: 4-8小时

### 90. [HIGH] 配置管理重复

**描述**: 发现5个配置管理重复相关文件
**文件**: `src/shared/infrastructure/testing/unified-test-config-manager.ts`
**相关文件**:
- `src/shared/infrastructure/testing/unified-test-config-manager.ts`
- `src/shared/infrastructure/risk/risk-enforcement-config-manager.ts`
- `src/shared/infrastructure/config/unified-config-manager.ts`
- `src/shared/infrastructure/config/dynamic/dynamic-config-manager.ts`
- `src/shared/infrastructure/config/dynamic/config-version-manager.ts`
**建议**: 分析功能重叠，合并相似实现，建立统一接口
**预计工作量**: 4-8小时

### 91. [MEDIUM] 类定义重复

**描述**: 发现重复的类: VectorClock
**文件**: `src/application/version-manager/clock/vector-clock-manager.ts`
**相关文件**:
- `src/application/version-manager/clock/vector-clock-manager.ts`
- `src/contexts/market-data/infrastructure/external/vector-clock-manager.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 92. [MEDIUM] 类定义重复

**描述**: 发现重复的类: RiskLevel
**文件**: `src/contexts/user-management/domain/value-objects/RiskLevel.ts`
**相关文件**:
- `src/contexts/user-management/domain/value-objects/RiskLevel.ts`
- `src/contexts/risk-management/domain/value-objects/risk-level.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 93. [MEDIUM] 类定义重复

**描述**: 发现重复的类: LLMRouter
**文件**: `src/contexts/user-config/infrastructure/services/EnhancedLLMRouter.ts`
**相关文件**:
- `src/contexts/user-config/infrastructure/services/EnhancedLLMRouter.ts`
- `src/contexts/ai-reasoning/infrastructure/llm-providers/llm-router.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 94. [MEDIUM] 类定义重复

**描述**: 发现重复的类: TestWebSocketAdapter
**文件**: `src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts`
**相关文件**:
- `src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts`
- `src/contexts/market-data/infrastructure/websocket/monitoring/__tests__/websocket-monitoring.test.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 95. [MEDIUM] 类定义重复

**描述**: 发现重复的类: FakeImplementationFixer
**文件**: `scripts/fix-all-fake-implementations.ts`
**相关文件**:
- `scripts/fix-all-fake-implementations.ts`
- `scripts/fix/comprehensive-fake-implementation-fixer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 96. [MEDIUM] 类定义重复

**描述**: 发现重复的类: DuplicationAnalyzer
**文件**: `scripts/comprehensive-duplication-analysis.ts`
**相关文件**:
- `scripts/comprehensive-duplication-analysis.ts`
- `scripts/advanced-duplication-analyzer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 97. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: PerformanceMetrics
**文件**: `src/shared/application/application-service-interfaces.ts`
**相关文件**:
- `src/shared/application/application-service-interfaces.ts`
- `src/application/sync/sync-types.ts`
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `scripts/comprehensive-v3-test.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 98. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TestPerformanceMetrics
**文件**: `src/tests/integration/real-performance.test.ts`
**相关文件**:
- `src/tests/integration/real-performance.test.ts`
- `src/tests/integration/learning-system-integration-test.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 99. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: SystemPerformanceMetrics
**文件**: `src/tests/integration/learning-system-integration-test.ts`
**相关文件**:
- `src/tests/integration/learning-system-integration-test.ts`
- `src/shared/infrastructure/types/unified-interfaces.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 100. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ValidationRule
**文件**: `src/application/state-sync/StateDataValidator.ts`
**相关文件**:
- `src/application/state-sync/StateDataValidator.ts`
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/market-data/unified-market-data-processor.ts`
- `src/shared/infrastructure/config/dynamic/dynamic-config.interface.ts`
- `src/contexts/market-data/infrastructure/external/stream-data-cleaning-engine.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 101. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: SyncStatistics
**文件**: `src/application/sync/sync-types.ts`
**相关文件**:
- `src/application/sync/sync-types.ts`
- `src/application/sync/types/sync-types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 102. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: SyncTransformation
**文件**: `src/application/sync/sync-types.ts`
**相关文件**:
- `src/application/sync/sync-types.ts`
- `src/application/sync/types/sync-types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 103. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: QueryOptions
**文件**: `src/shared/domain/repositories/base-repository.interface.ts`
**相关文件**:
- `src/shared/domain/repositories/base-repository.interface.ts`
- `src/shared/infrastructure/database/repository-base-service.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 104. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ILogger
**文件**: `src/shared/core/interfaces/ILogger.ts`
**相关文件**:
- `src/shared/core/interfaces/ILogger.ts`
- `src/shared/application/interfaces/logger.ts`
- `src/shared/infrastructure/logging/logger.interface.ts`
- `src/contexts/user-config/application/services/UserConfigService.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 105. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TradingSignal
**文件**: `src/shared/infrastructure/types/unified-service-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-service-interfaces.ts`
- `src/shared/infrastructure/testing/unified-test-data-generator.ts`
- `src/contexts/trading-execution/domain/services/trading-strategy-engine.ts`
- `src/contexts/learning/infrastructure/analyzers/signal-quality-analyzer.ts`
- `src/contexts/market-data/infrastructure/external/tokenmetrics-adapter.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 106. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: PositionAction
**文件**: `src/shared/infrastructure/types/unified-service-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-service-interfaces.ts`
- `src/contexts/trading-execution/domain/services/position-manager.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 107. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: StrategyPerformance
**文件**: `src/shared/infrastructure/types/unified-service-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-service-interfaces.ts`
- `src/contexts/ai-reasoning/domain/services/knowledge-graph.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 108. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: RiskViolation
**文件**: `src/shared/infrastructure/types/unified-service-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-service-interfaces.ts`
- `src/shared/infrastructure/risk/risk-enforcement-engine.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 109. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: RiskAlert
**文件**: `src/shared/infrastructure/types/unified-service-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-service-interfaces.ts`
- `src/shared/infrastructure/risk/real-time-risk-monitor.ts`
- `src/contexts/trading-execution/domain/services/risk-monitor.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 110. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: RiskMetrics
**文件**: `src/shared/infrastructure/types/unified-service-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-service-interfaces.ts`
- `src/shared/infrastructure/data-processing/strategies/risk/types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 111. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TimeRange
**文件**: `src/shared/infrastructure/types/unified-service-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-service-interfaces.ts`
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
- `src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 112. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TrendPrediction
**文件**: `src/shared/infrastructure/types/unified-service-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-service-interfaces.ts`
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/contexts/trend-analysis/domain/services/trend-prediction.interface.ts`
- `src/contexts/trend-analysis/domain/services/trend-analysis-engine.interface.ts`
- `src/contexts/trend-analysis/domain/value-objects/trend-analysis.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 113. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: CacheEntry
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/contexts/trend-analysis/infrastructure/services/performance-optimizer.ts`
- `src/contexts/ai-reasoning/infrastructure/reasoning/reasoning-chain.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 114. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: CacheStats
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/contexts/market-data/domain/services/multi-layer-cache-system.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 115. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: CacheOptions
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/ai/multi-tier-cache-service.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 116. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: SystemMetrics
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/monitoring/unified-monitoring-manager.ts`
- `src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts`
- `scripts/monitoring/continuous-verification.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 117. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: AlertStats
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/monitoring/unified-alert-system.ts`
- `src/contexts/risk-management/domain/repositories/alert-repository.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 118. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ResourceMetrics
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/performance/concurrency-optimizer.ts`
- `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
- `src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 119. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: LearningInsights
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/contexts/learning/infrastructure/engines/experience-engine.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/learning-analysis.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 120. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: MarketOutcome
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/contexts/learning/infrastructure/engines/experience-engine.ts`
- `src/contexts/ai-reasoning/domain/services/unified-learning-engine.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/unified-learning-engine.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 121. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TimeframeIsolatedParameters
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
- `src/contexts/ai-reasoning/infrastructure/services/multi-timeframe-learning-coordinator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 122. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ComponentHealthCheck
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/health/unified-health-service.ts`
- `src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 123. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: DependencyHealth
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/health/unified-health-service.ts`
- `src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 124. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: RiskLevelProps
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/contexts/user-management/domain/value-objects/RiskLevel.ts`
- `src/contexts/risk-management/domain/value-objects/risk-level.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 125. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: GeometricPattern
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts`
- `src/contexts/trend-analysis/infrastructure/modules/ai-enhancement/pattern-ai-enhancer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 126. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: FetchOptions
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/data-processing/interfaces/IExternalDataAdapter.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 127. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: BatchOperationOptions
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/database/query-manager.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 128. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: SemanticCacheOptions
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/ai/types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 129. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: PaginationOptions
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/ai/types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 130. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: AlertRule
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 131. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: Alert
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/monitoring/unified-alert-system.ts`
- `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 132. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: MonitoringPerformanceMetrics
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 133. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: VersionConflict
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/application/version-manager/types/version-types.ts`
- `src/contexts/market-data/infrastructure/external/vector-clock-manager.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 134. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: PositionInfo
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 135. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: OptimizationSuggestion
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/cache/cache-performance-monitor.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 136. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: MarketCondition
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/analysis/types/WeightingTypes.ts`
- `src/contexts/trend-analysis/infrastructure/config/dynamic-parameter-manager.ts`
- `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
- `src/contexts/learning/infrastructure/engines/experience-engine.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 137. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TimeframeWeights
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/analysis/types/WeightingTypes.ts`
- `src/contexts/learning/infrastructure/optimizers/parameter-optimizer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 138. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: WavePoint
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/analysis/algorithms/ElliottWaveAlgorithms.ts`
- `src/contexts/trend-analysis/infrastructure/services/target-stop-calculator.ts`
- `src/contexts/trend-analysis/infrastructure/services/professional-corrective-wave-analyzer.ts`
- `src/contexts/trend-analysis/infrastructure/services/fibonacci-analyzer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 139. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: QualityMetrics
**文件**: `src/shared/infrastructure/types/unified-interfaces.ts`
**相关文件**:
- `src/shared/infrastructure/types/unified-interfaces.ts`
- `src/shared/infrastructure/data-processing/interfaces/IDataProcessingPipeline.ts`
- `src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts`
- `src/contexts/market-data/infrastructure/external/exchange-router.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 140. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TestContainer
**文件**: `src/shared/infrastructure/testing/unified-integration-test-base.ts`
**相关文件**:
- `src/shared/infrastructure/testing/unified-integration-test-base.ts`
- `src/shared/infrastructure/testing/types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 141. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: PerformanceStats
**文件**: `src/shared/infrastructure/monitoring/enhanced-trading-execution-monitor.ts`
**相关文件**:
- `src/shared/infrastructure/monitoring/enhanced-trading-execution-monitor.ts`
- `src/contexts/ai-reasoning/infrastructure/monitoring/reasoning-performance-monitor.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 142. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: QualityTrendAnalysis
**文件**: `src/shared/infrastructure/data-quality/unified-data-quality-monitor.ts`
**相关文件**:
- `src/shared/infrastructure/data-quality/unified-data-quality-monitor.ts`
- `src/contexts/market-data/infrastructure/external/data-quality-analysis-engine.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 143. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: QualityAlert
**文件**: `src/shared/infrastructure/data-quality/data-quality-dashboard.ts`
**相关文件**:
- `src/shared/infrastructure/data-quality/data-quality-dashboard.ts`
- `src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts`
- `src/contexts/market-data/infrastructure/external/real-time-data-quality-monitor.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 144. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: LatencyMetrics
**文件**: `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
**相关文件**:
- `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
- `src/shared/infrastructure/monitoring/interfaces/unified-monitoring-interfaces.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 145. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: PerformanceReport
**文件**: `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
**相关文件**:
- `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
- `src/shared/infrastructure/di/base/di-performance-monitor.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 146. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: PerformanceSummary
**文件**: `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
**相关文件**:
- `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
- `src/shared/infrastructure/di/base/di-performance-monitor.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 147. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ReportAttachment
**文件**: `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
**相关文件**:
- `src/shared/infrastructure/monitoring/performance/performance-monitoring.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 148. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ThreatIndicator
**文件**: `src/contexts/user-management/domain/services/IThreatDetectionService.ts`
**相关文件**:
- `src/contexts/user-management/domain/services/IThreatDetectionService.ts`
- `src/contexts/user-management/domain/services/threat-detection/IThreatDetector.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 149. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: BehaviorAnomaly
**文件**: `src/contexts/user-management/domain/services/IThreatDetectionService.ts`
**相关文件**:
- `src/contexts/user-management/domain/services/IThreatDetectionService.ts`
- `src/contexts/user-management/domain/services/threat-detection/IBehaviorAnalyzer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 150. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: BehaviorPattern
**文件**: `src/contexts/user-management/domain/services/IThreatDetectionService.ts`
**相关文件**:
- `src/contexts/user-management/domain/services/IThreatDetectionService.ts`
- `src/contexts/user-management/domain/services/threat-detection/IBehaviorAnalyzer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 151. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: RiskFactor
**文件**: `src/contexts/user-management/domain/services/IThreatDetectionService.ts`
**相关文件**:
- `src/contexts/user-management/domain/services/IThreatDetectionService.ts`
- `src/contexts/risk-management/domain/value-objects/risk-assessment.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts`
- `src/contexts/user-management/domain/services/threat-detection/IBehaviorAnalyzer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 152. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TotpOptions
**文件**: `src/contexts/user-management/domain/services/IMfaService.ts`
**相关文件**:
- `src/contexts/user-management/domain/services/IMfaService.ts`
- `src/contexts/user-management/infrastructure/services/TotpService.ts`
- `src/contexts/user-management/domain/services/mfa/IMfaVerificationService.ts`
- `src/contexts/user-management/domain/services/mfa/IMfaCodeGenerator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 153. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: SmsOptions
**文件**: `src/contexts/user-management/domain/services/IMfaService.ts`
**相关文件**:
- `src/contexts/user-management/domain/services/IMfaService.ts`
- `src/contexts/user-management/infrastructure/services/SmsService.ts`
- `src/contexts/user-management/domain/services/mfa/IMfaCodeGenerator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 154. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: EmailOptions
**文件**: `src/contexts/user-management/domain/services/IMfaService.ts`
**相关文件**:
- `src/contexts/user-management/domain/services/IMfaService.ts`
- `src/contexts/user-management/infrastructure/services/EmailVerificationService.ts`
- `src/contexts/user-management/domain/services/mfa/IMfaCodeGenerator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 155. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: BackupCodesOptions
**文件**: `src/contexts/user-management/domain/services/IMfaService.ts`
**相关文件**:
- `src/contexts/user-management/domain/services/IMfaService.ts`
- `src/contexts/user-management/domain/services/mfa/IMfaCodeGenerator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 156. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TrendQualityAssessment
**文件**: `src/contexts/trend-analysis/domain/services/trend-analysis-engine.interface.ts`
**相关文件**:
- `src/contexts/trend-analysis/domain/services/trend-analysis-engine.interface.ts`
- `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 157. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TrendAnalysis
**文件**: `src/contexts/trend-analysis/domain/value-objects/trend-analysis.ts`
**相关文件**:
- `src/contexts/trend-analysis/domain/value-objects/trend-analysis.ts`
- `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 158. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TrendStrengthAnalysis
**文件**: `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
- `src/contexts/learning/infrastructure/analyzers/trend-consistency-analyzer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 159. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: QualityIssue
**文件**: `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
- `src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts`
- `src/contexts/market-data/infrastructure/external/real-time-data-quality-monitor.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 160. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: AlertThreshold
**文件**: `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
- `src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts`
- `src/contexts/risk-management/domain/value-objects/risk-assessment.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 161. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: PivotPoint
**文件**: `src/contexts/trend-analysis/infrastructure/services/professional-pivot-detector.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/services/professional-pivot-detector.ts`
- `src/contexts/trend-analysis/infrastructure/services/pattern-detection-helpers.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 162. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: AIEnhancedPattern
**文件**: `src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts`
- `src/contexts/trend-analysis/infrastructure/modules/ai-enhancement/pattern-ai-enhancer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 163. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: UserProfile
**文件**: `src/contexts/user-config/application/services/UserConfigService.ts`
**相关文件**:
- `src/contexts/user-config/application/services/UserConfigService.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 164. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ApiCredentials
**文件**: `src/contexts/trading-execution/domain/types/dual-track.types.ts`
**相关文件**:
- `src/contexts/trading-execution/domain/types/dual-track.types.ts`
- `src/contexts/trading-execution/domain/entities/trading-account.ts`
- `scripts/development/generate-dual-track-types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 165. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: SyncSettings
**文件**: `src/contexts/trading-execution/domain/types/dual-track.types.ts`
**相关文件**:
- `src/contexts/trading-execution/domain/types/dual-track.types.ts`
- `src/contexts/trading-execution/domain/entities/trading-account.ts`
- `scripts/development/generate-dual-track-types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 166. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: StrategySyncLog
**文件**: `src/contexts/trading-execution/domain/types/dual-track.types.ts`
**相关文件**:
- `src/contexts/trading-execution/domain/types/dual-track.types.ts`
- `scripts/development/generate-dual-track-types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 167. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: PerformanceComparison
**文件**: `src/contexts/trading-execution/domain/types/dual-track.types.ts`
**相关文件**:
- `src/contexts/trading-execution/domain/types/dual-track.types.ts`
- `src/contexts/trading-execution/domain/services/dual-track-monitoring-service.ts`
- `scripts/development/generate-dual-track-types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 168. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: DualTrackStatistics
**文件**: `src/contexts/trading-execution/domain/types/dual-track.types.ts`
**相关文件**:
- `src/contexts/trading-execution/domain/types/dual-track.types.ts`
- `scripts/development/generate-dual-track-types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 169. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ExtendedTradingAccountProps
**文件**: `src/contexts/trading-execution/domain/types/dual-track.types.ts`
**相关文件**:
- `src/contexts/trading-execution/domain/types/dual-track.types.ts`
- `scripts/development/generate-dual-track-types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 170. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ExtendedTradingOrderProps
**文件**: `src/contexts/trading-execution/domain/types/dual-track.types.ts`
**相关文件**:
- `src/contexts/trading-execution/domain/types/dual-track.types.ts`
- `scripts/development/generate-dual-track-types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 171. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ExtendedTradingPositionProps
**文件**: `src/contexts/trading-execution/domain/types/dual-track.types.ts`
**相关文件**:
- `src/contexts/trading-execution/domain/types/dual-track.types.ts`
- `scripts/development/generate-dual-track-types.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 172. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: MarketDepth
**文件**: `src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts`
**相关文件**:
- `src/contexts/trading-execution/domain/interfaces/execution-engine.interface.ts`
- `src/contexts/trading-execution/domain/services/real-slippage-calculator.ts`
- `src/contexts/market-data/infrastructure/services/real-market-data-service.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 173. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: StressTestScenario
**文件**: `src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`
**相关文件**:
- `src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`
- `src/contexts/risk-management/domain/services/ai-risk-analysis-engine.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 174. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ActionPlan
**文件**: `src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts`
**相关文件**:
- `src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 175. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: Milestone
**文件**: `src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts`
**相关文件**:
- `src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 176. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: RiskAssessment
**文件**: `src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts`
**相关文件**:
- `src/contexts/learning/infrastructure/generators/improvement-suggestion-generator.ts`
- `src/contexts/risk-management/domain/value-objects/risk-assessment.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 177. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: Position
**文件**: `src/contexts/risk-management/domain/value-objects/account-info.ts`
**相关文件**:
- `src/contexts/risk-management/domain/value-objects/account-info.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-traceability.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 178. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: MultiTimeframePredictions
**文件**: `src/contexts/learning/infrastructure/analyzers/trend-consistency-analyzer.ts`
**相关文件**:
- `src/contexts/learning/infrastructure/analyzers/trend-consistency-analyzer.ts`
- `src/contexts/learning/infrastructure/analyzers/signal-quality-analyzer.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 179. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: HistoricalPerformance
**文件**: `src/contexts/learning/infrastructure/analyzers/signal-quality-analyzer.ts`
**相关文件**:
- `src/contexts/learning/infrastructure/analyzers/signal-quality-analyzer.ts`
- `src/contexts/ai-reasoning/domain/services/learning-knowledge-base.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 180. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: QualityReport
**文件**: `src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts`
**相关文件**:
- `src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts`
- `src/contexts/market-data/infrastructure/external/data-quality-analysis-engine.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 181. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: DistributionNode
**文件**: `src/contexts/market-data/domain/services/high-performance-data-distribution-network.interface.ts`
**相关文件**:
- `src/contexts/market-data/domain/services/high-performance-data-distribution-network.interface.ts`
- `src/contexts/market-data/infrastructure/external/high-performance-data-distribution-network.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 182. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: IUnifiedPredictionEngine
**文件**: `src/contexts/ai-reasoning/domain/services/unified-prediction-engine.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/unified-prediction-engine.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 183. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: IUnifiedLearningEngine
**文件**: `src/contexts/ai-reasoning/domain/services/unified-learning-engine.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/unified-learning-engine.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/unified-learning-engine.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 184. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: CrossTimeframeInsight
**文件**: `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
- `src/contexts/ai-reasoning/infrastructure/services/multi-timeframe-learning-coordinator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 185. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TimeframeConflict
**文件**: `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 186. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TimeframeLearningUpdate
**文件**: `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 187. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ITimeframeCoordinator
**文件**: `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 188. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ParameterAdjustment
**文件**: `src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts`
- `src/contexts/ai-reasoning/domain/services/parameter-config-center.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 189. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: PerformanceMetric
**文件**: `src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts`
- `src/contexts/ai-reasoning/domain/services/parameter-config-center.interface.ts`
- `src/contexts/ai-reasoning/domain/services/learning-knowledge-base.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 190. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: LearningProgress
**文件**: `src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 191. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ReasoningChain
**文件**: `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 192. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ComplexQuery
**文件**: `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 193. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: SubQuery
**文件**: `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 194. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: Evidence
**文件**: `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 195. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: ReasoningStep
**文件**: `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 196. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: Conclusion
**文件**: `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/reasoning-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 197. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: QualityAssessment
**文件**: `src/contexts/ai-reasoning/domain/services/llm-provider.interface.ts`
**相关文件**:
- `src/contexts/ai-reasoning/domain/services/llm-provider.interface.ts`
- `src/contexts/ai-reasoning/domain/services/learning-knowledge-base.interface.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 198. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: FakeImplementation
**文件**: `scripts/fix-all-fake-implementations.ts`
**相关文件**:
- `scripts/fix-all-fake-implementations.ts`
- `scripts/detect-fake-implementations.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 199. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: CleanupPhase
**文件**: `scripts/duplication-cleanup-plan.ts`
**相关文件**:
- `scripts/duplication-cleanup-plan.ts`
- `scripts/comprehensive-duplication-analysis.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 200. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: CleanupTask
**文件**: `scripts/duplication-cleanup-plan.ts`
**相关文件**:
- `scripts/duplication-cleanup-plan.ts`
- `scripts/cleanup/final-fake-cleanup.ts`
- `scripts/cleanup/clean-production-fake-implementations.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 201. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: DetectionPattern
**文件**: `scripts/detect-specific-duplications.ts`
**相关文件**:
- `scripts/detect-specific-duplications.ts`
- `scripts/monitoring/known-duplications-validator.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 202. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: TestSummary
**文件**: `scripts/verification/verification-reporter.ts`
**相关文件**:
- `scripts/verification/verification-reporter.ts`
- `scripts/verification/global-verification-report.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 203. [MEDIUM] 接口定义重复

**描述**: 发现重复的接口: Recommendation
**文件**: `scripts/verification/verification-reporter.ts`
**相关文件**:
- `scripts/verification/verification-reporter.ts`
- `scripts/maintenance/comprehensive-database-audit.ts`
**建议**: 合并重复定义，建立继承关系或统一接口
**预计工作量**: 1-2小时

### 204. [MEDIUM] 服务层重复

**描述**: 发现14个AI分析服务服务
**文件**: `src/contexts/trend-analysis/infrastructure/services/trend-analysis-engine.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/services/trend-analysis-engine.ts`
- `src/contexts/trend-analysis/infrastructure/services/pure-ai-trend-analysis-engine.ts`
- `src/contexts/trend-analysis/infrastructure/services/key-level-analysis-engine.ts`
- `src/contexts/trend-analysis/infrastructure/services/base-trend-analysis-engine.ts`
- `src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts`
- `src/contexts/trend-analysis/domain/services/trend-analysis-engine.interface.ts`
- `src/contexts/risk-management/infrastructure/services/pure-ai-risk-analysis-engine.ts`
- `src/contexts/risk-management/domain/services/ai-risk-analysis-engine.interface.ts`
- `src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts`
- `src/contexts/ai-reasoning/domain/services/pure-ai-analysis.interface.ts`
- `src/contexts/ai-reasoning/domain/services/learning-analysis.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/pure-ai-analyzer.ts`
- `src/contexts/ai-reasoning/infrastructure/services/learning-analysis-engine.ts`
- `src/contexts/trend-analysis/infrastructure/services/__tests__/key-level-analysis-engine.test.ts`
**建议**: 分析服务职责，合并相似功能，建立服务层次结构
**预计工作量**: 2-4小时

### 205. [MEDIUM] 服务层重复

**描述**: 发现10个市场数据服务服务
**文件**: `src/contexts/trend-analysis/infrastructure/services/real-market-data-provider.ts`
**相关文件**:
- `src/contexts/trend-analysis/infrastructure/services/real-market-data-provider.ts`
- `src/contexts/trend-analysis/infrastructure/services/data-source-health-monitor.ts`
- `src/contexts/trend-analysis/infrastructure/services/data-quality-validator.ts`
- `src/contexts/market-data/infrastructure/services/real-market-data-service.ts`
- `src/contexts/market-data/infrastructure/services/multi-exchange-data-service.ts`
- `src/contexts/market-data/infrastructure/services/data-backfill-service.ts`
- `src/contexts/market-data/application/services/real-data-integration-service.ts`
- `src/contexts/market-data/application/services/market-data-application-service.ts`
- `src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts`
- `src/contexts/market-data/domain/services/high-performance-data-distribution-network.interface.ts`
**建议**: 分析服务职责，合并相似功能，建立服务层次结构
**预计工作量**: 2-4小时

### 206. [MEDIUM] 服务层重复

**描述**: 发现6个认证服务服务
**文件**: `src/contexts/user-management/infrastructure/services/AuthorizationService.ts`
**相关文件**:
- `src/contexts/user-management/infrastructure/services/AuthorizationService.ts`
- `src/contexts/user-management/infrastructure/services/AuthenticationService.ts`
- `src/contexts/user-management/domain/services/IAuthorizationService.ts`
- `src/contexts/user-management/domain/services/IAuthenticationService.ts`
- `src/contexts/trading-execution/infrastructure/services/security-manager.ts`
- `src/contexts/user-management/domain/services/mfa/IMfaSecurityMonitor.ts`
**建议**: 分析服务职责，合并相似功能，建立服务层次结构
**预计工作量**: 2-4小时

### 207. [MEDIUM] 服务层重复

**描述**: 发现8个配置服务服务
**文件**: `src/contexts/user-config/domain/services/user-config-validator.ts`
**相关文件**:
- `src/contexts/user-config/domain/services/user-config-validator.ts`
- `src/contexts/user-config/infrastructure/services/user-config-system-adapter.ts`
- `src/contexts/user-config/infrastructure/services/ConfigHotReloadService.ts`
- `src/contexts/user-config/application/services/UserConfigService.ts`
- `src/contexts/user-config/application/services/UserConfigApplicationService.ts`
- `src/contexts/ai-reasoning/domain/services/parameter-config-center.interface.ts`
- `src/contexts/ai-reasoning/infrastructure/services/parameter-config-center.ts`
- `src/contexts/user-management/domain/services/mfa/IMfaConfigurationService.ts`
**建议**: 分析服务职责，合并相似功能，建立服务层次结构
**预计工作量**: 2-4小时

### 208. [MEDIUM] 服务层重复

**描述**: 发现6个交易服务服务
**文件**: `src/contexts/trading-signals/application/services/signal-generation-application-service.ts`
**相关文件**:
- `src/contexts/trading-signals/application/services/signal-generation-application-service.ts`
- `src/contexts/trading-signals/application/services/real-signal-generation-service.ts`
- `src/contexts/trading-signals/application/services/production-signal-service.ts`
- `src/contexts/trading-execution/application/services/trading-execution-application-service.ts`
- `src/contexts/trading-execution/infrastructure/services/trading-limit-controller.ts`
- `src/contexts/trading-execution/domain/services/trading-strategy-engine.ts`
**建议**: 分析服务职责，合并相似功能，建立服务层次结构
**预计工作量**: 2-4小时

### 209. [MEDIUM] 服务层重复

**描述**: 发现4个风险评估服务服务
**文件**: `src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`
**相关文件**:
- `src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`
- `src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.interface.ts`
- `src/contexts/risk-management/application/services/risk-assessment-application-service.ts`
- `src/contexts/trading-execution/domain/services/risk-monitor.ts`
**建议**: 分析服务职责，合并相似功能，建立服务层次结构
**预计工作量**: 2-4小时


---

**报告生成完成** - 如需重新检测，请运行: `npm run detect:redundant`
