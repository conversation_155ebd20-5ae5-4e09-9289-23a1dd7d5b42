# 架构违规检测报告

**生成时间**: 2025/7/16 13:07:43
**检测器版本**: 1.0.0
**脚本位置**: `scripts/monitoring/architecture-violation-detector.ts`

## 脚本说明

### 基本信息

- **脚本名称**: 架构违规检测器
- **脚本路径**: `scripts/monitoring/architecture-violation-detector.ts`
- **主要功能**: 检测代码架构设计问题和违规
- **输出格式**: Markdown 报告文件

### 特性

- **模块耦合度分析**: 检测循环依赖和过度耦合
- **分层架构验证**: 确保DDD分层架构合规性
- **接口隔离检查**: 识别违反接口隔离原则的设计
- **单一职责评估**: 分析类和模块的职责单一性
- **智能报告生成**: 按严重程度分类并限制详细列表长度

### 使用方法

```bash
# 在 backend-ts 目录下运行
npx tsx scripts/monitoring/architecture-violation-detector.ts
```

### 输出文件

- **报告文件**: `docs/架构违规检测报告.md`
- **控制台输出**: 违规摘要和严重性统计

## 检测器概述

架构违规检测器是一个专业的代码架构质量分析工具，旨在识别和预防常见的架构设计问题。

### 功能模块

1. **模块耦合度分析**: 检测模块间的过度耦合和循环依赖
   - 循环依赖检测：识别模块间的循环引用关系
   - 耦合度评估：分析模块间的依赖强度
   - 依赖图构建：可视化模块依赖关系

2. **分层架构分析**: 验证分层架构的合规性
   - 层级识别：自动识别文件所属的架构层级
   - 违规检测：发现下层依赖上层的架构违规
   - Context边界检查：确保跨Context访问通过公共接口

3. **接口隔离分析**: 检查接口设计的合理性
   - 接口大小检测：识别过大的接口定义
   - 职责分离检查：分析接口方法的职责一致性
   - 接口提取建议：提供接口拆分的具体建议

4. **单一职责分析**: 评估类和模块的职责单一性
   - 类复杂度检测：分析类的方法数量和复杂度
   - 职责混合检查：识别承担多种职责的类
   - 文件长度分析：检测过长的源代码文件

## 检测结果概览

| 指标 | 数值 |
|------|------|
| 总违规数 | 252 |
| 严重违规 | 17 |
| 中等违规 | 47 |
| 轻微违规 | 188 |

## 违规分类统计

- **模块耦合度**: 20个违规
- **分层架构**: 2个违规
- **接口隔离**: 69个违规
- **单一职责**: 161个违规

## 详细违规列表

### 🔗 模块耦合度违规

> **注意**: 共发现 20 个模块耦合度违规，以下显示前 10 个，剩余 10 个未显示。

#### 1. 🟡 文件入度过高 (145)，被过多模块依赖

**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/types/index.ts`
**建议**: 检查是否违反单一职责原则，考虑拆分功能
**详情**: `{
  "inDegree": 145
}`

#### 2. 🟡 文件入度过高 (30)，被过多模块依赖

**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/logging/logger.interface.ts`
**建议**: 检查是否违反单一职责原则，考虑拆分功能
**详情**: `{
  "inDegree": 30
}`

#### 3. 🟡 文件入度过高 (30)，被过多模块依赖

**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/logging/logger-factory.ts`
**建议**: 检查是否违反单一职责原则，考虑拆分功能
**详情**: `{
  "inDegree": 30
}`

#### 4. 🟡 文件入度过高 (23)，被过多模块依赖

**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/config/config-validation.ts`
**建议**: 检查是否违反单一职责原则，考虑拆分功能
**详情**: `{
  "inDegree": 23
}`

#### 5. 🟡 文件入度过高 (19)，被过多模块依赖

**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/error/unified-error-handler.ts`
**建议**: 检查是否违反单一职责原则，考虑拆分功能
**详情**: `{
  "inDegree": 19
}`

#### 6. 🟡 文件入度过高 (22)，被过多模块依赖

**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/logging/interfaces.ts`
**建议**: 检查是否违反单一职责原则，考虑拆分功能
**详情**: `{
  "inDegree": 22
}`

#### 7. 🟡 文件入度过高 (28)，被过多模块依赖

**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/data-processing/interfaces/IDataProcessingPipeline.ts`
**建议**: 检查是否违反单一职责原则，考虑拆分功能
**详情**: `{
  "inDegree": 28
}`

#### 8. 🟡 文件入度过高 (17)，被过多模块依赖

**文件**: `/Users/<USER>/Ai/06/backend-ts/src/contexts/trend-analysis/domain/value-objects/multi-timeframe-data.ts`
**建议**: 检查是否违反单一职责原则，考虑拆分功能
**详情**: `{
  "inDegree": 17
}`

#### 9. 🔴 文件出度过高 (11)，依赖过多模块

**文件**: `src/shared/infrastructure/di/modules/risk-management-container-module.ts`
**建议**: 考虑拆分文件，减少依赖数量，应用依赖倒置原则
**详情**: `{
  "outDegree": 11,
  "dependencies": [
    "/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/types.ts",
    "/Users/<USER>/Ai/06/backend-ts/src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.interface.ts",
    "/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/analysis/interfaces/IFinancialMetricsService.ts",
    "/Users/<USER>/Ai/06/backend-ts/src/contexts/risk-management/domain/interfaces/risk-assessment-application-service.interface.ts",
    "/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/analysis/interfaces/IDynamicWeightingService.ts",
    "/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/analysis/interfaces/IPatternRecognitionService.ts",
    "/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/analysis/interfaces/IMultiTimeframeService.ts",
    "/Users/<USER>/Ai/06/backend-ts/src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts",
    "/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/analysis/services/FinancialMetricsService.ts",
    "/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/config/risk-config.service.ts",
    "/Users/<USER>/Ai/06/backend-ts/src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts"
  ]
}`

#### 10. 🟡 文件入度过高 (39)，被过多模块依赖

**文件**: `/Users/<USER>/Ai/06/backend-ts/src/contexts/user-management/domain/value-objects/UserId.ts`
**建议**: 检查是否违反单一职责原则，考虑拆分功能
**详情**: `{
  "inDegree": 39
}`

### 🏛️ 分层架构违规

#### 1. 🔴 分层架构违规: infrastructure层不应依赖application层

**文件**: `src/application/sync/messaging/message-delivery-service.ts`
**行号**: 11
**建议**: 重构依赖关系，遵循分层架构原则（上层可依赖下层，下层不可依赖上层）
**详情**: `{
  "currentLayer": "infrastructure",
  "targetLayer": "application",
  "dependency": "/Users/<USER>/Ai/06/backend-ts/src/application/sync/system/system-status-manager.ts"
}`

#### 2. 🔴 分层架构违规: infrastructure层不应依赖application层

**文件**: `src/application/sync/messaging/message-delivery-service.ts`
**行号**: 12
**建议**: 重构依赖关系，遵循分层架构原则（上层可依赖下层，下层不可依赖上层）
**详情**: `{
  "currentLayer": "infrastructure",
  "targetLayer": "application",
  "dependency": "/Users/<USER>/Ai/06/backend-ts/src/application/sync/types/sync-types.ts"
}`

### 🔌 接口隔离违规

> **注意**: 共发现 69 个接口隔离违规，以下显示前 10 个，剩余 59 个未显示。

#### 1. 🟢 接口 IStageConfigManager 混合了多种职责: data_access, configuration, data_mutation

**文件**: `src/shared/infrastructure/data-processing/interfaces/IPipelineStageCoordinator.ts`
**行号**: 76
**建议**: 按职责拆分接口，每个接口应该只有一个变化原因
**详情**: `{
  "interfaceName": "IStageConfigManager",
  "responsibilities": [
    "data_access",
    "configuration",
    "data_mutation"
  ]
}`

#### 2. 🟢 接口 IStageExecutor 混合了多种职责: validation, data_access, configuration

**文件**: `src/shared/infrastructure/data-processing/interfaces/IPipelineStageCoordinator.ts`
**行号**: 142
**建议**: 按职责拆分接口，每个接口应该只有一个变化原因
**详情**: `{
  "interfaceName": "IStageExecutor",
  "responsibilities": [
    "validation",
    "data_access",
    "configuration"
  ]
}`

#### 3. 🟢 接口 IConfigurableAdapter 混合了多种职责: data_mutation, configuration, data_access

**文件**: `src/shared/infrastructure/data-processing/interfaces/IExternalDataAdapter.ts`
**行号**: 119
**建议**: 按职责拆分接口，每个接口应该只有一个变化原因
**详情**: `{
  "interfaceName": "IConfigurableAdapter",
  "responsibilities": [
    "data_mutation",
    "configuration",
    "data_access"
  ]
}`

#### 4. 🟢 接口 IRiskConfigMutation 混合了多种职责: data_mutation, configuration, data_access

**文件**: `src/shared/infrastructure/config/interfaces/risk-config-mutation.interface.ts`
**行号**: 12
**建议**: 按职责拆分接口，每个接口应该只有一个变化原因
**详情**: `{
  "interfaceName": "IRiskConfigMutation",
  "responsibilities": [
    "data_mutation",
    "configuration",
    "data_access"
  ]
}`

#### 5. 🟢 接口 IMultiTimeframeService 混合了多种职责: data_access, data_mutation, configuration, validation, calculation

**文件**: `src/shared/infrastructure/analysis/interfaces/IMultiTimeframeService.ts`
**行号**: 145
**建议**: 按职责拆分接口，每个接口应该只有一个变化原因
**详情**: `{
  "interfaceName": "IMultiTimeframeService",
  "responsibilities": [
    "data_access",
    "data_mutation",
    "configuration",
    "validation",
    "calculation"
  ]
}`

#### 6. 🟡 接口 IFinancialMetricsService 包含过多方法 (11)

**文件**: `src/shared/infrastructure/analysis/interfaces/IFinancialMetricsService.ts`
**行号**: 7
**建议**: 拆分大接口为多个小接口，遵循接口隔离原则
**详情**: `{
  "interfaceName": "IFinancialMetricsService",
  "methodCount": 11,
  "methods": [
    "calculateVolatility",
    "calculateCorrelation",
    "calculateBeta",
    "calculateSharpeRatio",
    "calculateReturns",
    "calculateMovingAverage",
    "calculateStandardDeviation",
    "calculateCovariance",
    "runMonteCarloSimulation",
    "calculateRollingMetric",
    "calculateMaxDrawdown"
  ]
}`

#### 7. 🟢 接口 IWeightingStrategy 混合了多种职责: calculation, validation, data_access, configuration, data_mutation

**文件**: `src/shared/infrastructure/analysis/interfaces/IDynamicWeightingService.ts`
**行号**: 17
**建议**: 按职责拆分接口，每个接口应该只有一个变化原因
**详情**: `{
  "interfaceName": "IWeightingStrategy",
  "responsibilities": [
    "calculation",
    "validation",
    "data_access",
    "configuration",
    "data_mutation"
  ]
}`

#### 8. 🟢 接口 IDynamicWeightingService 混合了多种职责: data_access, validation, data_mutation, calculation

**文件**: `src/shared/infrastructure/analysis/interfaces/IDynamicWeightingService.ts`
**行号**: 59
**建议**: 按职责拆分接口，每个接口应该只有一个变化原因
**详情**: `{
  "interfaceName": "IDynamicWeightingService",
  "responsibilities": [
    "data_access",
    "validation",
    "data_mutation",
    "calculation"
  ]
}`

#### 9. 🟢 接口 IInvitationService 混合了多种职责: validation, data_mutation, data_access

**文件**: `src/contexts/user-management/infrastructure/services/InvitationService.ts`
**行号**: 11
**建议**: 按职责拆分接口，每个接口应该只有一个变化原因
**详情**: `{
  "interfaceName": "IInvitationService",
  "responsibilities": [
    "validation",
    "data_mutation",
    "data_access"
  ]
}`

#### 10. 🟡 接口 IAuthenticationService 包含过多方法 (11)

**文件**: `src/contexts/user-management/domain/services/IAuthenticationService.ts`
**行号**: 36
**建议**: 拆分大接口为多个小接口，遵循接口隔离原则
**详情**: `{
  "interfaceName": "IAuthenticationService",
  "methodCount": 11,
  "methods": [
    "authenticate",
    "register",
    "validateInvitationCode",
    "generateTokens",
    "refreshToken",
    "logout",
    "validateAccessToken",
    "validateRefreshToken",
    "revokeAllSessions",
    "changePassword",
    "resetPassword"
  ]
}`

### 🎯 单一职责违规

> **注意**: 共发现 161 个单一职责违规，以下显示前 10 个，剩余 151 个未显示。

#### 1. 🟢 文件过长 (557 行)，可能承担过多职责

**文件**: `src/api/routes/production-signal-router.ts`
**建议**: 考虑拆分文件，将相关功能组织到不同文件中
**详情**: `{
  "lineCount": 557
}`

#### 2. 🟢 文件过长 (516 行)，可能承担过多职责

**文件**: `src/api/routes/auth-routes.ts`
**建议**: 考虑拆分文件，将相关功能组织到不同文件中
**详情**: `{
  "lineCount": 516
}`

#### 3. 🟢 文件过长 (603 行)，可能承担过多职责

**文件**: `src/api/routes/ai-reasoning-router.ts`
**建议**: 考虑拆分文件，将相关功能组织到不同文件中
**详情**: `{
  "lineCount": 603
}`

#### 4. 🟢 文件过长 (584 行)，可能承担过多职责

**文件**: `src/api/routes/admin-routes.ts`
**建议**: 考虑拆分文件，将相关功能组织到不同文件中
**详情**: `{
  "lineCount": 584
}`

#### 5. 🟢 文件过长 (507 行)，可能承担过多职责

**文件**: `src/tests/performance/simple-stress-test.ts`
**建议**: 考虑拆分文件，将相关功能组织到不同文件中
**详情**: `{
  "lineCount": 507
}`

#### 6. 🟢 文件过长 (733 行)，可能承担过多职责

**文件**: `src/tests/performance/production-load-stress-test.ts`
**建议**: 考虑拆分文件，将相关功能组织到不同文件中
**详情**: `{
  "lineCount": 733
}`

#### 7. 🟢 文件过长 (725 行)，可能承担过多职责

**文件**: `src/api/controllers/trend-analysis-controller.ts`
**建议**: 考虑拆分文件，将相关功能组织到不同文件中
**详情**: `{
  "lineCount": 725
}`

#### 8. 🟢 文件过长 (595 行)，可能承担过多职责

**文件**: `src/api/controllers/config-controller.ts`
**建议**: 考虑拆分文件，将相关功能组织到不同文件中
**详情**: `{
  "lineCount": 595
}`

#### 9. 🟢 文件过长 (954 行)，可能承担过多职责

**文件**: `src/api/controllers/ai-analytics-controller.ts`
**建议**: 考虑拆分文件，将相关功能组织到不同文件中
**详情**: `{
  "lineCount": 954
}`

#### 10. 🟢 文件过长 (641 行)，可能承担过多职责

**文件**: `src/tests/integration/learning-system-integration-test.ts`
**建议**: 考虑拆分文件，将相关功能组织到不同文件中
**详情**: `{
  "lineCount": 641
}`

## 架构改进建议

1. **降低耦合度**: 应用依赖倒置原则，使用接口和依赖注入
2. **严格分层**: 确保下层不依赖上层，Context间通过公共接口通信
3. **接口隔离**: 拆分大接口为多个小接口，按职责分离
4. **单一职责**: 确保每个类和模块只有一个变化原因
5. **架构治理**: 建立架构审查机制，定期检查架构合规性
6. **重构计划**: 制定渐进式重构计划，逐步改善架构质量
