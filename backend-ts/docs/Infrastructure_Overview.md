# 后端基础设施概览

本文档简要概述了后端系统中使用的关键基础设施组件。

## 数据库 ORM: Prisma

-   **描述:** Prisma 是一个现代化的 Node.js 和 TypeScript ORM（对象关系映射器）。它极大地简化了数据库访问、结构迁移和类型安全的数据库查询。
-   **在项目中的作用:** 管理所有与 PostgreSQL 数据库的交互，确保数据操作的安全性、高效性，并与预定义的数据库模式保持一致。

## 缓存系统: Redis

-   **描述:** Redis 是一个高性能的内存数据结构存储系统，通常用作数据库缓存和消息代理。
-   **在项目中的作用:** 用于缓存频繁访问的数据（例如市场数据、用户会话），以减轻数据库负载，并显著提升 API 的响应速度。

## 消息队列: Bull

-   **描述:** Bull 是一个基于 Redis 构建的、功能强大的 Node.js 队列系统。
-   **在项目中的作用:** 负责管理后台作业和异步任务（如发送通知、处理大规模数据导入），从而避免这些耗时操作阻塞主应用程序的执行线程。

## Web 框架: Express.js

-   **描述:** Express.js 是一个轻量且灵活的 Node.js Web 应用框架。
-   **在项目中的作用:** 构成了我们 API 服务的核心骨架，负责处理 HTTP 路由、请求/响应周期以及中间件的集成。

## 依赖注入: InversifyJS

-   **描述:** InversifyJS 是一个功能强大且轻量级的 TypeScript/JavaScript 控制反转 (IoC) 容器。
-   **在项目中的作用:** 负责管理服务、仓储和控制器等组件的生命周期及其依赖关系。通过“注入”依赖，它帮助我们创建了解耦、模块化且易于测试的代码。

## 配置管理: Dotenv

-   **描述:** Dotenv 是一个零依赖的模块，它可以将 `.env` 文件中定义的环境变量加载到 `process.env` 中。
-   **在项目中的作用:** 用于管理不同环境（开发、生产）下的应用配置，使 API 密钥、数据库凭证等敏感信息不必硬编码在源代码中。

## 日志系统: Winston

-   **描述:** Winston 是一个功能丰富的 Node.js 日志库，支持多种日志传输方式（如文件、控制台）。
-   **在项目中的作用:** 提供了一个全面的日志系统，用于捕获应用程序的运行事件、错误和调试信息，这对于系统监控和故障排查至关重要。

## 监控: Prom-client

-   **描述:** `prom-client` 是一个客户端库，用于在 Node.js 应用中植入监控代码，以便向 Prometheus 暴露指标。
-   **在项目中的作用:** 收集并暴露关键性能指标（KPIs），如 API 延迟、请求速率和错误率，从而实现实时系统监控和告警。

## HTTP 客户端: Axios

-   **描述:** Axios 是一个基于 Promise 的 HTTP 客户端，可用于浏览器和 Node.js 环境。
-   **在项目中的作用:** 用于与外部服务和第三方 API 进行通信，例如连接加密货币交易所（如 Binance）或数据提供商（如 CoinGecko）。

## 实时通信: Socket.IO / ws

-   **描述:** Socket.IO 和 `ws` 是实现实时、双向和事件驱动通信的库。
-   **在项目中的作用:** 通过将服务器的实时数据（如价格更新、系统通知）主动推送给连接的客户端（如 Web 前端），为应用的实时功能提供支持。

## 容器化: Docker

-   **描述:** Docker 是一个用于在容器中开发、交付和运行应用程序的开放平台。
-   **在项目中的作用:** 将应用程序及其所有环境依赖（库、配置等）打包成一个可移植的容器。这确保了在开发、测试和生产环境之间的一致性，并极大地简化了部署流程。
