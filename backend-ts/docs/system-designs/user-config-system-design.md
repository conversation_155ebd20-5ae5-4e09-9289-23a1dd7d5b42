# 系统设计文档：User-Config (用户配置系统)

**版本**: 1.0
**状态**: ✅ 已生效
**维护团队**: User-Config 团队
**最后更新**: 2025-07-13

---

## 1. 系统概述

### 1.1. 核心价值

**User-Config (用户配置系统)** 是本项目个性化投顾功能的核心基础设施。它的核心价值在于，通过对用户的风险偏好、投资目标、交易习惯等多维度信息进行结构化管理，为下游系统（如交易信号生成、风险管理、趋势分析）提供个性化的用户画像数据，实现真正的"千人千面"智能投顾服务。

### 1.2. 系统目标

- **个性化**: 为每个用户建立独特的风险画像和偏好设置，支持个性化的投资策略推荐。
- **动态性**: 支持用户配置的实时更新和历史追踪，适应用户投资偏好的变化。
- **集成性**: 作为三维输入融合（trend-analysis + risk-management + user-config）的重要一环，与其他系统无缝集成。
- **可靠性**: 依赖项目统一的"数据质量监控"和"错误处理"，确保用户配置数据的准确性和一致性。

---

## 2. 架构设计

`user-config` 系统自身是一个遵循"洋葱架构"（或六边形架构）的、内聚的业务上下文。同时，它作为项目生态中的重要组成部分，与两大核心基础设施紧密协作。

### 2.1. 系统在项目中的位置

```
                                 ┌──────────────────────────┐
                                 │        API Gateway       │
                                 └────────────┬─────────────┘
                                              │ (HTTP Request)
                                              ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                                                                           │
│                          User-Config System                               │
│                                                                           │
│    ┌──────────────────────────┐      ┌──────────────────────────┐         │
│    │ Application Service      ├─────►│    Domain Entities       │         │
│    │ (user-profile-app...)    │      │    & Services            │         │
│    └──────────────────────────┘      └──────────────────────────┘         │
│                 │                                                           │
│                 │ (依赖注入)                                                │
│                 ▼                                                           │
│    ┌───────────────────────────────────────────────────────────────┐      │
│    │                        Infrastructure Layer                     │      │
│    │                                                                 │      │
│    │ ┌──────────────────┐ ┌──────────────────┐ ┌──────────────────┐ │      │
│    │ │ Controllers      │ │ Repositories     │ │ System Adapter   │ │      │
│    │ │                  │ │ (Prisma)         │ │                  │ │      │
│    │ │ Quality Adapter  │ │ Cache Service    │ │ Notification     │ │      │
│    │ └──────────────────┘ └──────────────────┘ └──────────────────┘ │      │
│    └───────────────────────────────────────────────────────────────┘      │
│                                                                           │
└────────────────────────────────┬──────────────────────────────────────────┘
                                 │
           (依赖/调用)           │
           ┌─────────────────────┼─────────────────────┐
           │                     │                     │
           ▼                     ▼                     ▼
┌──────────────────────────┐ ┌──────────────────┐ ┌──────────────────────┐
│ Trading-Signals System   │ │ Risk-Management  │ │ Unified Data Quality │
│ (策略个性化)             │ │ System           │ │ Monitor              │
│                          │ │ (风险控制)       │ │ (数据质量监控)       │
└──────────────────────────┘ └──────────────────┘ └──────────────────────┘
```

### 2.2. 内部核心模块

- **`application`**: 应用服务层，是系统的主要入口和工作流编排器。
- **`domain`**: 领域层，包含了用户配置相关的核心业务逻辑、实体和值对象。
- **`infrastructure`**: 基础设施层，负责与外部世界交互，包含了API控制器、依赖注入配置，以及最重要的——**系统集成适配器**。
- **`infrastructure/services`**: 这是`user-config`系统最重要的集成逻辑所在。每个`Service`负责一个特定的功能领域，例如：
    - `user-profile-application-service.ts`: 用户画像管理服务。
    - `user-preferences-application-service.ts`: 用户偏好管理服务。
    - `user-config-system-adapter.ts`: 系统集成适配器，为其他系统提供用户配置数据。
    - `user-config-quality-adapter.ts`: 数据质量适配器，集成到统一数据质量监控体系。

---

## 3. 核心工作流程

一个典型的用户配置请求，会经过以下流程：

1.  **API触发**: 外部请求通过API Gateway到达`UserProfileController`或`UserPreferencesController`。
2.  **应用服务编排**: `Controller`调用相应的`ApplicationService`的核心方法（如`createUserProfile`、`updateUserPreferences`）。
3.  **领域逻辑处理**: `ApplicationService`会调用领域服务执行核心业务逻辑，如风险画像计算、偏好验证等。
4.  **调用核心基础设施**:
    - 每个`ApplicationService`在执行业务逻辑时，会重度依赖两大核心基础设施：
        - **调用"统一数据质量监控"**: 例如，`UserProfileApplicationService`会通过`UserConfigQualityAdapter`将用户画像变更事件发送到统一数据质量监控系统。
        - **调用"统一错误处理"**: 所有异常都通过`UnifiedErrorHandler`进行统一处理和记录。
5.  **数据持久化**: 通过`PrismaUserProfileRepository`或`PrismaUserPreferencesRepository`将数据存储到数据库。
6.  **系统集成**: `UserConfigSystemAdapter`为其他系统（如`trading-signals`）提供标准化的用户配置数据访问接口。
7.  **返回结果**: `ApplicationService`将处理结果返回给`Controller`，完成响应。

---

## 4. 对核心公共设施的依赖

`user-config`系统的先进性，很大程度上建立在对两大核心公共设施的充分利用之上。

### 4.1. 统一数据质量监控

- **使用方式**: **作为用户配置数据质量的统一监控点**。
- **具体体现**: 系统内的任何数据变更操作都不再直接处理数据质量检查。取而代之的是，它向`UnifiedDataQualityMonitor`发送标准化的数据质量事件。
- **示例**:
    ```typescript
    // UserConfigQualityAdapter 中
    const qualityEvent = this.createDataEvent('userProfile', userId, 'UPDATE', profileData);
    await this.unifiedDataQualityMonitor.assessDataQuality('user-config.user_profiles', [qualityEvent]);
    ```
- **收益**: 使得`user-config`系统完全与数据质量检查的复杂性解耦，自身只需关注于如何管理高质量、标准化的用户配置数据。

### 4.2. 统一错误处理

- **使用方式**: **作为所有异常处理的统一提供者**。
- **具体体现**: 系统内不再包含任何分散的、重复的错误处理逻辑。
    - 需要处理业务异常时，注入并调用 `UnifiedErrorHandler`。
    - 需要记录错误日志时，注入并调用 `UnifiedErrorHandler`。
    - 需要生成错误响应时，注入并调用 `UnifiedErrorHandler`。
- **收益**: 使得`user-config`的业务逻辑更纯粹、更易于理解。错误处理的优化和迭代由统一错误处理团队负责，系统可以自动享受到错误处理升级带来的好处。

---

## 5. "完美运转"的未来工作 (Roadmap)

当前系统在架构层面已非常健康，但要达到"完美运转"的生产标准，还需要在健壮性和可维护性上进行投入。

1.  **实现智能风险评估**: 基于用户行为数据的动态风险画像调整，而不仅仅依赖静态问卷结果。
2.  **外部化个性化参数**: 将用户画像计算、偏好权重等核心参数从代码中剥离，迁移到统一的配置文件中，以实现生产环境的灵活调整。
3.  **建立E2E回归测试**: 创建一个"黄金标准"的端到端测试套件，使用固定的用户数据作为输入，确保对系统任何部分的修改都不会改变最终的用户配置结果，保障长期稳定性。

---

**最后更新**: 2025-07-13
**文档状态**: ✅ 已生效
**问题反馈**: 请联系User-Config团队或架构团队