# 系统设计文档：Risk-Management (风险管理系统)

**版本**: 1.0
**状态**: ✅ 已生效
**维护团队**: Risk-Management 团队
**最后更新**: 2025-07-13

---

## 1. 系统概述

### 1.1. 核心价值

**Risk-Management (风险管理系统)** 是本项目资产安全和风险控制的核心决策引擎。它的核心价值在于，通过对市场风险、持仓风险和流动性风险进行实时、多维度的深度分析，为所有交易决策提供明确的风险边界和仓位约束，并最终生成可供下游系统（如交易执行、策略调整）直接消费的、结构化的风险评估信号。

### 1.2. 系统目标

- **全面性**: 融合市场风险、信用风险、流动性风险、操作风险等多个维度的数据，提供一个360度的风险视图。
- **实时性**: 利用项目统一的"核心分析服务"，进行复杂的风险模型计算、多时间框架风险评估和动态阈值调整。
- **可靠性**: 依赖项目统一的"数据处理架构"，确保所有输入数据的质量和一致性。
- **高性能**: 能够在高并发场景下，快速响应风险评估请求，提供准实时的风险控制支持。

---

## 2. 架构设计

`risk-management` 系统自身是一个遵循"洋葱架构"（或六边形架构）的、内聚的业务上下文。同时，它作为项目生态中的重要组成部分，与两大核心基础设施紧密协作。

### 2.1. 系统在项目中的位置

```
                                 ┌──────────────────────────┐
                                 │        API Gateway       │
                                 └────────────┬─────────────┘
                                              │ (HTTP Request)
                                              ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                                                                           │
│                        Risk-Management System                             │
│                                                                           │
│    ┌──────────────────────────┐      ┌──────────────────────────┐         │
│    │ Application Service      ├─────►│    Domain Entities       │         │
│    │ (risk-assessment-app...) │      │    & Services            │         │
│    └──────────────────────────┘      └──────────────────────────┘         │
│                 │                                                           │
│                 │ (依赖注入)                                                │
│                 ▼                                                           │
│    ┌───────────────────────────────────────────────────────────────┐      │
│    │                        Infrastructure Layer                     │      │
│    │                                                                 │      │
│    │ ┌──────────────────┐ ┌──────────────────┐ ┌──────────────────┐ │      │
│    │ │ Controllers      │ │ Modules (e.g.,   │ │ ...              │ │      │
│    │ │                  │ │ Risk Metrics)    │ │                  │ │      │
│    │ └──────────────────┘ └──────────────────┘ └──────────────────┘ │      │
│    └───────────────────────────────────────────────────────────────┘      │
│                                                                           │
└────────────────────────────────┬──────────────────────────────────────────┘
                                 │
           (依赖/调用)           │
           ┌─────────────────────┴─────────────────────┐
           │                                           │
           ▼                                           ▼
┌──────────────────────────┐              ┌──────────────────────────┐
│ Unified Data Processing  │              │  Core Analysis Services  │
│ Architecture (V3.0)      │              │ (Weighting, Pattern,     │
│ (获取和处理所有外部数据)   │              │  Multi-Timeframe)        │
└──────────────────────────┘              └──────────────────────────┘
```

### 2.2. 内部核心模块

- **`application`**: 应用服务层，是系统的主要入口和工作流编排器。
- **`domain`**: 领域层，包含了风险管理相关的核心业务逻辑、实体和值对象。
- **`infrastructure`**: 基础设施层，负责与外部世界交互，包含了API控制器、依赖注入配置，以及最重要的——**业务模块 (Modules)**。
- **`infrastructure/modules`**: 这是`risk-management`系统最复杂的业务逻辑所在。每个`Module`负责一个特定的风险评估维度，例如：
    - `portfolio-risk-module.ts`: 投资组合风险分析模块。
    - `position-risk-module.ts`: 持仓风险评估模块。
    - `market-risk-module.ts`: 市场风险计算模块。
    - `risk-metrics-calculator.ts`: 风险指标计算器，负责将所有风险评估结果最终融合成一个风险报告。

---

## 3. 核心工作流程

一个典型的风险评估请求，会经过以下流程：

1.  **API触发**: 外部请求通过API Gateway到达`RiskAssessmentController`。
2.  **应用服务编排**: `Controller`调用`RiskAssessmentApplicationService`的核心方法（如`assessPortfolioRisk`）。
3.  **多维度并行分析**: `ApplicationService`会并行地调用多个内部的风险分析`Module`（如`PortfolioRiskModule`, `MarketRiskModule`等）。
4.  **调用核心基础设施**:
    - 每个`Module`在执行分析时，会重度依赖两大核心基础设施：
        - **调用"统一数据处理架构"**: 例如，`MarketRiskModule`会调用`DataProcessingPipeline`并传入`market-risk-strategy`，以获取权威、统一的市场数据。
        - **调用"核心分析服务"**: 例如，`PortfolioRiskModule`会调用`MultiTimeframeService`来分析多时间框架风险；`RiskMetricsCalculator`会调用`DynamicWeightingService`来为不同风险因子分配权重。
5.  **结果融合**: `RiskMetricsCalculator`获取所有`Module`的风险评估结果，并使用`DynamicWeightingService`进行最终的加权融合。
6.  **返回结果**: `ApplicationService`将最终的风险评估报告返回给`Controller`，完成响应。

---

## 4. 对核心公共设施的依赖

`risk-management`系统的先进性，很大程度上建立在对两大核心公共设施的充分利用之上。

### 4.1. 统一数据处理架构 (V3.0)

- **使用方式**: **作为所有外部数据的唯一来源**。
- **具体体现**: 系统内的任何模块（尤其是`MarketRiskModule`）都不再直接调用任何具体的API适配器（如`BinanceAdapter`）。取而代之的是，它向`DataProcessingPipeline`请求一种特定类型的处理**策略**（`strategy`）。
- **示例**:
    ```typescript
    // MarketRiskModule 中
    const marketRiskStrategy = [new MarketDataFetchingStage(), new RiskCalculationStage()];
    const unifiedMarketData = await this.pipeline.process(context, marketRiskStrategy);
    ```
- **收益**: 使得`risk-management`系统完全与外部数据源的复杂性和不一致性解耦，自身只需关注于如何使用高质量、标准化的数据。

### 4.2. 核心分析服务

- **使用方式**: **作为所有核心算法能力的提供者**。
- **具体体现**: 系统内不再包含任何手写的、重复的算法逻辑。
    - 需要识别风险模式时，注入并调用 `PatternRecognitionService`。
    - 需要分配风险权重时，注入并调用 `DynamicWeightingService`。
    - 需要处理多时间框架风险数据时，注入并调用 `MultiTimeframeService`。
- **收益**: 使得`risk-management`的业务逻辑更纯粹、更易于理解。算法的优化和迭代由核心服务团队统一负责，系统可以自动享受到算法升级带来的好处。

---

## 5. "完美运转"的未来工作 (Roadmap)

当前系统在架构层面已非常健康，但要达到"完美运转"的生产标准，还需要在健壮性和可维护性上进行投入。

1.  **实现优雅降级**: 修改`MarketRiskModule`等关键模块，在依赖的核心设施（如`DataProcessingPipeline`）失败时，能够返回一个保守的、带有降级标识的风险评估结果，而不是让整个风险评估流程崩溃。
2.  **外部化风险参数**: 将`DynamicWeightingService`等核心分析服务所依赖的风险参数（如VaR置信度、风险阈值、权重因子）从代码中剥离，迁移到统一的配置文件中，以实现生产环境的灵活调整。
3.  **建立E2E回归测试**: 创建一个"黄金标准"的端到端测试套件，使用固定的模拟持仓和市场数据作为输入，确保对系统任何部分的修改都不会改变最终的风险评估结果，保障长期稳定性。

---

**最后更新**: 2025-07-13
**文档状态**: ✅ 已生效
**问题反馈**: 请联系Risk-Management团队或架构团队
