# 系统设计文档：Trend-Analysis (趋势分析系统)

**版本**: 1.0
**状态**: ✅ 已生效
**维护团队**: Trend-Analysis 团队
**最后更新**: 2025-07-13

---

## 1. 系统概述

### 1.1. 核心价值

**Trend-Analysis (趋势分析系统)** 是本项目的核心决策引擎之一。它的核心价值在于，通过对海量的市场数据进行多维度、多时间框架的深度分析，识别出当前市场的主要趋势、强度和潜在的转折点，并最终生成可供下游系统（如交易执行、风险管理）直接消费的、结构化的趋势分析信号。

### 1.2. 系统目标

- **全面性**: 融合技术面、情绪面、基本面、量化面等多个维度的数据，提供一个360度的市场视图。
- **深度性**: 利用项目统一的“核心分析服务”，进行复杂的模式识别、多时间框架对齐和动态权重计算。
- **可靠性**: 依赖项目统一的“数据处理架构”，确保所有输入数据的质量和一致性。
- **高性能**: 能够在高并发场景下，快速响应分析请求，提供准实时的决策支持。

---

## 2. 架构设计

`trend-analysis` 系统自身是一个遵循“洋葱架构”（或六边形架构）的、内聚的业务上下文。同时，它作为项目生态中的一个重要组成部分，与两大核心基础设施紧密协作。

### 2.1. 系统在项目中的位置

```
                                 ┌──────────────────────────┐
                                 │        API Gateway       │
                                 └────────────┬─────────────┘
                                              │ (HTTP Request)
                                              ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                                                                           │
│                          Trend-Analysis System                            │
│                                                                           │
│    ┌──────────────────────────┐      ┌──────────────────────────┐         │
│    │ Application Service      ├─────►│    Domain Entities       │         │
│    │ (trend-analysis-app...)  │      │    & Services            │         │
│    └──────────────────────────┘      └──────────────────────────┘         │
│                 │                                                           │
│                 │ (依赖注入)
│                 ▼                                                           │
│    ┌───────────────────────────────────────────────────────────────┐      │
│    │                        Infrastructure Layer                     │      │
│    │                                                                 │      │
│    │ ┌──────────────────┐ ┌──────────────────┐ ┌──────────────────┐ │      │
│    │ │ Controllers      │ │ Modules (e.g.,   │ │ ...              │ │      │
│    │ │                  │ │ Sentiment)       │ │                  │ │      │
│    │ └──────────────────┘ └──────────────────┘ └──────────────────┘ │      │
│    └───────────────────────────────────────────────────────────────┘      │
│                                                                           │
└────────────────────────────────┬──────────────────────────────────────────┘
                                 │
           (依赖/调用)           │
           ┌─────────────────────┴─────────────────────┐
           │                                           │
           ▼                                           ▼
┌──────────────────────────┐              ┌──────────────────────────┐
│ Unified Data Processing  │              │  Core Analysis Services  │
│ Architecture (V3.0)      │              │ (Weighting, Pattern,     │
│ (获取和处理所有外部数据)   │              │  Multi-Timeframe)        │
└──────────────────────────┘              └──────────────────────────┘
```

### 2.2. 内部核心模块

- **`application`**: 应用服务层，是系统的主要入口和工作流编排器。
- **`domain`**: 领域层，包含了趋势分析相关的核心业务逻辑、实体和值对象。
- **`infrastructure`**: 基础设施层，负责与外部世界交互，包含了API控制器、依赖注入配置，以及最重要的——**业务模块 (Modules)**。
- **`infrastructure/modules`**: 这是`trend-analysis`系统最复杂的业务逻辑所在。每个`Module`负责一个特定的分析维度，例如：
    - `sentiment-analysis-module.ts`: 情绪分析模块。
    - `fundamental-analysis-module.ts`: 基本面分析模块。
    - `candlestick-pattern-module.ts`: K线形态分析模块。
    - `four-dimension-fusion-coordinator.ts`: 四维度融合协调器，负责将所有分析结果最终融合成一个信号。

---

## 3. 核心工作流程

一个典型的趋势分析请求，会经过以下流程：

1.  **API触发**: 外部请求通过API Gateway到达`TrendAnalysisController`。
2.  **应用服务编排**: `Controller`调用`TrendAnalysisApplicationService`的核心方法（如`analyze`）。
3.  **多维度并行分析**: `ApplicationService`会并行地调用多个内部的分析`Module`（如`SentimentAnalysisModule`, `FundamentalAnalysisModule`等）。
4.  **调用核心基础设施**: 
    - 每个`Module`在执行分析时，会重度依赖两大核心基础设施：
        - **调用“统一数据处理架构”**: 例如，`SentimentAnalysisModule`会调用`DataProcessingPipeline`并传入`sentiment-strategy`，以获取权威、统一的情绪数据。
        - **调用“核心分析服务”**: 例如，`CandlestickPatternModule`会调用`PatternRecognitionService`来识别K线形态；`FusionCoordinator`会调用`DynamicWeightingService`来为不同维度的分析结果分配权重。
5.  **结果融合**: `FourDimensionFusionCoordinator`获取所有`Module`的分析结果，并使用`DynamicWeightingService`进行最终的加权融合。
6.  **返回结果**: `ApplicationService`将最终的融合信号返回给`Controller`，完成响应。

---

## 4. 对核心公共设施的依赖

`trend-analysis`系统的先进性，很大程度上建立在对两大核心公共设施的充分利用之上。

### 4.1. 统一数据处理架构 (V3.0)

- **使用方式**: **作为所有外部数据的唯一来源**。
- **具体体现**: 系统内的任何模块（尤其是`SentimentAnalysisModule`）都不再直接调用任何具体的API适配器（如`FearGreedAdapter`）。取而代之的是，它向`DataProcessingPipeline`请求一种特定类型的处理**策略**（`strategy`）。
- **示例**:
    ```typescript
    // SentimentAnalysisModule 中
    const sentimentStrategy = [new SentimentFetchingStage(), new SentimentCalculationStage()];
    const unifiedSentimentData = await this.pipeline.process(context, sentimentStrategy);
    ```
- **收益**: 使得`trend-analysis`系统完全与外部数据源的复杂性和不一致性解耦，自身只需关注于如何使用高质量、标准化的数据。

### 4.2. 核心分析服务

- **使用方式**: **作为所有核心算法能力的提供者**。
- **具体体现**: 系统内不再包含任何手写的、重复的算法逻辑。
    - 需要识别形态时，注入并调用 `PatternRecognitionService`。
    - 需要分配权重时，注入并调用 `DynamicWeightingService`。
    - 需要处理多时间框架数据时，注入并调用 `MultiTimeframeService`。
- **收益**: 使得`trend-analysis`的业务逻辑更纯粹、更易于理解。算法的优化和迭代由核心服务团队统一负责，系统可以自动享受到算法升级带来的好处。

---

## 5. “完美运转”的未来工作 (Roadmap)

当前系统在架构层面已非常健康，但要达到“完美运转”的生产标准，还需要在健壮性和可维护性上进行投入。

1.  **实现优雅降级**: 修改`SentimentAnalysisModule`等关键模块，在依赖的核心设施（如`DataProcessingPipeline`）失败时，能够返回一个中性的、带有降级标识的结果，而不是让整个分析流程崩溃。
2.  **外部化算法配置**: 将`DynamicWeightingService`等核心分析服务所依赖的算法参数（如阈值、影响因子）从代码中剥离，迁移到统一的配置文件中，以实现生产环境的灵活调整。
3.  **建立E2E回归测试**: 创建一个“黄金标准”的端到端测试套件，使用固定的模拟数据作为输入，确保对系统任何部分的修改都不会改变最终的分析结果，保障长期稳定性。

---

**最后更新**: 2025-07-12
**文档状态**: ✅ 已生效
**问题反馈**: 请联系Trend-Analysis团队或架构团队
