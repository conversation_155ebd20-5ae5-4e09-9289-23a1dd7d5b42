# 系统设计文档：User-Management (用户管理系统)

**版本**: 1.0
**状态**: ✅ 已生效
**维护团队**: User-Management 团队
**最后更新**: 2025-07-14

---

## 1. 系统概述

### 1.1. 核心价值

**User-Management (用户管理系统)** 是本项目安全基础设施的核心组件。它的核心价值在于，通过提供完整的用户身份认证、授权管理、会话控制和安全审计功能，为整个平台构建坚实的安全防护体系，确保只有经过验证的用户才能访问相应的系统资源，并为所有用户操作提供完整的审计追踪。

### 1.2. 系统目标

- **安全性**: 提供多层次的安全防护，包括密码加密、JWT令牌管理、多因素认证和零信任安全模型。
- **可扩展性**: 支持多种认证方式和角色权限模型，适应不断增长的业务需求。
- **审计性**: 完整记录所有用户操作和安全事件，支持合规性要求和安全分析。
- **高可用性**: 依赖项目统一的"错误处理"和"数据质量监控"，确保系统的稳定性和可靠性。

---

## 2. 架构设计

`user-management` 系统自身是一个遵循"洋葱架构"（或六边形架构）的、内聚的业务上下文。同时，它作为项目安全基础设施的核心，为所有其他系统提供认证和授权服务。

### 2.1. 系统在项目中的位置

```
                                 ┌──────────────────────────┐
                                 │        API Gateway       │
                                 │    (认证中间件集成)      │
                                 └────────────┬─────────────┘
                                              │ (HTTP Request + Auth)
                                              ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                                                                           │
│                        User-Management System                             │
│                                                                           │
│    ┌──────────────────────────┐      ┌──────────────────────────┐         │
│    │ Application Service      ├─────►│    Domain Entities       │         │
│    │ (user-mgmt-app...)       │      │    & Services            │         │
│    └──────────────────────────┘      └──────────────────────────┘         │
│                 │                                                           │
│                 │ (依赖注入)                                                │
│                 ▼                                                           │
│    ┌───────────────────────────────────────────────────────────────┐      │
│    │                        Infrastructure Layer                     │      │
│    │                                                                 │      │
│    │ ┌──────────────────┐ ┌──────────────────┐ ┌──────────────────┐ │      │
│    │ │ Auth Controllers │ │ Security Services│ │ Audit Services   │ │      │
│    │ │                  │ │ (JWT, MFA, etc.) │ │                  │ │      │
│    │ │ User Controllers │ │ Repositories     │ │ Threat Detection │ │      │
│    │ └──────────────────┘ └──────────────────┘ └──────────────────┘ │      │
│    └───────────────────────────────────────────────────────────────┘      │
│                                                                           │
└────────────────────────────────┬──────────────────────────────────────────┘
                                 │
           (提供认证服务)         │
           ┌─────────────────────┼─────────────────────┐
           │                     │                     │
           ▼                     ▼                     ▼
┌──────────────────────────┐ ┌──────────────────┐ ┌──────────────────────┐
│ Trading-Signals System   │ │ Risk-Management  │ │ User-Config System   │
│ (需要用户认证)           │ │ System           │ │ (需要用户认证)       │
│                          │ │ (需要用户认证)   │ │                      │
└──────────────────────────┘ └──────────────────┘ └──────────────────────┘
```

### 2.2. 内部核心模块

- **`application`**: 应用服务层，包含用户管理和认证的核心业务逻辑编排。
- **`domain`**: 领域层，包含用户实体、安全策略、权限模型等核心业务概念。
- **`infrastructure`**: 基础设施层，负责与外部世界交互，包含认证服务、安全服务和系统集成。
- **`infrastructure/services`**: 这是`user-management`系统最重要的安全服务所在：
    - `AuthenticationService`: 用户身份认证服务，支持多种认证方式。
    - `AuthorizationService`: 用户授权服务，基于角色和权限的访问控制。
    - `SecurityManager`: 安全管理器，集成威胁检测和零信任安全模型。
    - `AuditService`: 审计服务，记录所有安全相关事件。

---

## 3. 核心工作流程

一个典型的用户认证请求，会经过以下流程：

1.  **认证请求**: 用户通过API Gateway发起登录请求到`AuthController`。
2.  **身份验证**: `AuthController`调用`UserManagementApplicationService`的认证方法。
3.  **多层安全检查**: 
    - **密码验证**: 通过`PasswordService`验证用户凭据。
    - **威胁检测**: `ThreatDetectionService`分析登录行为模式。
    - **设备信任**: `ZeroTrustService`评估设备和网络环境。
4.  **令牌生成**: 验证通过后，`JwtService`生成访问令牌和刷新令牌。
5.  **安全审计**: 
    - `AuditService`记录登录事件到审计日志。
    - `SecurityEventRepository`存储安全事件详情。
6.  **会话管理**: 创建用户会话并存储到`UserSessions`表。
7.  **响应返回**: 返回包含令牌的安全响应给客户端。

### 3.1. 授权工作流程

当其他系统需要验证用户权限时：

1.  **令牌验证**: 通过`EnhancedAuthMiddleware`验证JWT令牌。
2.  **权限检查**: `AuthorizationService`根据用户角色和资源权限进行授权决策。
3.  **审计记录**: 记录访问尝试和授权结果。

---

## 4. 对核心公共设施的依赖

`user-management`系统的安全性和可靠性，建立在对项目核心公共设施的充分利用之上。

### 4.1. 统一错误处理

- **使用方式**: **作为所有安全异常处理的统一提供者**。
- **具体体现**: 系统内的所有认证失败、授权拒绝、安全威胁等异常都通过统一错误处理进行标准化处理。
- **示例**:
    ```typescript
    // AuthenticationService 中
    try {
        await this.validateCredentials(email, password);
    } catch (error) {
        await this.unifiedErrorHandler.handleSecurityError(error, {
            context: 'authentication',
            userId: email,
            ipAddress: request.ip
        });
    }
    ```
- **收益**: 确保所有安全事件都得到一致的处理和记录，提高系统的安全监控能力。

### 4.2. 统一数据质量监控

- **使用方式**: **作为用户数据和安全事件质量的统一监控点**。
- **具体体现**: 所有用户数据变更和安全事件都通过数据质量监控进行验证和分析。
- **收益**: 确保用户数据的完整性和安全事件的准确性，支持安全分析和合规性要求。

---

## 5. 安全特性

### 5.1. 多因素认证 (MFA)

- **支持类型**: TOTP、SMS、邮箱验证
- **实现**: `MfaService`和`TotpService`提供完整的MFA支持
- **存储**: `MfaDevices`表管理用户的MFA设备

### 5.2. 零信任安全模型

- **设备信任**: `DeviceTrust`实体管理设备信任状态
- **行为分析**: `ThreatDetectionService`分析用户行为模式
- **动态授权**: 基于上下文的动态权限评估

### 5.3. 会话管理

- **安全会话**: `UserSessions`表管理用户会话状态
- **令牌轮换**: 支持访问令牌和刷新令牌的安全轮换
- **并发控制**: 支持单设备登录和多设备登录策略

### 5.4. 审计和合规

- **完整审计**: `AuditLog`记录所有用户操作
- **安全事件**: `SecurityEvent`跟踪所有安全相关事件
- **合规支持**: 支持GDPR、SOX等合规性要求

---

## 6. API 设计

### 6.1. 认证端点

- `POST /api/v1/users/register` - 用户注册
- `POST /api/v1/users/login` - 用户登录
- `POST /api/v1/users/logout` - 用户登出
- `POST /api/v1/users/refresh-token` - 刷新令牌

### 6.2. 用户管理端点

- `GET /api/v1/users/profile` - 获取用户资料
- `PUT /api/v1/users/profile` - 更新用户资料
- `PUT /api/v1/users/password` - 修改密码
- `GET /api/v1/users` - 获取用户列表（管理员）

### 6.3. API密钥管理

- `GET /api/v1/api-keys` - 获取API密钥列表
- `POST /api/v1/api-keys` - 创建API密钥
- `DELETE /api/v1/api-keys/:keyId` - 删除API密钥

---

## 7. 数据模型

### 7.1. 核心实体

- **User**: 用户基本信息和认证凭据
- **UserSessions**: 用户会话管理
- **ApiKey**: API密钥管理
- **SecurityEvent**: 安全事件记录
- **AuditLog**: 审计日志
- **MfaDevice**: 多因素认证设备
- **DeviceTrust**: 设备信任管理

### 7.2. 数据库设计

所有表严格遵循项目命名规范：
- 表名使用PascalCase
- 字段名使用camelCase
- 索引名使用camelCase
- 不使用@map注解

---

## 8. "完美运转"的未来工作 (Roadmap)

当前系统在安全架构层面已非常健全，但要达到"完美运转"的生产标准，还需要在以下方面进行投入：

1.  **高级威胁检测**: 集成机器学习算法，实现更智能的异常行为检测。
2.  **联邦身份认证**: 支持SAML、OAuth2.0等企业级身份认证协议。
3.  **安全分析仪表板**: 提供实时的安全监控和分析界面。
4.  **自动化安全响应**: 基于威胁等级的自动化安全响应机制。
5.  **密码策略增强**: 实现更复杂的密码策略和密码历史管理。

---

**最后更新**: 2025-07-14
**文档状态**: ✅ 已生效
**问题反馈**: 请联系User-Management团队或架构团队
