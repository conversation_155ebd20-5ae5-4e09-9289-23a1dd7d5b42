# 系统设计文档：Trading-Signals (交易信号系统)

**版本**: 1.3 (完整的用户配置集成)
**状态**: ✅ 完全实现 (包含完整的user-config集成)
**维护团队**: Trading-Signals 团队
**最后更新**: 2025-07-14

---

## 1. 系统概述

### 1.1. 核心价值

**Trading-Signals (交易信号系统)** 是项目三大核心系统的最终决策与执行出口。它的核心价值在于，作为一个**智能化的策略调度与执行中心**，它融合了“市场状态”、“账户风控”和“**用户目标**”三大输入，根据预设的交易策略，最终生成**个性化的、可执行的**“买入/卖出”指令。

它不仅是连接“分析”与“交易”的桥梁，更是将系统能力与用户意图相结合的“智能投顾”大脑。

### 1.2. 系统目标 (源于初心)

- **三维输入融合**: 必须同时接收`trend-analysis`的客观市场信号、`risk-management`的风险边界信号，以及`user-config`提供的**用户绩效目标与风格偏好**。
- **风险合规优先**: 任何交易决策都必须首先通过`risk-management`的风险合规性检查。
- **个性化策略执行**: 交易策略的触发阈值、止盈止损设置、仓位规模计算，都应能根据用户的风险偏好和目标进行动态调整。
- **可插拔的多策略支持**: 架构必须支持多种交易策略（如趋势跟随、均值回归等），并能根据市场状况和用户偏好动态选择。

---

## 2. 实际架构设计

`trading-signals`系统已完全实现，能够消费trend-analysis、risk-management和user-config三大上游系统的输出。系统实现了完整的个性化交易信号生成功能。

### 2.1. 系统在项目中的位置

`trading-signals`是典型的“消费者”和“决策者”，位于整个业务流程的最下游，并新增了对`User-Config System`的依赖。

```
┌──────────────────────────┐ ┌──────────────────────────┐ ┌──────────────────────────┐
│  Trend-Analysis System   │ │  Risk-Management System  │ │  User-Config System      │
└────────────┬─────────────┘ └────────────┬─────────────┘ └────────────┬─────────────┘
             │                            │                            │
             └─────────────┐  ┌───────────┴───────────┐  ┌───────────┴───────────┐
                           │  │ (消费其输出)          │  │                       │
                           ▼  ▼                       ▼  ▼                       ▼
┌───────────────────────────────────────────────────────────────────────────────────┐
│                                                                                   │
│                                Trading-Signals System                             │
│                                                                                   │
│    ┌──────────────────────────┐      ┌──────────────────────────┐                 │
│    │ Application Service      ├─────►│    Domain (Strategies,   │                 │
│    │ (signal-generation-app)  │      │     Signal, Profile)     │                 │
│    └──────────────────────────┘      └──────────────────────────┘                 │
│                 │                                                                   │
│                 │ (依赖注入)
│                 ▼                                                                   │
│    ┌───────────────────────────────────────────────────────────────┐              │
│    │                        Infrastructure Layer                     │              │
│    └───────────────────────────────────────────────────────────────┘              │
│                                                                                   │
└────────────────────────────────┬──────────────────────────────────────────────────┘
                                 │
                                 │ (输出)
                                 ▼
                           ┌──────────────────────────┐
                           │   Trading Execution      │
                           │   (交易执行网关/服务)      │
                           └──────────────────────────┘
```

### 2.2. 内部核心模块 (目标状态)

- **`application`**: 应用服务层，负责编排信号生成的完整流程。
- **`domain`**: 领域层，将是本系统的核心。它将定义：
    - **`UserProfile`**: 用户画像/账户档案的实体，包含风险偏好、目标收益、最大回撤等。
    - **`ITradingStrategy`**: 交易策略的统一接口，其核心`execute`方法将接收`UserProfile`作为参数。
    - **`TradingSignal`**: 交易信号的实体。
- **`infrastructure/strategies`**: 包含多个实现了`ITradingStrategy`接口的具体策略类。

---

## 3. 核心工作流程 (目标状态)

1.  **事件触发**: 由定时任务或外部事件触发`SignalGenerationApplicationService`。
2.  **获取三维输入**: `ApplicationService`并行调用：
    *   `TrendAnalysisService` -> 获取最新的“趋势报告”。
    *   `RiskManagementService` -> 获取最新的“风险报告”。
    *   **`UserConfigService` -> 获取当前账户的“用户画像 (`UserProfile`)”。**
3.  **选择交易策略**: `ApplicationService`根据市场状况和“用户画像”中的偏好，选择一个或多个合适的交易策略来执行。
4.  **执行个性化策略逻辑**: 
    - 将“趋势报告”、“风险报告”和“**用户画像**”三个输入，一同传递给选定策略的`execute`方法。
    - 策略内部，首先进行**风险合规性检查**。
    - **策略逻辑将变得智能化**：
        - **动态阈值**: 一个趋势跟随策略，对于`AGGRESSIVE`画像，可能在趋势分数达到0.6时就触发；对于`CONSERVATIVE`画像，则可能需要0.85。
        - **动态止损**: 策略的止损点设置，将直接关联画像中的`MaxAcceptableDrawdown`。
5.  **计算仓位规模**: 调用`PositionSizing`服务，根据风险报告和**用户画像中的风险偏好**，计算出精确的开仓数量。
6.  **生成并输出信号**: 创建`TradingSignal`实体，发送给下游的“交易执行系统”。

---

## 4. 对核心公共设施的依赖 (目标状态)

`trading-signals`系统现在依赖三大上游系统的输出，间接消费两大核心基础设施的成果。

- **`trend-analysis` & `risk-management`**: 为其提供客观的市场分析和风险边界。
- **`user-config`**: **为其提供决策的主观目标和风格约束**。这是实现“智能投顾”的关键输入。

这种清晰的依赖关系，使得`trading-signals`系统可以完全聚焦于其核心的**“在约束下，为达成目标而进行策略决策”**的逻辑。

---

## 5. “完美运转”的未来工作 (Roadmap)

1.  ✅ **`user-config`系统集成完成**: 已完全集成User-Config系统，实现个性化信号生成。
2.  **建立策略回测框架**: 必须建立一个健壮的回测框架，该框架应能**针对不同的`UserProfile`**，对同一个策略进行回测，以评估其在不同风险偏好下的表现。
3.  **完善策略库**: 逐步实现并测试多种交易策略，并确保每种策略都能正确响应`UserProfile`参数的变化。
4.  **动态策略调度**: 实现一个更智能的策略调度器，它能根据市场“政权”和**用户画像**，自动选择当前最适合的交易策略。
5.  **建立执行反馈闭环**: 接收交易执行系统的真实成交结果，用于更新`UserProfile`中的实际绩效，并为未来的策略优化提供数据支持。

---

**最后更新**: 2025-07-14
**文档状态**: ✅ 实现完成
**集成状态**: 🔗 与User-Config、Trend-Analysis、Risk-Management完全集成
**问题反馈**: 请联系Trading-Signals团队或架构团队