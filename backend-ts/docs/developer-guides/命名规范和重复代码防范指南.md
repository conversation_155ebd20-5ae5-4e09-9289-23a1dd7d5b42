# 命名规范和重复代码防范指南

## 📋 概述

基于问题1-15的解决经验，本文档制定了严格的命名规范和重复代码防范机制，确保项目代码质量和架构一致性。

## 🚨 禁用的问题前缀

### ❌ 严格禁止使用的前缀
```typescript
// 这些前缀会导致重复实现和架构混乱
enhanced-*     // 如：enhanced-signal-controller.ts
optimized-*    // 如：optimized-trend-analysis-service.ts
intelligent-*  // 如：intelligent-collaboration-service.ts
advanced-*     // 如：advanced-pattern-analyzer.ts
improved-*     // 如：improved-data-processor.ts
better-*       // 如：better-cache-manager.ts
new-*          // 如：new-signal-generator.ts
v2-*           // 如：v2-analysis-engine.ts
```

### ⚠️ 谨慎使用的前缀
```typescript
unified-*      // 仅用于真正的统一实现，需要架构审查
real-*         // 仅用于与测试数据区分的真实数据处理
production-*   // 仅用于生产环境特定实现
```

### ✅ 推荐的命名模式
```typescript
// 基于功能职责的清晰命名
base-*                    // 基类实现
*-service                 // 服务层
*-controller             // 控制器层
*-repository             // 数据访问层
*-adapter                // 适配器模式
*-factory                // 工厂模式
*-manager                // 管理器
*-engine                 // 引擎/核心逻辑
*-calculator             // 计算器
*-validator              // 验证器
*-transformer            // 转换器
```

## 🏗️ 架构命名规范

### 分层架构命名
```typescript
// 应用层
*-application-service.ts     // 应用服务
*-use-case.ts               // 用例

// 领域层
*-entity.ts                 // 实体
*-value-object.ts           // 值对象
*-domain-service.ts         // 领域服务
*-repository.interface.ts   // 仓储接口

// 基础设施层
*-adapter.ts                // 外部适配器
*-repository.ts             // 仓储实现
*-client.ts                 // 外部客户端

// 表现层
*-controller.ts             // 控制器
*-routes.ts                 // 路由
*-middleware.ts             // 中间件
```

### 模块命名规范
```typescript
// 上下文模块
contexts/
├── market-data/           // 市场数据上下文
├── trend-analysis/        // 趋势分析上下文
├── trading-signals/       // 交易信号上下文
├── risk-management/       // 风险管理上下文
├── ai-reasoning/          // AI推理上下文
└── user-management/       // 用户管理上下文

// 共享基础设施
shared/infrastructure/
├── analysis/              // 统一分析服务
├── technical-indicators/  // 技术指标计算
├── logging/              // 日志系统
├── caching/              // 缓存系统
├── database/             // 数据库
├── monitoring/           // 监控系统
└── di/                   // 依赖注入
```

## 🔍 重复代码检测规则

### 自动检测机制
```bash
# 1. 文件名相似度检测
npm run check:similar-names

# 2. 代码重复度检测
npm run check:duplication

# 3. 接口定义重复检测
npm run check:interface-duplication

# 4. 命名规范检测
npm run check:naming-convention
```

### 代码审查检查清单
```markdown
## 命名规范检查
- [ ] 文件名不包含禁用前缀
- [ ] 类名符合PascalCase规范
- [ ] 方法名符合camelCase规范
- [ ] 接口名以I开头（如：IUserService）
- [ ] 类型名以Type结尾（如：UserType）

## 重复代码检查
- [ ] 没有重复的技术指标计算
- [ ] 没有重复的HTTP客户端配置
- [ ] 没有重复的WebSocket连接逻辑
- [ ] 没有重复的缓存操作
- [ ] 没有重复的错误处理逻辑

## 架构合规检查
- [ ] 使用统一的基础设施服务
- [ ] 通过DI容器管理依赖
- [ ] 遵循分层架构原则
- [ ] 使用统一的接口定义
```

## 📝 具体命名示例

### ✅ 正确命名示例
```typescript
// 服务层
export class TrendAnalysisApplicationService
export class MarketDataIntegrationService
export class RiskAssessmentService

// 控制器层
export class ProductionSignalController
export class TrendAnalysisController
export class HealthCheckController

// 基础设施层
export class BaseTrendAnalysisEngine
export class UnifiedTechnicalIndicatorCalculator
export class MultiTierCacheService

// 适配器层
export class BinanceAdapter
export class CoinGeckoAdapter
export class BaseHttpClient
```

### ❌ 错误命名示例
```typescript
// 问题前缀 - 已在问题15中解决
export class EnhancedSignalController        // → ProductionSignalController
export class OptimizedTrendAnalysisService   // → TrendAnalysisApplicationService
export class IntelligentCollaborationService // → TrendCollaborationService
export class AdvancedPatternAnalyzer        // → PatternRecognitionService

// 模糊命名
export class DataProcessor                   // → MarketDataProcessor
export class SignalGenerator               // → TradingSignalGenerator
export class AnalysisEngine                // → TrendAnalysisEngine
```

## 🛡️ 防范机制

### 1. 开发阶段防范
```typescript
// .eslintrc.js 规则
rules: {
  'naming-convention': [
    'error',
    {
      'selector': 'class',
      'format': ['PascalCase'],
      'custom': {
        'regex': '^(?!Enhanced|Optimized|Intelligent|Advanced|Improved|Better|New|V2)',
        'match': true
      }
    }
  ]
}
```

### 2. Git Hook防范
```bash
# pre-commit hook
#!/bin/sh
# 检查文件名是否包含禁用前缀
if git diff --cached --name-only | grep -E "(enhanced-|optimized-|intelligent-|advanced-)"; then
  echo "❌ 错误：文件名包含禁用前缀"
  echo "请参考命名规范文档：docs/developer-guides/命名规范和重复代码防范指南.md"
  exit 1
fi
```

### 3. CI/CD检查
```yaml
# .github/workflows/code-quality.yml
- name: 检查命名规范
  run: |
    npm run check:naming-convention
    npm run check:duplication
    npm run check:similar-names
```

## 📊 问题解决经验总结

### 已解决的重复实现问题
1. **技术指标计算** → `UnifiedTechnicalIndicatorCalculator`
2. **HTTP客户端** → `BaseHttpClient` + 工厂模式
3. **WebSocket连接** → `BaseWebSocketAdapter`
4. **缓存系统** → `MultiTierCacheService`
5. **错误处理** → `UnifiedErrorHandler`
6. **数据库操作** → `BaseRepository`
7. **动态权重分配** → `DynamicWeightingService`
8. **模式识别** → `PatternRecognitionService`
9. **多时间框架分析** → `MultiTimeframeService`
10. **Logger系统** → `UnifiedLogger`
11. **配置管理** → `UnifiedConfigManager`
12. **数据验证转换** → 统一验证转换引擎
13. **监控健康检查** → `UnifiedHealthCheckService`
14. **依赖注入配置** → 统一DI架构
15. **文件命名模式** → 本规范文档

### 核心原则
1. **单一职责** - 每个类/文件只负责一个明确的职责
2. **统一实现** - 相同功能只有一个实现
3. **清晰命名** - 名称能准确反映功能和职责
4. **分层架构** - 严格遵循分层架构原则
5. **接口统一** - 使用统一的接口定义

## 🎯 持续改进

### 定期检查
- **每周** - 运行重复代码检测
- **每月** - 审查命名规范合规性
- **每季度** - 更新命名规范文档

### 团队培训
- **新员工入职** - 必须学习本规范
- **代码审查** - 严格执行规范检查
- **技术分享** - 定期分享最佳实践

---

**制定日期**: 2025-07-12
**基于经验**: 问题1-15解决方案
**适用范围**: 整个项目
**执行级别**: 强制性
