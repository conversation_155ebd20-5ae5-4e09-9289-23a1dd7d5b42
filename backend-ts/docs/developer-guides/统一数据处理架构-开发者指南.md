# 统一数据处理架构-开发者指南 V2.0

**版本**: 2.0  
**状态**: ✅ 已生效 (V3.0架构完全实现)  
**维护团队**: 架构团队  
**最后更新**: 2025-01-12

## 1. 概述

本文档旨在为项目提供一套统一、标准、可扩展的外部数据接入与处理规范。所有新的外部API对接和数据处理逻辑，都应遵循本文档描述的架构模式。

本规范的核心是**V3.0统一策略引擎**，它通过"适配器+策略+管理器"模式，构建了一个既能统一管理通用数据获取逻辑，又能灵活应对不同数据处理需求的强大框架。

### 核心优势
- **零重复获取逻辑**: 所有适配器继承统一基类，自动获得缓存、重试、限流、日志等通用功能。
- **统一处理入口**: 所有类型的数据处理都通过单一的策略引擎，避免了处理逻辑的分散和冲突。
- **高度可扩展**: 接入新数据源或定义新处理流程，只需创建新的"适配器"和"策略"，无需修改核心引擎。
- **职责清晰**: 适配器负责"获取"，策略负责"定义"，阶段负责"实现"，管理器负责"协调"，消费者负责"使用"。
- **完整监控**: 策略执行全程监控，包括性能指标、成功率、错误追踪等。
- **智能管理**: 策略缓存、超时控制、重试机制、质量评估等高级功能。

---

## 2. 核心架构

我们的数据处理架构分为三层：**数据获取层**、**数据处理层**和**策略管理层**。

### 2.1. 架构图

```
                                 ┌──────────────────────────┐
                                 │    StrategyManager       │
                                 │  (策略管理和监控)        │
                                 └────────────┬─────────────┘
                                              │
                                              │ executeStrategy(config, data, context)
                                              │
                                 ┌────────────▼─────────────┐
                                 │  DataProcessingPipeline  │
                                 │  (统一策略引擎)          │
                                 └────────────┬─────────────┘
                                              │
                                              │ process(context, strategy)
                                              │
           ┌──────────────────────────────────┴──────────────────────────────────┐
           │ ProcessingStrategy (一个由多个"阶段"组成的数组，定义处理流程)      │
           └───────────────────────────────────────────────────────────────────┘
                                              │
           ┌──────────────────────────────────┴──────────────────────────────────┐
           │ IProcessingStage (单个处理阶段，如获取、计算、验证)                │
           └──────────────────────────────────┬──────────────────────────────────┘
                                              │
                                              │ (在"获取阶段"中调用)
                                              ▼
┌──────────────────────────┐      ┌──────────────────────────┐      ┌──────────────────────────┐
│  BinanceAdapter          │      │  FearGreedAdapter        │      │  SentiCryptAdapter       │
│ (继承自Base)             │      │ (继承自Base)             │      │ (继承自Base)             │
└────────────┬─────────────┘      └────────────┬─────────────┘      └────────────┬─────────────┘
             │                                │                                │
             └───────────────┐                │                ┌───────────────┘
                             ▼                ▼                ▼
                           ┌────────────────────────────────────┐
                           │    ExternalDataAdapterBase         │
                           │ (提供通用获取能力：缓存、重试等)   │
                           └────────────────────────────────────┘
```

### 2.2. 核心组件

#### a. ExternalDataAdapterBase (获取层)
- **位置**: `src/shared/infrastructure/data-processing/adapters/external-data-adapter-base.ts`
- **功能**: 所有外部数据适配器的**基类**。它封装了所有通用的API交互逻辑（HTTP请求、缓存、重试、限流、日志）。
- **开发者职责**: 创建新的适配器时，**必须**继承此类。

#### b. DataProcessingPipeline (处理层)
- **位置**: `src/shared/infrastructure/data-processing/data-processing-pipeline.ts`
- **功能**: **统一的策略执行引擎**。它是系统内所有"适配器之后"的数据处理的唯一入口。
- **核心方法**: `process(context, strategy?)`。当传入`strategy`时，它会按顺序执行策略中的所有阶段；否则，执行默认流程。

#### c. StrategyManager (管理层) - V3.0新增
- **位置**: `src/shared/infrastructure/data-processing/strategies/strategy-manager.ts`
- **功能**: **策略执行管理器**。提供策略执行、监控、缓存、超时控制等高级功能。
- **核心方法**: `executeStrategy(config, rawData, context, options)`。

#### d. StrategyFactory (工厂层) - V3.0新增
- **位置**: `src/shared/infrastructure/data-processing/strategies/strategy-factory.ts`
- **功能**: **策略工厂**。统一创建和管理各种处理策略。
- **核心方法**: `createStrategy(config)`。

#### e. StrategyMonitor (监控层) - V3.0新增
- **位置**: `src/shared/infrastructure/data-processing/strategies/strategy-monitor.ts`
- **功能**: **策略监控器**。实时监控策略执行性能、成功率、错误率等指标。
- **核心方法**: `recordExecution(record)`、`getHealthStatus()`。

#### f. IProcessingStage & ProcessingStrategy (处理层)
- **`IProcessingStage`**: 一个处理阶段的**接口**，定义了处理逻辑的最小单元。
- **`ProcessingStrategy`**: 一个由多个`IProcessingStage`实例组成的**数组**，完整定义了处理某一类数据的端到端流程。

---

## 3. 实现规范：如何集成一种新数据类型

以集成"链上数据(On-Chain Data)"为例，完整步骤如下：

### 步骤1：创建数据适配器 (Adapter)

为每一个新的链上数据API源，在`src/contexts/market-data/infrastructure/external/`下创建一个新的适配器文件。

- **必须**继承 `ExternalDataAdapterBase`。
- **必须**在构造函数中调用 `super(adapterConfig)` 并传入该API的配置。
- **职责**: 实现与该API交互的方法，并调用基类的 `this.fetchAndProcess(...)` 来获取数据，然后将返回的原始数据转换为初步的、有类型的对象。

```typescript
// src/contexts/market-data/infrastructure/external/glassnode-adapter.ts

@injectable()
export class GlassnodeAdapter extends ExternalDataAdapterBase implements IGlassnodeAdapter {
  readonly adapterName = 'glassnode';
  readonly sourceName = 'glassnode';

  constructor() {
    super({ 
      name: 'glassnode', 
      baseUrl: 'https://api.glassnode.com',
      timeout: 10000,
      retryConfig: { maxRetries: 3, baseDelay: 1000, maxDelay: 5000 }
    });
  }

  async getSopr(): Promise<SoprData> {
    const rawData = await this.fetchAndProcess<any>(
      '/v1/metrics/indicators/sopr',
      {
        useCache: true,
        cacheKey: 'sopr-data',
        timeout: 15000
      },
      {
        dataType: 'on-chain',
        businessSystem: 'market-data',
        metadata: { source: 'glassnode', metric: 'sopr' }
      }
    );
    return this.transformSoprData(rawData);
  }

  private transformSoprData(data: any): SoprData {
    // 数据转换逻辑
    return {
      value: data.value,
      timestamp: new Date(data.timestamp * 1000),
      // ... 其他字段
    };
  }
}
```

### 步骤2：创建处理阶段 (Stages)

在 `src/shared/infrastructure/data-processing/strategies/` 目录下，为链上数据处理策略创建所需的`Stage`类。

- **必须**实现 `IProcessingStage` 接口。
- **职责**: 每个`Stage`应只关注一个单一的处理职责。

```typescript
// src/shared/infrastructure/data-processing/strategies/on-chain/on-chain-fetching-stage.ts
export class OnChainFetchingStage implements IProcessingStage {
  readonly stageName = 'on-chain-fetching';

  constructor(
    private readonly logger: ILogger,
    private readonly coinMetricsAdapter: CoinMetricsAdapter,
    private readonly glassnodeAdapter: GlassnodeAdapter
  ) {}

  async process(context: ProcessingContext, _input: any): Promise<any> {
    this.logger.info('开始获取链上数据', { context });

    // 并行调用多个适配器
    const [coinMetricsData, glassnodeData] = await Promise.allSettled([
      this.coinMetricsAdapter.getOnChainMetrics(context.symbol),
      this.glassnodeAdapter.getSopr()
    ]);

    return {
      coinMetrics: coinMetricsData.status === 'fulfilled' ? coinMetricsData.value : null,
      glassnode: glassnodeData.status === 'fulfilled' ? glassnodeData.value : null,
      timestamp: new Date()
    };
  }
}

// src/shared/infrastructure/data-processing/strategies/on-chain/on-chain-calculation-stage.ts
export class OnChainCalculationStage implements IProcessingStage {
  readonly stageName = 'on-chain-calculation';

  constructor(private readonly logger: ILogger) {}

  async process(context: ProcessingContext, fetchedData: any): Promise<UnifiedOnChainData> {
    this.logger.info('开始计算链上指标', { context });

    // 执行复杂的融合计算
    const unifiedMetrics = this.calculateUnifiedMetrics(fetchedData);
    
    return {
      symbol: context.symbol,
      metrics: unifiedMetrics,
      dataQuality: this.assessDataQuality(fetchedData),
      timestamp: new Date()
    };
  }

  private calculateUnifiedMetrics(data: any): any {
    // 复杂的链上指标计算逻辑
    return {
      networkValue: this.calculateNetworkValue(data),
      onChainActivity: this.calculateActivity(data),
      // ... 其他指标
    };
  }
}
```

### 步骤3：定义并使用处理策略

在需要使用统一链上数据的消费者服务中，使用V3.0策略管理器来执行处理策略。

```typescript
// 位于某个应用服务中
@injectable()
export class OnChainAnalysisService {
  constructor(
    @inject(TYPES.DataProcessing.StrategyManager)
    private readonly strategyManager: StrategyManager
  ) {}

  async getUnifiedOnChainData(symbol: string): Promise<UnifiedOnChainData> {
    // 1. 定义策略配置
    const strategyConfig = {
      type: StrategyType.ON_CHAIN_STANDARD,
      options: {
        includeNetworkMetrics: true,
        includeActivityMetrics: true,
        dataQualityThreshold: 0.7
      }
    };

    // 2. 定义处理上下文
    const context: ProcessingContext = {
      source: 'on-chain-analysis',
      dataType: 'on-chain',
      businessSystem: 'market-data',
      symbol,
      timestamp: new Date()
    };

    // 3. 使用策略管理器执行
    const result = await this.strategyManager.executeStrategy<UnifiedOnChainData>(
      strategyConfig,
      null, // 初始数据为空，由第一个阶段获取
      context,
      {
        enableMonitoring: true,
        timeout: 30000,
        retryCount: 2,
        qualityThreshold: 0.7
      }
    );

    return result.data;
  }
}
```

### 步骤4：注册策略到工厂 (可选)

如果需要预定义策略，可以在策略注册表中注册：

```typescript
// 在策略工厂初始化时注册
StrategyRegistry.register('on-chain-standard', {
  type: StrategyType.ON_CHAIN_STANDARD,
  options: {
    includeNetworkMetrics: true,
    includeActivityMetrics: true,
    dataQualityThreshold: 0.7
  }
});

// 使用预定义策略
const strategyConfig = StrategyRegistry.get('on-chain-standard');
```

---

## 4. 核心API参考

### ExternalDataAdapterBase
- `constructor(config)`: 初始化适配器，传入API配置。
- `fetchAndProcess(endpoint, options, context)`: **必须使用**。用于执行API请求，自动处理缓存、重试等。
- `getHealthStatus()`: 获取适配器健康状态。
- `testConnection()`: 测试API连接。

### DataProcessingPipeline
- `process(context, strategy?)`: **必须使用**。数据处理的唯一入口。传入`strategy`来执行自定义流程。
- `processBatch(dataItems)`: 批量处理数据。
- `getHealthStatus()`: 获取管道健康状态。

### StrategyManager (V3.0新增)
- `executeStrategy(config, rawData, context, options)`: **推荐使用**。执行策略并提供完整的监控和管理功能。
- `getStrategyMetrics(strategyType)`: 获取策略执行指标。
- `getHealthStatus()`: 获取策略系统健康状态。
- `preloadStrategy(config)`: 预加载策略以提升性能。

### StrategyFactory (V3.0新增)
- `createStrategy(config)`: 创建处理策略。
- `getSupportedStrategies()`: 获取支持的策略类型。
- `validateStrategyConfig(config)`: 验证策略配置。

### StrategyMonitor (V3.0新增)
- `recordExecution(record)`: 记录策略执行。
- `getMetrics(strategyType)`: 获取策略指标。
- `getHealthStatus()`: 获取监控系统健康状态。

---

## 5. 常见问题与最佳实践

### Q: 何时需要创建新适配器？
**A**: 当你需要从一个新的第三方API端点获取数据时。

### Q: 何时需要创建新策略？
**A**: 当你需要处理一种新的**业务数据类型**时（如情绪、链上、衍生品），或者你需要一种与现有策略不同的、全新的处理流程时。

### Q: 应该使用DataProcessingPipeline还是StrategyManager？
**A**: **推荐使用StrategyManager**。它提供了更完整的功能，包括监控、缓存、超时控制等。DataProcessingPipeline主要用于向后兼容。

### Q: `Stage`应该是无状态的吗？
**A**: 是的。`Stage`应该是无状态、可复用的。所有它需要的数据都应该从`context`或上一个`Stage`的输出中获取。如果需要依赖其他服务，应通过构造函数注入。

### Q: 如何监控策略执行性能？
**A**: 使用StrategyManager执行策略时，会自动记录性能指标。可以通过`getStrategyMetrics()`获取详细的执行统计信息。

### Q: 如何保证向后兼容？
**A**: `DataProcessingPipeline`的`process`方法是向后兼容的。不传入`strategy`参数，就会执行默认流程，不会影响旧代码。

### Q: 策略执行失败时如何处理？
**A**: StrategyManager提供了自动重试机制。可以在执行选项中配置`retryCount`。同时，所有错误都会被记录到监控系统中。

---

## 6. 实际使用示例

### 6.1 情绪分析策略使用示例

```typescript
@injectable()
export class SentimentAnalysisModule {
  constructor(
    @inject(TYPES.DataProcessing.StrategyManager)
    private readonly strategyManager: StrategyManager
  ) {}

  async analyzeSentiment(request: SentimentAnalysisRequest): Promise<SentimentAnalysisResult> {
    const strategyConfig = {
      type: StrategyType.SENTIMENT_STANDARD,
      options: {
        includeSocial: request.includeSocial,
        includeFearGreed: request.includeFearGreed,
        includeNews: request.includeNews,
        includeCommunity: request.includeCommunity
      }
    };

    const context: ProcessingContext = {
      source: 'sentiment-analysis',
      dataType: 'sentiment',
      businessSystem: 'trend-analysis',
      symbol: request.symbol.symbol,
      timeframe: request.timeframe
    };

    const result = await this.strategyManager.executeStrategy<ComprehensiveSentimentAnalysis>(
      strategyConfig,
      null,
      context,
      StrategyManagerHelper.createSentimentExecutionOptions()
    );

    return this.convertToSentimentResult(result.data);
  }
}
```

### 6.2 数据集成服务使用示例

```typescript
@injectable()
export class RealDataIntegrationService {
  constructor(
    @inject(TYPES.DataProcessing.StrategyManager)
    private readonly strategyManager: StrategyManager
  ) {}

  private async getSentimentData(symbol: string): Promise<IntegratedMarketData['sentiment']> {
    try {
      // 使用V3.0策略管理器获取情绪数据
      const strategyConfig = {
        type: StrategyType.SENTIMENT_QUICK,
        options: { includeFearGreed: true, includeSocial: true }
      };

      const context: ProcessingContext = {
        source: 'data-integration',
        dataType: 'sentiment',
        businessSystem: 'market-data',
        symbol
      };

      const result = await this.strategyManager.executeStrategy(
        strategyConfig,
        null,
        context,
        { enableMonitoring: true, timeout: 10000 }
      );

      return this.transformToIntegratedFormat(result.data);
    } catch (error) {
      this.logger.error('V3.0情绪数据获取失败', { error, symbol });
      throw error;
    }
  }
}
```

---

## 7. 相关文件索引

### 核心基础设施
- `src/shared/infrastructure/data-processing/adapters/external-data-adapter-base.ts` - **适配器基类**
- `src/shared/infrastructure/data-processing/data-processing-pipeline.ts` - **统一策略引擎**
- `src/shared/infrastructure/data-processing/strategies/strategy-manager.ts` - **策略管理器**
- `src/shared/infrastructure/data-processing/strategies/strategy-factory.ts` - **策略工厂**
- `src/shared/infrastructure/data-processing/strategies/strategy-monitor.ts` - **策略监控器**
- `src/shared/infrastructure/data-processing/interfaces/IDataProcessingPipeline.ts` - **核心接口定义**

### 策略实现示例
- `src/shared/infrastructure/data-processing/strategies/sentiment/` - 情绪分析策略实现
- `src/shared/infrastructure/data-processing/executors/` - 各种处理阶段执行器

### 适配器实现示例
- `src/contexts/market-data/infrastructure/external/fear-greed-adapter.ts` - V3.0适配器优秀实现范例
- `src/contexts/market-data/infrastructure/external/binance-adapter.ts` - V3.0适配器优秀实现范例
- `src/contexts/market-data/infrastructure/external/senticrypt-adapter.ts` - V3.0适配器优秀实现范例

### 文档和测试
- `src/shared/infrastructure/data-processing/adapters/README.md` - 适配器开发指南
- `src/shared/infrastructure/data-processing/tests/` - 测试用例
- `backend-ts/docs/数据处理架构问题修复完成报告.md` - 架构修复报告

---

## 8. 版本历史

### V2.0 (2025-01-12) - 当前版本
- ✅ 完全实现V3.0策略模式
- ✅ 新增StrategyManager、StrategyFactory、StrategyMonitor
- ✅ 清理所有重复实现，统一依赖注入
- ✅ 完善监控和管理功能
- ✅ 更新文档反映真实实现状态

### V1.0 (2025-07-12) - 初始版本
- ✅ 基础架构设计
- ✅ ExternalDataAdapterBase实现
- ✅ DataProcessingPipeline基础功能
- ✅ 适配器重构完成

---

**最后更新**: 2025-01-12
**文档状态**: ✅ 生效中 (完全反映真实实现状态)
**问题反馈**: 请联系架构团队
