# 防重复实现和组件统一使用指南

## 🎯 核心原则

**一个功能，一个实现，一个地方**

本指南为开发者提供明确的统一组件使用规范，确保代码复用和架构一致性。

## � 统一组件使用手册

### 🌐 网络通信组件

#### HTTP客户端 - 必须使用
```typescript
// ✅ 正确使用方式
import { BaseHttpClient } from '@shared/infrastructure/http/base-http-client';
import { ExternalDataAdapterBase } from '@shared/infrastructure/http/external-data-adapter-base';

@injectable()
export class BinanceApiService extends ExternalDataAdapterBase {
  constructor(@inject(TYPES.Shared.HttpClient) httpClient: BaseHttpClient) {
    super(httpClient);
  }

  async getMarketData(symbol: string) {
    return await this.get(`/api/v3/ticker/24hr?symbol=${symbol}`);
  }
}

// ❌ 禁止直接使用
import axios from 'axios';  // 禁止在应用代码中使用
const client = axios.create();  // 禁止创建HTTP客户端
```

#### WebSocket连接 - 必须使用
```typescript
// ✅ 正确使用方式
import { BaseWebSocketAdapter } from '@shared/infrastructure/websocket/base-websocket-adapter';

@injectable()
export class MarketDataWebSocket extends BaseWebSocketAdapter {
  constructor(@inject(TYPES.Shared.WebSocketServer) wsServer: WebSocketServer) {
    super(wsServer);
  }

  protected handleMessage(message: any) {
    // 处理WebSocket消息
  }
}
```

### 💾 数据存储组件

#### 缓存系统 - 必须使用
```typescript
// ✅ 正确使用方式
import { MultiTierCacheService } from '@shared/infrastructure/cache/multi-tier-cache-service';
import { CacheManager } from '@shared/infrastructure/cache/cache-manager';

@injectable()
export class MarketDataService {
  constructor(
    @inject(TYPES.Shared.MultiTierCacheService) private cache: MultiTierCacheService
  ) {}

  async getPriceData(symbol: string) {
    const cacheKey = `price:${symbol}`;
    let data = await this.cache.get(cacheKey);

    if (!data) {
      data = await this.fetchFromApi(symbol);
      await this.cache.set(cacheKey, data, 60); // 缓存60秒
    }

    return data;
  }
}

// ❌ 禁止直接使用
import Redis from 'ioredis';  // 禁止直接使用Redis
const redis = new Redis();  // 禁止创建Redis实例
class MyCache { ... }  // 禁止创建自定义缓存
```

#### 数据库操作 - 必须使用
```typescript
// ✅ 正确使用方式
import { BaseRepository } from '@shared/infrastructure/database/base-repository';

@injectable()
export class UserRepository extends BaseRepository<User> {
  constructor(@inject(TYPES.Shared.Database) prisma: PrismaClient) {
    super(prisma);
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.findFirst({ where: { email } });
  }
}

// ❌ 禁止直接使用
const prisma = new PrismaClient();  // 禁止直接创建PrismaClient
```

### 📊 数据分析组件

#### 技术指标计算 - 必须使用
```typescript
// ✅ 正确使用方式
import { UnifiedTechnicalIndicatorCalculator } from '@shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';

@injectable()
export class TradingStrategy {
  constructor(
    @inject(TYPES.Shared.TechnicalIndicatorCalculator)
    private calculator: UnifiedTechnicalIndicatorCalculator
  ) {}

  async analyzeMarket(symbol: string) {
    // 使用统一计算器
    const rsi = await this.calculator.calculateRSI(symbol, 14);
    const macd = await this.calculator.calculateMACD(symbol);
    const sma = await this.calculator.calculateSMA(symbol, 20);

    return { rsi, macd, sma };
  }
}

// ❌ 禁止重复实现
private calculateRSI(prices: number[]) { ... }  // 禁止
private calculateMACD(data: any[]) { ... }  // 禁止
private calculateSMA(prices: number[], period: number) { ... }  // 禁止
```

#### 核心分析服务 - 必须使用
```typescript
// ✅ 正确使用方式
import { DynamicWeightingService } from '@shared/infrastructure/analysis/services/DynamicWeightingService';
import { PatternRecognitionService } from '@shared/infrastructure/analysis/services/PatternRecognitionService';
import { MultiTimeframeService } from '@shared/infrastructure/analysis/services/MultiTimeframeService';

@injectable()
export class MarketAnalysisService {
  constructor(
    @inject(TYPES.Shared.DynamicWeightingService) private weighting: DynamicWeightingService,
    @inject(TYPES.Shared.PatternRecognitionService) private patterns: PatternRecognitionService,
    @inject(TYPES.Shared.MultiTimeframeService) private timeframes: MultiTimeframeService
  ) {}

  async performComprehensiveAnalysis(symbol: string) {
    // 使用统一分析服务
    const weights = await this.weighting.calculateWeights(symbol);
    const patterns = await this.patterns.detectPatterns(symbol);
    const multiTimeframe = await this.timeframes.analyzeMultipleTimeframes(symbol);

    return { weights, patterns, multiTimeframe };
  }
}
```

#### AI服务管理 - 必须使用
```typescript
// ✅ 正确使用方式
import { UnifiedAIServiceManager } from '@shared/infrastructure/ai/unified-ai-service-manager';

@injectable()
export class IntelligentTradingService {
  constructor(
    @inject(TYPES.Shared.AIServiceManager) private aiManager: UnifiedAIServiceManager
  ) {}

  async generateAIInsights(symbol: string) {
    return await this.aiManager.performUnifiedAnalysis({
      symbol,
      analysisTypes: ['trend', 'risk', 'pattern'],
      timeframe: '1h'
    });
  }
}
```

### 🏥 监控和健康检查组件

#### 健康检查系统 - 必须使用
```typescript
// ✅ 正确使用方式 - 统一健康检查端点
// 所有健康检查通过 GET /api/health 访问

// 如需自定义健康检查，实现健康检查提供者
import { IHealthCheckProvider } from '@shared/infrastructure/health/interfaces/health-check-provider.interface';

@injectable()
export class DatabaseHealthProvider implements IHealthCheckProvider {
  constructor(@inject(TYPES.Shared.Database) private prisma: PrismaClient) {}

  async checkHealth(): Promise<HealthCheckResult> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return {
        status: 'healthy',
        details: { database: 'connected', latency: '< 10ms' }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: { database: 'disconnected', error: error.message }
      };
    }
  }
}

// ❌ 禁止创建独立健康检查
@Controller('/my-service')
export class MyController {
  @Get('/health')  // 禁止
  healthCheck() { return { status: 'ok' }; }
}
```

#### 性能监控 - 必须使用
```typescript
// ✅ 正确使用方式
import { UnifiedPerformanceMonitoringService } from '@shared/infrastructure/monitoring/services/unified-performance-monitoring-service';

@injectable()
export class BusinessService {
  constructor(
    @inject(TYPES.Shared.PerformanceMonitoringService)
    private monitor: UnifiedPerformanceMonitoringService
  ) {}

  async performOperation() {
    const timer = this.monitor.startTimer('business_operation');
    try {
      // 业务逻辑
      const result = await this.doSomething();
      timer.end({ status: 'success' });
      return result;
    } catch (error) {
      timer.end({ status: 'error', error: error.message });
      throw error;
    }
  }
}
```

### 🔧 配置管理组件

#### 统一配置管理 - 必须使用
```typescript
// ✅ 正确使用方式
import { UnifiedConfigManager } from '@shared/infrastructure/config/unified-config-manager';

@injectable()
export class TradingService {
  constructor(
    @inject(TYPES.Shared.ConfigManager) private config: UnifiedConfigManager
  ) {}

  async initialize() {
    const tradingConfig = this.config.getTradingConfig();
    const apiKeys = this.config.getApiKeys();
    const riskLimits = this.config.getRiskLimits();

    // 使用配置初始化服务
  }
}

// ❌ 禁止创建独立配置管理
class MyConfigManager { ... }  // 禁止
const config = require('./config.json');  // 禁止直接读取配置文件
```

### 🏗️ 应用架构组件

#### 应用服务基类 - 必须使用
```typescript
// ✅ 正确使用方式
import { BaseApplicationService } from '@shared/application/base-application-service';

@injectable()
export class UserApplicationService extends BaseApplicationService {
  constructor(
    @inject(TYPES.UserManagement.UserRepository) private userRepo: IUserRepository,
    @inject(TYPES.Shared.UnifiedDtoMapperRegistry) mapper: UnifiedDtoMapperRegistry,
    @inject(TYPES.Shared.Logger) logger: ILogger
  ) {
    super(mapper, logger);
  }

  async getUser(id: string): Promise<UserDto> {
    const user = await this.userRepo.findById(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // 使用基类的统一映射方法
    return this.mapToResponse(user, 'UserDto');
  }
}

// ❌ 禁止重复实现应用服务模式
export class MyApplicationService {
  // 禁止重复实现错误处理、日志记录、DTO映射等通用逻辑
  private handleError(error: Error) { ... }  // 禁止
  private logRequest(request: any) { ... }  // 禁止
  private mapToResponse(entity: any) { ... }  // 禁止
}
```

#### DTO映射系统 - 必须使用
```typescript
// ✅ 正确使用方式
import { UnifiedDtoMapperRegistry } from '@shared/application/dto-mappers/unified-dto-mapper-registry';

@injectable()
export class MarketDataApplicationService extends BaseApplicationService {
  async getPriceData(symbol: string): Promise<PriceDataDto> {
    const priceData = await this.priceRepository.findBySymbol(symbol);

    // 使用统一映射器
    return this.mapToResponse(priceData, 'PriceDataDto');
  }

  async getKlineData(symbol: string, interval: string): Promise<KlineDataDto[]> {
    const klineData = await this.klineRepository.findBySymbolAndInterval(symbol, interval);

    // 批量映射
    return this.mapArrayToResponse(klineData, 'KlineDataDto');
  }
}

// ❌ 禁止手动映射
private mapPriceDataToDto(data: PriceData): PriceDataDto {
  return {
    symbol: data.symbol,
    price: data.price,
    // ... 手动映射每个字段
  };
}
```

#### 领域基础组件 - 必须使用
```typescript
// ✅ 正确使用方式
import { BaseEntity } from '@shared/domain/entities/base-entity';
import { BaseValueObject } from '@shared/domain/value-objects/base-value-object';

// 实体类
export class User extends BaseEntity<UserProps> {
  private constructor(props: UserProps, id?: UniqueEntityId) {
    super(props, id);
  }

  public static create(props: UserProps, id?: UniqueEntityId): User {
    return new User(props, id);
  }

  get email(): Email {
    return this.props.email;
  }
}

// 值对象类
export class Email extends BaseValueObject<string> {
  protected validate(value: string): void {
    if (!this.isValidEmail(value)) {
      throw new Error('Invalid email format');
    }
  }

  private isValidEmail(email: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }
}
```

## 🚫 严格禁止的模式

### 禁止的文件命名
```
❌ enhanced-xxx.ts
❌ optimized-xxx.ts
❌ intelligent-xxx.ts
❌ unified-xxx.ts (如果已有xxx.ts)
❌ xxx-v2.ts / xxx-new.ts / xxx-improved.ts
❌ comprehensive-analysis-routes.ts (在多个目录)
```

### 禁止的代码模式
```typescript
// ❌ 禁止直接创建实例
new PrismaClient()
new Redis()
axios.create()
new WebSocket()

// ❌ 禁止重复实现核心功能
calculateRSI()
calculateMACD()
healthCheck()
mapToResponse()
toDto()
fromEntity()

// ❌ 禁止使用随机数（虚假实现标志）
Math.random()

// ❌ 禁止直接导入第三方库（应用代码中）
import axios from 'axios';
import Redis from 'ioredis';
import WebSocket from 'ws';
```

## 🔍 快速组件查找指南

### 按功能查找统一组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| HTTP请求 | `BaseHttpClient` | `@shared/infrastructure/http/base-http-client` | `TYPES.Shared.HttpClient` |
| 外部API | `ExternalDataAdapterBase` | `@shared/infrastructure/http/external-data-adapter-base` | 继承使用 |
| WebSocket | `BaseWebSocketAdapter` | `@shared/infrastructure/websocket/base-websocket-adapter` | 继承使用 |
| 缓存操作 | `MultiTierCacheService` | `@shared/infrastructure/ai/multi-tier-cache-service` | `TYPES.Shared.MultiTierCacheService` |
| 缓存一致性 | `CacheConsistencyManager` | `@shared/infrastructure/cache/cache-consistency-manager` | `TYPES.Shared.CacheConsistencyManager` |
| 缓存性能监控 | `CachePerformanceMonitor` | `@shared/infrastructure/cache/cache-performance-monitor` | `TYPES.Shared.CachePerformanceMonitor` |
| 数据库操作 | `BaseRepository` | `@shared/infrastructure/database/base-repository` | 继承使用 |
| 数据库查询管理 | `QueryManager` | `@shared/infrastructure/database/query-manager` | `TYPES.Shared.QueryManager` |
| 数据映射服务 | `DataMappingService` | `@shared/infrastructure/database/data-mapping-service` | `TYPES.Shared.DataMappingService` |
| 统一数据映射器 | `UnifiedDataMapper` | `@shared/infrastructure/database/unified-data-mapper` | `TYPES.Shared.UnifiedDataMapper` |
| 技术指标 | `UnifiedTechnicalIndicatorCalculator` | `@shared/infrastructure/technical-indicators/unified-technical-indicator-calculator` | `TYPES.Shared.TechnicalIndicatorCalculator` |
| 模式识别 | `PatternRecognitionService` | `@shared/infrastructure/analysis/services/PatternRecognitionService` | `TYPES.Shared.PatternRecognitionService` |
| 权重分配 | `DynamicWeightingService` | `@shared/infrastructure/analysis/services/DynamicWeightingService` | `TYPES.Shared.DynamicWeightingService` |
| 多时间框架 | `MultiTimeframeService` | `@shared/infrastructure/analysis/services/MultiTimeframeService` | `TYPES.Shared.MultiTimeframeService` |
| AI服务 | `UnifiedAIServiceManager` | `@shared/infrastructure/ai/unified-ai-service-manager` | `TYPES.Shared.AIServiceManager` |
| AI调用日志 | `AICallLogService` | `@shared/infrastructure/ai/ai-call-log-service` | `TYPES.Shared.AICallLogService` |
| AI调用装饰器 | `AICallDecorator` | `@shared/infrastructure/ai/ai-call-decorator` | 装饰器使用 |
| 缓存AI调用装饰器 | `CachedAICallDecorator` | `@shared/infrastructure/ai/cached-ai-call-decorator` | 装饰器使用 |
| 语义缓存引擎 | `SemanticCacheEngine` | `@shared/infrastructure/ai/semantic-cache-engine` | `TYPES.Shared.SemanticCacheEngine` |
| 向量数据库服务 | `VectorDatabaseService` | `@shared/infrastructure/ai/vector-database-service` | `TYPES.Shared.VectorDatabaseService` |
| 统一相似性服务 | `UnifiedSimilarityService` | `@shared/infrastructure/ai/unified-similarity-service` | `TYPES.Shared.UnifiedSimilarityService` |
| 配置管理 | `UnifiedConfigManager` | `@shared/infrastructure/config/unified-config-manager` | `TYPES.Shared.ConfigManager` |
| 配置监控 | `ConfigMonitor` | `@shared/infrastructure/config/config-monitor` | `TYPES.Shared.ConfigMonitor` |
| 配置验证 | `ConfigValidation` | `@shared/infrastructure/config/config-validation` | `TYPES.Shared.ConfigValidation` |
| 风险配置服务 | `RiskConfigService` | `@shared/infrastructure/config/risk-config.service` | `TYPES.Shared.RiskConfigService` |
| 健康检查 | `IHealthCheckProvider` | `@shared/infrastructure/health/interfaces/health-check-provider.interface` | 实现接口 |
| 性能监控 | `UnifiedPerformanceMonitoringService` | `@shared/infrastructure/monitoring/services/unified-performance-monitoring-service` | `TYPES.Shared.PerformanceMonitoringService` |
| 统一监控管理器 | `UnifiedMonitoringManager` | `@shared/infrastructure/monitoring/unified-monitoring-manager` | `TYPES.Shared.MonitoringManager` |
| 统一告警系统 | `UnifiedAlertSystem` | `@shared/infrastructure/monitoring/unified-alert-system` | `TYPES.Shared.AlertSystem` |
| 执行监控装饰器 | `ExecutionMonitoringDecorator` | `@shared/infrastructure/monitoring/execution-monitoring-decorator` | 装饰器使用 |
| 应用服务 | `BaseApplicationService` | `@shared/application/base-application-service` | 继承使用 |
| DTO映射 | `UnifiedDtoMapperRegistry` | `@shared/application/dto-mappers/unified-dto-mapper-registry` | `TYPES.Shared.UnifiedDtoMapperRegistry` |
| 实体基类 | `BaseEntity` | `@shared/domain/entities/base-entity` | 继承使用 |
| 值对象基类 | `BaseValueObject` | `@shared/domain/value-objects/base-value-object` | 继承使用 |

### 🔧 数据处理组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| 数据处理管道 | `DataProcessingPipeline` | `@shared/infrastructure/data-processing/data-processing-pipeline` | `TYPES.Shared.DataProcessingPipeline` |
| 策略管理器 | `StrategyManager` | `@shared/infrastructure/data-processing/strategies/strategy-manager` | `TYPES.Shared.StrategyManager` |
| 策略工厂 | `StrategyFactory` | `@shared/infrastructure/data-processing/strategies/strategy-factory` | `TYPES.Shared.StrategyFactory` |
| 管道阶段协调器 | `PipelineStageCoordinator` | `@shared/infrastructure/data-processing/pipeline-stage-coordinator` | `TYPES.Shared.PipelineStageCoordinator` |
| 阶段执行器工厂 | `StageExecutorFactory` | `@shared/infrastructure/data-processing/stage-executor-factory` | `TYPES.Shared.StageExecutorFactory` |

### 🔍 数据验证组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| 真实数据验证器 | `RealDataValidator` | `@shared/infrastructure/validation/real-data-validator` | `TYPES.Shared.RealDataValidator` |
| 价格验证引擎 | `PriceValidationEngine` | `@shared/infrastructure/validation/price-validation-engine` | `TYPES.Shared.PriceValidationEngine` |
| AI决策验证器 | `AIDecisionValidator` | `@shared/infrastructure/validation/ai-decision-validator` | `TYPES.Shared.AIDecisionValidator` |
| 预测验证中间件 | `PredictionValidationMiddleware` | `@shared/infrastructure/validation/prediction-validation-middleware` | 中间件使用 |

### 📊 数据质量组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| 统一数据质量监控器 | `UnifiedDataQualityMonitor` | `@shared/infrastructure/data-quality/unified-data-quality-monitor` | `TYPES.Shared.UnifiedDataQualityMonitor` |
| 数据质量仪表板 | `DataQualityDashboard` | `@shared/infrastructure/data-quality/data-quality-dashboard` | `TYPES.Shared.DataQualityDashboard` |

### 🚀 性能优化组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| 并发优化器 | `ConcurrencyOptimizer` | `@shared/infrastructure/performance/concurrency-optimizer` | `TYPES.Shared.ConcurrencyOptimizer` |
| 统一性能管理器 | `UnifiedPerformanceManager` | `@shared/infrastructure/performance/unified-performance-manager` | `TYPES.Shared.UnifiedPerformanceManager` |

### 🔐 安全和认证组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| 认证中间件 | `AuthMiddleware` | `@shared/infrastructure/auth/auth-middleware` | 中间件使用 |
| 用户身份服务 | `UserIdentityService` | `@shared/infrastructure/services/user-identity-service` | `TYPES.Shared.UserIdentityService` |

### 🔧 工具和实用组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| 安全ID生成器 | `SecureIdGenerator` | `@shared/infrastructure/utils/secure-id-generator` | `TYPES.Shared.SecureIdGenerator` |
| 基础控制器 | `BaseController` | `@shared/infrastructure/controllers/base-controller` | 继承使用 |

### 🔄 消息和事件组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| 事件总线 | `EventBus` | `@shared/infrastructure/messaging/event-bus` | `TYPES.EventBus` |
| 消息队列 | `MessageQueue` | `@shared/infrastructure/messaging/message-queue` | `TYPES.MessageQueue` |
| Redis事件总线 | `RedisEventBus` | `@shared/infrastructure/messaging/redis` | `TYPES.Redis` |

### 🧪 测试基础设施组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| 统一测试数据生成器 | `UnifiedTestDataGenerator` | `@shared/infrastructure/testing/unified-test-data-generator` | `TYPES.Shared.UnifiedTestDataGenerator` |
| 统一测试断言 | `UnifiedTestAssertions` | `@shared/infrastructure/testing/unified-test-assertions` | `TYPES.Shared.UnifiedTestAssertions` |
| 统一集成测试基类 | `UnifiedIntegrationTestBase` | `@shared/infrastructure/testing/unified-integration-test-base` | 继承使用 |
| 统一测试配置管理器 | `UnifiedTestConfigManager` | `@shared/infrastructure/testing/unified-test-config-manager` | `TYPES.Shared.UnifiedTestConfigManager` |
| 统一测试设置 | `UnifiedTestSetup` | `@shared/infrastructure/testing/unified-test-setup` | `TYPES.Shared.UnifiedTestSetup` |
| Prisma模拟助手 | `PrismaMockHelper` | `@shared/infrastructure/testing/prisma-mock-helper` | 工具使用 |

### 🏗️ DI容器和生命周期组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| 基础容器模块 | `BaseContainerModule` | `@shared/infrastructure/di/base/base-container-module` | 继承使用 |
| 统一Symbol注册表 | `UnifiedSymbolRegistry` | `@shared/infrastructure/di/base/unified-symbol-registry` | 工具使用 |
| 服务工厂 | `ServiceFactory` | `@shared/infrastructure/di/base/service-factory` | `TYPES.Shared.ServiceFactory` |
| 依赖解析策略 | `DependencyResolutionStrategy` | `@shared/infrastructure/di/base/dependency-resolution-strategy` | 策略使用 |
| 模块化容器管理器 | `ModularContainerManager` | `@shared/infrastructure/di/modular-container-manager` | `TYPES.Shared.ModularContainerManager` |

### 🔄 系统集成组件

| 功能需求 | 使用组件 | 导入路径 | DI Symbol |
|---------|---------|----------|-----------|
| 系统集成协调器 | `SystemIntegrationCoordinator` | `@shared/services/system-integration-coordinator` | `TYPES.Shared.SystemIntegrationCoordinator` |
| 统一数据同步协调器 | `UnifiedDataSyncCoordinator` | `@application/unified-data-sync-coordinator` | `TYPES.Shared.DataSyncCoordinator` |
| 系统自动修复 | `SystemAutoRepair` | `@shared/infrastructure/startup/system-auto-repair` | `TYPES.Shared.SystemAutoRepair` |

### 🎯 特殊统一组件说明

#### 装饰器组件（无需DI注入）
```typescript
// AI调用装饰器 - 直接使用
import { AICallDecorator } from '@shared/infrastructure/ai/ai-call-decorator';
import { CachedAICallDecorator } from '@shared/infrastructure/ai/cached-ai-call-decorator';

@AICallDecorator()
@CachedAICallDecorator({ ttl: 3600 })
class MyAIService {
  async analyze(data: any) {
    // AI分析逻辑
  }
}

// 执行监控装饰器 - 直接使用
import { ExecutionMonitoringDecorator } from '@shared/infrastructure/monitoring/execution-monitoring-decorator';

@ExecutionMonitoringDecorator()
class MyBusinessService {
  async performOperation() {
    // 业务逻辑
  }
}
```

#### 中间件组件（Express中间件）
```typescript
// 认证中间件
import { AuthMiddleware } from '@shared/infrastructure/auth/auth-middleware';
app.use('/api/protected', AuthMiddleware);

// 预测验证中间件
import { PredictionValidationMiddleware } from '@shared/infrastructure/validation/prediction-validation-middleware';
app.use('/api/predictions', PredictionValidationMiddleware);
```

#### 工具类组件（静态使用）
```typescript
// 安全ID生成器
import { SecureIdGenerator } from '@shared/infrastructure/utils/secure-id-generator';
const id = SecureIdGenerator.generate();

// Prisma模拟助手（测试中使用）
import { PrismaMockHelper } from '@shared/infrastructure/testing/prisma-mock-helper';
const mockPrisma = PrismaMockHelper.createMock();
```

#### 策略模式组件
```typescript
// 依赖解析策略
import { DependencyResolutionStrategy } from '@shared/infrastructure/di/base/dependency-resolution-strategy';

// 创建自定义策略
const customStrategy = DependencyResolutionStrategy.createCustomStrategy({
  // 策略配置
});
```

## 🛠️ 开发工作流程

### 1. 开发前检查清单
- [ ] 查看上表确认所需功能是否有统一组件（**现在包含85+个统一组件**）
- [ ] 检查 `src/shared/infrastructure/types/unified-interfaces.ts` 是否有相关接口（466行统一接口）
- [ ] 确认文件命名不使用禁止前缀
- [ ] 验证是否真的需要新实现
- [ ] 检查是否需要装饰器、中间件或工具类组件

### 2. 开发中检查
```bash
# 实时ESLint检查
npm run lint

# 检查重复实现
npm run detect:redundant
```

### 3. 提交前验证
```bash
# 完整检查
npm run validate:known-duplications
npx ts-node scripts/ci/redundancy-gate-check.ts
```

## 📝 实用开发模板

### 模板1：外部API服务
```typescript
import { ExternalDataAdapterBase } from '@shared/infrastructure/http/external-data-adapter-base';
import { BaseHttpClient } from '@shared/infrastructure/http/base-http-client';
import { UnifiedConfigManager } from '@shared/infrastructure/config/unified-config-manager';

@injectable()
export class [ExchangeName]ApiService extends ExternalDataAdapterBase {
  constructor(
    @inject(TYPES.Shared.HttpClient) httpClient: BaseHttpClient,
    @inject(TYPES.Shared.ConfigManager) private config: UnifiedConfigManager
  ) {
    super(httpClient);
  }

  async getMarketData(symbol: string) {
    return await this.get(`/api/v1/ticker?symbol=${symbol}`);
  }
}
```

### 模板2：分析服务
```typescript
import { UnifiedTechnicalIndicatorCalculator } from '@shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';
import { PatternRecognitionService } from '@shared/infrastructure/analysis/services/PatternRecognitionService';

@injectable()
export class [Strategy]AnalysisService {
  constructor(
    @inject(TYPES.Shared.TechnicalIndicatorCalculator)
    private calculator: UnifiedTechnicalIndicatorCalculator,
    @inject(TYPES.Shared.PatternRecognitionService)
    private patterns: PatternRecognitionService
  ) {}

  async analyze(symbol: string) {
    const indicators = await this.calculator.calculateMultiple(symbol, ['RSI', 'MACD']);
    const patterns = await this.patterns.detectPatterns(symbol);
    return { indicators, patterns };
  }
}
```

### 模板3：应用服务
```typescript
import { BaseApplicationService } from '@shared/application/base-application-service';
import { UnifiedDtoMapperRegistry } from '@shared/application/dto-mappers/unified-dto-mapper-registry';

@injectable()
export class [Domain]ApplicationService extends BaseApplicationService {
  constructor(
    @inject(TYPES.[Domain].[Entity]Repository) private repository: I[Entity]Repository,
    @inject(TYPES.Shared.UnifiedDtoMapperRegistry) mapper: UnifiedDtoMapperRegistry,
    @inject(TYPES.Shared.Logger) logger: ILogger
  ) {
    super(mapper, logger);
  }

  async get[Entity](id: string): Promise<[Entity]Dto> {
    const entity = await this.repository.findById(id);
    return this.mapToResponse(entity, '[Entity]Dto');
  }
}
```

---

## 📈 统一组件体系总览

### 🎯 组件统计
- **总计**: 85+个统一组件
- **基础设施层**: 45+个组件
- **应用服务层**: 15+个组件
- **领域层**: 10+个组件
- **测试基础设施**: 10+个组件
- **工具和实用组件**: 5+个组件

### 🏆 覆盖范围
- **网络通信**: HTTP客户端、WebSocket、外部API适配器
- **数据存储**: 缓存系统、数据库操作、数据映射
- **数据分析**: 技术指标、AI服务、核心分析算法
- **系统监控**: 健康检查、性能监控、告警系统
- **配置管理**: 统一配置、环境管理、验证规则
- **应用架构**: 应用服务基类、DTO映射、领域基础
- **数据处理**: 处理管道、策略管理、数据验证
- **性能优化**: 并发优化、性能管理
- **安全认证**: 认证中间件、用户身份服务
- **测试支持**: 测试数据生成、断言、集成测试基类
- **系统集成**: DI容器、生命周期管理、系统协调

**开发者必记口诀：**
1. **先查表** - 查看85+个统一组件索引表确认是否有现成组件
2. **分类型** - 区分服务注入、基类继承、装饰器使用、中间件配置
3. **后继承** - 优先继承基类而非重新实现
4. **再注入** - 通过DI注入获取统一服务
5. **最后检查** - 提交前运行检测工具验证

**🚨 重要提醒**: 项目现有85+个统一组件，覆盖了几乎所有常见功能需求。在创建任何新组件前，请务必先查表确认！

## 🚨 违规处理

### 自动检测
- **ESLint**: 实时检测禁止模式
- **CI/CD**: 阻止包含重复实现的代码合并
- **重复检测器**: 每日生成检测报告

### 代码审查必检项
- [ ] 是否使用了统一组件？
- [ ] 是否避免了禁止的命名模式？
- [ ] 是否重复实现了已有功能？
- [ ] 是否正确注入了依赖？

## 📚 参考资源

### 统一组件文档
- [HTTP客户端使用指南](./http-client-usage-guide.md)
- [缓存系统使用指南](./cache-system-usage-guide.md)
- [技术指标计算器指南](./technical-indicator-guide.md)
- [健康检查系统指南](./health-check-guide.md)

### 检测工具
- `npm run detect:redundant` - 重复实现检测
- `npm run validate:known-duplications` - 已知重复验证
- `npm run lint` - ESLint规则检查

## 🔍 快速查找统一组件

### 按功能分类的组件索引

#### 🌐 网络通信
```typescript
// HTTP请求
BaseHttpClient                    // @shared/infrastructure/http/base-http-client
HttpClientFactory                 // @shared/infrastructure/http/http-client-factory
ExternalDataAdapterBase          // @shared/infrastructure/http/external-data-adapter-base

// WebSocket连接
BaseWebSocketAdapter             // @shared/infrastructure/websocket/base-websocket-adapter
WebSocketConnectionManager      // @shared/infrastructure/websocket/websocket-connection-manager
```

#### 💾 数据存储
```typescript
// 数据库操作
BaseRepository                   // @shared/infrastructure/database/base-repository
RepositoryBaseService           // @shared/infrastructure/database/repository-base-service
UnifiedDataMapper               // @shared/infrastructure/database/unified-data-mapper

// 缓存系统
MultiTierCacheService           // @shared/infrastructure/cache/multi-tier-cache-service
CacheManager                    // @shared/infrastructure/cache/cache-manager
SemanticCacheService           // @shared/infrastructure/cache/semantic-cache-service
```

#### 📊 数据分析
```typescript
// 技术指标
UnifiedTechnicalIndicatorCalculator  // @shared/infrastructure/technical-indicators/unified-technical-indicator-calculator

// 核心分析
DynamicWeightingService         // @shared/infrastructure/analysis/services/DynamicWeightingService
PatternRecognitionService       // @shared/infrastructure/analysis/services/PatternRecognitionService
MultiTimeframeService          // @shared/infrastructure/analysis/services/MultiTimeframeService

// AI服务
UnifiedAIServiceManager         // @shared/infrastructure/ai/unified-ai-service-manager
```

#### 🔧 系统管理
```typescript
// 配置管理
UnifiedConfigManager            // @shared/infrastructure/config/unified-config-manager
EnvironmentManager             // @shared/infrastructure/config/environment/environment-manager

// 监控健康
HealthCheckAggregator          // @shared/infrastructure/health/health-check-aggregator
UnifiedPerformanceMonitoringService  // @shared/infrastructure/monitoring/services/unified-performance-monitoring-service
UnifiedAlertSystem             // @shared/infrastructure/monitoring/unified-alert-system

// 错误处理
UnifiedErrorHandler            // @shared/infrastructure/error/unified-error-handler
```

#### 🏗️ 应用架构
```typescript
// 应用服务基类
BaseApplicationService         // @shared/application/base-application-service

// DTO映射
UnifiedDtoMapperRegistry       // @shared/application/dto-mappers/unified-dto-mapper-registry
BaseDTOMapper                  // @shared/application/application-service-interfaces

// 领域基础
BaseEntity                     // @shared/domain/entities/base-entity
BaseValueObject               // @shared/domain/value-objects/base-value-object
BaseRepository                // @shared/domain/repositories/base-repository.interface
```

## 🚨 常见错误和解决方案

### 错误1：直接使用第三方库
```typescript
// ❌ 错误做法
import axios from 'axios';
const response = await axios.get('https://api.example.com');

// ✅ 正确做法
@injectable()
export class MyService extends ExternalDataAdapterBase {
  constructor(@inject(TYPES.Shared.HttpClient) httpClient: BaseHttpClient) {
    super(httpClient);
  }

  async fetchData() {
    return await this.get('/endpoint');
  }
}
```

### 错误2：重复实现计算逻辑
```typescript
// ❌ 错误做法
private calculateRSI(prices: number[], period: number = 14): number {
  // 重复实现RSI计算逻辑
  let gains = 0, losses = 0;
  // ... 复杂的计算逻辑
}

// ✅ 正确做法
@injectable()
export class TradingStrategy {
  constructor(
    @inject(TYPES.Shared.TechnicalIndicatorCalculator)
    private calculator: UnifiedTechnicalIndicatorCalculator
  ) {}

  async analyzeSymbol(symbol: string) {
    const rsi = await this.calculator.calculateRSI(symbol, 14);
    return rsi;
  }
}
```

### 错误3：创建重复的健康检查
```typescript
// ❌ 错误做法
@Controller('/api/my-service')
export class MyController {
  @Get('/health')
  healthCheck() {
    return { status: 'healthy' };
  }
}

// ✅ 正确做法
// 1. 使用统一健康检查端点: GET /api/health
// 2. 如需自定义检查，实现健康检查提供者
@injectable()
export class MyServiceHealthProvider implements IHealthCheckProvider {
  async checkHealth(): Promise<HealthCheckResult> {
    // 自定义健康检查逻辑
    return {
      status: 'healthy',
      details: { service: 'MyService', version: '1.0.0' }
    };
  }
}
```

### 错误4：手动DTO映射
```typescript
// ❌ 错误做法
export class UserService {
  private mapUserToDto(user: User): UserDto {
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      // ... 手动映射每个字段
    };
  }
}

// ✅ 正确做法
@injectable()
export class UserApplicationService extends BaseApplicationService {
  constructor(
    @inject(TYPES.Shared.UnifiedDtoMapperRegistry) mapper: UnifiedDtoMapperRegistry,
    logger: ILogger
  ) {
    super(mapper, logger);
  }

  async getUser(id: string): Promise<UserDto> {
    const user = await this.userRepository.findById(id);
    return this.mapToResponse(user, 'UserDto');  // 使用统一映射器
  }
}
```

## 📋 开发检查清单

### 🔍 开发前检查
- [ ] 查看 `src/shared/infrastructure/` 是否已有相关组件
- [ ] 检查 `unified-interfaces.ts` 是否已有相关接口定义
- [ ] 确认文件命名不使用禁止的前缀（enhanced-, optimized-等）
- [ ] 验证功能是否真的需要新实现

### 💻 开发中检查
- [ ] 使用统一组件而非第三方库直接调用
- [ ] 通过DI注入获取服务实例
- [ ] 继承基类而非重复实现通用逻辑
- [ ] 实现接口而非创建独立实现

### 🚀 提交前检查
- [ ] 运行 `npm run lint` 检查ESLint规则
- [ ] 运行 `npm run detect:redundant` 检查重复实现
- [ ] 运行 `npm run validate:known-duplications` 检查回归
- [ ] 确保所有测试通过

### 👥 代码审查检查
- [ ] 审查者确认使用了正确的统一组件
- [ ] 验证没有重复实现已有功能
- [ ] 检查文件命名符合规范
- [ ] 确认依赖注入配置正确

## 🎯 成功案例

### 案例1：市场数据服务重构
```typescript
// 重构前：多个重复实现
class BinanceService { /* 重复HTTP逻辑 */ }
class OkxService { /* 重复HTTP逻辑 */ }
class BybitService { /* 重复HTTP逻辑 */ }

// 重构后：统一基类
@injectable()
export class BinanceService extends ExternalDataAdapterBase {
  constructor(@inject(TYPES.Shared.HttpClient) httpClient: BaseHttpClient) {
    super(httpClient);
  }
}

@injectable()
export class OkxService extends ExternalDataAdapterBase {
  constructor(@inject(TYPES.Shared.HttpClient) httpClient: BaseHttpClient) {
    super(httpClient);
  }
}
```

### 案例2：技术指标计算统一
```typescript
// 重构前：每个策略都有自己的计算方法
class Strategy1 { private calculateRSI() { ... } }
class Strategy2 { private calculateRSI() { ... } }
class Strategy3 { private calculateMACD() { ... } }

// 重构后：统一计算器
@injectable()
export class TradingStrategy1 {
  constructor(
    @inject(TYPES.Shared.TechnicalIndicatorCalculator)
    private calculator: UnifiedTechnicalIndicatorCalculator
  ) {}

  async execute(symbol: string) {
    const rsi = await this.calculator.calculateRSI(symbol, 14);
    // 策略逻辑
  }
}
```

---

**记住：每次编写代码前，先问自己三个问题：**
1. **这个功能是否已经有统一实现？**
2. **我是否在重复造轮子？**
3. **我的实现是否会与现有组件冲突？**

**如果答案是"是"、"是"、"是"，请停下来重新思考！**
