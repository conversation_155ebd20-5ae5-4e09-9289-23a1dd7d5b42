# 编码规范与最佳实践

## 🎯 核心原则

### 1. 杜绝重复代码 (DRY - Don't Repeat Yourself)
- **零容忍重复实现**：任何功能性代码不得重复实现
- **统一组件优先**：必须使用项目中已有的统一基础设施组件
- **共享模块强制**：跨模块复用必须通过 `src/shared` 或 `src/contexts` 实现

### 2. 真实实现原则
- **禁止虚假数据**：生产代码中不得使用 `Math.random()`、硬编码数据
- **禁止降级处理**：不得在错误处理中返回默认值，必须抛出明确错误
- **禁止临时实现**：不得使用"简化实现"、"临时实现"、"占位符"

### 3. 架构一致性
- **依赖注入强制**：所有服务必须通过DI容器管理，禁止直接实例化
- **接口优先**：面向接口编程，依赖抽象而非具体实现
- **单一职责**：每个类、方法只负责一个明确的职责

## 🏗️ 架构规范

### 统一基础设施组件使用规范

#### 1. 数据库操作
```typescript
// ✅ 正确：使用RepositoryBaseService
@injectable()
export class UserRepository {
  constructor(
    @inject(TYPES.Shared.RepositoryBaseService) 
    private readonly baseService: IRepositoryBaseService<User>
  ) {}
  
  async findById(id: string): Promise<User> {
    return this.baseService.executeWithMonitoring('findById', async () => {
      // 具体实现
    });
  }
}

// ❌ 错误：直接创建PrismaClient
const prisma = new PrismaClient(); // 违反ESLint规则
```

#### 2. HTTP客户端
```typescript
// ✅ 正确：继承BaseHttpClient
@injectable()
export class BinanceApiClient extends BaseHttpClient {
  constructor(@inject(TYPES.Logger) logger: ILogger) {
    super({
      name: 'Binance',
      baseURL: 'https://api.binance.com',
      timeout: 10000
    }, logger);
  }
}

// ❌ 错误：直接使用axios
const client = axios.create({ baseURL: 'https://api.binance.com' });
```

#### 3. 技术指标计算
```typescript
// ✅ 正确：使用统一技术指标计算器
@injectable()
export class TradingSignalService {
  constructor(
    @inject(UNIFIED_TECHNICAL_INDICATOR_TYPES.UnifiedTechnicalIndicatorCalculator)
    private readonly calculator: IUnifiedTechnicalIndicatorCalculator
  ) {}
  
  calculateRSI(prices: number[]): RSIResult {
    return this.calculator.calculateRSI(prices);
  }
}

// ❌ 错误：重复实现技术指标
function calculateRSI(prices: number[]): number {
  // 重复实现，违反ESLint规则
}
```

#### 4. 缓存管理
```typescript
// ✅ 正确：使用MultiTierCacheService
@injectable()
export class DataService {
  constructor(
    @inject(TYPES.Shared.MultiTierCacheService)
    private readonly cache: MultiTierCacheService
  ) {}
  
  async getData(key: string): Promise<any> {
    return this.cache.get(key, 'data-service');
  }
}

// ❌ 错误：直接创建缓存实例
const cache = new Map(); // 简化实现，不符合规范
```

### 目录结构规范

```
src/
├── shared/                 # 项目级通用工具库
│   ├── application/        # 应用服务接口
│   ├── domain/            # 领域实体和值对象
│   └── infrastructure/    # 基础设施实现
│       ├── analysis/      # 核心分析服务
│       ├── cache/         # 缓存管理
│       ├── config/        # 配置管理
│       ├── database/      # 数据库基础设施
│       ├── di/           # 依赖注入容器
│       ├── error/        # 错误处理
│       ├── http/         # HTTP客户端
│       ├── logging/      # 日志服务
│       ├── technical-indicators/ # 技术指标计算
│       └── websocket/    # WebSocket管理
├── contexts/              # 业务上下文
│   ├── market-data/      # 市场数据上下文
│   ├── trading-signals/  # 交易信号上下文
│   ├── trend-analysis/   # 趋势分析上下文
│   └── risk-management/  # 风险管理上下文
└── api/                  # API层
    ├── controllers/      # 控制器
    ├── routes/          # 路由定义
    └── dtos/           # 数据传输对象
```

## 📝 命名规范

### 1. 驼峰命名法 (camelCase/PascalCase)
- **类名**：PascalCase (`UserService`, `TradingSignalController`)
- **方法名**：camelCase (`calculateRSI`, `getUserById`)
- **变量名**：camelCase (`marketData`, `tradingSignal`)
- **常量**：UPPER_SNAKE_CASE (`MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`)

### 2. 数据库命名
- **表名**：camelCase (`userAccounts`, `tradingSignals`)
- **字段名**：camelCase (`userId`, `createdAt`)
- **禁止使用**：snake_case 和 @@map 指令

### 3. 文件命名
- **类文件**：kebab-case (`user-service.ts`, `trading-signal-controller.ts`)
- **接口文件**：kebab-case + `.interface.ts` (`user.interface.ts`)
- **类型文件**：kebab-case + `.types.ts` (`trading-signal.types.ts`)

## 🔒 安全规范

### 1. 数据验证
```typescript
// ✅ 正确：使用真实验证
export class UserValidator {
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new ValidationError('无效的邮箱格式');
    }
    return true;
  }
}

// ❌ 错误：虚假验证
function validateEmail(email: string): boolean {
  return true; // 虚假实现，不符合规范
}
```

### 2. 错误处理
```typescript
// ✅ 正确：抛出明确错误
async function fetchUserData(id: string): Promise<User> {
  try {
    const user = await userRepository.findById(id);
    if (!user) {
      throw new NotFoundError(`用户不存在: ${id}`);
    }
    return user;
  } catch (error) {
    logger.error('获取用户数据失败', { id, error });
    throw error; // 重新抛出，不返回默认值
  }
}

// ❌ 错误：返回默认值
async function fetchUserData(id: string): Promise<User> {
  try {
    return await userRepository.findById(id);
  } catch (error) {
    return getDefaultUser(); // 违反ESLint规则
  }
}
```

## 🧪 测试规范

### 1. 测试文件组织
```
tests/
├── unit/           # 单元测试
├── integration/    # 集成测试
├── api/           # API测试
└── performance/   # 性能测试
```

### 2. 测试命名
```typescript
describe('UserService', () => {
  describe('getUserById', () => {
    it('should return user when valid id provided', async () => {
      // 测试实现
    });
    
    it('should throw NotFoundError when user does not exist', async () => {
      // 测试实现
    });
  });
});
```

## 📊 代码质量监控

### 1. 自动化检查
- **重复代码检测**：`npm run check:duplication`
- **ESLint检查**：`npm run lint`
- **类型检查**：`npm run type-check`
- **架构检查**：`bash scripts/check-repository-architecture.sh`

### 2. CI/CD集成
- 所有PR必须通过代码质量检查
- 重复代码率不得超过5%
- ESLint规则必须全部通过
- 架构合规检查必须通过

## 🚫 禁止事项

### 绝对禁止
1. **Math.random()** 在生产代码中使用
2. **直接创建** PrismaClient、Logger、WebSocket实例
3. **重复实现** 技术指标计算、HTTP客户端、缓存逻辑
4. **返回默认值** 在错误处理中
5. **硬编码数据** 在业务逻辑中
6. **简化实现** 标记和临时实现
7. **snake_case** 命名和@@map指令

### 强制要求
1. **使用统一组件** 进行所有基础设施操作
2. **通过DI容器** 管理所有服务依赖
3. **抛出明确错误** 而非静默失败
4. **编写真实测试** 验证实际功能
5. **遵循架构规范** 进行模块划分

---

**记住：代码质量是我们的生命线，重复代码是技术债务的主要来源！**
