# 核心分析服务-开发者指南

**版本**: 2.0 (已更新为真实API)
**状态**: ✅ 已生效
**维护团队**: 架构团队

## 1. 概述

本文档为项目的核心算法与数据分析层提供了一套官方的开发者使用指南。核心分析服务是系统中所有高级决策、模式识别和多维度分析的基础，旨在为上层业务提供强大、一致且易于使用的分析能力。

所有需要进行复杂数据分析的业务场景，都应优先使用本文档中描述的核心服务。

### 核心优势
- **权威与一致**: 为每一类核心分析能力（权重分配、形态识别、多时间框架分析）提供唯一的、权威的实现，确保分析结果在整个系统中的一致性。
- **强大且易用**: 封装了复杂的底层算法，通过简洁的接口暴露强大的分析功能，使开发者可以专注于业务逻辑。
- **高可测试性**: 所有服务都通过依赖注入（DI）提供，易于在单元测试中进行模拟（Mock）。
- **可维护与可扩展**: 核心算法集中管理，便于未来的统一优化、升级和扩展新算法策略。

---

## 2. 核心服务概览

核心分析服务套件位于 `src/shared/infrastructure/analysis/` 目录下，由以下三个主要服务构成：

1.  **`DynamicWeightingService`**: **动态权重服务**。用于根据市场状况和数据质量，智能地为不同的信号、指标或时间框架分配权重。
2.  **`PatternRecognitionService`**: **形态识别服务**。用于从K线数据中自动识别出各种技术分析形态（如头肩顶、双底等）。
3.  **`MultiTimeframeService`**: **多时间框架服务**。用于聚合、处理和分析来自多个不同时间框架的数据，并评估其趋势一致性。

### 架构关系

这三个服务是独立的、可被单独使用的。它们通过依赖注入的方式提供给任何需要它们的上层应用服务或业务引擎。

```
                                 ┌──────────────────────────┐
                                 │   Application Services   │
                                 │ (e.g., TrendAnalysis,     │
                                 │  RiskManagement)         │
                                 └────────────┬─────────────┘
                                              │
                                              │ (依赖注入)
           ┌──────────────────────────────────┼──────────────────────────────────┐
           │                                  │                                  │
           ▼                                  ▼                                  ▼
┌──────────────────────────┐      ┌──────────────────────────┐      ┌──────────────────────────┐
│ DynamicWeightingService  │      │ PatternRecognitionService│      │  MultiTimeframeService   │
└──────────────────────────┘      └──────────────────────────┘      └──────────────────────────┘
```

---

## 3. 服务使用指南与API参考

### 3.1. DynamicWeightingService (动态权重服务)

*   **何时使用**: 当你需要根据一组动态的输入（如数据质量、市场波动性）来决定多个因素的相对重要性时。
*   **核心接口**: `IDynamicWeightingService`

    ```typescript
    interface IDynamicWeightingService {
      /**
       * 根据趋势和市场状况分配权重
       * @param trends 一系列时间框架的趋势数据
       * @param marketCondition 当前的市场状况
       * @param strategyName (可选) 指定使用的特定策略
       * @returns 返回归一化后的权重结果
       */
      allocate(trends: TimeframeTrend[], marketCondition: MarketCondition, strategyName?: string): Promise<WeightingResult>;

      /**
       * 注册权重计算策略
       * @param strategy 策略实例
       */
      registerStrategy(strategy: IWeightingStrategy): void;

      /**
       * 获取可用的策略列表
       */
      getAvailableStrategies(): string[];

      /**
       * 获取默认策略名称
       */
      getDefaultStrategy(): string;

      /**
       * 设置默认策略
       * @param strategyName 策略名称
       */
      setDefaultStrategy(strategyName: string): void;

      /**
       * 验证权重结果
       * @param weights 权重数据
       * @returns 验证结果
       */
      validateWeights(weights: TimeframeWeights): boolean;

      /**
       * 归一化权重（确保总和为1）
       * @param weights 原始权重
       * @returns 归一化后的权重
       */
      normalizeWeights(weights: TimeframeWeights): TimeframeWeights;
    }
    ```

*   **使用范例**:

    ```typescript
    @injectable()
    class DecisionEngine {
      constructor(
        @inject(TYPES.Shared.DynamicWeightingService)
        private readonly weightingService: IDynamicWeightingService
      ) {}

      async makeWeightedDecision(trends: TimeframeTrend[], marketCondition: MarketCondition) {
        // 调用服务，获取权重结果
        const result = await this.weightingService.allocate(trends, marketCondition);

        // 使用权重进行计算
        let finalScore = 0;
        trends.forEach(trend => {
          const weight = result.weights[trend.timeframe];
          finalScore += trend.strength * weight;
        });

        // 可以访问更多信息
        console.log(`使用策略: ${result.strategy}`);
        console.log(`置信度: ${result.confidence}`);
        console.log(`计算耗时: ${result.metadata.calculationTime}ms`);

        return {
          score: finalScore,
          confidence: result.confidence,
          strategy: result.strategy
        };
      }
    }
    ```

### 3.2. PatternRecognitionService (形态识别服务)

*   **何时使用**: 当你需要从一系列K线数据中，自动检测出技术分析中的经典形态时。
*   **核心接口**: `IPatternRecognitionService`

    ```typescript
    interface IPatternRecognitionService {
      /**
       * 从给定的K线数据中识别出所有支持的技术形态
       * @param klines K线数据数组
       * @param patternTypes (可选) 指定要识别的形态类型
       * @returns 返回识别出的形态结果数组
       */
      recognize(klines: KlineDataPoint[], patternTypes?: PatternType[]): Promise<PatternRecognitionResult[]>;

      /**
       * 识别单个特定形态
       * @param klines K线数据
       * @param patternType 要识别的形态类型
       * @returns 识别结果，如果未找到则返回null
       */
      recognizePattern(klines: KlineDataPoint[], patternType: PatternType): Promise<PatternRecognitionResult | null>;

      /**
       * 搜索历史形态
       * @param query 搜索查询参数
       * @returns 搜索结果
       */
      searchPatterns(query: PatternSearchQuery): Promise<PatternRecognitionResult[]>;

      /**
       * 验证形态的有效性
       * @param pattern 形态识别结果
       * @param klines 相关K线数据
       * @returns 验证结果
       */
      validatePattern(pattern: PatternRecognitionResult, klines: KlineDataPoint[]): Promise<boolean>;

      /**
       * 获取支持的形态类型列表
       */
      getSupportedPatterns(): PatternType[];

      /**
       * 获取形态统计信息
       * @param patternType 形态类型
       * @param timeRange 时间范围
       * @returns 统计信息
       */
      getPatternStatistics(patternType: PatternType, timeRange?: { start: Date; end: Date }): Promise<PatternStatistics>;
    }
    ```

*   **使用范例**:

    ```typescript
    @injectable()
    class ChartAnalysisService {
      constructor(
        @inject(TYPES.Shared.PatternRecognitionService)
        private readonly patternService: IPatternRecognitionService
      ) {}

      async findPatterns(klines: KlineDataPoint[]) {
        // 调用服务，识别所有支持的形态
        const allPatterns = await this.patternService.recognize(klines);

        console.log(`识别到 ${allPatterns.length} 个形态`);
        allPatterns.forEach(pattern => {
          console.log(`形态: ${pattern.type}, 置信度: ${pattern.confidence}, 位置: ${pattern.startIndex}-${pattern.endIndex}`);
        });

        // 或者只识别特定形态
        const headAndShoulders = await this.patternService.recognize(klines, [PatternType.HEAD_SHOULDERS]);

        // 识别单个特定形态
        const doubleBottom = await this.patternService.recognizePattern(klines, PatternType.DOUBLE_BOTTOM);
        if (doubleBottom) {
          console.log(`发现双底形态，置信度: ${doubleBottom.confidence}`);
        }

        // 验证形态有效性
        for (const pattern of allPatterns) {
          const isValid = await this.patternService.validatePattern(pattern, klines);
          if (!isValid) {
            console.warn(`形态 ${pattern.type} 验证失败`);
          }
        }

        return allPatterns;
      }

      async getPatternStats() {
        // 获取支持的形态类型
        const supportedPatterns = this.patternService.getSupportedPatterns();
        console.log('支持的形态类型:', supportedPatterns);

        // 获取形态统计信息
        const stats = await this.patternService.getPatternStatistics(
          PatternType.HEAD_SHOULDERS,
          { start: new Date('2024-01-01'), end: new Date() }
        );
        console.log('头肩顶形态统计:', stats);
      }
    }
    ```

### 3.3. MultiTimeframeService (多时间框架服务)

*   **何时使用**: 当你的分析需要同时考虑日线、4小时线、1小时线等多个时间框架的数据时。
*   **核心接口**: `IMultiTimeframeService`

    ```typescript
    interface IMultiTimeframeService {
      /**
       * 根据查询条件，并行获取多个时间框架的数据。
       */
      collectData(query: DataCollectionQuery): Promise<MultiTimeframeData>;

      /**
       * 分析多个时间框架的趋势方向是否一致。
       */
      getAlignment(data: MultiTimeframeData): Promise<TimeframeAlignmentResult>;

      /**
       * 将小周期的K线聚合成大周期的K线。
       */
      aggregateKlines(params: KlineAggregationParams): Promise<KlineDataPoint[]>;
    }
    ```

*   **使用范例**:

    ```typescript
    @injectable()
    class TrendAnalysisEngine {
      constructor(
        @inject(TYPES.Shared.MultiTimeframeService)
        private readonly timeframeService: IMultiTimeframeService
      ) {}

      async analyzeTrend(symbol: string) {
        // 1. 收集多个时间框架的数据
        const query: DataCollectionQuery = {
          symbol,
          timeframes: ['1d', '4h', '1h'],
          startTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
          endTime: new Date(),
          limit: 1000
        };
        const multiTimeframeData = await this.timeframeService.collectData(query);

        console.log(`收集到 ${Object.keys(multiTimeframeData.data).length} 个时间框架的数据`);

        // 2. 分析趋势一致性
        const alignment = await this.timeframeService.getAlignment(multiTimeframeData);

        console.log(`趋势一致性: ${alignment.overallConsistency}`);
        console.log(`主导趋势: ${alignment.dominantTrend.direction} (强度: ${alignment.dominantTrend.strength})`);

        if (alignment.overallConsistency > 0.7 && alignment.dominantTrend.direction === 'bullish') {
          console.log('多时间框架趋势看涨，一致性较高');
        }

        // 3. 处理冲突信号
        if (alignment.conflicts.length > 0) {
          console.log('发现冲突信号:');
          alignment.conflicts.forEach(conflict => {
            console.log(`- ${conflict.timeframes.join(', ')}: ${conflict.description} (严重程度: ${conflict.severity})`);
          });
        }

        // 4. K线聚合示例
        const aggregationParams: KlineAggregationParams = {
          sourceTimeframe: '1h',
          targetTimeframe: '4h',
          klines: multiTimeframeData.data['1h'],
          method: 'standard'
        };
        const aggregatedKlines = await this.timeframeService.aggregateKlines(aggregationParams);

        console.log(`聚合后得到 ${aggregatedKlines.length} 根4小时K线`);

        return {
          alignment,
          multiTimeframeData,
          aggregatedKlines
        };
      }
    }
    ```

---

## 4. 核心数据类型与返回结果

### 4.1. 权重分配相关类型

```typescript
// 权重分配结果
interface WeightingResult {
  weights: TimeframeWeights;        // 实际权重映射
  strategy: string;                 // 使用的策略名称
  confidence: number;               // 置信度 (0-1)
  metadata: {
    calculationTime: number;        // 计算耗时(ms)
    marketCondition: MarketCondition; // 市场状况
    adjustments: string[];          // 调整历史
  };
}

// 时间框架权重映射
interface TimeframeWeights {
  [timeframe: string]: number;      // 如: { "1h": 0.3, "4h": 0.5, "1d": 0.2 }
}

// 时间框架趋势数据
interface TimeframeTrend {
  timeframe: string;                // 时间框架
  direction: string;                // 趋势方向
  strength: number;                 // 强度 (0-1)
  confidence: number;               // 置信度 (0-1)
  volume: number;                   // 成交量
  momentum: number;                 // 动量
  timestamp: Date;                  // 时间戳
}
```

### 4.2. 形态识别相关类型

```typescript
// 形态识别结果
interface PatternRecognitionResult {
  type: PatternType;                // 形态类型
  confidence: number;               // 置信度 (0-1)
  startIndex: number;               // 开始位置
  endIndex: number;                 // 结束位置
  keyPoints: number[];              // 关键点位置
  metadata: {
    strength: number;               // 形态强度
    reliability: number;            // 可靠性
    timeframe: string;              // 时间框架
    detectedAt: Date;               // 检测时间
  };
}

// K线数据点
interface KlineDataPoint {
  timestamp: Date;                  // 时间戳
  open: number;                     // 开盘价
  high: number;                     // 最高价
  low: number;                      // 最低价
  close: number;                    // 收盘价
  volume: number;                   // 成交量
}
```

### 4.3. 多时间框架相关类型

```typescript
// 时间框架对齐结果
interface TimeframeAlignmentResult {
  overallConsistency: number;       // 整体一致性 (0-1)
  dominantTrend: {
    direction: 'bullish' | 'bearish' | 'neutral';
    strength: number;               // 强度
    timeframe: string;              // 主导时间框架
    confidence: number;             // 置信度
  };
  conflicts: Array<{
    timeframes: string[];           // 冲突的时间框架
    conflictType: 'direction' | 'strength' | 'timing';
    severity: 'low' | 'medium' | 'high';
    description: string;            // 冲突描述
  }>;
}

// 多时间框架数据
interface MultiTimeframeData {
  symbol: string;                   // 交易对
  data: {
    [timeframe: string]: KlineDataPoint[]; // 各时间框架的K线数据
  };
  metadata: {
    collectionTime: Date;           // 收集时间
    dataQuality: number;            // 数据质量评分
    completeness: {                 // 数据完整性
      [timeframe: string]: number;
    };
  };
}
```

---

## 5. 最佳实践与常见问题

### Q: 如何注入这些服务？
**A**: 始终通过构造函数使用`@inject()`装饰器进行注入。所有服务的DI令牌（Token）都定义在`src/shared/infrastructure/di/types.ts`中的`TYPES.Shared`命名空间下。

### Q: `DynamicWeightingService`内部有哪些可用的策略？
**A**: 服务内部默认实现了`IntelligentWeightingStrategy`策略。可以通过以下方式查看和管理策略：
```typescript
// 获取可用策略列表
const strategies = weightingService.getAvailableStrategies();
console.log('可用策略:', strategies); // ['intelligent']

// 获取默认策略
const defaultStrategy = weightingService.getDefaultStrategy();
console.log('默认策略:', defaultStrategy); // 'intelligent'

// 注册新策略
weightingService.registerStrategy(new CustomWeightingStrategy());
```

### Q: 我可以扩展这些服务吗？
**A**: 可以。例如，为`DynamicWeightingService`添加一个新的权重计算策略：

```typescript
// 1. 实现IWeightingStrategy接口
class CustomWeightingStrategy implements IWeightingStrategy {
  readonly name = 'custom';
  readonly description = '自定义权重策略';

  async calculate(trends: TimeframeTrend[], marketCondition: MarketCondition): Promise<TimeframeWeights> {
    // 自定义权重计算逻辑
    const weights: TimeframeWeights = {};
    trends.forEach(trend => {
      weights[trend.timeframe] = trend.confidence * trend.strength;
    });
    return weights;
  }

  validateInput(trends: TimeframeTrend[], marketCondition: MarketCondition): boolean {
    return trends.length > 0 && marketCondition != null;
  }

  getConfig(): WeightingStrategyConfig {
    return {
      name: this.name,
      description: this.description,
      parameters: {},
      enabled: true
    };
  }

  updateConfig(config: Partial<WeightingStrategyConfig>): void {
    // 更新配置逻辑
  }
}

// 2. 注册策略
weightingService.registerStrategy(new CustomWeightingStrategy());

// 3. 使用自定义策略
const result = await weightingService.allocate(trends, marketCondition, 'custom');
```

### Q: 这些服务是否处理数据获取？
**A**: 不。这些是纯粹的**分析计算服务**。它们接收已经获取和处理好的数据作为输入。数据的获取和处理由**统一数据处理架构**负责。

### Q: 如何处理服务调用中的错误？
**A**: 所有服务都会抛出具体的错误信息，建议使用try-catch处理：

```typescript
try {
  const result = await weightingService.allocate(trends, marketCondition);
  // 处理成功结果
} catch (error) {
  if (error.message === '输入数据验证失败') {
    // 处理输入数据问题
  } else if (error.message === '权重计算结果验证失败') {
    // 处理计算结果问题
  } else {
    // 处理其他错误
    logger.error('权重分配失败', error);
  }
}
```

### Q: 服务的性能如何？
**A**: 所有服务都包含性能监控，可以通过返回结果的metadata查看：

```typescript
const result = await weightingService.allocate(trends, marketCondition);
console.log(`权重计算耗时: ${result.metadata.calculationTime}ms`);

// 对于形态识别，大量K线数据可能需要更长时间
const patterns = await patternService.recognize(klines);
// 建议对大数据集进行分批处理
```

---

## 6. 相关文件索引

### 核心服务与接口
- `src/shared/infrastructure/analysis/services/` - 服务实现目录
  - `DynamicWeightingService.ts` - 动态权重分配服务
  - `PatternRecognitionService.ts` - 形态识别服务
  - `MultiTimeframeService.ts` - 多时间框架服务
- `src/shared/infrastructure/analysis/interfaces/` - 服务接口目录
  - `IDynamicWeightingService.ts` - 权重服务接口
  - `IPatternRecognitionService.ts` - 形态识别接口
  - `IMultiTimeframeService.ts` - 多时间框架接口
- `src/shared/infrastructure/analysis/types/` - 类型定义目录
  - `WeightingTypes.ts` - 权重相关类型
  - `PatternTypes.ts` - 形态相关类型
  - `TimeframeTypes.ts` - 时间框架相关类型
- `src/shared/infrastructure/analysis/strategies/` - 策略实现目录
  - `IntelligentWeightingStrategy.ts` - 智能权重策略
- `src/shared/infrastructure/di/types.ts` - DI类型定义

### 示例实现
- `src/contexts/trend-analysis/application/trend-analysis-application.service.ts` - 一个重度使用所有核心分析服务的优秀范例

### 测试文件
- `src/shared/infrastructure/analysis/__tests__/services/` - 服务测试目录
- `src/shared/infrastructure/analysis/tests/` - 集成测试目录

---

**最后更新**: 2025-07-12
**文档版本**: 2.0 (已更新为真实API)
**文档状态**: ✅ 生效中
**问题反馈**: 请联系架构团队
