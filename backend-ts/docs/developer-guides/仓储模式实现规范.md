# 仓储模式实现规范

**版本**: 1.0  
**状态**: ✅ 已生效  
**维护团队**: 架构团队

## 1. 概述

本文档旨在为项目的数据访问层提供一套统一、标准、高效的实现规范。所有新的数据持久化逻辑都应遵循本文档描述的架构模式。

本规范的核心原则是 **“组合优于继承” (Composition over Inheritance)**，通过依赖注入（DI）和可组合的服务，来构建一个既健壮又灵活的数据访问层。

### 核心优势
- **零重复代码**: 所有仓储共享统一的基础设施，自动获得监控、事务、缓存、日志等企业级功能。
- **高可测试性**: 通过依赖注入，可以轻松地在单元测试中模拟（Mock）底层服务。
- **架构一致性**: 所有仓储遵循相同的、经过验证的模式，降低了维护成本和系统风险。
- **开发高效**: 开发者可以专注于业务逻辑，无需关心底层数据库操作的复杂性。

---

## 2. 核心架构

我们的数据访问层架构基于**组合模式**。具体的业务仓储（Repository）不再继承一个庞大的基类，而是持有（组合）一个轻量的基础服务实例，并将通用职责委托给它。

### 2.1. 架构图

```
                                  ┌──────────────────────────┐
                                  │  RepositoryBaseService   │
                                  │ (提供通用监控、事务、日志) │
                                  └──────────────────────────┘
                                                ↑
                                                │
┌──────────────────────────┐      (持有/组合)      ┌──────────────────────────┐
│ IAnyRepository (接口)    ├<--implements--│  PrismaAnyRepository     │
└──────────────────────────┘                  │ (实现具体的业务逻辑)     │
                                              └──────────────────────────┘
```

### 2.2. 核心组件

#### a. RepositoryBaseService
- **位置**: `src/shared/infrastructure/database/repository-base-service.ts`
- **功能**: 提供统一的监控、事务、缓存、日志功能。这是所有仓储实现必须依赖的核心服务。
- **模式**: 一个可被依赖注入的具体服务类。

#### b. DataMappingService
- **位置**: `src/shared/infrastructure/database/unified-data-mapper.ts`
- **功能**: 提供一套通用的、类型安全的数据转换工具函数（如安全转换日期、数字、JSON等），用于在领域实体（Entity）和数据库记录（Record）之间进行转换。

#### c. 全局PrismaClient单例
- **位置**: `src/shared/infrastructure/database/database.ts`
- **功能**: 统一的数据库连接管理，通过`getGlobalPrismaClient()`提供全局唯一的实例，并通过DI容器进行注入。

---

## 3. 实现规范：如何创建一个新仓储

创建或重构一个仓储需要遵循以下标准步骤。

### 步骤1：定义仓储接口 (领域层)

在对应业务上下文的 `domain/repositories` 目录下，定义一个清晰的接口，描述该仓储需要提供的所有业务方法。

```typescript
// src/contexts/some-context/domain/repositories/ISomeObjectRepository.ts

export interface ISomeObjectRepository {
  findById(id: UniqueEntityId): Promise<SomeObject | null>;
  findByName(name: string): Promise<SomeObject | null>;
  save(entity: SomeObject): Promise<void>;
}
```

### 步骤2：创建仓储实现 (基础设施层)

在 `infrastructure/repositories` 目录下创建实现类。该类必须实现上一步定义的接口。

```typescript
// src/contexts/some-context/infrastructure/repositories/PrismaSomeObjectRepository.ts

@injectable()
export class PrismaSomeObjectRepository implements ISomeObjectRepository {
  // ... 实现细节
}
```

### 步骤3：注入核心服务

在实现类的构造函数中，使用依赖注入（DI）来获取所有必需的服务。

```typescript
import { TYPES } from '@shared/infrastructure/di/types';

@injectable()
export class PrismaSomeObjectRepository implements ISomeObjectRepository {
  constructor(
    // 注入1: Prisma 客户端，用于执行数据库查询
    @inject(TYPES.Database) private readonly prisma: PrismaClient,
    // 注入2: 仓储基础服务，用于执行通用逻辑
    @inject(TYPES.Shared.RepositoryBaseService) private readonly baseService: IRepositoryBaseService<SomeObject>,
    // 注入3: 数据映射服务，用于安全的数据转换
    @inject(TYPES.Shared.DataMappingService) private readonly dataMapper: DataMappingService
  ) {}

  // ...
}
```

### 步骤4：实现业务方法

实现接口中定义的所有方法。**关键**：所有数据库操作都必须被包裹在`baseService.executeWithMonitoring()`中，以确保它们被统一监控、记录和错误处理。

```typescript
async findByName(name: string): Promise<SomeObject | null> {
  // 将业务逻辑包裹在 executeWithMonitoring 中
  return this.baseService.executeWithMonitoring('findByName', async () => {
    const record = await this.prisma.someObject.findFirst({ where: { name } });
    return record ? this.toDomain(record) : null;
  });
}

async save(entity: SomeObject): Promise<void> {
  await this.baseService.executeWithMonitoring('save', async () => {
    const data = this.toPersistence(entity);
    await this.prisma.someObject.create({ data });
  });
}
```

### 步骤5：实现数据转换

创建私有的 `toDomain` 和 `toPersistence` 方法，并使用 `DataMappingService` 来确保转换过程的健壮性和类型安全。

```typescript
private toDomain(record: any): SomeObject {
  return new SomeObject({
    id: this.dataMapper.createEntityIdPublic(record.id),
    name: this.dataMapper.mapRequiredFieldPublic(record.name, 'name'),
    createdAt: this.dataMapper.safeDateConversionPublic(record.createdAt) || new Date(),
    // ... 其他字段
  });
}

private toPersistence(entity: SomeObject): any {
  return {
    id: entity.id.toString(),
    name: entity.name,
    createdAt: entity.createdAt,
    // ... 其他字段
  };
}
```

### 步骤6：在DI容器中注册

最后，在对应业务上下文的 `di/bindings.ts` 或相关模块绑定文件中，将接口与其新的实现进行绑定。

```typescript
// 在 DI 配置文件中
bind<ISomeObjectRepository>(TYPES.SomeContext.SomeObjectRepository)
  .to(PrismaSomeObjectRepository)
  .inSingletonScope();
```

---

## 4. 核心API参考

`RepositoryBaseService` 提供以下核心方法：

- `executeWithMonitoring(operationName, executor)`: **必须使用**。用于包裹所有独立的数据库读写操作。提供自动日志、性能监控和错误处理。
- `executeWithTransaction(operationName, executor)`: 用于需要原子性的批量或连续数据库写操作。`executor`会接收一个事务化的`PrismaClient`实例。
- `executeWithCache(cacheKey, executor, ttl)`: （待提供）用于包裹需要缓存的读操作。

---

## 5. 自动化保障

为了确保本规范得到严格遵守，我们在CI/CD流程中集成了自动化检查脚本。

- **脚本位置**: `scripts/check-repository-architecture.sh`
- **核心规则**:
  1.  禁止直接实例化 `PrismaClient`。
  2.  禁止仓储继承 `UnifiedBaseRepository`。
  3.  强制仓储注入 `RepositoryBaseService`。
  4.  推荐所有数据库操作使用 `executeWithMonitoring`。

任何违反上述规则的代码都将导致CI/CD流程失败，从而保障了架构的长期一致性。

---

## 6. 常见问题与最佳实践

### Q: 为什么不使用继承模式？
**A**: 继承模式会导致类爆炸、僵化的架构和难以测试的代码。组合模式提供了更好的灵活性和可维护性。

### Q: 如何处理复杂查询？
**A**: 对于复杂的业务查询，仍然在 `executeWithMonitoring` 中编写，但可以将查询逻辑分解为多个私有方法来提高可读性。

### Q: 批量操作如何处理？
**A**: 使用 `executeWithTransaction` 来确保原子性：
```typescript
async saveMany(entities: Entity[]): Promise<void> {
  return this.baseService.executeWithTransaction('saveMany', async (tx) => {
    const operations = entities.map(entity => {
      const data = this.toPersistence(entity);
      return tx.entity.create({ data });
    });
    await Promise.all(operations);
  });
}
```

### Q: 如何运行架构检查？
**A**: 在项目根目录执行：
```bash
bash scripts/check-repository-architecture.sh
```

---

## 7. 相关文件索引

### 核心基础设施
- `src/shared/infrastructure/database/repository-base-service.ts` - 基础服务实现
- `src/shared/infrastructure/database/unified-data-mapper.ts` - 数据映射服务
- `src/shared/infrastructure/database/database.ts` - 数据库连接管理
- `src/shared/infrastructure/di/types.ts` - DI类型定义

### 工具和检查
- `scripts/check-repository-architecture.sh` - 架构合规检查脚本
- `scripts/verify-websocket-refactor.sh` - WebSocket重构验证脚本
- `.eslintrc.custom-rules.js` - ESLint自定义规则

### 示例实现
- `src/contexts/user-config/infrastructure/repositories/PrismaUserLLMConfigRepository.ts` - 完整示例
- `src/contexts/market-data/infrastructure/repositories/prisma-market-symbol-repository.ts` - 市场数据示例

---

**最后更新**: 2025-07-09
**文档状态**: ✅ 生效中
**问题反馈**: 请联系架构团队
