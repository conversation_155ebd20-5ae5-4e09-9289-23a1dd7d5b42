# 杜绝重复代码开发规范

**文档版本**: 2.0  
**生效日期**: 2025年1月17日  
**适用范围**: 所有开发人员  
**强制执行**: 是  

---

## 🚨 核心声明

**本项目已投入巨大资源解决20个重复实现问题，绝不允许重蹈覆辙！**

违反本规范的开发人员将承担相应责任，包括但不限于代码回退、重新培训、绩效影响。

---

## 📋 三大核心原则

### 1. 🚫 零容忍原则
- **绝对禁止**复制粘贴代码
- **绝对禁止**重复实现已有功能  
- **绝对禁止**创建功能相似的组件
- **绝对禁止**虚假实现和占位符代码

### 2. 🎯 统一优先原则
- 优先使用项目统一组件
- 优先扩展现有功能
- 优先通过依赖注入获取服务
- 优先查阅文档和咨询团队

### 3. 🏗️ 架构一致性原则
- 遵循既定架构模式
- 使用统一命名规范（驼峰命名）
- 保持代码风格一致
- 维护清晰的分层结构

---

## ✅ 强制检查清单

### 开发前（必须执行）
- [ ] 在`src/shared`搜索类似功能
- [ ] 检查统一服务和工具
- [ ] 确认DI容器中的服务注册
- [ ] 验证`TYPES`定义存在性
- [ ] 查阅项目文档和架构设计
- [ ] 与团队确认需求和方案

### 开发中（持续执行）
- [ ] 每10行代码检查重复逻辑
- [ ] 发现相似代码立即抽象
- [ ] 使用统一工具函数
- [ ] 避免硬编码，使用配置管理
- [ ] 运行ESLint和重复检测
- [ ] 保持测试覆盖率≥80%

### 提交前（强制执行）
- [ ] 运行完整测试套件
- [ ] 执行重复代码检测
- [ ] 检查依赖冲突
- [ ] 验证功能正常
- [ ] 更新相关文档
- [ ] 通过所有自动化检查

---

## 🔧 统一组件使用指南

### 基础设施层

#### 技术指标计算
```typescript
// ✅ 正确 - 使用统一计算器
@inject(TYPES.UnifiedTechnicalIndicatorCalculator)
private readonly calculator: IUnifiedTechnicalIndicatorCalculator;

// ❌ 禁止 - 重复实现
function calculateRSI(prices: number[]): number {
  // 禁止重复实现
}
```

#### HTTP客户端
```typescript
// ✅ 正确 - 使用工厂模式
@inject(TYPES.HttpClientFactory)
private readonly httpFactory: IHttpClientFactory;

// ❌ 禁止 - 直接使用第三方库
import axios from 'axios';
```

#### WebSocket连接
```typescript
// ✅ 正确 - 继承基类
export class MyWebSocketAdapter extends BaseWebSocketAdapter {
  // 实现具体逻辑
}

// ❌ 禁止 - 重复实现连接管理
class MyWebSocket {
  private reconnectAttempts = 0;
}
```

#### 缓存系统
```typescript
// ✅ 正确 - 使用统一缓存
@inject(TYPES.MultiTierCacheService)
private readonly cache: IMultiTierCacheService;

// ❌ 禁止 - 直接使用Redis
import Redis from 'ioredis';
```

#### 错误处理
```typescript
// ✅ 正确 - 使用统一错误处理
@inject(TYPES.UnifiedErrorHandler)
private readonly errorHandler: IUnifiedErrorHandler;

// ❌ 禁止 - 重复try-catch模式
try {
  // 业务逻辑
} catch (error) {
  // 禁止重复错误处理
}
```

### 业务逻辑层

#### 数据库操作
```typescript
// ✅ 正确 - 继承基础Repository
export class MyRepository extends RepositoryBaseService<MyEntity> {
  // 实现特定逻辑
}

// ❌ 禁止 - 直接使用Prisma
@inject(TYPES.PrismaClient)
private readonly prisma: PrismaClient;
```

#### 配置管理
```typescript
// ✅ 正确 - 使用统一配置
@inject(TYPES.UnifiedConfigManager)
private readonly config: IUnifiedConfigManager;

// ❌ 禁止 - 直接读取环境变量
const apiKey = process.env.API_KEY;
```

#### 日志记录
```typescript
// ✅ 正确 - 使用统一Logger
@inject(TYPES.Logger)
private readonly logger: ILogger;

// ❌ 禁止 - 使用console
console.log('禁止使用');
```

#### 健康检查
```typescript
// ✅ 正确 - 实现统一接口
export class MyHealthProvider implements IHealthCheckProvider {
  // 实现健康检查
}

// ❌ 禁止 - 重复实现
async function checkHealth(): Promise<boolean> {
  // 禁止重复实现
}
```

---

## 🚫 严格禁止行为

### 1. 代码复制粘贴
```typescript
// ❌ 绝对禁止
function duplicatedFunction() {
  // 复制粘贴的代码
}
```

### 2. 重复实现功能
```typescript
// ❌ 绝对禁止
class MyTechnicalIndicators {
  calculateSMA() { /* 重复实现 */ }
}
```

### 3. 绕过统一组件
```typescript
// ❌ 绝对禁止
import axios from 'axios';
import Redis from 'ioredis';
```

### 4. 虚假实现
```typescript
// ❌ 绝对禁止
function calculateBollingerBands() {
  throw new Error('请使用统一实现');
  // 临时保留 - 这种做法被严格禁止
}
```

### 5. 违反命名规范
```typescript
// ❌ 禁止下划线命名
const api_client = new ApiClient();

// ✅ 使用驼峰命名
const apiClient = new ApiClient();
```

---

## 🔍 代码审查标准

### 审查者职责
1. **严格检查**重复实现
2. **验证**统一组件使用
3. **确认**架构规范遵循
4. **检查**命名规范
5. **验证**测试质量

### 审查清单
- [ ] 使用统一组件？
- [ ] 无重复实现？
- [ ] 通过依赖注入？
- [ ] 驼峰命名规范？
- [ ] 统一错误处理？
- [ ] 统一配置管理？
- [ ] 统一日志记录？
- [ ] 测试覆盖充分？
- [ ] 文档已更新？

### 审查结果
- **通过**: 符合所有规范
- **需修改**: 指出具体问题
- **拒绝**: 严重违规，需重新设计

---

## ⚡ 自动化工具

### ESLint规则
```json
{
  "rules": {
    "no-duplicate-imports": "error",
    "no-duplicate-case": "error",
    "@typescript-eslint/no-duplicate-enum-values": "error",
    "import/no-duplicates": "error"
  }
}
```

### 检查脚本
```bash
# 重复代码检测
npm run check:duplication

# 架构合规检查
npm run check:architecture

# 依赖注入检查
npm run check:di-config

# 测试覆盖率
npm run test:coverage
```

### Git Hooks
```bash
# Pre-commit检查
#!/bin/sh
echo "🔍 执行提交前检查..."

# ESLint检查
npm run lint || exit 1

# 重复代码检测
./scripts/check-duplicates.sh || exit 1

# 架构合规检查
./scripts/check-architecture.sh || exit 1

# 运行测试
npm test || exit 1

echo "✅ 检查通过，允许提交"
```

---

## 🎯 违规处理机制

### 轻微违规（警告）
- 命名不规范
- 缺少注释
- 测试覆盖不足

**处理**: 立即修改，重新提交

### 中等违规（严重警告）
- 未使用统一组件
- 绕过依赖注入
- 部分重复实现

**处理**: 代码回退，重新设计

### 严重违规（零容忍）
- 复制粘贴代码
- 重复实现功能
- 虚假实现

**处理**: 
1. 立即回退代码
2. 重新学习架构
3. 记录违规行为
4. 影响绩效考核

---

## 📖 实战场景指南

### 场景1：技术指标计算
```typescript
// ❌ 错误做法
export class MyAnalysisService {
  private calculateSMA(prices: number[], period: number): number {
    return prices.slice(-period).reduce((a, b) => a + b) / period;
  }
}

// ✅ 正确做法
@injectable()
export class MyAnalysisService {
  constructor(
    @inject(TYPES.UnifiedTechnicalIndicatorCalculator)
    private readonly calculator: IUnifiedTechnicalIndicatorCalculator
  ) {}

  async analyzeMarket(prices: number[]): Promise<AnalysisResult> {
    const sma20 = await this.calculator.calculateSMA(prices, 20);
    const rsi = await this.calculator.calculateRSI(prices);
    return { sma20, rsi, signal: this.generateSignal(sma20, rsi) };
  }
}
```

### 场景2：HTTP请求
```typescript
// ❌ 错误做法
import axios from 'axios';

export class MyApiService {
  private client = axios.create({
    baseURL: 'https://api.example.com',
    timeout: 5000
  });

  async fetchData(): Promise<any> {
    try {
      const response = await this.client.get('/data');
      return response.data;
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  }
}

// ✅ 正确做法
@injectable()
export class MyApiService {
  private client: IHttpClient;

  constructor(
    @inject(TYPES.HttpClientFactory)
    private readonly httpFactory: IHttpClientFactory,
    @inject(TYPES.UnifiedErrorHandler)
    private readonly errorHandler: IUnifiedErrorHandler
  ) {
    this.client = this.httpFactory.createClient({
      baseURL: 'https://api.example.com',
      timeout: 5000
    });
  }

  async fetchData(): Promise<any> {
    try {
      return await this.client.get('/data');
    } catch (error) {
      await this.errorHandler.handleError(error, {
        context: 'MyApiService.fetchData',
        severity: 'medium'
      });
      throw error;
    }
  }
}
```

### 场景3：缓存操作
```typescript
// ❌ 错误做法
import Redis from 'ioredis';

export class MyDataService {
  private redis = new Redis({ host: 'localhost', port: 6379 });

  async getData(key: string): Promise<any> {
    const cached = await this.redis.get(key);
    if (cached) {
      return JSON.parse(cached);
    }
    const data = await this.fetchFromDatabase(key);
    await this.redis.setex(key, 3600, JSON.stringify(data));
    return data;
  }
}

// ✅ 正确做法
@injectable()
export class MyDataService {
  constructor(
    @inject(TYPES.MultiTierCacheService)
    private readonly cache: IMultiTierCacheService
  ) {}

  async getData(key: string): Promise<any> {
    let data = await this.cache.get<any>(key);
    if (!data) {
      data = await this.fetchFromDatabase(key);
      await this.cache.set(key, data, 3600);
    }
    return data;
  }
}
```

---

## 🚨 虚假实现杜绝规则

### 核心要求

#### 零容忍原则
**禁止任何形式的虚假实现存在于代码中**

- ❌ 抛出错误的占位方法
- ❌ "临时保留"的无用代码
- ❌ 返回硬编码值的占位符
- ❌ 仅为编译通过的空实现

#### 清理标准
发现虚假实现时必须：

1. **完全移除**虚假代码
2. **导入使用**统一实现
3. **更新调用点**
4. **删除临时注释**

#### 实施示例
```typescript
// ❌ 虚假实现（严格禁止）
function calculateBollingerBands() {
  throw new Error('请使用统一实现');
  // 临时保留 - 这种做法被禁止
}

// ✅ 正确做法（完全移除并使用统一实现）
import { UnifiedTechnicalIndicatorCalculator } from '../shared/indicators';
// 直接使用统一实现
```

#### 预防措施
- 代码审查检查虚假实现
- 集成自动检测脚本
- 设置Git hooks预防
- 定期运行验证脚本

#### 违规处理
1. **立即停止开发**
2. **记录问题详情**
3. **制定清理计划**
4. **执行完整清理**
5. **验证清理结果**

---

## 📊 质量监控

### 核心指标
- **重复代码率**: 目标0%，容忍度0%
- **架构合规率**: 目标100%，最低95%
- **测试覆盖率**: 目标90%，最低80%
- **审查通过率**: 目标95%，最低90%

### 监控方式
- **自动检测**: 每次提交自动检测
- **定期扫描**: 每周全量扫描
- **人工审查**: 每月质量审查
- **趋势分析**: 季度趋势分析

### 持续改进
- 收集开发反馈
- 记录审查发现
- 分析工具报告
- 更新规范和工具

---

## 🎓 培训体系

### 新员工培训（3周计划）

**第1周：基础学习**
- 项目架构概览
- 统一组件介绍
- 依赖注入原理
- 代码规范学习

**第2周：实践练习**
- 导师指导开发
- 参与代码审查
- 工具使用练习
- 测试编写练习

**第3周：独立开发**
- 独立功能开发
- 接受代码审查
- 问题解决练习
- 文档编写练习

### 持续学习

**月度分享**
- 最佳实践分享
- 新技术介绍
- 问题案例分析
- 架构演进讨论

**季度评估**
- 代码质量评估
- 架构理解测试
- 问题解决能力
- 团队协作评价

---

## 📚 必读文档

### 开发前必读
1. [项目架构概览](./项目架构概览.md)
2. [统一组件使用指南](./统一组件使用指南.md)
3. [依赖注入配置指南](./依赖注入配置指南.md)
4. [测试开发标准](./测试开发标准与最佳实践.md)

### 开发参考
1. [API设计规范](./API设计规范.md)
2. [错误处理最佳实践](./错误处理最佳实践.md)
3. [性能优化指南](./性能优化指南.md)
4. [安全开发规范](./安全开发规范.md)

### 问题解决
1. [常见问题解答](./常见问题解答.md)
2. [故障排除指南](./故障排除指南.md)
3. [重复实现解决报告](../目前发现的重复实现.md)

---

## 📞 支持渠道

### 技术支持
- **架构问题**: 联系技术负责人
- **组件使用**: 查阅统一组件文档
- **最佳实践**: 参考项目示例代码

### 培训资源
- **新人培训**: 架构和规范培训
- **定期培训**: 技术和实践分享
- **问题解答**: 定期答疑会议

### 文档维护
- **问题反馈**: 及时反馈文档问题
- **改进建议**: 提出规范改进建议
- **经验分享**: 分享开发经验教训

---

## 📝 总结

### 核心要求
1. **严格遵循**本规范所有要求
2. **优先使用**项目统一组件
3. **保持架构**一致性和代码质量
4. **持续学习**和改进开发实践

### 成功标准
- 零重复代码实现
- 100%使用统一组件
- 架构规范完全合规
- 代码质量持续提升

### 最终目标
**维护一个高质量、无重复、可维护的代码库，确保项目长期健康发展！**

---

**文档维护**: 开发团队  
**最后更新**: 2025年1月17日  
**下次审查**: 2025年4月17日  

**重要提醒**: 本规范强制执行，违反将影响绩效考核。让我们共同维护高质量代码库！
