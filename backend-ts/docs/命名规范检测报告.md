# 命名规范检测报告

**生成时间**: 2025/7/18 19:20:21

## 🔍 脚本概述

### 功能说明
本脚本是一个全面的命名规范检测工具，旨在确保代码库中的命名一致性和规范性。

### 检测范围
- **Prisma Schema**: `prisma/schema.prisma` - 检测模型名、字段名和@map注解
- **TypeScript源码**: `src/**/*.ts` - 检测所有生产代码（排除测试文件）
- **覆盖内容**: 接口、变量、常量、对象属性、数据库查询字段等

### 检测原则
- **统一命名**: 拒绝@map注解，要求模型名与表名保持一致
- **TypeScript社区标准**: 接口使用PascalCase，变量使用camelCase，常量使用CONSTANT_CASE
- **智能跳过**: 自动识别并跳过合理的命名模式（私有属性、Prisma聚合字段、Express参数等）
- **零容忍混乱**: 检测并报告命名不一致问题

### 严重性分级
- **🔴 高严重性**: 存在@map注解（表明命名不一致）
- **🟡 中等严重性**: 模型名不符合PascalCase、字段命名不一致
- **🟢 低严重性**: 变量、属性命名不规范

## 📊 检测摘要

- **总问题数**: 21
- **高严重性问题**: 0
- **中等严重性问题**: 1
- **低严重性问题**: 20
- **包含@map注解的模型**: 0
- **字段命名不一致**: 1

## 问题分类

### 命名规范违反 (20个)

**LOW** - 变量名 GLOBAL_PRISMA_CLIENT 不符合 camelCase 命名规范
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/database/database.ts`:10
- 建议: 使用 camelCase: GLOBAL_PRISMA_CLIENT

**LOW** - 常量名 levels 不符合 CONSTANT_CASE 命名规范
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/config/risk-config.service.ts`:170
- 建议: 使用 CONSTANT_CASE: LEVELS

**LOW** - 常量名 horizons 不符合 CONSTANT_CASE 命名规范
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/config/risk-config.service.ts`:176
- 建议: 使用 CONSTANT_CASE: HORIZONS

**LOW** - 变量名 RealtimePushService 不符合 camelCase 命名规范
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/ai/realtime-push-service.ts`:732
- 建议: 使用 camelCase: RealtimePushService

**LOW** - 变量名 PerformanceMonitoringContainerModule 不符合 camelCase 命名规范
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/modules/performance-monitoring-container-module.ts`:9
- 建议: 使用 camelCase: PerformanceMonitoringContainerModule

**LOW** - 变量名 DynamicConfigContainerModule 不符合 camelCase 命名规范
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/modules/dynamic-config-container-module.ts`:9
- 建议: 使用 camelCase: DynamicConfigContainerModule

**LOW** - 变量名 ServiceClass 不符合 camelCase 命名规范
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/base/base-container-module.ts`:137
- 建议: 使用 camelCase: ServiceClass

**LOW** - 变量名 ServiceClass 不符合 camelCase 命名规范
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/base/base-container-module.ts`:176
- 建议: 使用 camelCase: ServiceClass

**LOW** - 变量名 TestDataGenerator 不符合 camelCase 命名规范
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/analysis/__tests__/vitest.setup.ts`:62
- 建议: 使用 camelCase: TestDataGenerator

**LOW** - 变量名 LegacyTestDataGenerator 不符合 camelCase 命名规范
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/analysis/__tests__/vitest.setup.ts`:66
- 建议: 使用 camelCase: LegacyTestDataGenerator

... 还有 10 个类似问题

### 字段名不一致 (1个)

**MEDIUM** - 查询中使用了 snake_case 字段名 block_height，建议使用 camelCase
- 文件: `/Users/<USER>/Ai/06/backend-ts/src/contexts/market-data/infrastructure/external/mempool-adapter.ts`:279
- 建议: 使用 camelCase: blockHeight

## 改进建议

1. 移除所有@map注解，确保模型名和表名命名一致
2. 统一使用camelCase命名TypeScript变量、字段和属性
3. 统一使用PascalCase命名接口、类和Prisma模型
4. 统一使用CONSTANT_CASE命名常量（TypeScript社区标准）
5. 避免在同一项目中混用不同的命名规范
6. 建立代码审查机制，确保命名规范的一致性
7. 使用ESLint规则自动检查命名规范
8. 定期运行此脚本检测命名问题

## 详细问题列表

### 1. [LOW] 命名规范违反

**描述**: 变量名 GLOBAL_PRISMA_CLIENT 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/database/database.ts`:10
**建议**: 使用 camelCase: GLOBAL_PRISMA_CLIENT

---

### 2. [LOW] 命名规范违反

**描述**: 常量名 levels 不符合 CONSTANT_CASE 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/config/risk-config.service.ts`:170
**建议**: 使用 CONSTANT_CASE: LEVELS

---

### 3. [LOW] 命名规范违反

**描述**: 常量名 horizons 不符合 CONSTANT_CASE 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/config/risk-config.service.ts`:176
**建议**: 使用 CONSTANT_CASE: HORIZONS

---

### 4. [LOW] 命名规范违反

**描述**: 变量名 RealtimePushService 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/ai/realtime-push-service.ts`:732
**建议**: 使用 camelCase: RealtimePushService

---

### 5. [LOW] 命名规范违反

**描述**: 变量名 PerformanceMonitoringContainerModule 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/modules/performance-monitoring-container-module.ts`:9
**建议**: 使用 camelCase: PerformanceMonitoringContainerModule

---

### 6. [LOW] 命名规范违反

**描述**: 变量名 DynamicConfigContainerModule 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/modules/dynamic-config-container-module.ts`:9
**建议**: 使用 camelCase: DynamicConfigContainerModule

---

### 7. [LOW] 命名规范违反

**描述**: 变量名 ServiceClass 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/base/base-container-module.ts`:137
**建议**: 使用 camelCase: ServiceClass

---

### 8. [LOW] 命名规范违反

**描述**: 变量名 ServiceClass 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/base/base-container-module.ts`:176
**建议**: 使用 camelCase: ServiceClass

---

### 9. [LOW] 命名规范违反

**描述**: 变量名 TestDataGenerator 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/analysis/__tests__/vitest.setup.ts`:62
**建议**: 使用 camelCase: TestDataGenerator

---

### 10. [LOW] 命名规范违反

**描述**: 变量名 LegacyTestDataGenerator 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/analysis/__tests__/vitest.setup.ts`:66
**建议**: 使用 camelCase: LegacyTestDataGenerator

---

### 11. [LOW] 命名规范违反

**描述**: 变量名 TestUtils 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/analysis/__tests__/vitest.setup.ts`:95
**建议**: 使用 camelCase: TestUtils

---

### 12. [LOW] 命名规范违反

**描述**: 变量名 vol_i 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`:1009
**建议**: 使用 camelCase: volI

---

### 13. [LOW] 命名规范违反

**描述**: 变量名 vol_j 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`:1010
**建议**: 使用 camelCase: volJ

---

### 14. [LOW] 命名规范违反

**描述**: 变量名 aggregatedVar95_1d 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`:1032
**建议**: 使用 camelCase: aggregatedVar95_1d

---

### 15. [LOW] 命名规范违反

**描述**: 变量名 aggregatedVar99_1d 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts`:1033
**建议**: 使用 camelCase: aggregatedVar99_1d

---

### 16. [LOW] 命名规范违反

**描述**: 变量名 PrismaPriceDataRepository 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/contexts/market-data/infrastructure/repositories/prisma-price-data-repository.ts`:862
**建议**: 使用 camelCase: PrismaPriceDataRepository

---

### 17. [LOW] 命名规范违反

**描述**: 变量名 PrismaMarketSymbolRepository 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/contexts/market-data/infrastructure/repositories/prisma-market-symbol-repository.ts`:739
**建议**: 使用 camelCase: PrismaMarketSymbolRepository

---

### 18. [MEDIUM] 字段名不一致

**描述**: 查询中使用了 snake_case 字段名 block_height，建议使用 camelCase
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/contexts/market-data/infrastructure/external/mempool-adapter.ts`:279
**字段**: block_height
**建议**: 使用 camelCase: blockHeight

---

### 19. [LOW] 命名规范违反

**描述**: 变量名 TradingSymbol 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/api/routes/trend-analysis/routes/fundamental-analysis-routes.ts`:82
**建议**: 使用 camelCase: TradingSymbol

---

### 20. [LOW] 命名规范违反

**描述**: 变量名 RiskDataStrategyUsageExamples 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/data-processing/strategies/risk/risk-data-usage-example.ts`:252
**建议**: 使用 camelCase: RiskDataStrategyUsageExamples

---

### 21. [LOW] 命名规范违反

**描述**: 变量名 depthResponse_json 不符合 camelCase 命名规范
**文件**: `/Users/<USER>/Ai/06/backend-ts/src/contexts/trend-analysis/infrastructure/modules/fundamental/fundamental-analysis-module.ts`:1004
**建议**: 使用 camelCase: depthResponseJson

---

