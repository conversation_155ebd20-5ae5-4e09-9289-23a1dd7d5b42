# 代码审查检查清单：防重复实现专项

## 📋 审查前准备

### 自动化检查
- [ ] **ESLint检查通过** - 确保没有违反防重复实现规则
- [ ] **重复实现检测器通过** - 运行 `npm run detect:redundant`
- [ ] **已知重复验证通过** - 运行 `npm run validate:known-duplications`
- [ ] **CI/CD质量门禁通过** - 检查GitHub Actions状态

### 文件范围确认
- [ ] **识别变更文件类型** - 区分应用代码、脚本、测试文件
- [ ] **确认影响范围** - 检查是否涉及核心基础设施
- [ ] **评估复杂度** - 大型重构需要额外关注

## 🔍 核心检查项目

### 1. HTTP客户端使用检查

**🚨 严重违规指标：**
- [ ] 直接导入 `axios`（应用代码中）
- [ ] 创建新的HTTP客户端类
- [ ] 使用 `fetch` 或其他HTTP库
- [ ] 重复实现请求重试逻辑
- [ ] 重复实现错误处理逻辑

**✅ 正确实现确认：**
- [ ] 继承 `BaseHttpClient`
- [ ] 使用 `HttpClientFactory`
- [ ] 通过DI注入HTTP客户端
- [ ] 使用 `ExternalDataAdapterBase`

**📝 检查命令：**
```bash
# 检查axios导入
grep -r "import.*axios" src/ --include="*.ts" | grep -v test

# 检查HTTP客户端创建
grep -r "new.*Client" src/ --include="*.ts"
```

### 2. 健康检查逻辑检查

**🚨 严重违规指标：**
- [ ] 实现 `healthCheck` 方法
- [ ] 创建 `/health` 路由
- [ ] 重复的健康状态检查逻辑
- [ ] 分散的服务状态检查

**✅ 正确实现确认：**
- [ ] 使用 `UnifiedHealthController`
- [ ] 实现 `IHealthCheckProvider` 接口
- [ ] 注册到 `HealthCheckAggregator`
- [ ] 通过统一端点 `/api/health` 访问

**📝 检查命令：**
```bash
# 检查健康检查方法
grep -r "healthCheck\|checkHealth" src/ --include="*.ts" | grep -v unified-health

# 检查健康检查路由
grep -r "'/health'" src/ --include="*.ts"
```

### 3. DTO映射逻辑检查

**🚨 严重违规指标：**
- [ ] 实现 `mapToResponse` 方法
- [ ] 实现 `toDto` 或 `fromDto` 方法
- [ ] 重复的数据转换逻辑
- [ ] 手动字段映射代码

**✅ 正确实现确认：**
- [ ] 使用 `UnifiedDtoMapperRegistry`
- [ ] 继承 `BaseDTOMapper`
- [ ] 通过工厂获取映射器
- [ ] 注册自定义映射器

**📝 检查命令：**
```bash
# 检查映射方法
grep -r "mapTo\|toDto\|fromDto" src/ --include="*.ts" | grep -v dto-mapper

# 检查响应映射
grep -r "ToResponse\|mapToResponse" src/ --include="*.ts"
```

### 4. 技术指标计算检查

**🚨 严重违规指标：**
- [ ] 实现 `calculateRSI` 等技术指标方法
- [ ] 重复的数学计算逻辑
- [ ] 硬编码的指标参数
- [ ] 私有的指标计算方法

**✅ 正确实现确认：**
- [ ] 使用 `UnifiedTechnicalIndicatorCalculator`
- [ ] 通过DI注入技术指标服务
- [ ] 使用标准化的指标接口
- [ ] 配置化的指标参数

**📝 检查命令：**
```bash
# 检查技术指标计算
grep -r "calculate.*RSI\|calculate.*MACD\|calculate.*SMA" src/ --include="*.ts" | grep -v unified-technical

# 检查私有计算方法
grep -r "private.*calculate" src/ --include="*.ts"
```

### 5. 数据库客户端检查

**🚨 严重违规指标：**
- [ ] 创建 `new PrismaClient()`
- [ ] 多个数据库连接实例
- [ ] 直接数据库操作（非Repository模式）
- [ ] 重复的数据库配置

**✅ 正确实现确认：**
- [ ] 通过DI注入 `PrismaClient`
- [ ] 使用Repository模式
- [ ] 统一的数据库配置
- [ ] 连接池管理

**📝 检查命令：**
```bash
# 检查PrismaClient实例
grep -r "new PrismaClient" src/ --include="*.ts"

# 检查数据库直接操作
grep -r "prisma\." src/ --include="*.ts" | grep -v repository
```

## 🎯 特殊场景检查

### 1. 新增服务类
- [ ] **DI注册** - 是否正确注册到容器
- [ ] **接口实现** - 是否实现了标准接口
- [ ] **依赖注入** - 是否通过构造函数注入依赖
- [ ] **单一职责** - 是否遵循单一职责原则

### 2. 外部API集成
- [ ] **适配器模式** - 是否继承 `ExternalDataAdapterBase`
- [ ] **错误处理** - 是否使用统一的错误处理
- [ ] **重试机制** - 是否使用统一的重试逻辑
- [ ] **缓存策略** - 是否集成缓存系统

### 3. 新增路由
- [ ] **控制器分离** - 是否将逻辑放在控制器中
- [ ] **中间件使用** - 是否使用标准中间件
- [ ] **错误处理** - 是否使用统一的错误处理
- [ ] **文档更新** - 是否更新API文档

### 4. 数据处理逻辑
- [ ] **验证逻辑** - 是否使用统一的验证器
- [ ] **清理逻辑** - 是否使用统一的数据清理器
- [ ] **转换逻辑** - 是否使用统一的转换器
- [ ] **缓存集成** - 是否适当使用缓存

## 📊 审查评分标准

### 严重问题 (阻止合并)
- 直接违反防重复实现规则
- 创建新的重复实现
- 破坏现有架构约束
- 引入安全风险

### 重要问题 (需要修改)
- 不符合编码规范
- 缺少必要的测试
- 性能问题
- 文档缺失

### 轻微问题 (建议改进)
- 代码风格问题
- 命名不规范
- 注释不充分
- 可读性问题

## 🔧 审查工具使用

### 1. 自动化检查脚本
```bash
# 运行完整检查
./scripts/ci/quality-gate-check.sh

# 运行重复实现检测
npm run detect:redundant

# 运行ESLint检查
npm run lint
```

### 2. 手动检查命令
```bash
# 检查特定模式
grep -r "pattern" src/ --include="*.ts"

# 检查文件变更
git diff --name-only HEAD~1

# 检查代码复杂度
npx ts-node scripts/analyze-complexity.ts
```

## 📝 审查报告模板

### 审查通过
```markdown
## ✅ 代码审查通过

### 检查项目
- [x] 防重复实现规则检查
- [x] 架构约束验证
- [x] 代码质量评估
- [x] 测试覆盖率检查

### 评价
代码质量良好，符合项目规范，可以合并。

### 建议
[可选的改进建议]
```

### 审查不通过
```markdown
## ❌ 代码审查不通过

### 发现问题
1. **严重问题**：[具体描述]
2. **重要问题**：[具体描述]

### 修改要求
- [ ] 修复重复实现问题
- [ ] 遵循架构约束
- [ ] 补充必要测试

### 修改后重新审查
请修改后重新提交审查。
```

## 🔄 持续改进

### 审查质量跟踪
- 记录常见问题类型
- 统计审查通过率
- 分析问题趋势

### 规则更新
- 根据新发现的问题更新检查清单
- 完善自动化检测规则
- 优化审查流程

---

**记住：代码审查是保证代码质量的最后一道防线，每个审查者都有责任严格执行这些检查项目。**
