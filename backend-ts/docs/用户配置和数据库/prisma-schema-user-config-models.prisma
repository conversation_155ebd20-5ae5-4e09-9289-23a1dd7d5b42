// 用户配置系统Prisma模型定义
// 严格遵循项目命名规范：
// - 模型名使用PascalCase
// - 字段名使用camelCase  
// - 不使用@map注解，确保模型名与表名一致
// - 索引名使用camelCase

// 用户画像模型
model UserProfiles {
  id                     String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId                 String   @unique @db.VarChar(255)
  
  // 风险偏好
  riskTolerance          String   @default("BALANCED") @db.VarChar(20)
  targetReturn           Decimal  @default(0.15) @db.Decimal(5, 4)
  maxAcceptableDrawdown  Decimal  @default(0.20) @db.Decimal(5, 4)
  
  // 投资期限
  investmentHorizon      String   @default("MEDIUM") @db.VarChar(20)
  
  // 交易偏好
  preferredAssets        Json     @default("[\"BTC\", \"ETH\"]")
  tradingStyle           String   @default("MIXED") @db.VarChar(20)
  
  // 仓位管理
  maxPositionSize        Decimal  @default(0.10) @db.Decimal(5, 4)
  enableStopLoss         Boolean  @default(true)
  enableTakeProfit       Boolean  @default(true)
  
  // 自定义阈值
  customThresholds       Json     @default("{}")
  
  // 元数据
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  version                Int      @default(1)
  
  // 关系
  user                   Users    @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // 索引
  @@index([userId], name: "idxUserProfilesUserId")
  @@index([riskTolerance], name: "idxUserProfilesRiskTolerance")
  @@index([updatedAt], name: "idxUserProfilesUpdatedAt")
  @@index([riskTolerance, investmentHorizon, tradingStyle], name: "idxUserProfilesRiskComposite")
  @@index([customThresholds], name: "idxUserProfilesCustomThresholdsGin", type: Gin)
}

// 用户偏好设置模型
model UserPreferences {
  id                    String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId                String   @unique @db.VarChar(255)
  
  // 设置分类
  notificationSettings  Json     @default("{}")
  displaySettings       Json     @default("{}")
  tradingSettings       Json     @default("{}")
  customSettings        Json     @default("{}")
  
  // 元数据
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  
  // 关系
  user                  Users    @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // 索引
  @@index([userId], name: "idxUserPreferencesUserId")
  @@index([notificationSettings, displaySettings, tradingSettings], name: "idxUserPreferencesSettingsGin", type: Gin)
}

// 配置历史记录模型
model UserConfigHistory {
  id             String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId         String   @db.VarChar(255)
  configType     String   @db.VarChar(50) // 'profile', 'preferences', 'llmConfig'
  configId       String   @db.Uuid
  
  // 变更信息
  oldValues      Json?
  newValues      Json?
  changedFields  String[]
  
  // 变更元数据
  changeReason   String?  @db.VarChar(255)
  changedBy      String?  @db.VarChar(255)
  changeSource   String?  @db.VarChar(50) // 'user', 'system', 'admin'
  
  createdAt      DateTime @default(now())
  
  // 关系
  user           Users    @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // 索引
  @@index([userId], name: "idxConfigHistoryUserId")
  @@index([configType], name: "idxConfigHistoryConfigType")
  @@index([createdAt], name: "idxConfigHistoryCreatedAt")
  @@index([userId, configType, createdAt], name: "idxConfigHistoryComposite")
  @@index([userId, configType], name: "idxRecentConfigHistory")
}

// 扩展现有UserLlmConfigs模型（如果需要修改）
// 注意：这里展示如何添加新字段，实际使用时需要通过迁移添加
/*
model UserLlmConfigs {
  // ... 现有字段保持不变 ...
  
  // 新增字段
  version    Int     @default(1)
  isActive   Boolean @default(true)
  priority   Int     @default(0)
  
  // 新增索引
  @@index([userId, provider, isActive], name: "idxUserLlmConfigsComposite")
  @@index([userId, priority], name: "idxActiveLlmConfigs")
}
*/

// 用户配置枚举类型定义（在TypeScript中使用）
// enum RiskTolerance {
//   CONSERVATIVE = "CONSERVATIVE"
//   BALANCED = "BALANCED"
//   AGGRESSIVE = "AGGRESSIVE"
// }

// enum InvestmentHorizon {
//   SHORT = "SHORT"
//   MEDIUM = "MEDIUM"
//   LONG = "LONG"
// }

// enum TradingStyle {
//   SCALPING = "SCALPING"
//   DAY_TRADING = "DAY_TRADING"
//   SWING_TRADING = "SWING_TRADING"
//   POSITION_TRADING = "POSITION_TRADING"
//   MIXED = "MIXED"
// }

// enum ConfigChangeSource {
//   USER = "user"
//   SYSTEM = "system"
//   ADMIN = "admin"
// }

// 默认配置常量（在TypeScript中使用）
// const DEFAULT_USER_PROFILE = {
//   riskTolerance: "BALANCED",
//   targetReturn: 0.15,
//   maxAcceptableDrawdown: 0.20,
//   investmentHorizon: "MEDIUM",
//   preferredAssets: ["BTC", "ETH"],
//   tradingStyle: "MIXED",
//   maxPositionSize: 0.10,
//   enableStopLoss: true,
//   enableTakeProfit: true,
//   customThresholds: {
//     trendStrengthThreshold: 0.6,
//     riskScoreThreshold: 0.7,
//     confidenceThreshold: 0.5
//   }
// };

// const DEFAULT_USER_PREFERENCES = {
//   notificationSettings: {
//     email: true,
//     push: true,
//     sms: false,
//     tradingSignals: true,
//     riskAlerts: true,
//     marketUpdates: false
//   },
//   displaySettings: {
//     theme: "dark",
//     language: "zh-CN",
//     timezone: "Asia/Shanghai",
//     currency: "USD",
//     dateFormat: "YYYY-MM-DD",
//     numberFormat: "en-US"
//   },
//   tradingSettings: {
//     autoExecute: false,
//     confirmBeforeOrder: true,
//     defaultOrderType: "LIMIT",
//     slippageTolerance: 0.005,
//     gasPrice: "standard"
//   }
// };

// 数据验证约束（在数据库层面实现）
// ALTER TABLE "UserProfiles" ADD CONSTRAINT "chkTargetReturnRange" 
//   CHECK (targetReturn >= 0 AND targetReturn <= 1);
// 
// ALTER TABLE "UserProfiles" ADD CONSTRAINT "chkMaxDrawdownRange" 
//   CHECK (maxAcceptableDrawdown >= 0 AND maxAcceptableDrawdown <= 1);
// 
// ALTER TABLE "UserProfiles" ADD CONSTRAINT "chkPositionSizeRange" 
//   CHECK (maxPositionSize >= 0 AND maxPositionSize <= 1);
// 
// ALTER TABLE "UserProfiles" ADD CONSTRAINT "chkRiskToleranceValues" 
//   CHECK (riskTolerance IN ('CONSERVATIVE', 'BALANCED', 'AGGRESSIVE'));
// 
// ALTER TABLE "UserProfiles" ADD CONSTRAINT "chkInvestmentHorizonValues" 
//   CHECK (investmentHorizon IN ('SHORT', 'MEDIUM', 'LONG'));
// 
// ALTER TABLE "UserProfiles" ADD CONSTRAINT "chkTradingStyleValues" 
//   CHECK (tradingStyle IN ('SCALPING', 'DAY_TRADING', 'SWING_TRADING', 'POSITION_TRADING', 'MIXED'));

// 性能优化建议：
// 1. 定期清理UserConfigHistory表中的旧记录
// 2. 为JSONB字段创建适当的GIN索引
// 3. 使用部分索引优化查询性能
// 4. 考虑对UserConfigHistory表进行分区
// 5. 实现配置缓存机制减少数据库查询
