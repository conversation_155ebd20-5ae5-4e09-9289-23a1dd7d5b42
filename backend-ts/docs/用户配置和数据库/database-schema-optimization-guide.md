# 数据库Schema优化详细实施指南

## 概述

本文档提供数据库Schema优化的详细实施步骤，包括性能优化、索引策略、数据完整性和扩展性设计。

## 1. 当前数据库状态分析

### 1.1 现有表结构评估
```bash
# 检查当前表结构
npx prisma db pull
npx prisma generate

# 分析表大小和索引使用情况
psql -d your_database -c "
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public'
ORDER BY tablename, attname;
"
```

### 1.2 性能瓶颈识别
```sql
-- 查找慢查询
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%user%config%'
ORDER BY mean_time DESC
LIMIT 10;

-- 检查索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
```

## 2. 优化策略

### 2.1 索引优化策略

#### 2.1.1 复合索引设计 - 符合命名规范
```sql
-- 用户配置查询优化 - 使用PascalCase表名和camelCase字段名
CREATE INDEX CONCURRENTLY "idxUserLlmConfigsComposite"
ON "UserLlmConfigs"(userId, provider, isActive)
WHERE isActive = true;

-- 配置历史查询优化
CREATE INDEX CONCURRENTLY "idxConfigHistoryComposite"
ON "UserConfigHistory"(userId, configType, createdAt DESC);

-- 用户画像风险查询优化
CREATE INDEX CONCURRENTLY "idxUserProfilesRiskComposite"
ON "UserProfiles"(riskTolerance, investmentHorizon, tradingStyle);
```

#### 2.1.2 部分索引优化 - 符合命名规范
```sql
-- 只为活跃配置创建索引
CREATE INDEX CONCURRENTLY "idxActiveLlmConfigs"
ON "UserLlmConfigs"(userId, priority DESC)
WHERE isActive = true;

-- 只为最近的配置历史创建索引
CREATE INDEX CONCURRENTLY "idxRecentConfigHistory"
ON "UserConfigHistory"(userId, configType)
WHERE createdAt > NOW() - INTERVAL '30 days';
```

#### 2.1.3 JSONB索引优化 - 符合命名规范
```sql
-- 为JSONB字段创建GIN索引
CREATE INDEX CONCURRENTLY "idxUserProfilesCustomThresholdsGin"
ON "UserProfiles" USING GIN (customThresholds);

CREATE INDEX CONCURRENTLY "idxUserPreferencesSettingsGin"
ON "UserPreferences" USING GIN (notificationSettings, displaySettings, tradingSettings);

-- 为特定JSONB路径创建索引
CREATE INDEX CONCURRENTLY "idxUserProfilesTrendThreshold"
ON "UserProfiles" USING BTREE ((customThresholds->>'trendStrengthThreshold'));

CREATE INDEX CONCURRENTLY "idxNotificationEmailEnabled"
ON "UserPreferences" USING BTREE ((notificationSettings->>'email'));
```

### 2.2 分区策略

#### 2.2.1 配置历史表分区
```sql
-- 按时间分区配置历史表
CREATE TABLE user_config_history_partitioned (
    LIKE user_config_history INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- 创建月度分区
CREATE TABLE user_config_history_2024_01 PARTITION OF user_config_history_partitioned
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE user_config_history_2024_02 PARTITION OF user_config_history_partitioned
FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

-- 自动分区管理函数
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
RETURNS void AS $$
DECLARE
    partition_name text;
    end_date date;
BEGIN
    partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM');
    end_date := start_date + interval '1 month';
    
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF %I
                    FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;
```

### 2.3 数据类型优化

#### 2.3.1 枚举类型定义
```sql
-- 创建枚举类型提高性能和数据一致性
CREATE TYPE risk_tolerance_enum AS ENUM ('CONSERVATIVE', 'BALANCED', 'AGGRESSIVE');
CREATE TYPE investment_horizon_enum AS ENUM ('SHORT', 'MEDIUM', 'LONG');
CREATE TYPE trading_style_enum AS ENUM ('SCALPING', 'DAY_TRADING', 'SWING_TRADING', 'POSITION_TRADING', 'MIXED');
CREATE TYPE config_change_source_enum AS ENUM ('user', 'system', 'admin');

-- 修改表结构使用枚举类型
ALTER TABLE user_profiles 
ALTER COLUMN risk_tolerance TYPE risk_tolerance_enum USING risk_tolerance::risk_tolerance_enum;

ALTER TABLE user_profiles 
ALTER COLUMN investment_horizon TYPE investment_horizon_enum USING investment_horizon::investment_horizon_enum;

ALTER TABLE user_profiles 
ALTER COLUMN trading_style TYPE trading_style_enum USING trading_style::trading_style_enum;
```

## 3. 数据完整性约束

### 3.1 检查约束
```sql
-- 添加业务规则约束
ALTER TABLE user_profiles 
ADD CONSTRAINT chk_target_return_range 
CHECK (target_return >= 0 AND target_return <= 1);

ALTER TABLE user_profiles 
ADD CONSTRAINT chk_max_drawdown_range 
CHECK (max_acceptable_drawdown >= 0 AND max_acceptable_drawdown <= 1);

ALTER TABLE user_profiles 
ADD CONSTRAINT chk_position_size_range 
CHECK (max_position_size >= 0 AND max_position_size <= 1);

-- 逻辑一致性约束
ALTER TABLE user_profiles 
ADD CONSTRAINT chk_risk_return_consistency 
CHECK (
    (risk_tolerance = 'CONSERVATIVE' AND target_return <= 0.3) OR
    (risk_tolerance = 'BALANCED' AND target_return <= 0.5) OR
    (risk_tolerance = 'AGGRESSIVE' AND target_return <= 1.0)
);
```

### 3.2 触发器实现

#### 3.2.1 自动更新时间戳
```sql
-- 创建更新时间戳函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为相关表添加触发器
CREATE TRIGGER trigger_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

#### 3.2.2 配置变更历史记录
```sql
-- 配置变更记录函数
CREATE OR REPLACE FUNCTION record_config_change()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        INSERT INTO user_config_history (
            user_id,
            config_type,
            config_id,
            old_values,
            new_values,
            changed_fields,
            change_source
        ) VALUES (
            NEW.user_id,
            TG_TABLE_NAME,
            NEW.id,
            to_jsonb(OLD),
            to_jsonb(NEW),
            ARRAY(SELECT key FROM jsonb_each(to_jsonb(NEW)) WHERE to_jsonb(NEW)->>key != to_jsonb(OLD)->>key),
            'system'
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为配置表添加变更记录触发器
CREATE TRIGGER trigger_user_profiles_change_history
    AFTER UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION record_config_change();
```

## 4. 性能监控和维护

### 4.1 性能监控视图
```sql
-- 创建配置查询性能监控视图
CREATE VIEW v_config_query_performance AS
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    n_tup_hot_upd,
    n_live_tup,
    n_dead_tup,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
WHERE tablename LIKE '%user_%config%' OR tablename LIKE '%user_profile%';

-- 索引使用效率监控
CREATE VIEW v_config_index_efficiency AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_tup_read = 0 THEN 'INEFFICIENT'
        ELSE 'ACTIVE'
    END as index_status
FROM pg_stat_user_indexes
WHERE schemaname = 'public' 
AND (tablename LIKE '%user_%config%' OR tablename LIKE '%user_profile%');
```

### 4.2 自动维护脚本
```sql
-- 创建自动清理过期配置历史的函数
CREATE OR REPLACE FUNCTION cleanup_old_config_history()
RETURNS void AS $$
BEGIN
    -- 删除90天前的配置历史记录
    DELETE FROM user_config_history 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- 记录清理日志
    INSERT INTO system_logs (level, message, created_at)
    VALUES ('INFO', 'Cleaned up old config history records', NOW());
END;
$$ LANGUAGE plpgsql;

-- 创建定期统计信息更新函数
CREATE OR REPLACE FUNCTION update_config_table_stats()
RETURNS void AS $$
BEGIN
    ANALYZE user_profiles;
    ANALYZE user_preferences;
    ANALYZE user_llm_configs;
    ANALYZE user_model_preferences;
    ANALYZE user_config_history;
END;
$$ LANGUAGE plpgsql;
```

## 5. 迁移执行计划

### 5.1 迁移脚本模板
```typescript
// migrations/002_optimize_user_config_schema.ts
import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
    // 1. 创建枚举类型
    await knex.raw(`
        CREATE TYPE risk_tolerance_enum AS ENUM ('CONSERVATIVE', 'BALANCED', 'AGGRESSIVE');
        CREATE TYPE investment_horizon_enum AS ENUM ('SHORT', 'MEDIUM', 'LONG');
        CREATE TYPE trading_style_enum AS ENUM ('SCALPING', 'DAY_TRADING', 'SWING_TRADING', 'POSITION_TRADING', 'MIXED');
    `);

    // 2. 添加新列
    await knex.schema.alterTable('user_profiles', (table) => {
        table.integer('version').notNullable().defaultTo(1);
        table.boolean('is_active').notNullable().defaultTo(true);
    });

    // 3. 创建索引
    await knex.raw('CREATE INDEX CONCURRENTLY idx_user_profiles_composite ON user_profiles(user_id, risk_tolerance, is_active)');
    
    // 4. 添加约束
    await knex.raw(`
        ALTER TABLE user_profiles 
        ADD CONSTRAINT chk_target_return_range 
        CHECK (target_return >= 0 AND target_return <= 1)
    `);

    // 5. 创建触发器
    await knex.raw(`
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        CREATE TRIGGER trigger_user_profiles_updated_at
            BEFORE UPDATE ON user_profiles
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    `);
}

export async function down(knex: Knex): Promise<void> {
    // 回滚操作
    await knex.raw('DROP TRIGGER IF EXISTS trigger_user_profiles_updated_at ON user_profiles');
    await knex.raw('DROP FUNCTION IF EXISTS update_updated_at_column()');
    
    await knex.schema.alterTable('user_profiles', (table) => {
        table.dropColumn('version');
        table.dropColumn('is_active');
    });
    
    await knex.raw('DROP INDEX CONCURRENTLY IF EXISTS idx_user_profiles_composite');
    await knex.raw('DROP TYPE IF EXISTS risk_tolerance_enum CASCADE');
    await knex.raw('DROP TYPE IF EXISTS investment_horizon_enum CASCADE');
    await knex.raw('DROP TYPE IF EXISTS trading_style_enum CASCADE');
}
```

### 5.2 执行步骤
1. **备份数据库**
   ```bash
   pg_dump -h localhost -U username -d database_name > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **执行迁移**
   ```bash
   npm run migrate:up
   ```

3. **验证迁移**
   ```bash
   npm run migrate:status
   psql -d database_name -c "SELECT * FROM v_config_query_performance;"
   ```

4. **性能测试**
   ```bash
   npm run test:performance
   ```

## 6. 监控和告警

### 6.1 关键指标监控
- 配置查询响应时间
- 索引命中率
- 表扫描频率
- 死锁和阻塞情况
- 存储空间使用

### 6.2 告警规则
```sql
-- 慢查询告警
SELECT 
    query,
    mean_time
FROM pg_stat_statements 
WHERE query LIKE '%user%config%' 
AND mean_time > 1000; -- 超过1秒的查询

-- 索引使用率告警
SELECT 
    tablename,
    indexname
FROM pg_stat_user_indexes 
WHERE idx_scan = 0 
AND schemaname = 'public';
```

这份详细的数据库Schema优化指南提供了完整的实施步骤和最佳实践，请根据实际情况调整和执行。
