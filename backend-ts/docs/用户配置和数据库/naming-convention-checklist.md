# 命名规范检查清单

## 概述

根据项目的命名规范检测报告，本文档提供了严格的命名规范检查清单，确保所有新代码都符合项目标准。

## 🔴 严重违规（零容忍）

### 1. 禁止使用@map注解
```prisma
// ❌ 错误 - 使用了@map注解
model UserProfile {
  id String @id
  user_id String @map("user_id")
}

// ✅ 正确 - 模型名与表名一致，字段使用camelCase
model UserProfiles {
  id String @id
  userId String
}
```

### 2. 数据库表名必须使用PascalCase
```sql
-- ❌ 错误
CREATE TABLE user_profiles (...);
CREATE TABLE user_llm_configs (...);

-- ✅ 正确
CREATE TABLE "UserProfiles" (...);
CREATE TABLE "UserLlmConfigs" (...);
```

## 🟡 中等违规

### 3. Prisma模型名必须使用PascalCase
```prisma
// ❌ 错误
model userProfile { ... }
model user_profile { ... }

// ✅ 正确
model UserProfiles { ... }
model UserLlmConfigs { ... }
```

### 4. 数据库字段名必须使用camelCase
```prisma
// ❌ 错误
model UserProfiles {
  user_id String
  risk_tolerance String
  created_at DateTime
}

// ✅ 正确
model UserProfiles {
  userId String
  riskTolerance String
  createdAt DateTime
}
```

### 5. 索引名必须使用camelCase
```sql
-- ❌ 错误
CREATE INDEX idx_user_profiles_user_id ON "UserProfiles"(userId);

-- ✅ 正确
CREATE INDEX "idxUserProfilesUserId" ON "UserProfiles"(userId);
```

### 6. 约束名必须使用camelCase
```sql
-- ❌ 错误
CONSTRAINT fk_user_profiles_user_id FOREIGN KEY ...

-- ✅ 正确
CONSTRAINT "fkUserProfilesUserId" FOREIGN KEY ...
```

## 🟢 低严重性违规

### 7. TypeScript接口使用PascalCase
```typescript
// ❌ 错误
interface userProfile { ... }
interface user_config { ... }

// ✅ 正确
interface UserProfile { ... }
interface UserConfig { ... }
```

### 8. TypeScript变量使用camelCase
```typescript
// ❌ 错误
const user_id = "123";
const UserName = "John";
const USER_PROFILE = {};

// ✅ 正确
const userId = "123";
const userName = "John";
const userProfile = {};
```

### 9. TypeScript常量使用CONSTANT_CASE
```typescript
// ❌ 错误
const defaultConfig = {};
const maxRetries = 3;

// ✅ 正确
const DEFAULT_CONFIG = {};
const MAX_RETRIES = 3;
```

### 10. 对象属性使用camelCase
```typescript
// ❌ 错误
const config = {
  risk_tolerance: "BALANCED",
  max_position_size: 0.1,
  enable_stop_loss: true
};

// ✅ 正确
const config = {
  riskTolerance: "BALANCED",
  maxPositionSize: 0.1,
  enableStopLoss: true
};
```

## 特殊情况处理

### 11. 枚举类型
```typescript
// ✅ 正确 - 枚举名使用PascalCase，值使用CONSTANT_CASE
enum RiskTolerance {
  CONSERVATIVE = "CONSERVATIVE",
  BALANCED = "BALANCED", 
  AGGRESSIVE = "AGGRESSIVE"
}
```

### 12. 类名和方法名
```typescript
// ✅ 正确
class UserConfigService {
  getUserProfile(userId: string): Promise<UserProfile> { ... }
  updateUserPreferences(userId: string, preferences: UserPreferences): Promise<void> { ... }
}
```

### 13. 文件名
```
// ✅ 正确 - 使用kebab-case
user-config-service.ts
user-profile-repository.ts
database-schema-optimization-guide.md
```

## 自动检查工具配置

### ESLint规则
```json
{
  "rules": {
    "@typescript-eslint/naming-convention": [
      "error",
      {
        "selector": "interface",
        "format": ["PascalCase"]
      },
      {
        "selector": "variable",
        "format": ["camelCase", "UPPER_CASE"]
      },
      {
        "selector": "function",
        "format": ["camelCase"]
      },
      {
        "selector": "class",
        "format": ["PascalCase"]
      },
      {
        "selector": "enum",
        "format": ["PascalCase"]
      },
      {
        "selector": "enumMember",
        "format": ["UPPER_CASE"]
      }
    ]
  }
}
```

### Prisma Lint配置
```json
{
  "rules": {
    "model-name-format": "PascalCase",
    "field-name-format": "camelCase",
    "no-map-annotation": "error"
  }
}
```

## 检查清单

在提交代码前，请确认以下项目：

### 数据库相关
- [ ] 表名使用PascalCase且加双引号
- [ ] 字段名使用camelCase
- [ ] 索引名使用camelCase且加双引号
- [ ] 约束名使用camelCase且加双引号
- [ ] 没有使用@map注解

### Prisma相关
- [ ] 模型名使用PascalCase
- [ ] 字段名使用camelCase
- [ ] 关系字段名使用camelCase
- [ ] 索引名使用camelCase

### TypeScript相关
- [ ] 接口名使用PascalCase
- [ ] 类名使用PascalCase
- [ ] 变量名使用camelCase
- [ ] 常量名使用CONSTANT_CASE
- [ ] 方法名使用camelCase
- [ ] 枚举名使用PascalCase，枚举值使用CONSTANT_CASE

### 文件相关
- [ ] 文件名使用kebab-case
- [ ] 目录名使用kebab-case

## 常见错误修正示例

### 1. 数据库迁移修正
```sql
-- 修正表名
ALTER TABLE user_profiles RENAME TO "UserProfiles";

-- 修正字段名
ALTER TABLE "UserProfiles" RENAME COLUMN user_id TO userId;
ALTER TABLE "UserProfiles" RENAME COLUMN risk_tolerance TO riskTolerance;
ALTER TABLE "UserProfiles" RENAME COLUMN created_at TO createdAt;
ALTER TABLE "UserProfiles" RENAME COLUMN updated_at TO updatedAt;

-- 修正索引名
DROP INDEX idx_user_profiles_user_id;
CREATE INDEX "idxUserProfilesUserId" ON "UserProfiles"(userId);
```

### 2. Prisma Schema修正
```prisma
// 修正前
model user_profile {
  id String @id
  user_id String @map("user_id")
  risk_tolerance String @map("risk_tolerance")
  created_at DateTime @map("created_at")
  
  @@map("user_profiles")
}

// 修正后
model UserProfiles {
  id String @id
  userId String
  riskTolerance String
  createdAt DateTime
}
```

### 3. TypeScript代码修正
```typescript
// 修正前
interface user_profile {
  user_id: string;
  risk_tolerance: string;
}

const get_user_profile = async (user_id: string) => {
  const user_data = await db.user_profile.findUnique({
    where: { user_id }
  });
  return user_data;
};

// 修正后
interface UserProfile {
  userId: string;
  riskTolerance: string;
}

const getUserProfile = async (userId: string) => {
  const userData = await db.userProfiles.findUnique({
    where: { userId }
  });
  return userData;
};
```

## 验证工具

使用以下命令验证命名规范：

```bash
# 运行命名规范检测脚本
npm run check:naming

# 运行ESLint检查
npm run lint

# 运行Prisma格式检查
npx prisma format
npx prisma validate
```

## 总结

严格遵循这些命名规范可以确保：
1. 代码的一致性和可读性
2. 团队协作的效率
3. 维护成本的降低
4. 新开发者的快速上手

记住：**零容忍@map注解，统一使用camelCase和PascalCase**
