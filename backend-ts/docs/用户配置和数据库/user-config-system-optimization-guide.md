# 用户配置系统完善和数据库Schema优化指导文档

## 概述

本文档提供用户配置系统完善和数据库Schema优化的详细指导，确保系统的可扩展性、性能和数据一致性。

## 1. 用户配置系统架构

### 1.1 系统组件
```
用户配置系统
├── 用户画像管理 (UserProfile)
├── 用户偏好设置 (UserPreferences)  
├── LLM配置管理 (UserLLMConfig)
├── 模型偏好管理 (UserModelPreference)
└── 配置验证和默认值处理
```

### 1.2 核心实体关系
```
User (1) -----> (1) UserProfile
User (1) -----> (*) UserLLMConfig
User (1) -----> (*) UserModelPreference
User (1) -----> (1) UserPreferences
```

## 2. 数据库Schema设计

### 2.1 用户画像表 (UserProfiles) - 符合命名规范
```sql
-- 注意：根据项目命名规范，表名使用PascalCase，字段名使用camelCase
-- 不使用@map注解，确保模型名与表名一致

CREATE TABLE "UserProfiles" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    userId VARCHAR(255) NOT NULL UNIQUE,

    -- 风险偏好
    riskTolerance VARCHAR(20) NOT NULL DEFAULT 'BALANCED'
        CHECK (riskTolerance IN ('CONSERVATIVE', 'BALANCED', 'AGGRESSIVE')),
    targetReturn DECIMAL(5,4) NOT NULL DEFAULT 0.15,
    maxAcceptableDrawdown DECIMAL(5,4) NOT NULL DEFAULT 0.20,

    -- 投资期限
    investmentHorizon VARCHAR(20) NOT NULL DEFAULT 'MEDIUM'
        CHECK (investmentHorizon IN ('SHORT', 'MEDIUM', 'LONG')),

    -- 交易偏好
    preferredAssets JSONB NOT NULL DEFAULT '["BTC", "ETH"]',
    tradingStyle VARCHAR(20) NOT NULL DEFAULT 'MIXED'
        CHECK (tradingStyle IN ('SCALPING', 'DAY_TRADING', 'SWING_TRADING', 'POSITION_TRADING', 'MIXED')),

    -- 仓位管理
    maxPositionSize DECIMAL(5,4) NOT NULL DEFAULT 0.10,
    enableStopLoss BOOLEAN NOT NULL DEFAULT true,
    enableTakeProfit BOOLEAN NOT NULL DEFAULT true,

    -- 自定义阈值
    customThresholds JSONB NOT NULL DEFAULT '{}',

    -- 元数据
    createdAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updatedAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    version INTEGER NOT NULL DEFAULT 1,

    -- 外键约束
    CONSTRAINT "fkUserProfilesUserId" FOREIGN KEY (userId) REFERENCES "Users"(id) ON DELETE CASCADE
);

-- 索引 - 使用camelCase命名
CREATE INDEX "idxUserProfilesUserId" ON "UserProfiles"(userId);
CREATE INDEX "idxUserProfilesRiskTolerance" ON "UserProfiles"(riskTolerance);
CREATE INDEX "idxUserProfilesUpdatedAt" ON "UserProfiles"(updatedAt);
```

### 2.2 用户偏好设置表 (UserPreferences) - 符合命名规范
```sql
CREATE TABLE "UserPreferences" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    userId VARCHAR(255) NOT NULL UNIQUE,

    -- 通知设置
    notificationSettings JSONB NOT NULL DEFAULT '{}',

    -- 显示设置
    displaySettings JSONB NOT NULL DEFAULT '{}',

    -- 交易设置
    tradingSettings JSONB NOT NULL DEFAULT '{}',

    -- 自定义设置
    customSettings JSONB NOT NULL DEFAULT '{}',

    -- 元数据
    createdAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updatedAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    CONSTRAINT "fkUserPreferencesUserId" FOREIGN KEY (userId) REFERENCES "Users"(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX "idxUserPreferencesUserId" ON "UserPreferences"(userId);
```

### 2.3 用户LLM配置表优化 (UserLlmConfigs) - 符合命名规范
```sql
-- 添加缺失的列和约束 - 注意使用camelCase字段名
ALTER TABLE "UserLlmConfigs" ADD COLUMN IF NOT EXISTS version INTEGER NOT NULL DEFAULT 1;
ALTER TABLE "UserLlmConfigs" ADD COLUMN IF NOT EXISTS isActive BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "UserLlmConfigs" ADD COLUMN IF NOT EXISTS priority INTEGER NOT NULL DEFAULT 0;

-- 添加索引 - 使用camelCase命名
CREATE INDEX IF NOT EXISTS "idxUserLlmConfigsUserId" ON "UserLlmConfigs"(userId);
CREATE INDEX IF NOT EXISTS "idxUserLlmConfigsProvider" ON "UserLlmConfigs"(provider);
CREATE INDEX IF NOT EXISTS "idxUserLlmConfigsIsActive" ON "UserLlmConfigs"(isActive);
CREATE INDEX IF NOT EXISTS "idxUserLlmConfigsPriority" ON "UserLlmConfigs"(priority DESC);
```

### 2.4 配置历史表 (UserConfigHistory) - 符合命名规范
```sql
CREATE TABLE "UserConfigHistory" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    userId VARCHAR(255) NOT NULL,
    configType VARCHAR(50) NOT NULL, -- 'profile', 'preferences', 'llmConfig'
    configId UUID NOT NULL,

    -- 变更信息
    oldValues JSONB,
    newValues JSONB,
    changedFields TEXT[],

    -- 变更元数据
    changeReason VARCHAR(255),
    changedBy VARCHAR(255),
    changeSource VARCHAR(50), -- 'user', 'system', 'admin'

    createdAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    CONSTRAINT "fkConfigHistoryUserId" FOREIGN KEY (userId) REFERENCES "Users"(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX "idxConfigHistoryUserId" ON "UserConfigHistory"(userId);
CREATE INDEX "idxConfigHistoryConfigType" ON "UserConfigHistory"(configType);
CREATE INDEX "idxConfigHistoryCreatedAt" ON "UserConfigHistory"(createdAt);
```

## 3. 数据迁移脚本

### 3.1 创建迁移文件
```typescript
// migrations/001_create_user_profiles.ts
import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
    await knex.schema.createTable('user_profiles', (table) => {
        table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
        table.string('user_id', 255).notNullable().unique();
        
        // 风险偏好
        table.string('risk_tolerance', 20).notNullable().defaultTo('BALANCED');
        table.decimal('target_return', 5, 4).notNullable().defaultTo(0.15);
        table.decimal('max_acceptable_drawdown', 5, 4).notNullable().defaultTo(0.20);
        
        // 投资期限
        table.string('investment_horizon', 20).notNullable().defaultTo('MEDIUM');
        
        // 交易偏好
        table.jsonb('preferred_assets').notNullable().defaultTo('["BTC", "ETH"]');
        table.string('trading_style', 20).notNullable().defaultTo('MIXED');
        
        // 仓位管理
        table.decimal('max_position_size', 5, 4).notNullable().defaultTo(0.10);
        table.boolean('enable_stop_loss').notNullable().defaultTo(true);
        table.boolean('enable_take_profit').notNullable().defaultTo(true);
        
        // 自定义阈值
        table.jsonb('custom_thresholds').notNullable().defaultTo('{}');
        
        // 元数据
        table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(knex.fn.now());
        table.timestamp('updated_at', { useTz: true }).notNullable().defaultTo(knex.fn.now());
        table.integer('version').notNullable().defaultTo(1);
        
        // 外键约束
        table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
        
        // 索引
        table.index('user_id');
        table.index('risk_tolerance');
        table.index('updated_at');
    });
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.dropTableIfExists('user_profiles');
}
```

## 4. Repository层实现

### 4.1 用户画像Repository
```typescript
// src/contexts/user-config/infrastructure/repositories/PrismaUserProfileRepository.ts
import { injectable, inject } from 'inversify';
import { PrismaClient } from '@prisma/client';
import { TYPES } from '@shared/infrastructure/di/types';
import { UserProfile } from '../../domain/entities/user-profile';
import { IUserProfileRepository } from '../../domain/repositories/IUserProfileRepository';

@injectable()
export class PrismaUserProfileRepository implements IUserProfileRepository {
    constructor(
        @inject(TYPES.Database) private readonly prisma: PrismaClient
    ) {}

    async findByUserId(userId: string): Promise<UserProfile | null> {
        const profile = await this.prisma.userProfile.findUnique({
            where: { userId }
        });

        return profile ? this.toDomain(profile) : null;
    }

    async save(profile: UserProfile): Promise<void> {
        const data = this.toPersistence(profile);
        
        await this.prisma.userProfile.upsert({
            where: { userId: profile.userId },
            create: data,
            update: {
                ...data,
                version: { increment: 1 },
                updatedAt: new Date()
            }
        });
    }

    async delete(userId: string): Promise<void> {
        await this.prisma.userProfile.delete({
            where: { userId }
        });
    }

    private toDomain(data: any): UserProfile {
        return {
            userId: data.userId,
            riskTolerance: data.riskTolerance,
            targetReturn: data.targetReturn,
            maxAcceptableDrawdown: data.maxAcceptableDrawdown,
            investmentHorizon: data.investmentHorizon,
            preferredAssets: data.preferredAssets,
            tradingStyle: data.tradingStyle,
            maxPositionSize: data.maxPositionSize,
            enableStopLoss: data.enableStopLoss,
            enableTakeProfit: data.enableTakeProfit,
            customThresholds: data.customThresholds,
            createdAt: data.createdAt,
            updatedAt: data.updatedAt
        };
    }

    private toPersistence(profile: UserProfile): any {
        return {
            userId: profile.userId,
            riskTolerance: profile.riskTolerance,
            targetReturn: profile.targetReturn,
            maxAcceptableDrawdown: profile.maxAcceptableDrawdown,
            investmentHorizon: profile.investmentHorizon,
            preferredAssets: profile.preferredAssets,
            tradingStyle: profile.tradingStyle,
            maxPositionSize: profile.maxPositionSize,
            enableStopLoss: profile.enableStopLoss,
            enableTakeProfit: profile.enableTakeProfit,
            customThresholds: profile.customThresholds
        };
    }
}
```

## 5. 配置验证和默认值处理

### 5.1 配置验证器
```typescript
// src/contexts/user-config/domain/services/UserConfigValidator.ts
import { injectable } from 'inversify';
import { UserProfile } from '../entities/user-profile';

export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

@injectable()
export class UserConfigValidator {
    validateUserProfile(profile: UserProfile): ValidationResult {
        const errors: string[] = [];
        const warnings: string[] = [];

        // 验证风险容忍度
        if (!['CONSERVATIVE', 'BALANCED', 'AGGRESSIVE'].includes(profile.riskTolerance)) {
            errors.push('无效的风险容忍度');
        }

        // 验证目标收益率
        if (profile.targetReturn < 0 || profile.targetReturn > 1) {
            errors.push('目标收益率必须在0-100%之间');
        }

        // 验证最大回撤
        if (profile.maxAcceptableDrawdown < 0 || profile.maxAcceptableDrawdown > 1) {
            errors.push('最大可接受回撤必须在0-100%之间');
        }

        // 验证仓位大小
        if (profile.maxPositionSize < 0 || profile.maxPositionSize > 1) {
            errors.push('最大仓位大小必须在0-100%之间');
        }

        // 警告检查
        if (profile.targetReturn > 0.5) {
            warnings.push('目标收益率过高，风险较大');
        }

        if (profile.maxAcceptableDrawdown > 0.3) {
            warnings.push('最大回撤设置过高，建议降低');
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}
```

### 5.2 默认配置提供者
```typescript
// src/contexts/user-config/domain/services/DefaultConfigProvider.ts
import { injectable } from 'inversify';
import { UserProfile } from '../entities/user-profile';

@injectable()
export class DefaultConfigProvider {
    getDefaultUserProfile(userId: string): UserProfile {
        return {
            userId,
            riskTolerance: 'BALANCED',
            targetReturn: 0.15,
            maxAcceptableDrawdown: 0.20,
            investmentHorizon: 'MEDIUM',
            preferredAssets: ['BTC', 'ETH'],
            tradingStyle: 'MIXED',
            maxPositionSize: 0.10,
            enableStopLoss: true,
            enableTakeProfit: true,
            customThresholds: {
                trendStrengthThreshold: 0.6,
                riskScoreThreshold: 0.7,
                confidenceThreshold: 0.5
            },
            createdAt: new Date(),
            updatedAt: new Date()
        };
    }

    getDefaultUserPreferences(userId: string): Record<string, any> {
        return {
            notifications: {
                email: true,
                push: true,
                sms: false,
                tradingSignals: true,
                riskAlerts: true,
                marketUpdates: false
            },
            display: {
                theme: 'dark',
                language: 'zh-CN',
                timezone: 'Asia/Shanghai',
                currency: 'USD',
                dateFormat: 'YYYY-MM-DD',
                numberFormat: 'en-US'
            },
            trading: {
                autoExecute: false,
                confirmBeforeOrder: true,
                defaultOrderType: 'LIMIT',
                slippageTolerance: 0.005,
                gasPrice: 'standard'
            }
        };
    }
}
```

## 6. 实施步骤

### 6.1 阶段一：数据库Schema创建
1. 创建迁移文件
2. 执行数据库迁移
3. 验证表结构和索引

### 6.2 阶段二：Repository层实现
1. 实现UserProfileRepository
2. 实现UserPreferencesRepository
3. 优化现有LLMConfigRepository

### 6.3 阶段三：业务逻辑完善
1. 实现配置验证器
2. 实现默认配置提供者
3. 完善UserConfigService

### 6.4 阶段四：测试和优化
1. 编写单元测试
2. 编写集成测试
3. 性能测试和优化

## 7. 注意事项

### 7.1 数据一致性
- 使用事务确保配置更新的原子性
- 实现乐观锁防止并发更新冲突
- 定期备份配置数据

### 7.2 性能优化
- 合理使用索引
- 实现配置缓存机制
- 避免N+1查询问题

### 7.3 安全性
- 敏感配置数据加密存储
- 实现访问权限控制
- 记录配置变更审计日志

### 7.4 可扩展性
- 使用JSONB存储灵活配置
- 预留扩展字段
- 支持配置版本管理

这份文档提供了完整的用户配置系统优化指导，请按照步骤逐步实施。
