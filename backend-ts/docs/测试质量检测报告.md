# 测试质量检测报告

## 脚本信息

**脚本名称**: 测试质量检测器 (Test Quality Detector)
**脚本路径**: `scripts/monitoring/test-quality-detector.ts`
**生成时间**: 2025/7/13 21:56:39
**检测器版本**: 1.0.0
**执行命令**: `npm run test:quality` 或 `ts-node scripts/monitoring/test-quality-detector.ts`

## 相关文档

📖 **测试开发标准文档**: [测试开发标准与最佳实践](./developer-guides/测试开发标准与最佳实践.md)

本报告基于项目测试开发标准进行检测，建议开发人员参考标准文档了解：
- 统一测试工具库的使用方法
- 测试命名规范和最佳实践
- Mock数据质量管理标准
- 集成测试和E2E测试规范
- 测试数据生成和断言标准

## 检测器概述

测试质量检测器是一个专业的测试代码分析工具，专门用于评估TypeScript/JavaScript项目的测试质量。该检测器通过多维度分析，为开发团队提供全面的测试质量评估报告。

### 核心功能模块

1. **测试覆盖率分析**: 计算整体测试覆盖率，识别缺少测试的关键业务文件
   - 整体覆盖率统计：计算源文件与测试文件的对应关系
   - 关键业务文件检测：重点关注service、engine等核心模块
   - 缺失测试识别：列出所有未覆盖的源文件

2. **测试策略分析**: 全面检测测试组织和框架统一性
   - 测试位置策略：检测就近测试vs集中测试的一致性
   - 测试框架统一性：检测断言库、Mock库、测试运行器的统一使用
   - 测试配置一致性：验证测试配置文件和脚本的规范性
   - 测试命名规范：检查.test.ts vs .spec.ts的命名一致性
   - 中文测试描述：检测测试用例是否使用中文描述提高可读性
   - 统一测试工具库：检测UnifiedTestDataGenerator等工具的采用率
   - 命名风格一致性：检查驼峰命名与下划线命名的混合使用

3. **Mock数据质量分析**: 检测测试数据的质量和稳定性
   - 随机数据检测：识别Math.random()等不稳定因素
   - 硬编码数据识别：发现测试中的硬编码业务数据
   - 边界值测试检查：验证是否包含边界条件测试
   - 统一测试数据生成器：检测UnifiedTestDataGenerator的使用
   - 固定种子检测：确保随机数生成的可重现性
   - 测试数据工厂模式：检查是否使用工厂模式管理测试数据
   - 统一断言库：检测UnifiedTestAssertions的采用情况

4. **E2E测试完整性分析**: 评估关键业务流程的端到端测试覆盖
   - 业务流程覆盖：检查trading、order、signal等关键流程
   - API端点测试：评估API接口的集成测试覆盖率
   - 端到端测试比例：分析E2E测试与总体测试的合理比例

## 检测结果概览

| 指标 | 数值 |
|------|------|
| 总问题数 | 1021 |
| 严重问题 | 198 |
| 中等问题 | 131 |
| 轻微问题 | 692 |

## 问题分类统计

- **测试覆盖率**: 178个问题
- **测试策略**: 491个问题
- **Mock数据质量**: 352个问题

## 详细问题列表

### 📊 测试覆盖率问题

#### 1. 🔴 测试覆盖率过低: 25.6% (161/628 文件)

**文件**: `project-wide`
**建议**: 增加单元测试，目标覆盖率应达到80%以上

#### 2. 🔴 关键业务文件缺少测试

**文件**: `src/services/websocket-event-server.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 3. 🔴 关键业务文件缺少测试

**文件**: `src/services/unified-data-sync-coordinator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 4. 🔴 关键业务文件缺少测试

**文件**: `src/services/short-term-learning-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 5. 🔴 关键业务文件缺少测试

**文件**: `src/services/real-time-sync-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 6. 🔴 关键业务文件缺少测试

**文件**: `src/services/long-term-learning-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 7. 🔴 关键业务文件缺少测试

**文件**: `src/services/data-version-manager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 8. 🔴 关键业务文件缺少测试

**文件**: `src/services/data-update-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 9. 🔴 关键业务文件缺少测试

**文件**: `src/services/data-change-event-broadcaster.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 10. 🔴 关键业务文件缺少测试

**文件**: `src/services/cross-system-state-sync.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 11. 🔴 关键业务文件缺少测试

**文件**: `src/services/alerting-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 12. 🔴 关键业务文件缺少测试

**文件**: `src/services/adaptive-learning-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 13. 🔴 关键业务文件缺少测试

**文件**: `src/services/RefactoredDataUpdateService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 14. 🔴 关键业务文件缺少测试

**文件**: `src/services/RefactoredCrossSystemStateSyncManager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 15. 🔴 关键业务文件缺少测试

**文件**: `src/shared/services/system-integration-coordinator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 16. 🔴 关键业务文件缺少测试

**文件**: `src/shared/application/base-application-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 17. 🔴 关键业务文件缺少测试

**文件**: `src/shared/application/application-service-interfaces.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 18. 🔴 关键业务文件缺少测试

**文件**: `src/services/state-sync/StateSyncScheduler.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 19. 🔴 关键业务文件缺少测试

**文件**: `src/services/state-sync/StateDataValidator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 20. 🔴 关键业务文件缺少测试

**文件**: `src/services/state-sync/StateConflictResolver.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 21. 🔴 关键业务文件缺少测试

**文件**: `src/services/market-data/RealTimeDataStreamService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 22. 🔴 关键业务文件缺少测试

**文件**: `src/services/market-data/MarketDataSyncService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 23. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/test-data-service-trading.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 24. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/test-data-service-integration.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 25. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/validation/price-validation-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 26. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/types/unified-service-interfaces.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 27. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/technical-indicators/unified-technical-indicator-calculator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 28. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/services/user-identity-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 29. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/health/unified-health-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 30. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/database/repository-base-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 31. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/config/risk-config.service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 32. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/analysis/unified-analysis-service-manager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 33. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/ai/unified-similarity-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 34. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/ai/unified-ai-service-manager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 35. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/ai/realtime-push-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 36. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/ai/ai-call-log-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 37. 🔴 关键业务文件缺少测试

**文件**: `src/shared/application/services/base-application-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 38. 🔴 关键业务文件缺少测试

**文件**: `src/shared/application/interfaces/external-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 39. 🔴 关键业务文件缺少测试

**文件**: `src/shared/application/interfaces/domain-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 40. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/monitoring/services/unified-performance-monitoring-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 41. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/monitoring/services/unified-health-check-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 42. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/monitoring/di/monitoring-service-bindings.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 43. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/health/providers/ai-service-health-provider.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 44. 🔴 关键业务文件缺少测试

**文件**: `src/shared/infrastructure/analysis/services/PatternDetectionAlgorithms.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 45. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/ZeroTrustService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 46. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/TotpService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 47. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/ThreatDetectionService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 48. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/SmsService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 49. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/PasswordService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 50. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/MfaService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 51. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/JwtService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 52. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/InvitationService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 53. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/EmailVerificationService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 54. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/AuthorizationService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 55. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/AuthenticationService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 56. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/infrastructure/services/AuditService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 57. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/IZeroTrustService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 58. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/IThreatDetectionService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 59. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/IMfaService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 60. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/IAuthorizationService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 61. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/IAuthenticationService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 62. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/IAuditService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 63. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/IApiKeyService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 64. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/application/services/UserManagementApplicationService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 65. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/application/services/ApiKeyService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 66. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-config/infrastructure/services/user-config-system-adapter.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 67. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-config/infrastructure/services/EnhancedLLMRouter.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 68. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-config/infrastructure/services/ConfigHotReloadService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 69. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-config/infrastructure/services/AESEncryptionService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 70. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-config/domain/services/user-config-validator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 71. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-config/application/services/user-profile-application-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 72. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-config/application/services/user-preferences-application-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 73. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-config/application/services/UserConfigService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 74. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-config/application/services/UserConfigApplicationService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 75. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-config/application/services/ModelSelectionService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 76. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 77. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/trend-change-detector.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 78. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/real-market-data-provider.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 79. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/pure-ai-trend-analysis-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 80. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/professional-pivot-detector.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 81. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 82. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/pattern-module-base.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 83. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/key-level-helpers.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 84. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/data-source-health-monitor.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 85. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/data-quality-validator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 86. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 87. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/base-trend-analysis-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 88. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 89. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/di/monitoring-services-bindings.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 90. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/di/domain-services-bindings.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 91. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/di/application-services-bindings.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 92. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/infrastructure/di/ai-services-bindings.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 93. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trend-analysis/domain/services/trend-analysis-engine.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 94. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 95. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-signals/application/services/signal-generation-application-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 96. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-signals/application/services/real-signal-generation-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 97. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-signals/application/services/production-signal-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 98. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-signals/application/services/dynamic-strategy-selector.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 99. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-signals/domain/services/strategy-selector.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 100. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-signals/domain/services/position-size-calculator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 101. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/risk-management/domain/interfaces/risk-assessment-application-service.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 102. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/infrastructure/services/trading-limit-controller.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 103. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/infrastructure/services/symbol-validator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 104. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/infrastructure/services/security-manager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 105. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/infrastructure/services/order-id-manager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 106. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/infrastructure/services/emergency-stop-manager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 107. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/infrastructure/services/credential-manager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 108. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/infrastructure/services/binance-websocket-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 109. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/infrastructure/services/binance-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 110. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/domain/services/webhook-alert-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 111. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/domain/services/trading-strategy-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 112. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/domain/services/strategy-sync-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 113. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/domain/services/risk-monitor.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 114. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/domain/services/real-slippage-calculator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 115. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/domain/services/real-order-execution-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 116. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/domain/services/position-manager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 117. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/domain/services/order-executor.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 118. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/domain/services/dual-track-monitoring-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 119. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/domain/services/dual-environment-learning-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 120. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/trading-execution/application/services/trading-execution-application-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 121. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/presentation/websocket/market-data-websocket-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 122. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/application/services/weight-validation-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 123. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/application/services/real-data-integration-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 124. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/application/services/market-data-application-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 125. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/real-time-data-quality-monitor.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 126. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/multi-layer-cache-system.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 127. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/multi-exchange-websocket-manager.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 128. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/high-performance-data-distribution-network.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 129. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/exchange-router.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 130. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/exchange-adapter.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 131. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/infrastructure/services/real-market-data-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 132. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/infrastructure/services/multi-exchange-data-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 133. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/infrastructure/services/data-backfill-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 134. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/infrastructure/external/stream-data-cleaning-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 135. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/infrastructure/external/data-quality-analysis-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 136. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/infrastructure/external/data-anomaly-detection-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 137. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/infrastructure/external/conflict-resolution-strategy-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 138. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/learning/infrastructure/services/real-prediction-result-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 139. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/learning/infrastructure/engines/experience-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 140. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/learning/infrastructure/analyzers/trend-consistency-analyzer.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 141. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/learning/infrastructure/analyzers/signal-quality-analyzer.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 142. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/learning/infrastructure/analyzers/prediction-result-analyzer.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 143. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/infrastructure/reasoning/short-cycle-prediction-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 144. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/timeframe-isolated-parameter-manager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 145. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/pure-ai-analyzer.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 146. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/multi-timeframe-learning-coordinator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 147. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/llm-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 148. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/learning-analysis-engine.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 149. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/gradual-adjuster.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 150. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/effectiveness-validator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 151. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/domain/services/unified-prediction-engine.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 152. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/domain/services/timeframe-coordinator.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 153. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/domain/services/short-cycle-learning.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 154. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/domain/services/reasoning-chain.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 155. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/domain/services/pure-ai-analysis.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 156. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/domain/services/llm-service.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 157. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/domain/services/learning-analysis.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 158. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/domain/services/knowledge-graph.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 159. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/domain/services/continuous-learning-engine.interface.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 160. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/ai-reasoning/application/services/ai-reasoning-application-service.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 161. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/threat-detection/IThreatStatisticsService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 162. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/threat-detection/IThreatScoreCalculator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 163. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/threat-detection/IThreatDetector.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 164. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/threat-detection/IThreatDetectionServiceComposite.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 165. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/threat-detection/IReputationAnalyzer.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 166. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/threat-detection/IBehaviorAnalyzer.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 167. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/mfa/IMfaVerificationService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 168. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/mfa/IMfaStatisticsService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 169. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/mfa/IMfaServiceComposite.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 170. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/mfa/IMfaSecurityMonitor.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 171. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/mfa/IMfaDeviceManager.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 172. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/mfa/IMfaConfigurationService.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 173. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/user-management/domain/services/mfa/IMfaCodeGenerator.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 174. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/external-adapters/ISentiCryptAdapter.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 175. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/external-adapters/IMempoolAdapter.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 176. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/external-adapters/IFearGreedAdapter.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 177. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/external-adapters/ICoinMetricsAdapter.ts`
**建议**: 为核心业务逻辑添加单元测试

#### 178. 🔴 关键业务文件缺少测试

**文件**: `src/contexts/market-data/domain/services/external-adapters/IBinanceFuturesAdapter.ts`
**建议**: 为核心业务逻辑添加单元测试

### 🎯 测试策略问题

#### 1. 🟡 测试文件分散在33个不同位置，策略不统一

**文件**: `project-wide`
**建议**: 统一测试文件组织结构，建议采用单一测试目录或就近测试策略

#### 2. 🟢 测试文件命名不一致，同时使用了.test.ts和.spec.ts

**文件**: `project-wide`
**建议**: 统一测试文件命名规范，建议使用.test.ts

#### 3. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `tests/integration/repository-migration-validation.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 4. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `tests/api/user-config.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 5. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/node-sarif-builder/src/lib/sarif-builder.spec.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 6. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `src/contexts/trading-execution/domain/services/__tests__/execution-engine-router.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 7. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/tuple.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 8. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 9. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/string.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 10. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/set.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 11. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/refine.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 12. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/record.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 13. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 14. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/parseUtil.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 15. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/object.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 16. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/object-in-es5-env.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 17. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/map.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 18. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/literal.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 19. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/function.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 20. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/error.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 21. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/description.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 22. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 23. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 24. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/array.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 25. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v3/tests/all-errors.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 26. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/mini/tests/prototypes.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 27. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 28. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 29. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/template-literal.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 30. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 31. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 32. 🟢 测试文件中混合使用驼峰命名和下划线命名

**文件**: `node_modules/zod/src/v4/classic/tests/registries.test.ts`
**建议**: 统一使用驼峰命名规范，保持代码风格一致性

#### 33. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/refine.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 34. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/prototypes.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 35. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/optional.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 36. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 37. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/nested-refine.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 38. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/map.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 39. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/literal.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 40. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 41. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 42. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 43. 🟢 测试文件中混合使用驼峰命名和下划线命名

**文件**: `node_modules/zod/src/v4/classic/tests/enum.test.ts`
**建议**: 统一使用驼峰命名规范，保持代码风格一致性

#### 44. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/description.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 45. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/default.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 46. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 47. 🟢 测试用例缺少中文描述，不利于业务理解

**文件**: `node_modules/zod/src/v4/classic/tests/array.test.ts`
**建议**: 使用中文描述测试用例，提高测试的可读性和业务表达力

#### 48. 🟡 UnifiedTestDataGenerator采用率过低: 3.5%

**文件**: `project-wide`
**建议**: 推广使用UnifiedTestDataGenerator统一管理测试数据生成

#### 49. 🟢 UnifiedTestAssertions采用率过低: 4.0%

**文件**: `project-wide`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 50. 🟡 UnifiedIntegrationTestBase采用率过低: 0.4%

**文件**: `project-wide`
**建议**: 在集成测试中使用UnifiedIntegrationTestBase统一测试基础设施

#### 51. 🟡 测试位置策略不统一: 194个就近测试，33个集中测试

**文件**: `project-wide`
**建议**: 选择统一的测试位置策略：要么全部就近放置，要么全部集中管理

#### 52. 🟢 191个测试文件没有对应的源文件

**文件**: `project-wide`
**建议**: 检查测试文件命名是否与源文件对应，或考虑重构测试组织结构

#### 53. 🟢 重复的测试名称: "应该提供缓存统计信息"

**文件**: `tests/performance-optimization.test.ts, src/shared/infrastructure/ai/vector-service.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 54. 🟢 重复的测试名称: "应该正确分类网络错误"

**文件**: `tests/error-handling-integration.test.ts, src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 55. 🟢 重复的测试名称: "应该记录错误日志"

**文件**: `tests/error-handling-integration.test.ts, src/shared/infrastructure/ai/vector-service.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 56. 🟢 重复的测试名称: "应该能够处理并发请求"

**文件**: `tests/integration/llm-router.test.ts, src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts, src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 57. 🟢 重复的测试名称: "错误处理测试"

**文件**: `tests/integration/binance-simple.test.ts, tests/integration/binance-integration.test.ts, src/tests/integration/real-api-test.test.ts, src/contexts/risk-management/tests/risk-management-api.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 58. 🟢 重复的测试名称: "性能测试"

**文件**: `tests/integration/binance-simple.test.ts, tests/integration/binance-integration.test.ts, tests/api/comprehensive-api.test.ts, src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 59. 🟢 重复的测试名称: "应该能够获取账户信息"

**文件**: `tests/integration/binance-simple.test.ts, tests/integration/binance-integration.test.ts, tests/api/comprehensive-api.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 60. 🟢 重复的测试名称: "应该正确处理网络错误"

**文件**: `tests/integration/binance-simple.test.ts, tests/integration/binance-integration.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 61. 🟢 重复的测试名称: "API调用应该在合理时间内完成"

**文件**: `tests/integration/binance-simple.test.ts, tests/integration/binance-integration.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 62. 🟢 重复的测试名称: "健康检查端点"

**文件**: `tests/api/routes.test.ts, src/tests/integration/real-api-test.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 63. 🟢 重复的测试名称: "错误处理"

**文件**: `tests/api/routes.test.ts, tests/unit/market-data/infrastructure/prisma-price-data-repository.test.ts, src/shared/infrastructure/ai/vector-service.test.ts, src/shared/infrastructure/analysis/tests/PatternRecognitionService.test.ts, src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.test.ts, src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts, src/shared/infrastructure/analysis/__tests__/services/DynamicWeightingService.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/intelligent-target-stop-calculator.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/advanced-fibonacci-analyzer.test.ts, src/contexts/market-data/infrastructure/external/__tests__/binance-websocket-adapter.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 64. 🟢 重复的测试名称: "构造函数"

**文件**: `tests/unit/trading-analysis/enhanced-signal-fusion-coordinator.test.ts, tests/unit/trading-analysis/collaborative-decision-config.test.ts, tests/unit/market-data/value-objects/symbol.test.ts, tests/unit/market-data/infrastructure/binance-adapter.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 65. 🟢 重复的测试名称: "配置管理"

**文件**: `tests/unit/trading-analysis/enhanced-signal-fusion-coordinator.test.ts, src/contexts/trend-analysis/tests/unified-multi-timeframe-service.test.ts, src/contexts/trend-analysis/tests/trend-prediction.test.ts, src/contexts/trend-analysis/tests/trend-analysis-engine.test.ts, src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts, src/shared/infrastructure/analysis/__tests__/strategies/IntelligentWeightingStrategy.test.ts, src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts, src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 66. 🟢 重复的测试名称: "性能基准测试"

**文件**: `src/tests/integration/end-to-end-signal-workflow.test.ts, src/contexts/trend-analysis/tests/performance-benchmark.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 67. 🟢 重复的测试名称: "应该能够识别技术形态"

**文件**: `src/tests/integration/core-analysis-services-integration.test.ts, src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts, src/contexts/trend-analysis/tests/trend-analysis-integration-simple.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 68. 🟢 重复的测试名称: "投资组合聚合功能修复验证"

**文件**: `src/tests/integration/aggregate-positions-fix.integration.test.ts, src/contexts/risk-management/tests/aggregate-positions-fix.integration.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 69. 🟢 重复的测试名称: "getHealthStatus"

**文件**: `tests/unit/market-data/infrastructure/binance-adapter.test.ts, src/contexts/trend-analysis/tests/trend-analysis-api.test.ts, src/shared/infrastructure/data-processing/tests/data-processing-pipeline.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 70. 🟢 重复的测试名称: "应该正确初始化适配器"

**文件**: `tests/unit/market-data/infrastructure/binance-adapter.test.ts, src/contexts/market-data/infrastructure/external/__tests__/binance-websocket-adapter-refactored.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 71. 🟢 重复的测试名称: "缓存管理"

**文件**: `src/shared/infrastructure/ai/vector-service.test.ts, src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 72. 🟢 重复的测试名称: "边界情况处理"

**文件**: `src/shared/infrastructure/ai/vector-service.test.ts, src/contexts/risk-management/tests/aggregate-positions-fix.integration.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 73. 🟢 重复的测试名称: "批量处理"

**文件**: `src/shared/infrastructure/ai/vector-service.test.ts, src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 74. 🟢 重复的测试名称: "应该抛出维度不匹配错误"

**文件**: `src/shared/infrastructure/ai/vector-service.test.ts, src/shared/infrastructure/ai/vector-service.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 75. 🟢 重复的测试名称: "端到端集成测试"

**文件**: `src/shared/infrastructure/ai/phase1-integration.test.ts, src/contexts/ai-reasoning/infrastructure/services/__tests__/learning-integration.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 76. 🟢 重复的测试名称: "应该按层级顺序查找缓存"

**文件**: `src/shared/infrastructure/ai/phase1-integration.test.ts, src/shared/infrastructure/ai/multi-tier-cache-service.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 77. 🟢 重复的测试名称: "应该提供正确的统计信息"

**文件**: `src/shared/infrastructure/ai/multi-tier-cache-service.test.ts, src/shared/infrastructure/data-processing/adapters/tests/adapter-factory.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 78. 🟢 重复的测试名称: "应该能够设置和获取数据"

**文件**: `src/shared/infrastructure/ai/multi-tier-cache-service.test.ts, src/shared/infrastructure/ai/multi-tier-cache-service.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 79. 🟢 重复的测试名称: "collectData"

**文件**: `src/contexts/trend-analysis/tests/unified-multi-timeframe-service.test.ts, src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 80. 🟢 重复的测试名称: "aggregateKlines"

**文件**: `src/contexts/trend-analysis/tests/unified-multi-timeframe-service.test.ts, src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 81. 🟢 重复的测试名称: "getAlignment"

**文件**: `src/contexts/trend-analysis/tests/unified-multi-timeframe-service.test.ts, src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 82. 🟢 重复的测试名称: "市场状态评估"

**文件**: `src/contexts/trend-analysis/tests/trend-prediction.test.ts, src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 83. 🟢 重复的测试名称: "应该处理AI分析失败的情况"

**文件**: `src/contexts/trend-analysis/tests/trend-prediction.test.ts, src/contexts/trend-analysis/tests/trend-analysis-engine.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 84. 🟢 重复的测试名称: "趋势分析系统集成测试"

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts, src/contexts/trend-analysis/tests/system-integration.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 85. 🟢 重复的测试名称: "错误处理和边界情况"

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/professional-corrective-wave-analyzer.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 86. 🟢 重复的测试名称: "应该能够生成趋势预测"

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts, src/contexts/trend-analysis/tests/trend-analysis-integration-simple.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 87. 🟢 重复的测试名称: "应该能够评估市场状态"

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts, src/contexts/trend-analysis/tests/trend-analysis-integration-simple.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 88. 🟢 重复的测试名称: "analyzeTrend"

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-engine.test.ts, src/contexts/trend-analysis/tests/trend-analysis-api.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 89. 🟢 重复的测试名称: "quickTrendAssessment"

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-engine.test.ts, src/contexts/trend-analysis/tests/trend-analysis-api.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 90. 🟢 重复的测试名称: "应该处理缺少必需参数的情况"

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-api.test.ts, src/contexts/risk-management/tests/risk-management-api.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 91. 🟢 重复的测试名称: "应该返回健康状态"

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-api.test.ts, src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts, src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-starter.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 92. 🟢 重复的测试名称: "应该能够设置和获取缓存"

**文件**: `src/contexts/trend-analysis/tests/core-services.test.ts, src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 93. 🟢 重复的测试名称: "performStressTest"

**文件**: `src/contexts/risk-management/tests/risk-calculation-engine.test.ts, src/contexts/risk-management/tests/ai-risk-analysis-engine.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 94. 🟢 重复的测试名称: "PatternRecognitionService"

**文件**: `src/shared/infrastructure/analysis/tests/PatternRecognitionService.test.ts, src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 95. 🟢 重复的测试名称: "配置验证"

**文件**: `src/shared/infrastructure/analysis/tests/PatternRecognitionService.test.ts, src/shared/infrastructure/data-processing/adapters/tests/adapter-factory.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-real-time-monitor.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 96. 🟢 重复的测试名称: "应该处理不支持的形态类型"

**文件**: `src/shared/infrastructure/analysis/tests/PatternRecognitionService.test.ts, src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 97. 🟢 重复的测试名称: "应该处理空数据"

**文件**: `src/shared/infrastructure/analysis/tests/PatternRecognitionService.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/key-level-analysis-engine.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-real-time-monitor.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 98. 🟢 重复的测试名称: "集成测试"

**文件**: `src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.test.ts, src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts, src/shared/infrastructure/data-processing/strategies/sentiment/__tests__/sentiment-strategy.test.ts, src/contexts/market-data/infrastructure/websocket/monitoring/__tests__/websocket-monitoring.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 99. 🟢 重复的测试名称: "基本属性"

**文件**: `src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts, src/shared/infrastructure/analysis/__tests__/strategies/IntelligentWeightingStrategy.test.ts, src/contexts/trading-execution/domain/services/__tests__/simulation-engine.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 100. 🟢 重复的测试名称: "连接测试"

**文件**: `src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts, src/contexts/trading-execution/infrastructure/adapters/__tests__/binance-api-client.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 101. 🟢 重复的测试名称: "应该成功测试连接"

**文件**: `src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts, src/contexts/trading-execution/infrastructure/adapters/__tests__/binance-api-client.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 102. 🟢 重复的测试名称: "历史准确性管理"

**文件**: `src/shared/infrastructure/analysis/__tests__/strategies/IntelligentWeightingStrategy.test.ts, src/shared/infrastructure/analysis/__tests__/services/DynamicWeightingService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 103. 🟢 重复的测试名称: "应该有默认配置"

**文件**: `src/shared/infrastructure/analysis/__tests__/strategies/IntelligentWeightingStrategy.test.ts, src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts, src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 104. 🟢 重复的测试名称: "应该支持更新配置"

**文件**: `src/shared/infrastructure/analysis/__tests__/strategies/IntelligentWeightingStrategy.test.ts, src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts, src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 105. 🟢 重复的测试名称: "应该保留未更新的配置项"

**文件**: `src/shared/infrastructure/analysis/__tests__/strategies/IntelligentWeightingStrategy.test.ts, src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts, src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 106. 🟢 重复的测试名称: "应该返回默认准确性"

**文件**: `src/shared/infrastructure/analysis/__tests__/strategies/IntelligentWeightingStrategy.test.ts, src/shared/infrastructure/analysis/__tests__/services/DynamicWeightingService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 107. 🟢 重复的测试名称: "初始化"

**文件**: `src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts, src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts, src/shared/infrastructure/analysis/__tests__/services/DynamicWeightingService.test.ts, src/contexts/trading-execution/infrastructure/adapters/__tests__/binance-api-client.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 108. 🟢 重复的测试名称: "应该正确初始化服务"

**文件**: `src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts, src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts, src/shared/infrastructure/analysis/__tests__/services/DynamicWeightingService.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 109. 🟢 重复的测试名称: "应该检测方向冲突"

**文件**: `src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts, src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-engine.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 110. 🟢 重复的测试名称: "应该处理无效的波浪数据"

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/professional-corrective-wave-analyzer.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/advanced-fibonacci-analyzer.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 111. 🟢 重复的测试名称: "应该处理不足的数据点"

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/professional-corrective-wave-analyzer.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/advanced-fibonacci-analyzer.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 112. 🟢 重复的测试名称: "应该在配置加载失败时返回默认结果"

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-volume-analyzer.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-pivot-detector.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 113. 🟢 重复的测试名称: "应该在数据不足时返回默认值"

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-volume-analyzer.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-volume-analyzer.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 114. 🟢 重复的测试名称: "应该限制返回的历史记录数量"

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-real-time-monitor.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-real-time-monitor.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 115. 🟢 重复的测试名称: "应该计算转折点强度"

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-pivot-detector.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-pivot-detector.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 116. 🟢 重复的测试名称: "应该计算转折点显著性"

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-pivot-detector.test.ts, src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-pivot-detector.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 117. 🟢 重复的测试名称: "应该在连接测试失败时抛出错误"

**文件**: `src/contexts/trading-execution/infrastructure/adapters/__tests__/binance-api-client.test.ts, src/contexts/trading-execution/infrastructure/adapters/__tests__/binance-api-client.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 118. 🟢 重复的测试名称: "应该在未初始化时抛出错误"

**文件**: `src/contexts/trading-execution/infrastructure/adapters/__tests__/binance-api-client.test.ts, src/contexts/trading-execution/infrastructure/adapters/__tests__/binance-api-client.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 119. 🟢 重复的测试名称: "executeOrder"

**文件**: `src/contexts/trading-execution/domain/services/__tests__/simulation-engine.test.ts, src/contexts/trading-execution/domain/services/__tests__/execution-engine-router.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 120. 🟢 重复的测试名称: "openPosition"

**文件**: `src/contexts/trading-execution/domain/services/__tests__/simulation-engine.test.ts, src/contexts/trading-execution/domain/services/__tests__/execution-engine-router.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 121. 🟢 重复的测试名称: "start"

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-starter.test.ts, src/contexts/ai-reasoning/infrastructure/services/__tests__/macro-prediction-service.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 122. 🟢 重复的测试名称: "stop"

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-starter.test.ts, src/contexts/ai-reasoning/infrastructure/services/__tests__/macro-prediction-service.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 123. 🟢 重复的测试名称: "应该成功集成学习机制"

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/learning-integration.test.ts, src/contexts/ai-reasoning/infrastructure/services/__tests__/learning-integration.test.ts, src/contexts/ai-reasoning/infrastructure/services/__tests__/learning-integration.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 124. 🟢 重复的测试名称: "message"

**文件**: `src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts, src/contexts/market-data/infrastructure/websocket/monitoring/__tests__/websocket-monitoring.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 125. 🟢 重复的测试名称: "应该能够启动和停止"

**文件**: `src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts, src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 126. 🟢 重复的测试名称: "应该能够获取健康状态"

**文件**: `src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts, src/contexts/market-data/infrastructure/websocket/monitoring/__tests__/websocket-monitoring.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 127. 🟢 重复的测试名称: "应该提供统计信息"

**文件**: `src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts, src/contexts/market-data/infrastructure/external/__tests__/binance-websocket-adapter-refactored.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 128. 🟢 重复的测试名称: "应该能够处理多个订阅"

**文件**: `src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts, src/contexts/market-data/infrastructure/external/__tests__/binance-websocket-adapter-refactored.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 129. 🟢 重复的测试名称: "void"

**文件**: `node_modules/zod/src/v3/tests/void.test.ts, node_modules/zod/src/v4/classic/tests/void.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 130. 🟢 重复的测试名称: "array length"

**文件**: `node_modules/zod/src/v3/tests/validations.test.ts, node_modules/zod/src/v4/classic/tests/array.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 131. 🟢 重复的测试名称: "string length"

**文件**: `node_modules/zod/src/v3/tests/validations.test.ts, node_modules/zod/src/v4/classic/tests/validations.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 132. 🟢 重复的测试名称: "string max"

**文件**: `node_modules/zod/src/v3/tests/validations.test.ts, node_modules/zod/src/v4/classic/tests/validations.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 133. 🟢 重复的测试名称: "number min"

**文件**: `node_modules/zod/src/v3/tests/validations.test.ts, node_modules/zod/src/v4/classic/tests/validations.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 134. 🟢 重复的测试名称: "number max"

**文件**: `node_modules/zod/src/v3/tests/validations.test.ts, node_modules/zod/src/v4/classic/tests/validations.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 135. 🟢 重复的测试名称: "number nonnegative"

**文件**: `node_modules/zod/src/v3/tests/validations.test.ts, node_modules/zod/src/v4/classic/tests/validations.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 136. 🟢 重复的测试名称: "number nonpositive"

**文件**: `node_modules/zod/src/v3/tests/validations.test.ts, node_modules/zod/src/v4/classic/tests/validations.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 137. 🟢 重复的测试名称: "number negative"

**文件**: `node_modules/zod/src/v3/tests/validations.test.ts, node_modules/zod/src/v4/classic/tests/validations.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 138. 🟢 重复的测试名称: "number positive"

**文件**: `node_modules/zod/src/v3/tests/validations.test.ts, node_modules/zod/src/v4/classic/tests/validations.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 139. 🟢 重复的测试名称: "function parsing"

**文件**: `node_modules/zod/src/v3/tests/unions.test.ts, node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/union.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 140. 🟢 重复的测试名称: "union 2"

**文件**: `node_modules/zod/src/v3/tests/unions.test.ts, node_modules/zod/src/v4/classic/tests/union.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 141. 🟢 重复的测试名称: "return valid over invalid"

**文件**: `node_modules/zod/src/v3/tests/unions.test.ts, node_modules/zod/src/v4/classic/tests/union.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 142. 🟢 重复的测试名称: "options getter"

**文件**: `node_modules/zod/src/v3/tests/unions.test.ts, node_modules/zod/src/v4/classic/tests/union.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 143. 🟢 重复的测试名称: "readonly union"

**文件**: `node_modules/zod/src/v3/tests/unions.test.ts, node_modules/zod/src/v4/classic/tests/union.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 144. 🟢 重复的测试名称: "successful validation"

**文件**: `node_modules/zod/src/v3/tests/tuple.test.ts, node_modules/zod/src/v4/classic/tests/tuple.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 145. 🟢 重复的测试名称: "tuple with rest schema"

**文件**: `node_modules/zod/src/v3/tests/tuple.test.ts, node_modules/zod/src/v4/classic/tests/tuple.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 146. 🟢 重复的测试名称: "tuple with optional elements"

**文件**: `node_modules/zod/src/v3/tests/tuple.test.ts, node_modules/zod/src/v4/classic/tests/tuple.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 147. 🟢 重复的测试名称: "transform ctx.addIssue with parse"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 148. 🟢 重复的测试名称: "transform ctx.addIssue with parseAsync"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 149. 🟢 重复的测试名称: "z.NEVER in transform"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 150. 🟢 重复的测试名称: "basic transformations"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 151. 🟢 重复的测试名称: "coercion"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 152. 🟢 重复的测试名称: "async coercion"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 153. 🟢 重复的测试名称: "sync coercion async error"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 154. 🟢 重复的测试名称: "default"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 155. 🟢 重复的测试名称: "dynamic default"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 156. 🟢 重复的测试名称: "default when property is null or undefined"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 157. 🟢 重复的测试名称: "default with falsy values"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 158. 🟢 重复的测试名称: "object typing"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 159. 🟢 重复的测试名称: "transform method overloads"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 160. 🟢 重复的测试名称: "multiple transformers"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 161. 🟢 重复的测试名称: "short circuit on dirty"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 162. 🟢 重复的测试名称: "async short circuit on dirty"

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts, node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 163. 🟢 重复的测试名称: "passing validations"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v3/tests/number.test.ts, node_modules/zod/src/v3/tests/nan.test.ts, node_modules/zod/src/v3/tests/literal.test.ts, node_modules/zod/src/v3/tests/date.test.ts, node_modules/zod/src/v3/tests/custom.test.ts, node_modules/zod/src/v3/tests/bigint.test.ts, node_modules/zod/src/v3/tests/array.test.ts, node_modules/zod/src/v4/classic/tests/nan.test.ts, node_modules/zod/src/v4/classic/tests/literal.test.ts, node_modules/zod/src/v4/classic/tests/file.test.ts, node_modules/zod/src/v4/classic/tests/date.test.ts, node_modules/zod/src/v4/classic/tests/custom.test.ts, node_modules/zod/src/v4/classic/tests/bigint.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 164. 🟢 重复的测试名称: "failing validations"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v3/tests/number.test.ts, node_modules/zod/src/v3/tests/nan.test.ts, node_modules/zod/src/v3/tests/literal.test.ts, node_modules/zod/src/v3/tests/date.test.ts, node_modules/zod/src/v3/tests/bigint.test.ts, node_modules/zod/src/v3/tests/array.test.ts, node_modules/zod/src/v4/classic/tests/nan.test.ts, node_modules/zod/src/v4/classic/tests/literal.test.ts, node_modules/zod/src/v4/classic/tests/file.test.ts, node_modules/zod/src/v4/classic/tests/date.test.ts, node_modules/zod/src/v4/classic/tests/bigint.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 165. 🟢 重复的测试名称: "email validations"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 166. 🟢 重复的测试名称: "url validations"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 167. 🟢 重复的测试名称: "url error overrides"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 168. 🟢 重复的测试名称: "emoji validations"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 169. 🟢 重复的测试名称: "nanoid"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 170. 🟢 重复的测试名称: "bad nanoid"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 171. 🟢 重复的测试名称: "cuid"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 172. 🟢 重复的测试名称: "cuid2"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 173. 🟢 重复的测试名称: "ulid"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 174. 🟢 重复的测试名称: "regex"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 175. 🟢 重复的测试名称: "regexp error message"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 176. 🟢 重复的测试名称: "regex lastIndex reset"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 177. 🟢 重复的测试名称: "min max getters"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v3/tests/number.test.ts, node_modules/zod/src/v3/tests/date.test.ts, node_modules/zod/src/v3/tests/bigint.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/date.test.ts, node_modules/zod/src/v4/classic/tests/bigint.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 178. 🟢 重复的测试名称: "trim"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 179. 🟢 重复的测试名称: "lowerCase"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 180. 🟢 重复的测试名称: "date parsing"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/datetime.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 181. 🟢 重复的测试名称: "time parsing"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/datetime.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 182. 🟢 重复的测试名称: "duration"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/datetime.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 183. 🟢 重复的测试名称: "IP validation"

**文件**: `node_modules/zod/src/v3/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 184. 🟢 重复的测试名称: "assignability"

**文件**: `node_modules/zod/src/v3/tests/standard-schema.test.ts, node_modules/zod/src/v3/tests/generics.test.ts, node_modules/zod/src/v4/mini/tests/assignability.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/generics.test.ts, node_modules/zod/src/v4/classic/tests/assignability.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 185. 🟢 重复的测试名称: "type inference"

**文件**: `node_modules/zod/src/v3/tests/standard-schema.test.ts, node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v3/tests/record.test.ts, node_modules/zod/src/v3/tests/map.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/record.test.ts, node_modules/zod/src/v4/classic/tests/map.test.ts, node_modules/zod/src/v4/classic/tests/array.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 186. 🟢 重复的测试名称: "valid parse"

**文件**: `node_modules/zod/src/v3/tests/standard-schema.test.ts, node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v3/tests/map.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/map.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 187. 🟢 重复的测试名称: "valid parse async"

**文件**: `node_modules/zod/src/v3/tests/standard-schema.test.ts, node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v3/tests/map.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/map.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 188. 🟢 重复的测试名称: "valid parse: size-related methods"

**文件**: `node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 189. 🟢 重复的测试名称: "failing when parsing empty set in nonempty "

**文件**: `node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 190. 🟢 重复的测试名称: "failing when set is smaller than min() "

**文件**: `node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 191. 🟢 重复的测试名称: "failing when set is bigger than max() "

**文件**: `node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 192. 🟢 重复的测试名称: "doesn’t throw when an empty set is given"

**文件**: `node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 193. 🟢 重复的测试名称: "throws when a Map is given"

**文件**: `node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 194. 🟢 重复的测试名称: "throws when the given set has invalid input"

**文件**: `node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 195. 🟢 重复的测试名称: "throws when the given set has multiple invalid entries"

**文件**: `node_modules/zod/src/v3/tests/set.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 196. 🟢 重复的测试名称: "refinement type guard"

**文件**: `node_modules/zod/src/v3/tests/refine.test.ts, node_modules/zod/src/v4/classic/tests/refine.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 197. 🟢 重复的测试名称: "custom path"

**文件**: `node_modules/zod/src/v3/tests/refine.test.ts, node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 198. 🟢 重复的测试名称: "superRefine - type narrowing"

**文件**: `node_modules/zod/src/v3/tests/refine.test.ts, node_modules/zod/src/v4/classic/tests/refine.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 199. 🟢 重复的测试名称: "chained mixed refining types"

**文件**: `node_modules/zod/src/v3/tests/refine.test.ts, node_modules/zod/src/v4/classic/tests/refine.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 200. 🟢 重复的测试名称: "chained refinements"

**文件**: `node_modules/zod/src/v3/tests/refine.test.ts, node_modules/zod/src/v4/classic/tests/refine.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 201. 🟢 重复的测试名称: "recursion with z.lazy"

**文件**: `node_modules/zod/src/v3/tests/recursive.test.ts, node_modules/zod/src/v4/mini/tests/recursive-types.test.ts, node_modules/zod/src/v4/classic/tests/recursive-types.test.ts, node_modules/zod/src/v4/classic/tests/lazy.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 202. 🟢 重复的测试名称: "schema getter"

**文件**: `node_modules/zod/src/v3/tests/recursive.test.ts, node_modules/zod/src/v3/tests/recursive.test.ts, node_modules/zod/src/v4/classic/tests/lazy.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 203. 🟢 重复的测试名称: "recursion involving union type"

**文件**: `node_modules/zod/src/v3/tests/recursive.test.ts, node_modules/zod/src/v4/mini/tests/recursive-types.test.ts, node_modules/zod/src/v4/classic/tests/recursive-types.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 204. 🟢 重复的测试名称: "string record parse - pass"

**文件**: `node_modules/zod/src/v3/tests/record.test.ts, node_modules/zod/src/v4/classic/tests/record.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 205. 🟢 重复的测试名称: "string record parse - fail"

**文件**: `node_modules/zod/src/v3/tests/record.test.ts, node_modules/zod/src/v3/tests/record.test.ts, node_modules/zod/src/v3/tests/record.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 206. 🟢 重复的测试名称: "key and value getters"

**文件**: `node_modules/zod/src/v3/tests/record.test.ts, node_modules/zod/src/v4/classic/tests/record.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 207. 🟢 重复的测试名称: "is not vulnerable to prototype pollution"

**文件**: `node_modules/zod/src/v3/tests/record.test.ts, node_modules/zod/src/v4/classic/tests/record.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 208. 🟢 重复的测试名称: "flat inference"

**文件**: `node_modules/zod/src/v3/tests/readonly.test.ts, node_modules/zod/src/v4/classic/tests/readonly.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 209. 🟢 重复的测试名称: "deep inference"

**文件**: `node_modules/zod/src/v3/tests/readonly.test.ts, node_modules/zod/src/v4/classic/tests/readonly.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 210. 🟢 重复的测试名称: "object freezing"

**文件**: `node_modules/zod/src/v3/tests/readonly.test.ts, node_modules/zod/src/v4/classic/tests/readonly.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 211. 🟢 重复的测试名称: "async object freezing"

**文件**: `node_modules/zod/src/v3/tests/readonly.test.ts, node_modules/zod/src/v4/classic/tests/readonly.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 212. 🟢 重复的测试名称: "promise inference"

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts, node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 213. 🟢 重复的测试名称: "promise parsing success"

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts, node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 214. 🟢 重复的测试名称: "promise parsing fail"

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts, node_modules/zod/src/v3/tests/promise.test.ts, node_modules/zod/src/v4/classic/tests/promise.test.ts, node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 215. 🟢 重复的测试名称: "promise parsing fail 2"

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts, node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 216. 🟢 重复的测试名称: "sync promise parsing"

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts, node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 217. 🟢 重复的测试名称: "async function pass"

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts, node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 218. 🟢 重复的测试名称: "async function fail"

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts, node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 219. 🟢 重复的测试名称: "async promise parsing"

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts, node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 220. 🟢 重复的测试名称: "resolves"

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts, node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 221. 🟢 重复的测试名称: "literal string boolean"

**文件**: `node_modules/zod/src/v3/tests/primitive.test.ts, node_modules/zod/src/v3/tests/primitive.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 222. 🟢 重复的测试名称: "primitive inference"

**文件**: `node_modules/zod/src/v3/tests/primitive.test.ts, node_modules/zod/src/v4/classic/tests/primitive.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 223. 🟢 重复的测试名称: "preprocess"

**文件**: `node_modules/zod/src/v3/tests/preprocess.test.ts, node_modules/zod/src/v4/classic/tests/preprocess.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 224. 🟢 重复的测试名称: "async preprocess"

**文件**: `node_modules/zod/src/v3/tests/preprocess.test.ts, node_modules/zod/src/v4/classic/tests/preprocess.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 225. 🟢 重复的测试名称: "preprocess ctx.addIssue with parse"

**文件**: `node_modules/zod/src/v3/tests/preprocess.test.ts, node_modules/zod/src/v4/classic/tests/preprocess.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 226. 🟢 重复的测试名称: "preprocess ctx.addIssue non-fatal by default"

**文件**: `node_modules/zod/src/v3/tests/preprocess.test.ts, node_modules/zod/src/v4/classic/tests/preprocess.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 227. 🟢 重复的测试名称: "preprocess ctx.addIssue fatal true"

**文件**: `node_modules/zod/src/v3/tests/preprocess.test.ts, node_modules/zod/src/v4/classic/tests/preprocess.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 228. 🟢 重复的测试名称: "z.NEVER in preprocess"

**文件**: `node_modules/zod/src/v3/tests/preprocess.test.ts, node_modules/zod/src/v4/classic/tests/preprocess.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 229. 🟢 重复的测试名称: "preprocess as the second property of object"

**文件**: `node_modules/zod/src/v3/tests/preprocess.test.ts, node_modules/zod/src/v4/classic/tests/preprocess.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 230. 🟢 重复的测试名称: "preprocess validates with sibling errors"

**文件**: `node_modules/zod/src/v3/tests/preprocess.test.ts, node_modules/zod/src/v4/classic/tests/preprocess.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 231. 🟢 重复的测试名称: "pick type inference"

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts, node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 232. 🟢 重复的测试名称: "pick parse - success"

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts, node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 233. 🟢 重复的测试名称: "pick parse - fail"

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts, node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 234. 🟢 重复的测试名称: "omit type inference"

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts, node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 235. 🟢 重复的测试名称: "omit parse - success"

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts, node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 236. 🟢 重复的测试名称: "omit parse - fail"

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts, node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 237. 🟢 重复的测试名称: "nonstrict inference"

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts, node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 238. 🟢 重复的测试名称: "nonstrict parsing - pass"

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts, node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 239. 🟢 重复的测试名称: "nonstrict parsing - fail"

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts, node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 240. 🟢 重复的测试名称: "pick/omit/required/partial - do not allow unknown keys"

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts, node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 241. 🟢 重复的测试名称: "shallow inference"

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts, node_modules/zod/src/v4/classic/tests/partial.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 242. 🟢 重复的测试名称: "shallow partial parse"

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts, node_modules/zod/src/v4/classic/tests/partial.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 243. 🟢 重复的测试名称: "deep partial inference"

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts, node_modules/zod/src/v3/tests/partials.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 244. 🟢 重复的测试名称: "required"

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts, node_modules/zod/src/v4/classic/tests/partial.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 245. 🟢 重复的测试名称: "required inference"

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts, node_modules/zod/src/v4/classic/tests/partial.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 246. 🟢 重复的测试名称: "required with mask"

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts, node_modules/zod/src/v4/classic/tests/partial.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 247. 🟢 重复的测试名称: "required with mask -- ignore falsy values"

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts, node_modules/zod/src/v4/classic/tests/partial.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 248. 🟢 重复的测试名称: "partial with mask"

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts, node_modules/zod/src/v4/classic/tests/partial.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 249. 🟢 重复的测试名称: "partial with mask -- ignore falsy values"

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts, node_modules/zod/src/v4/classic/tests/partial.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 250. 🟢 重复的测试名称: "Should have error messages appropriate for the underlying type"

**文件**: `node_modules/zod/src/v3/tests/optional.test.ts, node_modules/zod/src/v3/tests/nullable.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 251. 🟢 重复的测试名称: "unwrap"

**文件**: `node_modules/zod/src/v3/tests/optional.test.ts, node_modules/zod/src/v3/tests/nullable.test.ts, node_modules/zod/src/v4/classic/tests/optional.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 252. 🟢 重复的测试名称: "object type inference"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 253. 🟢 重复的测试名称: "unknown throw"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 254. 🟢 重复的测试名称: "shape() should return schema of particular key"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 255. 🟢 重复的测试名称: "correct parsing"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 256. 🟢 重复的测试名称: "nonstrict by default"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 257. 🟢 重复的测试名称: "strip by default"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 258. 🟢 重复的测试名称: "unknownkeys override"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 259. 🟢 重复的测试名称: "passthrough unknown"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 260. 🟢 重复的测试名称: "strip unknown"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 261. 🟢 重复的测试名称: "strict"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 262. 🟢 重复的测试名称: "catchall inference"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 263. 🟢 重复的测试名称: "catchall overrides strict"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 264. 🟢 重复的测试名称: "test async union"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 265. 🟢 重复的测试名称: "test inferred merged type"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 266. 🟢 重复的测试名称: "inferred merged object type with optional properties"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 267. 🟢 重复的测试名称: "inferred unioned object type with optional properties"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 268. 🟢 重复的测试名称: "inferred enum type"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 269. 🟢 重复的测试名称: "inferred partial object type with optional properties"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 270. 🟢 重复的测试名称: "inferred picked object type with optional properties"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 271. 🟢 重复的测试名称: "inferred type for unknown/any keys"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 272. 🟢 重复的测试名称: "object with refine"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 273. 🟢 重复的测试名称: "intersection of object with date"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 274. 🟢 重复的测试名称: "intersection of object with refine with date"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 275. 🟢 重复的测试名称: "constructor key"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 276. 🟢 重复的测试名称: "unknownkeys merging"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 277. 🟢 重复的测试名称: "extend() should return schema with new key"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 278. 🟢 重复的测试名称: "extend() should have power to override existing key"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 279. 🟢 重复的测试名称: "passthrough index signature"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 280. 🟢 重复的测试名称: "xor"

**文件**: `node_modules/zod/src/v3/tests/object.test.ts, node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 281. 🟢 重复的测试名称: "int getter"

**文件**: `node_modules/zod/src/v3/tests/number.test.ts, node_modules/zod/src/v4/classic/tests/number.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 282. 🟢 重复的测试名称: "finite getter"

**文件**: `node_modules/zod/src/v3/tests/number.test.ts, node_modules/zod/src/v4/classic/tests/number.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 283. 🟢 重复的测试名称: "throws when a Set is given"

**文件**: `node_modules/zod/src/v3/tests/map.test.ts, node_modules/zod/src/v4/classic/tests/map.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 284. 🟢 重复的测试名称: "throws when the given map has invalid key and invalid input"

**文件**: `node_modules/zod/src/v3/tests/map.test.ts, node_modules/zod/src/v4/classic/tests/map.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 285. 🟢 重复的测试名称: "throws when the given map has multiple invalid entries"

**文件**: `node_modules/zod/src/v3/tests/map.test.ts, node_modules/zod/src/v4/classic/tests/map.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 286. 🟢 重复的测试名称: "dirty"

**文件**: `node_modules/zod/src/v3/tests/map.test.ts, node_modules/zod/src/v4/classic/tests/map.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 287. 🟢 重复的测试名称: "object intersection"

**文件**: `node_modules/zod/src/v3/tests/intersection.test.ts, node_modules/zod/src/v4/classic/tests/intersection.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 288. 🟢 重复的测试名称: "deep intersection"

**文件**: `node_modules/zod/src/v3/tests/intersection.test.ts, node_modules/zod/src/v4/classic/tests/intersection.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 289. 🟢 重复的测试名称: "deep intersection of arrays"

**文件**: `node_modules/zod/src/v3/tests/intersection.test.ts, node_modules/zod/src/v4/classic/tests/intersection.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 290. 🟢 重复的测试名称: "invalid intersection types"

**文件**: `node_modules/zod/src/v3/tests/intersection.test.ts, node_modules/zod/src/v4/classic/tests/intersection.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 291. 🟢 重复的测试名称: "instanceof"

**文件**: `node_modules/zod/src/v3/tests/instanceof.test.ts, node_modules/zod/src/v4/classic/tests/instanceof.test.ts, node_modules/zod/src/v4/classic/tests/custom.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 292. 🟢 重复的测试名称: "instanceof fatal"

**文件**: `node_modules/zod/src/v3/tests/instanceof.test.ts, node_modules/zod/src/v4/classic/tests/instanceof.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 293. 🟢 重复的测试名称: "generics"

**文件**: `node_modules/zod/src/v3/tests/generics.test.ts, node_modules/zod/src/v4/classic/tests/generics.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 294. 🟢 重复的测试名称: "nested no undefined"

**文件**: `node_modules/zod/src/v3/tests/generics.test.ts, node_modules/zod/src/v4/classic/tests/generics.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 295. 🟢 重复的测试名称: "parsed function fail 1"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 296. 🟢 重复的测试名称: "parsed function fail 2"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 297. 🟢 重复的测试名称: "function inference 1"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 298. 🟢 重复的测试名称: "method parsing"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 299. 🟢 重复的测试名称: "async method parsing"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 300. 🟢 重复的测试名称: "args method"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 301. 🟢 重复的测试名称: "function inference 2"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 302. 🟢 重复的测试名称: "valid function run"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 303. 🟢 重复的测试名称: "input validation error"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 304. 🟢 重复的测试名称: "output validation error"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 305. 🟢 重复的测试名称: "function with async refinements"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 306. 🟢 重复的测试名称: "non async function with async refinements should fail"

**文件**: `node_modules/zod/src/v3/tests/function.test.ts, node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 307. 🟢 重复的测试名称: "first party switch"

**文件**: `node_modules/zod/src/v3/tests/firstparty.test.ts, node_modules/zod/src/v4/classic/tests/firstparty.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 308. 🟢 重复的测试名称: "error creation"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 309. 🟢 重复的测试名称: "type error with custom error map"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 310. 🟢 重复的测试名称: "refinement fail with params"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 311. 🟢 重复的测试名称: "default error message"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 312. 🟢 重复的测试名称: "override error in refine"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 313. 🟢 重复的测试名称: "override error in refinement"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 314. 🟢 重复的测试名称: "array minimum"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 315. 🟢 重复的测试名称: "custom path in custom error map"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 316. 🟢 重复的测试名称: "error metadata from value"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 317. 🟢 重复的测试名称: "root level formatting"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 318. 🟢 重复的测试名称: "no abort early on refinements"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 319. 🟢 重复的测试名称: "formatting"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 320. 🟢 重复的测试名称: "formatting with nullable and optional fields"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 321. 🟢 重复的测试名称: "schema-bound error map"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 322. 🟢 重复的测试名称: "invalid and required"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 323. 🟢 重复的测试名称: "Fallback to default required error"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 324. 🟢 重复的测试名称: "invalid and required and errorMap"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 325. 🟢 重复的测试名称: "strict error message"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 326. 🟢 重复的测试名称: "enum error message, invalid enum elementstring"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/enum.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 327. 🟢 重复的测试名称: "enum error message, invalid type"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/enum.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 328. 🟢 重复的测试名称: "nativeEnum default error message"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/enum.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 329. 🟢 重复的测试名称: "literal default error message"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/literal.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 330. 🟢 重复的测试名称: "literal bigint default error message"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/literal.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 331. 🟢 重复的测试名称: "enum with message returns the custom error message"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/enum.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 332. 🟢 重复的测试名称: "dont short circuit on continuable errors"

**文件**: `node_modules/zod/src/v3/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 333. 🟢 重复的测试名称: "get options"

**文件**: `node_modules/zod/src/v3/tests/enum.test.ts, node_modules/zod/src/v4/classic/tests/enum.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 334. 🟢 重复的测试名称: "readonly enum"

**文件**: `node_modules/zod/src/v3/tests/enum.test.ts, node_modules/zod/src/v4/classic/tests/enum.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 335. 🟢 重复的测试名称: "readonly in ZodEnumDef"

**文件**: `node_modules/zod/src/v3/tests/enum.test.ts, node_modules/zod/src/v4/classic/tests/enum.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 336. 🟢 重复的测试名称: "valid - discriminator value of various primitive types"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 337. 🟢 重复的测试名称: "invalid - null"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 338. 🟢 重复的测试名称: "invalid discriminator value"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 339. 🟢 重复的测试名称: "valid discriminator value, invalid data"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 340. 🟢 重复的测试名称: "wrong schema - missing discriminator"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 341. 🟢 重复的测试名称: "wrong schema - duplicate discriminator values"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 342. 🟢 重复的测试名称: "async - valid"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 343. 🟢 重复的测试名称: "async - invalid"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 344. 🟢 重复的测试名称: "enum and nativeEnum"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 345. 🟢 重复的测试名称: "branded"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 346. 🟢 重复的测试名称: "optional and nullable"

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts, node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 347. 🟢 重复的测试名称: "passing `description` to schema should add a description"

**文件**: `node_modules/zod/src/v3/tests/description.test.ts, node_modules/zod/src/v4/classic/tests/description.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 348. 🟢 重复的测试名称: "description should carry over to chained schemas"

**文件**: `node_modules/zod/src/v3/tests/description.test.ts, node_modules/zod/src/v4/classic/tests/description.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 349. 🟢 重复的测试名称: "basic defaults"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v4/classic/tests/default.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 350. 🟢 重复的测试名称: "default with transform"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v4/classic/tests/default.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 351. 🟢 重复的测试名称: "default on existing optional"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v4/classic/tests/default.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 352. 🟢 重复的测试名称: "optional on default"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v4/classic/tests/default.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 353. 🟢 重复的测试名称: "complex chain example"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/default.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 354. 🟢 重复的测试名称: "removeDefault"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v4/classic/tests/default.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 355. 🟢 重复的测试名称: "nested"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/default.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 356. 🟢 重复的测试名称: "chained defaults"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v4/classic/tests/default.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 357. 🟢 重复的测试名称: "factory"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v3/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 358. 🟢 重复的测试名称: "native enum"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 359. 🟢 重复的测试名称: "enum"

**文件**: `node_modules/zod/src/v3/tests/default.test.ts, node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/to-json-schema.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 360. 🟢 重复的测试名称: "test"

**文件**: `node_modules/zod/src/v3/tests/deepmasking.test.ts, node_modules/zod/src/v4/core/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 361. 🟢 重复的测试名称: "array masking"

**文件**: `node_modules/zod/src/v3/tests/deepmasking.test.ts, node_modules/zod/src/v3/tests/deepmasking.test.ts, node_modules/zod/src/v3/tests/deepmasking.test.ts, node_modules/zod/src/v3/tests/deepmasking.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 362. 🟢 重复的测试名称: "string params"

**文件**: `node_modules/zod/src/v3/tests/custom.test.ts, node_modules/zod/src/v4/classic/tests/custom.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 363. 🟢 重复的测试名称: "string coercion"

**文件**: `node_modules/zod/src/v3/tests/coerce.test.ts, node_modules/zod/src/v4/classic/tests/coerce.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 364. 🟢 重复的测试名称: "number coercion"

**文件**: `node_modules/zod/src/v3/tests/coerce.test.ts, node_modules/zod/src/v4/classic/tests/coerce.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 365. 🟢 重复的测试名称: "boolean coercion"

**文件**: `node_modules/zod/src/v3/tests/coerce.test.ts, node_modules/zod/src/v4/classic/tests/coerce.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 366. 🟢 重复的测试名称: "bigint coercion"

**文件**: `node_modules/zod/src/v3/tests/coerce.test.ts, node_modules/zod/src/v4/classic/tests/coerce.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 367. 🟢 重复的测试名称: "date coercion"

**文件**: `node_modules/zod/src/v3/tests/coerce.test.ts, node_modules/zod/src/v4/classic/tests/coerce.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 368. 🟢 重复的测试名称: "basic catch"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 369. 🟢 重复的测试名称: "catch fn does not run when parsing succeeds"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 370. 🟢 重复的测试名称: "basic catch async"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 371. 🟢 重复的测试名称: "catch replace wrong types"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 372. 🟢 重复的测试名称: "catch with transform"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 373. 🟢 重复的测试名称: "catch on existing optional"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 374. 🟢 重复的测试名称: "optional on catch"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 375. 🟢 重复的测试名称: "removeCatch"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 376. 🟢 重复的测试名称: "chained catch"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 377. 🟢 重复的测试名称: "reported issues with nested usage"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 378. 🟢 重复的测试名称: "catch error"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 379. 🟢 重复的测试名称: "ctx.input"

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts, node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 380. 🟢 重复的测试名称: "branded types"

**文件**: `node_modules/zod/src/v3/tests/branded.test.ts, node_modules/zod/src/v4/mini/tests/brand.test.ts, node_modules/zod/src/v4/classic/tests/brand.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 381. 🟢 重复的测试名称: "test this binding"

**文件**: `node_modules/zod/src/v3/tests/base.test.ts, node_modules/zod/src/v4/classic/tests/base.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 382. 🟢 重复的测试名称: "parseAsync async test"

**文件**: `node_modules/zod/src/v3/tests/async-refinements.test.ts, node_modules/zod/src/v3/tests/async-refinements.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 383. 🟢 重复的测试名称: "string async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 384. 🟢 重复的测试名称: "number async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 385. 🟢 重复的测试名称: "bigInt async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 386. 🟢 重复的测试名称: "boolean async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 387. 🟢 重复的测试名称: "date async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 388. 🟢 重复的测试名称: "undefined async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 389. 🟢 重复的测试名称: "null async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 390. 🟢 重复的测试名称: "any async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 391. 🟢 重复的测试名称: "unknown async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 392. 🟢 重复的测试名称: "void async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 393. 🟢 重复的测试名称: "array async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 394. 🟢 重复的测试名称: "object async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 395. 🟢 重复的测试名称: "union async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 396. 🟢 重复的测试名称: "record async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 397. 🟢 重复的测试名称: "function async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 398. 🟢 重复的测试名称: "literal async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 399. 🟢 重复的测试名称: "enum async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 400. 🟢 重复的测试名称: "nativeEnum async parse"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 401. 🟢 重复的测试名称: "promise async parse good"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 402. 🟢 重复的测试名称: "promise async parse bad"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 403. 🟢 重复的测试名称: "async validation non-empty strings"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 404. 🟢 重复的测试名称: "async validation multiple errors 1"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 405. 🟢 重复的测试名称: "async validation multiple errors 2"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 406. 🟢 重复的测试名称: "ensure early async failure prevents follow-up refinement checks"

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts, node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 407. 🟢 重复的测试名称: "parse empty array in nonempty"

**文件**: `node_modules/zod/src/v3/tests/array.test.ts, node_modules/zod/src/v4/classic/tests/array.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 408. 🟢 重复的测试名称: "get element"

**文件**: `node_modules/zod/src/v3/tests/array.test.ts, node_modules/zod/src/v4/classic/tests/array.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 409. 🟢 重复的测试名称: "continue parsing despite array size error"

**文件**: `node_modules/zod/src/v3/tests/array.test.ts, node_modules/zod/src/v4/classic/tests/array.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 410. 🟢 重复的测试名称: "parse should fail given sparse array"

**文件**: `node_modules/zod/src/v3/tests/array.test.ts, node_modules/zod/src/v4/classic/tests/array.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 411. 🟢 重复的测试名称: "check any inference"

**文件**: `node_modules/zod/src/v3/tests/anyunknown.test.ts, node_modules/zod/src/v4/classic/tests/anyunknown.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 412. 🟢 重复的测试名称: "check unknown inference"

**文件**: `node_modules/zod/src/v3/tests/anyunknown.test.ts, node_modules/zod/src/v4/classic/tests/anyunknown.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 413. 🟢 重复的测试名称: "check never inference"

**文件**: `node_modules/zod/src/v3/tests/anyunknown.test.ts, node_modules/zod/src/v4/classic/tests/anyunknown.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 414. 🟢 重复的测试名称: "all errors"

**文件**: `node_modules/zod/src/v3/tests/all-errors.test.ts, node_modules/zod/src/v4/classic/tests/error-utils.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 415. 🟢 重复的测试名称: "mutual recursion - native"

**文件**: `node_modules/zod/src/v4/mini/tests/recursive-types.test.ts, node_modules/zod/src/v4/classic/tests/recursive-types.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 416. 🟢 重复的测试名称: "pick and omit with getter"

**文件**: `node_modules/zod/src/v4/mini/tests/recursive-types.test.ts, node_modules/zod/src/v4/classic/tests/recursive-types.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 417. 🟢 重复的测试名称: "deferred self-recursion"

**文件**: `node_modules/zod/src/v4/mini/tests/recursive-types.test.ts, node_modules/zod/src/v4/classic/tests/recursive-types.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 418. 🟢 重复的测试名称: "recursion compatibility"

**文件**: `node_modules/zod/src/v4/mini/tests/recursive-types.test.ts, node_modules/zod/src/v4/classic/tests/recursive-types.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 419. 🟢 重复的测试名称: "prototype extension"

**文件**: `node_modules/zod/src/v4/mini/tests/prototypes.test.ts, node_modules/zod/src/v4/mini/tests/prototypes.test.ts, node_modules/zod/src/v4/classic/tests/prototypes.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 420. 🟢 重复的测试名称: "z.boolean"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 421. 🟢 重复的测试名称: "z.bigint"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 422. 🟢 重复的测试名称: "z.symbol"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 423. 🟢 重复的测试名称: "z.date"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 424. 🟢 重复的测试名称: "z.coerce.string"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 425. 🟢 重复的测试名称: "z.coerce.number"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 426. 🟢 重复的测试名称: "z.coerce.boolean"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 427. 🟢 重复的测试名称: "z.coerce.bigint"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 428. 🟢 重复的测试名称: "z.coerce.date"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 429. 🟢 重复的测试名称: "z.iso.datetime"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 430. 🟢 重复的测试名称: "z.iso.date"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 431. 🟢 重复的测试名称: "z.iso.time"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 432. 🟢 重复的测试名称: "z.iso.duration"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 433. 🟢 重复的测试名称: "z.undefined"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 434. 🟢 重复的测试名称: "z.null"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/nullable.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 435. 🟢 重复的测试名称: "z.any"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 436. 🟢 重复的测试名称: "z.unknown"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 437. 🟢 重复的测试名称: "z.never"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 438. 🟢 重复的测试名称: "z.void"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 439. 🟢 重复的测试名称: "z.array"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 440. 🟢 重复的测试名称: "z.union"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 441. 🟢 重复的测试名称: "z.intersection"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 442. 🟢 重复的测试名称: "z.tuple"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 443. 🟢 重复的测试名称: "z.record"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 444. 🟢 重复的测试名称: "z.map"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 445. 🟢 重复的测试名称: "z.map invalid_element"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 446. 🟢 重复的测试名称: "z.map async"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 447. 🟢 重复的测试名称: "z.set"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 448. 🟢 重复的测试名称: "z.enum"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 449. 🟢 重复的测试名称: "z.enum - native"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 450. 🟢 重复的测试名称: "z.nativeEnum"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 451. 🟢 重复的测试名称: "z.literal"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 452. 🟢 重复的测试名称: "z.file"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 453. 🟢 重复的测试名称: "z.transform"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 454. 🟢 重复的测试名称: "z.transform async"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 455. 🟢 重复的测试名称: "z.preprocess"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 456. 🟢 重复的测试名称: "z.preprocess async"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 457. 🟢 重复的测试名称: "z.optional"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 458. 🟢 重复的测试名称: "z.nullable"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 459. 🟢 重复的测试名称: "z.default"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 460. 🟢 重复的测试名称: "z.catch"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 461. 🟢 重复的测试名称: "z.nan"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 462. 🟢 重复的测试名称: "z.pipe"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 463. 🟢 重复的测试名称: "z.readonly"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 464. 🟢 重复的测试名称: "z.templateLiteral"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 465. 🟢 重复的测试名称: "z.check"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 466. 🟢 重复的测试名称: "z.instanceof"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 467. 🟢 重复的测试名称: "z.refine"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 468. 🟢 重复的测试名称: "z.superRefine"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 469. 🟢 重复的测试名称: "z.$brand()"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 470. 🟢 重复的测试名称: "z.lazy"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 471. 🟢 重复的测试名称: "z.json"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 472. 🟢 重复的测试名称: "z.stringbool"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/stringbool.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 473. 🟢 重复的测试名称: "z.promise"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 474. 🟢 重复的测试名称: "type assertions"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 475. 🟢 重复的测试名称: "def typing"

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 476. 🟢 重复的测试名称: "z.function"

**文件**: `node_modules/zod/src/v4/mini/tests/functions.test.ts, node_modules/zod/src/v4/mini/tests/functions.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 477. 🟢 重复的测试名称: "error inheritance"

**文件**: `node_modules/zod/src/v4/mini/tests/error.test.ts, node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 478. 🟢 重复的测试名称: "min/max"

**文件**: `node_modules/zod/src/v4/mini/tests/computed.test.ts, node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 479. 🟢 重复的测试名称: "async validation"

**文件**: `node_modules/zod/src/v4/core/tests/index.test.ts, node_modules/zod/src/v4/classic/tests/tuple.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 480. 🟢 重复的测试名称: "pipe"

**文件**: `node_modules/zod/src/v4/classic/tests/to-json-schema.test.ts, node_modules/zod/src/v4/classic/tests/to-json-schema.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 481. 🟢 重复的测试名称: "length checks"

**文件**: `node_modules/zod/src/v4/classic/tests/string.test.ts, node_modules/zod/src/v4/classic/tests/standard-schema.test.ts, node_modules/zod/src/v4/classic/tests/standard-schema.test.ts, node_modules/zod/src/v4/classic/tests/standard-schema.test.ts, node_modules/zod/src/v4/classic/tests/standard-schema.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 482. 🟢 重复的测试名称: "string format methods"

**文件**: `node_modules/zod/src/v4/classic/tests/string-formats.test.ts, node_modules/zod/src/v4/classic/tests/number.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 483. 🟢 重复的测试名称: ".describe"

**文件**: `node_modules/zod/src/v4/classic/tests/registries.test.ts, node_modules/zod/src/v4/classic/tests/description.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 484. 🟢 重复的测试名称: "async parsing"

**文件**: `node_modules/zod/src/v4/classic/tests/record.test.ts, node_modules/zod/src/v4/classic/tests/record.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 485. 🟢 重复的测试名称: "nonoptional with default"

**文件**: `node_modules/zod/src/v4/classic/tests/nonoptional.test.ts, node_modules/zod/src/v4/classic/tests/coalesce.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 486. 🟢 重复的测试名称: "nonoptional in object"

**文件**: `node_modules/zod/src/v4/classic/tests/nonoptional.test.ts, node_modules/zod/src/v4/classic/tests/coalesce.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 487. 🟢 重复的测试名称: "parsedType"

**文件**: `node_modules/zod/src/v4/core/tests/locales/tr.test.ts, node_modules/zod/src/v4/core/tests/locales/en.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 488. 🟢 重复的测试名称: "pluralization rules"

**文件**: `node_modules/zod/src/v4/core/tests/locales/ru.test.ts, node_modules/zod/src/v4/core/tests/locales/be.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 489. 🟢 重复的测试名称: "handles negative numbers correctly"

**文件**: `node_modules/zod/src/v4/core/tests/locales/ru.test.ts, node_modules/zod/src/v4/core/tests/locales/be.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 490. 🟢 重复的测试名称: "handles zero correctly"

**文件**: `node_modules/zod/src/v4/core/tests/locales/ru.test.ts, node_modules/zod/src/v4/core/tests/locales/be.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

#### 491. 🟢 重复的测试名称: "handles bigint values correctly"

**文件**: `node_modules/zod/src/v4/core/tests/locales/ru.test.ts, node_modules/zod/src/v4/core/tests/locales/be.test.ts`
**建议**: 使用更具体的测试名称，避免测试逻辑重复

### 🎭 Mock数据质量问题

#### 1. 🟢 未使用统一测试断言库

**文件**: `tests/performance-optimization.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 2. 🟡 缺少边界值和异常情况测试

**文件**: `tests/performance-optimization.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 3. 🟢 未使用统一测试断言库

**文件**: `tests/monitoring-logging.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 4. 🟢 未使用统一测试断言库

**文件**: `tests/error-handling-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 5. 🟢 未使用统一测试断言库

**文件**: `tests/config-management.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 6. 🟡 测试中存在硬编码的业务数据

**文件**: `tests/config-management.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 7. 🟡 缺少边界值和异常情况测试

**文件**: `tests/config-management.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 8. 🟢 未使用统一测试断言库

**文件**: `tests/integration/repository-migration-validation.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 9. 🟡 缺少边界值和异常情况测试

**文件**: `tests/integration/repository-migration-validation.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 10. 🟢 未使用统一测试断言库

**文件**: `tests/integration/performance-stability.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 11. 🟡 缺少边界值和异常情况测试

**文件**: `tests/integration/performance-stability.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 12. 🟢 未使用统一测试断言库

**文件**: `tests/integration/llm-router.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 13. 🟢 未使用统一测试断言库

**文件**: `tests/integration/binance-simple.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 14. 🟢 未使用统一测试断言库

**文件**: `tests/integration/binance-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 15. 🟡 缺少边界值和异常情况测试

**文件**: `tests/integration/binance-integration.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 16. 🟢 未使用统一测试断言库

**文件**: `tests/integration/api-endpoints-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 17. 🟡 缺少边界值和异常情况测试

**文件**: `tests/integration/api-endpoints-integration.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 18. 🟡 未使用统一测试数据生成器

**文件**: `tests/integration/ai-modules-integration.test.ts`
**建议**: 使用UnifiedTestDataGenerator统一管理测试数据生成

#### 19. 🟡 缺少边界值和异常情况测试

**文件**: `tests/integration/ai-modules-integration.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 20. 🟢 未使用统一测试断言库

**文件**: `tests/examples/real-data-testing-example.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 21. 🟡 缺少边界值和异常情况测试

**文件**: `tests/examples/real-data-testing-example.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 22. 🟢 未使用统一测试断言库

**文件**: `tests/api/user-config.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 23. 🟡 未使用统一测试数据生成器

**文件**: `tests/api/routes.test.ts`
**建议**: 使用UnifiedTestDataGenerator统一管理测试数据生成

#### 24. 🟢 未使用统一测试断言库

**文件**: `tests/api/routes.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 25. 🟡 缺少边界值和异常情况测试

**文件**: `tests/api/routes.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 26. 🟡 未使用统一测试数据生成器

**文件**: `tests/api/comprehensive-api.test.ts`
**建议**: 使用UnifiedTestDataGenerator统一管理测试数据生成

#### 27. 🟢 未使用统一测试断言库

**文件**: `tests/api/comprehensive-api.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 28. 🟡 缺少边界值和异常情况测试

**文件**: `tests/api/comprehensive-api.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 29. 🟡 未使用统一测试数据生成器

**文件**: `tests/unit/trading-analysis/enhanced-signal-fusion-coordinator.test.ts`
**建议**: 使用UnifiedTestDataGenerator统一管理测试数据生成

#### 30. 🟢 未使用统一测试断言库

**文件**: `tests/unit/trading-analysis/enhanced-signal-fusion-coordinator.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 31. 🟡 缺少边界值和异常情况测试

**文件**: `tests/unit/trading-analysis/enhanced-signal-fusion-coordinator.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 32. 🟢 未使用统一测试断言库

**文件**: `tests/unit/trading-analysis/collaborative-decision-config.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 33. 🟡 缺少边界值和异常情况测试

**文件**: `tests/unit/trading-analysis/collaborative-decision-config.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 34. 🟢 未使用统一测试断言库

**文件**: `tests/integration/trading-analysis/enhanced-signal-fusion-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 35. 🟡 缺少边界值和异常情况测试

**文件**: `tests/integration/trading-analysis/enhanced-signal-fusion-integration.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 36. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/tests/integration/real-performance.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 37. 🟡 未使用测试数据工厂模式

**文件**: `src/tests/integration/real-performance.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 38. 🟡 随机数生成未使用固定种子

**文件**: `src/tests/integration/real-performance.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 39. 🟡 缺少边界值和异常情况测试

**文件**: `src/tests/integration/real-performance.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 40. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/tests/integration/real-api-test.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 41. 🟢 未使用统一测试断言库

**文件**: `src/tests/integration/real-api-test.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 42. 🟡 随机数生成未使用固定种子

**文件**: `src/tests/integration/real-api-test.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 43. 🟡 缺少边界值和异常情况测试

**文件**: `src/tests/integration/real-api-test.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 44. 🟢 未使用统一测试断言库

**文件**: `src/tests/integration/end-to-end-signal-workflow.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 45. 🟡 缺少边界值和异常情况测试

**文件**: `src/tests/integration/end-to-end-signal-workflow.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 46. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/tests/integration/core-analysis-services-integration.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 47. 🟢 未使用统一测试断言库

**文件**: `src/tests/integration/core-analysis-services-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 48. 🟡 随机数生成未使用固定种子

**文件**: `src/tests/integration/core-analysis-services-integration.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 49. 🟢 未使用统一测试断言库

**文件**: `src/tests/integration/architecture-validation.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 50. 🟡 缺少边界值和异常情况测试

**文件**: `src/tests/integration/architecture-validation.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 51. 🟢 未使用统一测试断言库

**文件**: `src/tests/integration/aggregate-positions-fix.integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 52. 🟢 未使用统一测试断言库

**文件**: `tests/unit/market-data/value-objects/symbol.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 53. 🟢 未使用统一测试断言库

**文件**: `tests/unit/market-data/infrastructure/prisma-price-data-repository.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 54. 🟡 测试中存在硬编码的业务数据

**文件**: `tests/unit/market-data/infrastructure/prisma-price-data-repository.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 55. 🟡 测试中存在硬编码的业务数据

**文件**: `tests/unit/market-data/infrastructure/prisma-price-data-repository.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 56. 🟢 未使用统一测试断言库

**文件**: `tests/unit/market-data/infrastructure/prisma-market-symbol-repository.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 57. 🟢 未使用统一测试断言库

**文件**: `tests/unit/market-data/infrastructure/binance-adapter.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 58. 🟡 测试中存在硬编码的业务数据

**文件**: `tests/unit/market-data/infrastructure/binance-adapter.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 59. 🟢 未使用统一测试断言库

**文件**: `src/shared/infrastructure/ai/vector-service.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 60. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/ai/vector-service.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 61. 🟢 未使用统一测试断言库

**文件**: `src/shared/infrastructure/ai/phase1-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 62. 🟢 未使用统一测试断言库

**文件**: `src/shared/infrastructure/ai/multi-tier-cache-service.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 63. 🟢 未使用统一测试断言库

**文件**: `src/shared/infrastructure/ai/dynamic-cache-strategy.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 64. 🟡 测试中存在硬编码的业务数据

**文件**: `src/shared/infrastructure/ai/dynamic-cache-strategy.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 65. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/ai/dynamic-cache-strategy.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 66. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/contexts/risk-management/tests/risk-management-integration.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 67. 🟢 未使用统一测试断言库

**文件**: `src/contexts/risk-management/tests/risk-management-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 68. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/risk-management/tests/risk-management-integration.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 69. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/risk-management/tests/risk-management-integration.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 70. 🟡 随机数生成未使用固定种子

**文件**: `src/contexts/risk-management/tests/risk-management-integration.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 71. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/risk-management/tests/risk-management-integration.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 72. 🟢 未使用统一测试断言库

**文件**: `src/contexts/risk-management/tests/risk-management-api.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 73. 🟢 未使用统一测试断言库

**文件**: `src/contexts/risk-management/tests/risk-calculation-engine.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 74. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/risk-management/tests/risk-calculation-engine.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 75. 🟢 未使用统一测试断言库

**文件**: `src/contexts/risk-management/tests/ai-risk-analysis-engine.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 76. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/risk-management/tests/ai-risk-analysis-engine.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 77. 🟡 未使用统一测试数据生成器

**文件**: `src/contexts/risk-management/tests/aggregate-positions-fix.integration.test.ts`
**建议**: 使用UnifiedTestDataGenerator统一管理测试数据生成

#### 78. 🟢 未使用统一测试断言库

**文件**: `src/contexts/risk-management/tests/aggregate-positions-fix.integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 79. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/contexts/trend-analysis/tests/unified-pattern-recognition-integration.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 80. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/unified-pattern-recognition-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 81. 🟡 随机数生成未使用固定种子

**文件**: `src/contexts/trend-analysis/tests/unified-pattern-recognition-integration.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 82. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/trend-analysis/tests/unified-pattern-recognition-integration.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 83. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/unified-multi-timeframe-service.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 84. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/trend-analysis/tests/unified-multi-timeframe-service.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 85. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/trend-prediction.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 86. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/trend-analysis/tests/trend-prediction.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 87. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/trend-analysis/tests/trend-prediction.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 88. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 89. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-integration-simple.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 90. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-engine.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 91. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-engine.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 92. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/trend-analysis-api.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 93. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/contexts/trend-analysis/tests/system-integration.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 94. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/system-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 95. 🟡 随机数生成未使用固定种子

**文件**: `src/contexts/trend-analysis/tests/system-integration.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 96. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/simple-performance-benchmark.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 97. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/performance-optimization.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 98. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/contexts/trend-analysis/tests/performance-benchmark.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 99. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/performance-benchmark.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 100. 🟡 随机数生成未使用固定种子

**文件**: `src/contexts/trend-analysis/tests/performance-benchmark.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 101. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/trend-analysis/tests/performance-benchmark.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 102. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/contexts/trend-analysis/tests/pattern-recognition-expansion.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 103. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/pattern-recognition-expansion.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 104. 🟡 随机数生成未使用固定种子

**文件**: `src/contexts/trend-analysis/tests/pattern-recognition-expansion.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 105. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/contexts/trend-analysis/tests/data-source-integration.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 106. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/data-source-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 107. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/trend-analysis/tests/data-source-integration.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 108. 🟡 随机数生成未使用固定种子

**文件**: `src/contexts/trend-analysis/tests/data-source-integration.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 109. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/tests/core-services.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 110. 🟢 未使用统一测试断言库

**文件**: `src/shared/infrastructure/data-processing/tests/data-processing-pipeline.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 111. 🟡 未使用测试数据工厂模式

**文件**: `src/shared/infrastructure/data-processing/tests/data-processing-pipeline.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 112. 🟡 测试中存在硬编码的业务数据

**文件**: `src/shared/infrastructure/data-processing/tests/data-processing-pipeline.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 113. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/data-processing/tests/data-processing-pipeline.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 114. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/shared/infrastructure/analysis/tests/PatternRecognitionService.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 115. 🟢 未使用统一测试断言库

**文件**: `src/shared/infrastructure/analysis/tests/PatternRecognitionService.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 116. 🟡 随机数生成未使用固定种子

**文件**: `src/shared/infrastructure/analysis/tests/PatternRecognitionService.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 117. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `node_modules/node-sarif-builder/src/lib/sarif-builder.spec.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 118. 🟡 随机数生成未使用固定种子

**文件**: `node_modules/node-sarif-builder/src/lib/sarif-builder.spec.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 119. 🟡 未使用测试数据工厂模式

**文件**: `src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 120. 🟡 测试中存在硬编码的业务数据

**文件**: `src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 121. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/data-processing/strategies/risk/risk-data-strategy.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 122. 🟢 未使用统一测试断言库

**文件**: `src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 123. 🟡 未使用测试数据工厂模式

**文件**: `src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 124. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/data-processing/adapters/tests/external-data-adapter-base.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 125. 🟢 未使用统一测试断言库

**文件**: `src/shared/infrastructure/data-processing/adapters/tests/adapter-factory.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 126. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/data-processing/adapters/tests/adapter-factory.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 127. 🟡 未使用测试数据工厂模式

**文件**: `src/shared/infrastructure/analysis/__tests__/strategies/IntelligentWeightingStrategy.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 128. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/analysis/__tests__/strategies/IntelligentWeightingStrategy.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 129. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 130. 🟡 未使用测试数据工厂模式

**文件**: `src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 131. 🟡 随机数生成未使用固定种子

**文件**: `src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 132. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/analysis/__tests__/services/PatternRecognitionService.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 133. 🟡 未使用测试数据工厂模式

**文件**: `src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 134. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/analysis/__tests__/services/MultiTimeframeService.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 135. 🟡 未使用测试数据工厂模式

**文件**: `src/shared/infrastructure/analysis/__tests__/services/DynamicWeightingService.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 136. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/analysis/__tests__/services/DynamicWeightingService.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 137. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trading-execution/infrastructure/adapters/__tests__/binance-api-client.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 138. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/trading-execution/infrastructure/adapters/__tests__/binance-api-client.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 139. 🟡 未使用统一测试数据生成器

**文件**: `src/contexts/trading-execution/domain/services/__tests__/simulation-engine.test.ts`
**建议**: 使用UnifiedTestDataGenerator统一管理测试数据生成

#### 140. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trading-execution/domain/services/__tests__/simulation-engine.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 141. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/trading-execution/domain/services/__tests__/simulation-engine.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 142. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/trading-execution/domain/services/__tests__/simulation-engine.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 143. 🟡 未使用统一测试数据生成器

**文件**: `src/contexts/trading-execution/domain/services/__tests__/execution-engine-router.test.ts`
**建议**: 使用UnifiedTestDataGenerator统一管理测试数据生成

#### 144. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trading-execution/domain/services/__tests__/execution-engine-router.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 145. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/trading-execution/domain/services/__tests__/execution-engine-router.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 146. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/professional-corrective-wave-analyzer.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 147. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/pattern-detection.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 148. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/pattern-detection.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 149. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/pattern-detection.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 150. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/pattern-detection.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 151. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/key-level-analysis-engine.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 152. 🟡 未使用测试数据工厂模式

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/key-level-analysis-engine.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 153. 🟡 随机数生成未使用固定种子

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/key-level-analysis-engine.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 154. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/intelligent-target-stop-calculator.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 155. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/intelligent-target-stop-calculator.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 156. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/intelligent-target-stop-calculator.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 157. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-volume-analyzer.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 158. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-volume-analyzer.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 159. 🟡 未使用测试数据工厂模式

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-real-time-monitor.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 160. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-real-time-monitor.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 161. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-pivot-detector.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 162. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/enhanced-pivot-detector.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 163. 🟢 未使用统一测试断言库

**文件**: `src/contexts/trend-analysis/infrastructure/services/__tests__/advanced-fibonacci-analyzer.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 164. 🟢 未使用统一测试断言库

**文件**: `src/contexts/market-data/infrastructure/websocket/__tests__/websocket-refactoring.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 165. 🟢 未使用统一测试断言库

**文件**: `src/contexts/ai-reasoning/infrastructure/reasoning/__tests__/continuous-learning-engine.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 166. 🟢 未使用统一测试断言库

**文件**: `src/contexts/market-data/infrastructure/external/__tests__/binance-websocket-adapter.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 167. 🟢 未使用统一测试断言库

**文件**: `src/contexts/market-data/infrastructure/external/__tests__/binance-websocket-adapter-refactored.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 168. 🟢 未使用统一测试断言库

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-prediction-engine.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 169. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-prediction-engine.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 170. 🟢 未使用统一测试断言库

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-starter.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 171. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-starter.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 172. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-performance.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 173. 🟢 未使用统一测试断言库

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-performance.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 174. 🟡 随机数生成未使用固定种子

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-performance.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 175. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-performance.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 176. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-integration.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 177. 🟢 未使用统一测试断言库

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 178. 🟡 随机数生成未使用固定种子

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-integration.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 179. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-integration.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 180. 🟢 未使用统一测试断言库

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-api.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 181. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-system-api.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 182. 🟡 未使用统一测试数据生成器

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-engine.test.ts`
**建议**: 使用UnifiedTestDataGenerator统一管理测试数据生成

#### 183. 🟢 未使用统一测试断言库

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-engine.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 184. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/unified-learning-engine.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 185. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/macro-prediction-service.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 186. 🟡 未使用统一测试数据生成器

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/macro-prediction-service.test.ts`
**建议**: 使用UnifiedTestDataGenerator统一管理测试数据生成

#### 187. 🟢 未使用统一测试断言库

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/macro-prediction-service.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 188. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/macro-prediction-service.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 189. 🟡 随机数生成未使用固定种子

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/macro-prediction-service.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 190. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/macro-prediction-service.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 191. 🟢 未使用统一测试断言库

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/learning-integration.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 192. 🟡 测试中存在硬编码的业务数据

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/learning-integration.test.ts`
**建议**: 使用测试数据工厂或fixture文件管理测试数据

#### 193. 🟡 缺少边界值和异常情况测试

**文件**: `src/contexts/ai-reasoning/infrastructure/services/__tests__/learning-integration.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 194. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/void.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 195. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/validations.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 196. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/unions.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 197. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/tuple.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 198. 🟡 未使用测试数据工厂模式

**文件**: `node_modules/zod/src/v3/tests/tuple.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 199. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/transformer.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 200. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/string.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 201. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/standard-schema.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 202. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v3/tests/standard-schema.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 203. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/set.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 204. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/safeparse.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 205. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/refine.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 206. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/recursive.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 207. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/record.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 208. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/readonly.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 209. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 210. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v3/tests/promise.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 211. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/primitive.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 212. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/preprocess.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 213. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v3/tests/preprocess.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 214. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/pipeline.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 215. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/pickomit.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 216. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/partials.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 217. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/parser.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 218. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/parseUtil.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 219. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/optional.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 220. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/object.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 221. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/object-in-es5-env.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 222. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/object-augmentation.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 223. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/number.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 224. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/nullable.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 225. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/nativeEnum.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 226. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/nan.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 227. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/map.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 228. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/literal.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 229. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/language-server.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 230. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/intersection.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 231. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/instanceof.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 232. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/generics.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 233. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/function.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 234. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v3/tests/function.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 235. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/error.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 236. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/enum.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 237. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 238. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v3/tests/discriminated-unions.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 239. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/description.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 240. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/default.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 241. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/deepmasking.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 242. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/date.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 243. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/custom.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 244. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/coerce.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 245. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/catch.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 246. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/bigint.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 247. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/base.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 248. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/async-refinements.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 249. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/async-parsing.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 250. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/array.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 251. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/anyunknown.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 252. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v3/tests/all-errors.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 253. 🟢 未使用统一测试断言库

**文件**: `src/shared/infrastructure/data-processing/strategies/sentiment/__tests__/sentiment-strategy.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 254. 🟡 缺少边界值和异常情况测试

**文件**: `src/shared/infrastructure/data-processing/strategies/sentiment/__tests__/sentiment-strategy.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 255. 🟢 未使用统一测试断言库

**文件**: `src/contexts/market-data/infrastructure/websocket/monitoring/__tests__/websocket-monitoring.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 256. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/mini/tests/string.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 257. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/mini/tests/recursive-types.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 258. 🟡 未使用测试数据工厂模式

**文件**: `node_modules/zod/src/v4/mini/tests/recursive-types.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 259. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/mini/tests/prototypes.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 260. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/mini/tests/object.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 261. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/mini/tests/number.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 262. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 263. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v4/mini/tests/index.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 264. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/mini/tests/functions.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 265. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/mini/tests/error.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 266. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/mini/tests/computed.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 267. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/mini/tests/checks.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 268. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/core/tests/index.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 269. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/void.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 270. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/validations.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 271. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/union.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 272. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/tuple.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 273. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/transform.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 274. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/to-json-schema.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 275. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v4/classic/tests/to-json-schema.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 276. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/template-literal.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 277. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v4/classic/tests/template-literal.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 278. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/stringbool.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 279. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/string.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 280. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/string-formats.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 281. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/standard-schema.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 282. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v4/classic/tests/standard-schema.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 283. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/set.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 284. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/registries.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 285. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/refine.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 286. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v4/classic/tests/refine.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 287. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/recursive-types.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 288. 🟡 未使用测试数据工厂模式

**文件**: `node_modules/zod/src/v4/classic/tests/recursive-types.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 289. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/record.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 290. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/readonly.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 291. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/prototypes.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 292. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 293. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v4/classic/tests/promise.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 294. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `node_modules/zod/src/v4/classic/tests/primitive.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 295. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/primitive.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 296. 🟡 随机数生成未使用固定种子

**文件**: `node_modules/zod/src/v4/classic/tests/primitive.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 297. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/preprocess.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 298. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v4/classic/tests/preprocess.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 299. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/prefault.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 300. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/pipe.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 301. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/pickomit.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 302. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/partial.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 303. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `node_modules/zod/src/v4/classic/tests/optional.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 304. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/optional.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 305. 🟡 随机数生成未使用固定种子

**文件**: `node_modules/zod/src/v4/classic/tests/optional.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 306. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/object.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 307. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/number.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 308. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/nullable.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 309. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/nonoptional.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 310. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/nested-refine.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 311. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v4/classic/tests/nested-refine.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 312. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/nan.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 313. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/map.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 314. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/literal.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 315. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/lazy.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 316. 🟡 未使用测试数据工厂模式

**文件**: `node_modules/zod/src/v4/classic/tests/lazy.test.ts`
**建议**: 使用工厂模式创建测试数据，提高测试数据的可维护性

#### 317. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/json.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 318. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/intersection.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 319. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/instanceof.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 320. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 321. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v4/classic/tests/index.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 322. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/generics.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 323. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/function.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 324. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/file.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 325. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/error.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 326. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/error-utils.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 327. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/enum.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 328. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 329. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 330. 🟡 随机数生成未使用固定种子

**文件**: `node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 331. 🟡 缺少边界值和异常情况测试

**文件**: `node_modules/zod/src/v4/classic/tests/discriminated-unions.test.ts`
**建议**: 添加边界值、空值、异常输入的测试用例

#### 332. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/description.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 333. 🔴 测试中使用Math.random()，可能导致测试结果不稳定

**文件**: `node_modules/zod/src/v4/classic/tests/default.test.ts`
**建议**: 使用固定种子的随机数生成器或UnifiedTestDataGenerator

#### 334. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/default.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 335. 🟡 随机数生成未使用固定种子

**文件**: `node_modules/zod/src/v4/classic/tests/default.test.ts`
**建议**: 使用固定种子确保测试结果的可重现性

#### 336. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/datetime.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 337. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/date.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 338. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/custom.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 339. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/continuability.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 340. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/coerce.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 341. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/coalesce.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 342. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/catch.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 343. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/bigint.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 344. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/base.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 345. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/async-refinements.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 346. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/async-parsing.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 347. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/array.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 348. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/classic/tests/anyunknown.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 349. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/core/tests/locales/tr.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 350. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/core/tests/locales/ru.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 351. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/core/tests/locales/en.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

#### 352. 🟢 未使用统一测试断言库

**文件**: `node_modules/zod/src/v4/core/tests/locales/be.test.ts`
**建议**: 考虑使用UnifiedTestAssertions提供更丰富的断言方法

## 改进建议

1. **提高测试覆盖率**: 为核心业务逻辑添加单元测试，目标覆盖率80%+
2. **统一测试策略**: 建立一致的测试文件组织和命名规范
3. **改善Mock质量**: 使用测试数据工厂，避免硬编码和随机数据
4. **完善E2E测试**: 为关键业务流程添加端到端测试
5. **推广统一测试工具库**: 使用UnifiedTestDataGenerator、UnifiedTestAssertions等统一工具
6. **使用中文测试描述**: 提高测试用例的业务可读性和表达力
7. **统一命名规范**: 在测试代码中统一使用驼峰命名风格
8. **固定种子机制**: 确保随机数生成的可重现性，避免测试不稳定
9. **建立测试标准**: 制定测试编写指南和代码审查清单
10. **自动化测试**: 集成测试覆盖率检查到CI/CD流程
