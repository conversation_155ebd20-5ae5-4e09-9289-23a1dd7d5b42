import 'reflect-metadata';
import { config } from 'dotenv';
import express from 'express';
import { getLogger } from './config/logging';
import { getEnvironment } from './config/environment';

// 加载环境变量
config();

/**
 * 极简版Express应用程序启动器
 * 只测试最基础的HTTP服务器启动
 */
class SimpleExpressApplication {
  private logger = getLogger();
  private app = express();

  async bootstrap(): Promise<void> {
    try {
      this.logger.info('🚀 开始简化版Express应用程序初始化...');

      // 设置基础中间件
      this.app.use(express.json());
      this.app.use(express.urlencoded({ extended: true }));

      // 添加健康检查端点
      this.app.get('/health', (req, res) => {
        res.json({
          status: 'OK',
          timestamp: new Date().toISOString(),
          message: '简化版服务器运行正常'
        });
      });

      // 添加根路径
      this.app.get('/', (req, res) => {
        res.json({
          message: '欢迎使用加密货币监控系统（简化版）',
          version: '1.0.0-simple',
          timestamp: new Date().toISOString()
        });
      });

      // 启动服务器
      const env = getEnvironment();
      const PORT = env.PORT || 3001;
      
      this.app.listen(PORT, () => {
        this.logger.info(`🚀 简化版服务器启动成功，端口: ${PORT}`);
        this.logger.info(`📡 健康检查: http://localhost:${PORT}/health`);
        this.logger.info(`🌐 主页: http://localhost:${PORT}/`);
      });

    } catch (error) {
      this.logger.error('❌ 简化版服务器启动失败:', error);
      process.exit(1);
    }
  }
}

// 启动应用程序
const app = new SimpleExpressApplication();
app.bootstrap().catch((error) => {
  const logger = getLogger();
  logger.error('❌ 应用程序启动失败:', error);
  process.exit(1);
}); 