import 'reflect-metadata';
import { config } from 'dotenv';
import { getLogger, ILogger } from './config/logging';
import { getEnvironment } from './config/environment';

// 加载环境变量
config();

/**
 * 调试版Express应用程序启动器
 */
class DebugExpressApplicationBootstrap {
  private logger: ILogger = getLogger();

  async bootstrap(): Promise<void> {
    try {
      this.logger.info('🚀 开始调试模式初始化...');

      // 步骤 1: 初始化日志系统
      await this.step1_initializeLogging();

      // 步骤 2: 初始化数据库
      await this.step2_initializeDatabase();

      // 步骤 3: 初始化Redis
      await this.step3_initializeRedis();

      // 步骤 4: 初始化事件总线
      await this.step4_initializeEventBus();

      // 步骤 5: 测试基础DI容器
      await this.step5_testBasicContainer();

      // 步骤 6: 测试模块化容器初始化
      await this.step6_testModularContainer();

      this.logger.info('✅ 调试模式初始化完成');
    } catch (error) {
      this.logger.error('❌ 调试模式初始化失败:', error);
      process.exit(1);
    }
  }

  private async step1_initializeLogging(): Promise<void> {
    this.logger.info('📝 步骤1: 初始化日志系统...');
    // 日志系统已经在getLogger()中初始化
    this.logger.info('✅ 步骤1: 日志系统初始化完成');
  }

  private async step2_initializeDatabase(): Promise<void> {
    this.logger.info('📊 步骤2: 初始化数据库连接...');
    try {
      const { setupDatabase } = await import('./shared/infrastructure/database/database');
      await setupDatabase();
      this.logger.info('✅ 步骤2: 数据库初始化完成');
    } catch (error) {
      this.logger.error('❌ 步骤2: 数据库初始化失败:', error);
      throw error;
    }
  }

  private async step3_initializeRedis(): Promise<void> {
    this.logger.info('🔴 步骤3: 初始化Redis连接...');
    try {
      const { setupRedis } = await import('./shared/infrastructure/messaging/redis');
      const env = getEnvironment();
      await setupRedis(env);
      this.logger.info('✅ 步骤3: Redis初始化完成');
    } catch (error) {
      this.logger.error('❌ 步骤3: Redis初始化失败:', error);
      throw error;
    }
  }

  private async step4_initializeEventBus(): Promise<void> {
    this.logger.info('📡 步骤4: 初始化事件总线...');
    try {
      const { setupEventBus } = await import('./shared/infrastructure/messaging/event-bus');
      await setupEventBus('redis');
      this.logger.info('✅ 步骤4: 事件总线初始化完成');
    } catch (error) {
      this.logger.error('❌ 步骤4: 事件总线初始化失败:', error);
      throw error;
    }
  }

  private async step5_testBasicContainer(): Promise<void> {
    this.logger.info('🏗️ 步骤5: 测试基础DI容器...');
    try {
      // 只测试最基础的容器创建
      const { Container } = await import('inversify');
      const testContainer = new Container();
      
      // 测试基础绑定
      const { TYPES } = await import('./shared/infrastructure/di/types/index');
      this.logger.info('TYPES加载成功');
      
      // 测试环境管理器
      if (TYPES.Shared?.UnifiedEnvironmentManager) {
        this.logger.info('✅ UnifiedEnvironmentManager类型定义正常');
      } else {
        this.logger.error('❌ UnifiedEnvironmentManager类型定义缺失');
      }

      this.logger.info('✅ 步骤5: 基础DI容器测试完成');
    } catch (error) {
      this.logger.error('❌ 步骤5: 基础DI容器测试失败:', error);
      throw error;
    }
  }

  private async step6_testModularContainer(): Promise<void> {
    this.logger.info('🏗️ 步骤6: 测试模块化容器初始化...');
    try {
      // 测试基础设施模块
      this.logger.info('6.1: 测试基础设施模块...');
      const { infrastructureContainerModule } = await import('./shared/infrastructure/di/modules/infrastructure-container-module');
      this.logger.info('✅ 6.1: 基础设施模块导入成功');

      // 测试共享基础设施模块  
      this.logger.info('6.2: 测试共享基础设施模块...');
      const { sharedInfrastructureModule } = await import('./shared/infrastructure/di/modules/infrastructure-container-module');
      this.logger.info('✅ 6.2: 共享基础设施模块导入成功');

      // 测试模块化容器管理器
      this.logger.info('6.3: 测试模块化容器管理器...');
      const { ModularContainerManager } = await import('./shared/infrastructure/di/modular-container-manager');
      const containerManager = ModularContainerManager.getInstance();
      this.logger.info('✅ 6.3: 模块化容器管理器创建成功');

      // 尝试初始化容器（这里可能是问题所在）
      this.logger.info('6.4: 开始容器初始化...');
      await containerManager.initialize();
      this.logger.info('✅ 6.4: 容器初始化完成');

      this.logger.info('✅ 步骤6: 模块化容器测试完成');
    } catch (error) {
      this.logger.error('❌ 步骤6: 模块化容器测试失败:', error);
      throw error;
    }
  }
}

// 启动调试应用程序
const bootstrap = new DebugExpressApplicationBootstrap();
bootstrap.bootstrap().catch((error) => {
  const logger = getLogger();
  logger.error('❌ 调试应用程序启动失败:', error);
  process.exit(1);
}); 