# Express启动文件示例

这个目录包含了不同版本的Express启动文件，用于不同的开发和调试场景。

## 文件说明

### express-main-simple.ts
**用途**: 极简版Express应用程序启动器
**特点**: 
- 只测试最基础的HTTP服务器启动
- 不包含复杂的依赖注入和基础设施
- 适合快速验证Express基础功能

**使用场景**:
- 快速测试HTTP服务器是否能正常启动
- 调试基础路由和中间件
- 开发环境的快速原型验证

### express-main-minimal.ts
**用途**: 最小化Express应用启动器
**特点**:
- 包含核心基础设施（数据库、Redis、DI容器）
- 专注于核心功能验证
- 比完整版本更轻量，但比simple版本更完整

**使用场景**:
- 验证核心基础设施集成
- 调试数据库和Redis连接问题
- 测试依赖注入容器配置

### express-main-debug.ts
**用途**: 调试版Express应用程序启动器
**特点**:
- 分步骤初始化，便于调试
- 详细的日志输出
- 每个步骤都有独立的错误处理

**使用场景**:
- 调试应用启动过程中的问题
- 定位具体哪个初始化步骤失败
- 开发环境的问题排查

## 主要启动文件

项目的主要启动文件是 `src/express-main.ts`，它包含了完整的生产级功能：
- 完整的依赖注入容器初始化
- 所有基础设施组件的设置
- 生产级的错误处理和监控
- WebSocket支持
- API路由和中间件

## 使用方法

```bash
# 运行主要应用
npm run start

# 运行简化版本（用于调试）
npx ts-node examples/express-variants/express-main-simple.ts

# 运行最小版本（用于核心功能测试）
npx ts-node examples/express-variants/express-main-minimal.ts

# 运行调试版本（用于问题排查）
npx ts-node examples/express-variants/express-main-debug.ts
```

## 注意事项

1. 这些示例文件仅用于开发和调试目的
2. 生产环境请使用 `src/express-main.ts`
3. 如果修改了主要的基础设施组件，可能需要同步更新这些示例文件
4. 这些文件不包含在生产构建中
