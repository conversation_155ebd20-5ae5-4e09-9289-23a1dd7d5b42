import 'reflect-metadata';
import { config } from 'dotenv';
import express from 'express';
import { createServer } from 'http';
import { setupDatabase } from './shared/infrastructure/database/database';
import { setupRedis } from './shared/infrastructure/messaging/redis';
import { getContainer } from './shared/infrastructure/di/container';
import { getEnvironment } from './config/environment';
import { getLogger } from './config/logging';

// 加载环境变量
config();

/**
 * 最小化Express应用启动器 - 专注于核心功能验证
 */
class MinimalExpressApplication {
  private logger = getLogger();
  private app = express();
  private container: any = null;

  async bootstrap(): Promise<void> {
    try {
      this.logger.info('🚀 启动最小化Express应用...');

      // 1. 基础设置
      await this.setupBasics();

      // 2. 初始化核心基础设施
      await this.initializeCore();

      // 3. 设置基础路由
      this.setupBasicRoutes();

      // 4. 启动服务器
      await this.startServer();

      this.logger.info('✅ 最小化应用启动成功');
    } catch (error) {
      this.logger.error('❌ 最小化应用启动失败:', error);
      process.exit(1);
    }
  }

  private async setupBasics(): Promise<void> {
    // 基础中间件
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // CORS
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // 响应格式化
    this.app.use((req: any, res: any, next) => {
      req.id = Math.random().toString(36).substring(2, 15);
      
      res.success = (data: any, message = 'Success') => {
        res.json({
          success: true,
          message,
          data,
          requestId: req.id,
          timestamp: new Date().toISOString()
        });
      };

      res.error = (message: string, statusCode = 500, details?: any) => {
        res.status(statusCode).json({
          success: false,
          error: message,
          details,
          requestId: req.id,
          timestamp: new Date().toISOString()
        });
      };

      next();
    });

    this.logger.info('✅ 基础中间件设置完成');
  }

  private async initializeCore(): Promise<void> {
    try {
      // 初始化数据库
      this.logger.info('📊 初始化数据库...');
      await setupDatabase();
      this.logger.info('✅ 数据库初始化完成');

      // 初始化Redis
      this.logger.info('🔴 初始化Redis...');
      const env = getEnvironment();
      await setupRedis(env);
      this.logger.info('✅ Redis初始化完成');

      // 初始化DI容器
      this.logger.info('🏗️ 初始化DI容器...');
      this.container = getContainer();
      this.logger.info('✅ DI容器初始化完成');

    } catch (error) {
      this.logger.error('❌ 核心基础设施初始化失败:', error);
      throw error;
    }
  }

  private setupBasicRoutes(): void {
    // 健康检查
    this.app.get('/health', (req: any, res: any) => {
      res.success({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '1.0.0-minimal'
      }, '系统健康状态正常');
    });

    // 根路径
    this.app.get('/', (req: any, res: any) => {
      res.success({
        message: 'Crypto Monitor API - Minimal Version',
        version: '1.0.0-minimal',
        status: 'running',
        endpoints: {
          health: '/health',
          test: '/test'
        }
      }, '最小化API服务正常运行');
    });

    // 测试路由
    this.app.get('/test', (req: any, res: any) => {
      res.success({
        database: 'connected',
        redis: 'connected',
        container: this.container ? 'initialized' : 'not_initialized',
        timestamp: new Date().toISOString()
      }, '核心组件测试通过');
    });

    // 404处理
    this.app.use((req: any, res: any) => {
      res.error(`Route not found: ${req.method} ${req.url}`, 404, {
        availableEndpoints: ['GET /', 'GET /health', 'GET /test']
      });
    });

    // 错误处理
    this.app.use((error: any, req: any, res: any, next: any) => {
      this.logger.error('Express错误:', {
        requestId: req.id,
        error: error.message,
        url: req.url,
        method: req.method
      });

      res.error(error.message || 'Internal Server Error', error.statusCode || 500);
    });

    this.logger.info('✅ 基础路由设置完成');
  }

  private async startServer(): Promise<void> {
    const env = getEnvironment();
    const PORT = env.PORT || 3001;

    const httpServer = createServer(this.app);

    return new Promise<void>((resolve, reject) => {
      httpServer.listen(PORT, (error?: Error) => {
        if (error) {
          this.logger.error('❌ 服务器启动失败', { error: error.message, port: PORT });
          reject(error);
        } else {
          this.logger.info(`🚀 最小化服务器启动成功，端口: ${PORT}`);
          this.logger.info(`📡 健康检查: http://localhost:${PORT}/health`);
          this.logger.info(`🧪 功能测试: http://localhost:${PORT}/test`);
          resolve();
        }
      });
    });
  }
}

// 启动应用程序
const app = new MinimalExpressApplication();
app.bootstrap().catch((error) => {
  const logger = getLogger();
  logger.error('❌ 最小化应用启动失败:', error);
  process.exit(1);
});
