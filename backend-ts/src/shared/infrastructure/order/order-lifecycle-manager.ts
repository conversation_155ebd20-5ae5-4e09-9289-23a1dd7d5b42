/**
 * 订单生命周期管理器
 * 统一管理订单从创建到完成的整个生命周期，提供状态跟踪、同步和监控功能
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../di/types';
import { IBasicLogger } from '../logging/interfaces/basic-logger.interface';
import { UnifiedErrorHandler } from '../error/unified-error-handler';
import { EventEmitter } from 'events';
import { SecureIdGenerator } from '../utils/secure-id-generator';

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  CREATED = 'CREATED',                     // 订单已创建
  PENDING = 'PENDING',                     // 等待执行
  SUBMITTED = 'SUBMITTED',                 // 已提交到交易所
  PARTIALLY_FILLED = 'PARTIALLY_FILLED',   // 部分成交
  FILLED = 'FILLED',                       // 完全成交
  CANCELLED = 'CANCELLED',                 // 已取消
  REJECTED = 'REJECTED',                   // 被拒绝
  EXPIRED = 'EXPIRED',                     // 已过期
  FAILED = 'FAILED',                       // 执行失败
  UNKNOWN = 'UNKNOWN'                      // 状态未知
}

/**
 * 订单类型枚举
 */
export enum OrderType {
  MARKET = 'MARKET',
  LIMIT = 'LIMIT',
  STOP = 'STOP',
  STOP_LIMIT = 'STOP_LIMIT',
  TAKE_PROFIT = 'TAKE_PROFIT',
  TAKE_PROFIT_LIMIT = 'TAKE_PROFIT_LIMIT'
}

/**
 * 订单方向枚举
 */
export enum OrderSide {
  BUY = 'BUY',
  SELL = 'SELL'
}

/**
 * 订单信息
 */
export interface OrderInfo {
  id: string;                              // 系统订单ID
  exchangeOrderId?: string;                // 交易所订单ID
  clientOrderId?: string;                  // 客户端订单ID
  accountId: string;                       // 账户ID
  symbol: string;                          // 交易对
  type: OrderType;                         // 订单类型
  side: OrderSide;                         // 订单方向
  quantity: number;                        // 订单数量
  price?: number;                          // 订单价格
  stopPrice?: number;                      // 止损价格
  status: OrderStatus;                     // 订单状态
  filledQuantity: number;                  // 已成交数量
  averagePrice?: number;                   // 平均成交价格
  commission: number;                      // 手续费
  createdAt: Date;                         // 创建时间
  updatedAt: Date;                         // 更新时间
  executedAt?: Date;                       // 执行时间
  metadata?: any;                          // 元数据
}

/**
 * 订单状态变更事件
 */
export interface OrderStatusChangeEvent {
  orderId: string;
  oldStatus: OrderStatus;
  newStatus: OrderStatus;
  timestamp: Date;
  reason?: string;
  metadata?: any;
}

/**
 * 订单执行事件
 */
export interface OrderExecutionEvent {
  orderId: string;
  eventType: 'CREATED' | 'SUBMITTED' | 'FILLED' | 'CANCELLED' | 'FAILED';
  quantity?: number;
  price?: number;
  commission?: number;
  timestamp: Date;
  metadata?: any;
}

/**
 * 订单同步配置
 */
export interface OrderSyncConfig {
  enableRealTimeSync: boolean;             // 启用实时同步
  syncInterval: number;                    // 同步间隔（毫秒）
  maxRetries: number;                      // 最大重试次数
  retryDelay: number;                      // 重试延迟（毫秒）
  enableStatusValidation: boolean;         // 启用状态验证
  enableOrderReconciliation: boolean;      // 启用订单对账
}

/**
 * 订单生命周期管理器
 */
@injectable()
export class OrderLifecycleManager extends EventEmitter {
  private readonly config: OrderSyncConfig;
  private readonly orders: Map<string, OrderInfo> = new Map();
  private readonly statusHistory: Map<string, OrderStatusChangeEvent[]> = new Map();
  private syncTimer: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler
  ) {
    super();
    
    this.config = {
      enableRealTimeSync: true,
      syncInterval: 5000,                  // 5秒同步一次
      maxRetries: 3,
      retryDelay: 1000,                    // 1秒重试延迟
      enableStatusValidation: true,
      enableOrderReconciliation: true
    };

    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    this.on('orderStatusChanged', (event: OrderStatusChangeEvent) => {
      this.handleOrderStatusChange(event);
    });

    this.on('orderExecuted', (event: OrderExecutionEvent) => {
      this.handleOrderExecution(event);
    });

    this.on('orderFailed', (event: { orderId: string; error: string; timestamp: Date }) => {
      this.handleOrderFailure(event);
    });
  }

  /**
   * 启动订单生命周期管理
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('订单生命周期管理器已在运行');
      return;
    }

    this.logger.info('启动订单生命周期管理器', {
      syncInterval: this.config.syncInterval,
      enableRealTimeSync: this.config.enableRealTimeSync
    });

    this.isRunning = true;

    if (this.config.enableRealTimeSync) {
      this.startPeriodicSync();
    }

    this.emit('managerStarted');
  }

  /**
   * 停止订单生命周期管理
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('停止订单生命周期管理器');

    this.isRunning = false;
    this.stopPeriodicSync();

    this.emit('managerStopped');
  }

  /**
   * 创建新订单
   */
  async createOrder(orderInfo: Omit<OrderInfo, 'id' | 'status' | 'filledQuantity' | 'commission' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const orderId = this.generateOrderId();
    
    const order: OrderInfo = {
      ...orderInfo,
      id: orderId,
      status: OrderStatus.CREATED,
      filledQuantity: 0,
      commission: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.orders.set(orderId, order);
    this.statusHistory.set(orderId, []);

    this.logger.info('创建新订单', {
      orderId,
      symbol: order.symbol,
      type: order.type,
      side: order.side,
      quantity: order.quantity
    });

    // 记录状态变更
    await this.recordStatusChange(orderId, OrderStatus.CREATED, OrderStatus.CREATED, '订单创建');

    this.emit('orderExecuted', {
      orderId,
      eventType: 'CREATED',
      timestamp: new Date()
    });

    return orderId;
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(
    orderId: string, 
    newStatus: OrderStatus, 
    updateData?: {
      filledQuantity?: number;
      averagePrice?: number;
      commission?: number;
      exchangeOrderId?: string;
      executedAt?: Date;
      metadata?: any;
    },
    reason?: string
  ): Promise<boolean> {
    const order = this.orders.get(orderId);
    if (!order) {
      this.logger.error('订单不存在', { orderId });
      return false;
    }

    const oldStatus = order.status;

    // 验证状态转换的合法性
    if (this.config.enableStatusValidation && !this.isValidStatusTransition(oldStatus, newStatus)) {
      this.logger.error('无效的状态转换', {
        orderId,
        oldStatus,
        newStatus
      });
      return false;
    }

    // 更新订单信息
    order.status = newStatus;
    order.updatedAt = new Date();

    if (updateData) {
      if (updateData.filledQuantity !== undefined) {
        order.filledQuantity = updateData.filledQuantity;
      }
      if (updateData.averagePrice !== undefined) {
        order.averagePrice = updateData.averagePrice;
      }
      if (updateData.commission !== undefined) {
        order.commission = updateData.commission;
      }
      if (updateData.exchangeOrderId !== undefined) {
        order.exchangeOrderId = updateData.exchangeOrderId;
      }
      if (updateData.executedAt !== undefined) {
        order.executedAt = updateData.executedAt;
      }
      if (updateData.metadata !== undefined) {
        order.metadata = { ...order.metadata, ...updateData.metadata };
      }
    }

    this.orders.set(orderId, order);

    // 记录状态变更
    await this.recordStatusChange(orderId, oldStatus, newStatus, reason);

    this.logger.info('订单状态更新', {
      orderId,
      oldStatus,
      newStatus,
      filledQuantity: order.filledQuantity,
      averagePrice: order.averagePrice
    });

    // 发送状态变更事件
    this.emit('orderStatusChanged', {
      orderId,
      oldStatus,
      newStatus,
      timestamp: new Date(),
      reason,
      metadata: updateData?.metadata
    });

    // 发送执行事件
    if (newStatus === OrderStatus.FILLED) {
      this.emit('orderExecuted', {
        orderId,
        eventType: 'FILLED',
        quantity: order.filledQuantity,
        price: order.averagePrice,
        commission: order.commission,
        timestamp: new Date()
      });
    } else if (newStatus === OrderStatus.CANCELLED) {
      this.emit('orderExecuted', {
        orderId,
        eventType: 'CANCELLED',
        timestamp: new Date()
      });
    } else if (newStatus === OrderStatus.FAILED) {
      this.emit('orderFailed', {
        orderId,
        error: reason || '订单执行失败',
        timestamp: new Date()
      });
    }

    return true;
  }

  /**
   * 获取订单信息
   */
  getOrder(orderId: string): OrderInfo | null {
    return this.orders.get(orderId) || null;
  }

  /**
   * 获取订单状态历史
   */
  getOrderStatusHistory(orderId: string): OrderStatusChangeEvent[] {
    return this.statusHistory.get(orderId) || [];
  }

  /**
   * 获取账户的所有订单
   */
  getAccountOrders(accountId: string, status?: OrderStatus): OrderInfo[] {
    const orders = Array.from(this.orders.values()).filter(order => order.accountId === accountId);
    
    if (status) {
      return orders.filter(order => order.status === status);
    }
    
    return orders;
  }

  /**
   * 获取活跃订单（未完成的订单）
   */
  getActiveOrders(accountId?: string): OrderInfo[] {
    const activeStatuses = [
      OrderStatus.CREATED,
      OrderStatus.PENDING,
      OrderStatus.SUBMITTED,
      OrderStatus.PARTIALLY_FILLED
    ];

    let orders = Array.from(this.orders.values()).filter(order => 
      activeStatuses.includes(order.status)
    );

    if (accountId) {
      orders = orders.filter(order => order.accountId === accountId);
    }

    return orders;
  }

  /**
   * 批量更新订单状态
   */
  async batchUpdateOrderStatus(updates: Array<{
    orderId: string;
    status: OrderStatus;
    updateData?: any;
    reason?: string;
  }>): Promise<{ success: number; failed: number; errors: string[] }> {
    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    for (const update of updates) {
      try {
        const result = await this.updateOrderStatus(
          update.orderId,
          update.status,
          update.updateData,
          update.reason
        );

        if (result) {
          success++;
        } else {
          failed++;
          errors.push(`订单 ${update.orderId} 状态更新失败`);
        }
      } catch (error) {
        failed++;
        errors.push(`订单 ${update.orderId} 状态更新异常: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    this.logger.info('批量订单状态更新完成', {
      total: updates.length,
      success,
      failed
    });

    return { success, failed, errors };
  }

  /**
   * 启动定期同步
   */
  private startPeriodicSync(): void {
    this.syncTimer = setInterval(() => {
      this.performOrderSync();
    }, this.config.syncInterval);

    this.logger.info('启动订单定期同步', {
      interval: this.config.syncInterval
    });
  }

  /**
   * 停止定期同步
   */
  private stopPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
      this.logger.info('停止订单定期同步');
    }
  }

  /**
   * 执行订单同步
   */
  private async performOrderSync(): Promise<void> {
    try {
      const activeOrders = this.getActiveOrders();
      
      this.logger.debug('执行订单同步', {
        activeOrdersCount: activeOrders.length
      });

      // 实现与交易所的订单状态同步逻辑
      for (const order of activeOrders) {
        try {
          await this.syncOrderWithExchange(order);
        } catch (syncError) {
          this.logger.error('单个订单同步失败', {
            orderId: order.id,
            error: syncError instanceof Error ? syncError.message : String(syncError)
          });
        }
      }

    } catch (error) {
      this.logger.error('订单同步失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 与交易所同步单个订单状态
   * @param order 需要同步的订单
   */
  private async syncOrderWithExchange(order: any): Promise<void> {
    this.logger.debug('同步订单状态', {
      orderId: order.id,
      currentStatus: order.status,
      exchange: order.exchange
    });
    
    try {
      // 根据交易所获取最新订单状态
      // 这里应该调用具体交易所的API
      const exchangeStatus = await this.getExchangeOrderStatus(order);
      
      // 如果交易所状态与本地状态不同，更新本地状态
      if (exchangeStatus && exchangeStatus !== order.status) {
        await this.updateOrderStatus(order.id, exchangeStatus, {
          reason: 'exchange_sync',
          details: '通过交易所同步更新'
        });
      }
    } catch (error) {
      this.logger.error('从交易所获取订单状态失败', {
        orderId: order.id,
        exchange: order.exchange,
        error: error instanceof Error ? error.message : String(error)
      });
      // 不抛出异常，让同步过程继续进行
    }
  }

  /**
   * 从交易所获取订单状态
   * @param order 订单信息
   * @returns 交易所返回的订单状态
   */
  private async getExchangeOrderStatus(order: any): Promise<OrderStatus | null> {
    // 这里应该实现与具体交易所API的集成
    // 目前返回模拟状态用于演示
    return Promise.resolve(order.status);
  }

  /**
   * 验证状态转换的合法性
   */
  private isValidStatusTransition(oldStatus: OrderStatus, newStatus: OrderStatus): boolean {
    // 如果状态相同，允许转换（用于更新其他字段）
    if (oldStatus === newStatus) {
      return true;
    }

    const validTransitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.CREATED]: [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.FAILED, OrderStatus.CANCELLED],
      [OrderStatus.PENDING]: [OrderStatus.SUBMITTED, OrderStatus.CANCELLED, OrderStatus.FAILED],
      [OrderStatus.SUBMITTED]: [OrderStatus.PARTIALLY_FILLED, OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXPIRED],
      [OrderStatus.PARTIALLY_FILLED]: [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.SUBMITTED], // 允许回到SUBMITTED状态
      [OrderStatus.FILLED]: [OrderStatus.CANCELLED], // 允许取消已成交订单（用于错误修正）
      [OrderStatus.CANCELLED]: [OrderStatus.SUBMITTED, OrderStatus.PENDING], // 允许重新激活取消的订单
      [OrderStatus.REJECTED]: [OrderStatus.PENDING, OrderStatus.SUBMITTED], // 允许重新提交被拒绝的订单
      [OrderStatus.EXPIRED]: [OrderStatus.PENDING, OrderStatus.SUBMITTED], // 允许重新激活过期订单
      [OrderStatus.FAILED]: [OrderStatus.PENDING, OrderStatus.SUBMITTED], // 允许重试失败的订单
      [OrderStatus.UNKNOWN]: [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.FAILED]
    };

    return validTransitions[oldStatus]?.includes(newStatus) || false;
  }

  /**
   * 记录状态变更
   */
  private async recordStatusChange(
    orderId: string,
    oldStatus: OrderStatus,
    newStatus: OrderStatus,
    reason?: string
  ): Promise<void> {
    const history = this.statusHistory.get(orderId) || [];
    
    const changeEvent: OrderStatusChangeEvent = {
      orderId,
      oldStatus,
      newStatus,
      timestamp: new Date(),
      reason
    };

    history.push(changeEvent);
    this.statusHistory.set(orderId, history);

    // 限制历史记录大小
    if (history.length > 100) {
      history.shift();
    }
  }

  /**
   * 生成订单ID
   */
  private generateOrderId(): string {
    return SecureIdGenerator.generatePrefixedId('order', 8);
  }

  /**
   * 处理订单状态变更
   */
  private async handleOrderStatusChange(event: OrderStatusChangeEvent): Promise<void> {
    this.logger.debug('处理订单状态变更', event);
    
    // 这里可以添加状态变更的业务逻辑
    // 例如：通知相关服务、更新数据库、发送警报等
  }

  /**
   * 处理订单执行
   */
  private async handleOrderExecution(event: OrderExecutionEvent): Promise<void> {
    this.logger.debug('处理订单执行事件', event);
    
    // 这里可以添加订单执行的业务逻辑
    // 例如：更新仓位、计算盈亏、发送通知等
  }

  /**
   * 处理订单失败
   */
  private async handleOrderFailure(event: { orderId: string; error: string; timestamp: Date }): Promise<void> {
    this.logger.warn('处理订单失败', event);
    
    // 这里可以添加订单失败的处理逻辑
    // 例如：重试机制、错误报告、风险控制等
  }

  /**
   * 取消订单
   */
  async cancelOrder(orderId: string, reason?: string): Promise<boolean> {
    const order = this.orders.get(orderId);
    if (!order) {
      this.logger.error('尝试取消不存在的订单', { orderId });
      return false;
    }

    // 检查订单是否可以取消
    const cancellableStatuses = [
      OrderStatus.CREATED,
      OrderStatus.PENDING,
      OrderStatus.SUBMITTED,
      OrderStatus.PARTIALLY_FILLED
    ];

    if (!cancellableStatuses.includes(order.status)) {
      this.logger.warn('订单状态不允许取消', {
        orderId,
        currentStatus: order.status
      });
      return false;
    }

    return await this.updateOrderStatus(
      orderId,
      OrderStatus.CANCELLED,
      { executedAt: new Date() },
      reason || '用户取消'
    );
  }

  /**
   * 获取订单统计信息
   */
  getOrderStatistics(accountId?: string): {
    total: number;
    byStatus: Record<string, number>;
    byType: Record<string, number>;
    totalVolume: number;
    totalCommission: number;
  } {
    let orders = Array.from(this.orders.values());

    if (accountId) {
      orders = orders.filter(order => order.accountId === accountId);
    }

    const stats = {
      total: orders.length,
      byStatus: {} as Record<string, number>,
      byType: {} as Record<string, number>,
      totalVolume: 0,
      totalCommission: 0
    };

    orders.forEach(order => {
      // 按状态统计
      stats.byStatus[order.status] = (stats.byStatus[order.status] || 0) + 1;

      // 按类型统计
      stats.byType[order.type] = (stats.byType[order.type] || 0) + 1;

      // 计算总交易量和手续费
      if (order.averagePrice && order.filledQuantity > 0) {
        stats.totalVolume += order.averagePrice * order.filledQuantity;
      }
      stats.totalCommission += order.commission;
    });

    return stats;
  }

  /**
   * 清理已完成的订单
   */
  async cleanupCompletedOrders(olderThanHours: number = 24): Promise<number> {
    const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
    const completedStatuses = [
      OrderStatus.FILLED,
      OrderStatus.CANCELLED,
      OrderStatus.REJECTED,
      OrderStatus.EXPIRED,
      OrderStatus.FAILED
    ];

    let cleanedCount = 0;

    for (const [orderId, order] of this.orders) {
      if (completedStatuses.includes(order.status) && order.updatedAt < cutoffTime) {
        this.orders.delete(orderId);
        this.statusHistory.delete(orderId);
        cleanedCount++;
      }
    }

    this.logger.info('清理已完成订单', {
      cleanedCount,
      cutoffTime,
      remainingOrders: this.orders.size
    });

    return cleanedCount;
  }

  /**
   * 导出订单数据
   */
  exportOrderData(accountId?: string, format: 'json' | 'csv' = 'json'): string {
    let orders = Array.from(this.orders.values());

    if (accountId) {
      orders = orders.filter(order => order.accountId === accountId);
    }

    if (format === 'json') {
      return JSON.stringify({
        exportTime: new Date(),
        totalOrders: orders.length,
        orders: orders.map(order => ({
          ...order,
          statusHistory: this.statusHistory.get(order.id) || []
        }))
      }, null, 2);
    } else {
      // CSV格式
      const headers = [
        'orderId', 'exchangeOrderId', 'accountId', 'symbol', 'type', 'side',
        'quantity', 'price', 'status', 'filledQuantity', 'averagePrice',
        'commission', 'createdAt', 'updatedAt', 'executedAt'
      ];

      const rows = orders.map(order => [
        order.id,
        order.exchangeOrderId || '',
        order.accountId,
        order.symbol,
        order.type,
        order.side,
        order.quantity,
        order.price || '',
        order.status,
        order.filledQuantity,
        order.averagePrice || '',
        order.commission,
        order.createdAt.toISOString(),
        order.updatedAt.toISOString(),
        order.executedAt?.toISOString() || ''
      ]);

      return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    }
  }

  /**
   * 获取订单执行性能指标
   */
  getExecutionMetrics(accountId?: string): {
    averageExecutionTime: number;
    fillRate: number;
    cancellationRate: number;
    failureRate: number;
    totalOrders: number;
  } {
    let orders = Array.from(this.orders.values());

    if (accountId) {
      orders = orders.filter(order => order.accountId === accountId);
    }

    const totalOrders = orders.length;
    if (totalOrders === 0) {
      return {
        averageExecutionTime: 0,
        fillRate: 0,
        cancellationRate: 0,
        failureRate: 0,
        totalOrders: 0
      };
    }

    let totalExecutionTime = 0;
    let executedOrdersCount = 0;
    let filledOrders = 0;
    let cancelledOrders = 0;
    let failedOrders = 0;

    orders.forEach(order => {
      if (order.executedAt) {
        totalExecutionTime += order.executedAt.getTime() - order.createdAt.getTime();
        executedOrdersCount++;
      }

      switch (order.status) {
        case OrderStatus.FILLED:
          filledOrders++;
          break;
        case OrderStatus.CANCELLED:
          cancelledOrders++;
          break;
        case OrderStatus.FAILED:
        case OrderStatus.REJECTED:
          failedOrders++;
          break;
      }
    });

    return {
      averageExecutionTime: executedOrdersCount > 0 ? totalExecutionTime / executedOrdersCount : 0,
      fillRate: filledOrders / totalOrders,
      cancellationRate: cancelledOrders / totalOrders,
      failureRate: failedOrders / totalOrders,
      totalOrders
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<OrderSyncConfig>): void {
    Object.assign(this.config, newConfig);

    this.logger.info('订单生命周期管理器配置已更新', { newConfig });

    // 如果同步间隔改变，重启定期同步
    if (newConfig.syncInterval && this.isRunning && this.config.enableRealTimeSync) {
      this.stopPeriodicSync();
      this.startPeriodicSync();
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): OrderSyncConfig {
    return { ...this.config };
  }

  /**
   * 获取管理器状态
   */
  getManagerStatus(): {
    isRunning: boolean;
    totalOrders: number;
    activeOrders: number;
    config: OrderSyncConfig;
    uptime: number;
  } {
    const activeOrders = this.getActiveOrders();

    return {
      isRunning: this.isRunning,
      totalOrders: this.orders.size,
      activeOrders: activeOrders.length,
      config: this.config,
      uptime: this.isRunning ? Date.now() - (this.startTime || Date.now()) : 0
    };
  }

  private startTime?: number;

  /**
   * 重写start方法以记录启动时间
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('订单生命周期管理器已在运行');
      return;
    }

    this.startTime = Date.now();

    this.logger.info('启动订单生命周期管理器', {
      syncInterval: this.config.syncInterval,
      enableRealTimeSync: this.config.enableRealTimeSync
    });

    this.isRunning = true;

    if (this.config.enableRealTimeSync) {
      this.startPeriodicSync();
    }

    this.emit('managerStarted');
  }

  /**
   * 销毁管理器
   */
  async destroy(): Promise<void> {
    await this.stop();
    this.orders.clear();
    this.statusHistory.clear();
    this.removeAllListeners();

    this.logger.info('订单生命周期管理器已销毁');
  }
}
