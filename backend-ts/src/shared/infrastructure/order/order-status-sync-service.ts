/**
 * 订单状态同步服务
 * 负责与交易所同步订单状态，确保系统中的订单状态与交易所保持一致
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../di/types';
import { IBasicLogger } from '../logging/interfaces/basic-logger.interface';
import { OrderLifecycleManager, OrderStatus, OrderInfo } from './order-lifecycle-manager';
import { EventEmitter } from 'events';

/**
 * 同步结果
 */
export interface SyncResult {
  success: boolean;
  syncedOrders: number;
  failedOrders: number;
  errors: string[];
  timestamp: Date;
  duration: number;
}

/**
 * 订单差异
 */
export interface OrderDiscrepancy {
  orderId: string;
  systemStatus: OrderStatus;
  exchangeStatus: OrderStatus;
  systemData: Partial<OrderInfo>;
  exchangeData: any;
  discrepancyType: 'STATUS_MISMATCH' | 'QUANTITY_MISMATCH' | 'PRICE_MISMATCH' | 'MISSING_ORDER';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

/**
 * 同步配置
 */
export interface SyncConfig {
  enableAutoSync: boolean;
  syncInterval: number;
  batchSize: number;
  maxRetries: number;
  retryDelay: number;
  enableDiscrepancyDetection: boolean;
  enableAutoReconciliation: boolean;
  reconciliationThreshold: number;
}

/**
 * 交易所接口（抽象）
 */
export interface ExchangeInterface {
  getOrderStatus(orderId: string): Promise<any>;
  getOrderHistory(symbol?: string, limit?: number): Promise<any[]>;
  getActiveOrders(symbol?: string): Promise<any[]>;
}

/**
 * 订单状态同步服务
 */
@injectable()
export class OrderStatusSyncService extends EventEmitter {
  private readonly config: SyncConfig;
  private syncTimer: NodeJS.Timeout | null = null;
  private isRunning = false;
  private lastSyncTime: Date | null = null;
  private syncCount = 0;

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.OrderLifecycleManager) private readonly orderManager: OrderLifecycleManager
  ) {
    super();
    
    this.config = {
      enableAutoSync: true,
      syncInterval: 10000,                 // 10秒同步一次
      batchSize: 50,                       // 每批处理50个订单
      maxRetries: 3,
      retryDelay: 2000,                    // 2秒重试延迟
      enableDiscrepancyDetection: true,
      enableAutoReconciliation: true,
      reconciliationThreshold: 0.01        // 1%的差异阈值
    };

    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    this.on('syncCompleted', (result: SyncResult) => {
      this.handleSyncCompleted(result);
    });

    this.on('discrepancyDetected', (discrepancy: OrderDiscrepancy) => {
      this.handleDiscrepancyDetected(discrepancy);
    });

    this.on('reconciliationCompleted', (result: { orderId: string; success: boolean; action: string }) => {
      this.handleReconciliationCompleted(result);
    });
  }

  /**
   * 启动同步服务
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('订单状态同步服务已在运行');
      return;
    }

    this.logger.info('启动订单状态同步服务', {
      syncInterval: this.config.syncInterval,
      batchSize: this.config.batchSize
    });

    this.isRunning = true;

    if (this.config.enableAutoSync) {
      this.startAutoSync();
    }

    this.emit('serviceStarted');
  }

  /**
   * 停止同步服务
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('停止订单状态同步服务');

    this.isRunning = false;
    this.stopAutoSync();

    this.emit('serviceStopped');
  }

  /**
   * 手动同步所有活跃订单
   */
  async syncActiveOrders(exchangeInterface: ExchangeInterface): Promise<SyncResult> {
    const startTime = Date.now();
    const result: SyncResult = {
      success: true,
      syncedOrders: 0,
      failedOrders: 0,
      errors: [],
      timestamp: new Date(),
      duration: 0
    };

    try {
      this.logger.info('开始同步活跃订单');

      const activeOrders = this.orderManager.getActiveOrders();
      this.logger.debug('获取活跃订单', { count: activeOrders.length });

      // 分批处理订单
      const batches = this.chunkArray(activeOrders, this.config.batchSize);

      for (const batch of batches) {
        const batchResult = await this.syncOrderBatch(batch, exchangeInterface);
        result.syncedOrders += batchResult.syncedOrders;
        result.failedOrders += batchResult.failedOrders;
        result.errors.push(...batchResult.errors);
      }

      result.duration = Date.now() - startTime;
      this.lastSyncTime = new Date();
      this.syncCount++;

      this.logger.info('活跃订单同步完成', {
        syncedOrders: result.syncedOrders,
        failedOrders: result.failedOrders,
        duration: result.duration
      });

      this.emit('syncCompleted', result);
      return result;

    } catch (error) {
      result.success = false;
      result.duration = Date.now() - startTime;
      result.errors.push(error instanceof Error ? error.message : String(error));

      this.logger.error('订单同步失败', {
        error: error instanceof Error ? error.message : String(error),
        duration: result.duration
      });

      return result;
    }
  }

  /**
   * 同步单个订单
   */
  async syncSingleOrder(orderId: string, exchangeInterface: ExchangeInterface): Promise<boolean> {
    try {
      const order = this.orderManager.getOrder(orderId);
      if (!order) {
        this.logger.error('订单不存在', { orderId });
        return false;
      }

      // 从交易所获取订单状态
      const exchangeOrderData = await this.getExchangeOrderData(order, exchangeInterface);
      if (!exchangeOrderData) {
        this.logger.warn('无法从交易所获取订单数据', { orderId });
        return false;
      }

      // 检测差异
      const discrepancy = this.detectDiscrepancy(order, exchangeOrderData);
      if (discrepancy) {
        this.emit('discrepancyDetected', discrepancy);

        // 自动对账
        if (this.config.enableAutoReconciliation) {
          await this.reconcileOrder(discrepancy, exchangeOrderData);
        }
      }

      return true;

    } catch (error) {
      this.logger.error('同步单个订单失败', {
        orderId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 检测订单差异
   */
  private detectDiscrepancy(systemOrder: OrderInfo, exchangeData: any): OrderDiscrepancy | null {
    if (!this.config.enableDiscrepancyDetection) {
      return null;
    }

    const exchangeStatus = this.mapExchangeStatus(exchangeData.status);
    
    // 状态不匹配
    if (systemOrder.status !== exchangeStatus) {
      return {
        orderId: systemOrder.id,
        systemStatus: systemOrder.status,
        exchangeStatus,
        systemData: {
          status: systemOrder.status,
          filledQuantity: systemOrder.filledQuantity,
          averagePrice: systemOrder.averagePrice
        },
        exchangeData,
        discrepancyType: 'STATUS_MISMATCH',
        severity: this.calculateDiscrepancySeverity(systemOrder.status, exchangeStatus)
      };
    }

    // 成交数量不匹配
    const exchangeFilledQty = parseFloat(exchangeData.executedQty || '0');
    if (Math.abs(systemOrder.filledQuantity - exchangeFilledQty) > this.config.reconciliationThreshold) {
      return {
        orderId: systemOrder.id,
        systemStatus: systemOrder.status,
        exchangeStatus,
        systemData: {
          filledQuantity: systemOrder.filledQuantity
        },
        exchangeData: {
          filledQuantity: exchangeFilledQty
        },
        discrepancyType: 'QUANTITY_MISMATCH',
        severity: 'MEDIUM'
      };
    }

    // 成交价格不匹配
    const exchangeAvgPrice = parseFloat(exchangeData.avgPrice || exchangeData.price || '0');
    if (systemOrder.averagePrice && exchangeAvgPrice > 0) {
      const priceDiff = Math.abs(systemOrder.averagePrice - exchangeAvgPrice) / systemOrder.averagePrice;
      if (priceDiff > this.config.reconciliationThreshold) {
        return {
          orderId: systemOrder.id,
          systemStatus: systemOrder.status,
          exchangeStatus,
          systemData: {
            averagePrice: systemOrder.averagePrice
          },
          exchangeData: {
            averagePrice: exchangeAvgPrice
          },
          discrepancyType: 'PRICE_MISMATCH',
          severity: 'LOW'
        };
      }
    }

    return null;
  }

  /**
   * 对账订单
   */
  private async reconcileOrder(discrepancy: OrderDiscrepancy, exchangeData: any): Promise<void> {
    try {
      this.logger.info('开始对账订单', {
        orderId: discrepancy.orderId,
        discrepancyType: discrepancy.discrepancyType,
        severity: discrepancy.severity
      });

      let reconciliationAction = '';

      switch (discrepancy.discrepancyType) {
        case 'STATUS_MISMATCH':
          // 更新系统中的订单状态
          await this.orderManager.updateOrderStatus(
            discrepancy.orderId,
            discrepancy.exchangeStatus,
            {
              filledQuantity: parseFloat(exchangeData.executedQty || '0'),
              averagePrice: parseFloat(exchangeData.avgPrice || exchangeData.price || '0'),
              executedAt: new Date(exchangeData.updateTime || Date.now())
            },
            '交易所状态同步'
          );
          reconciliationAction = '状态同步';
          break;

        case 'QUANTITY_MISMATCH':
          // 更新成交数量
          await this.orderManager.updateOrderStatus(
            discrepancy.orderId,
            discrepancy.systemStatus,
            {
              filledQuantity: parseFloat(exchangeData.executedQty || '0')
            },
            '成交数量同步'
          );
          reconciliationAction = '数量同步';
          break;

        case 'PRICE_MISMATCH':
          // 更新成交价格
          await this.orderManager.updateOrderStatus(
            discrepancy.orderId,
            discrepancy.systemStatus,
            {
              averagePrice: parseFloat(exchangeData.avgPrice || exchangeData.price || '0')
            },
            '成交价格同步'
          );
          reconciliationAction = '价格同步';
          break;
      }

      this.emit('reconciliationCompleted', {
        orderId: discrepancy.orderId,
        success: true,
        action: reconciliationAction
      });

    } catch (error) {
      this.logger.error('订单对账失败', {
        orderId: discrepancy.orderId,
        error: error instanceof Error ? error.message : String(error)
      });

      this.emit('reconciliationCompleted', {
        orderId: discrepancy.orderId,
        success: false,
        action: '对账失败'
      });
    }
  }

  /**
   * 启动自动同步
   */
  private startAutoSync(): void {
    this.syncTimer = setInterval(() => {
      // 执行自动同步
      this.syncAllActiveOrders()
        .catch(error => {
          this.logger.error('自动同步执行失败', {
            error: error instanceof Error ? error.message : String(error)
          });
        });
      this.logger.debug('执行自动同步检查');
    }, this.config.syncInterval);

    this.logger.info('启动自动同步', {
      interval: this.config.syncInterval
    });
  }

  /**
   * 停止自动同步
   */
  private stopAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
      this.logger.info('停止自动同步');
    }
  }

  /**
   * 同步订单批次
   */
  private async syncOrderBatch(orders: OrderInfo[], exchangeInterface: ExchangeInterface): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      syncedOrders: 0,
      failedOrders: 0,
      errors: [],
      timestamp: new Date(),
      duration: 0
    };

    const startTime = Date.now();

    for (const order of orders) {
      try {
        const success = await this.syncSingleOrder(order.id, exchangeInterface);
        if (success) {
          result.syncedOrders++;
        } else {
          result.failedOrders++;
        }
      } catch (error) {
        result.failedOrders++;
        result.errors.push(`订单 ${order.id} 同步失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    result.duration = Date.now() - startTime;
    return result;
  }

  /**
   * 从交易所获取订单数据
   */
  private async getExchangeOrderData(order: OrderInfo, exchangeInterface: ExchangeInterface): Promise<any> {
    if (!order.exchangeOrderId) {
      return null;
    }

    try {
      return await exchangeInterface.getOrderStatus(order.exchangeOrderId);
    } catch (error) {
      this.logger.error('获取交易所订单数据失败', {
        orderId: order.id,
        exchangeOrderId: order.exchangeOrderId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 映射交易所状态到系统状态
   */
  private mapExchangeStatus(exchangeStatus: string): OrderStatus {
    const statusMapping: Record<string, OrderStatus> = {
      'NEW': OrderStatus.PENDING,
      'PARTIALLY_FILLED': OrderStatus.PARTIALLY_FILLED,
      'FILLED': OrderStatus.FILLED,
      'CANCELED': OrderStatus.CANCELLED,
      'CANCELLED': OrderStatus.CANCELLED,
      'REJECTED': OrderStatus.REJECTED,
      'EXPIRED': OrderStatus.EXPIRED
    };

    return statusMapping[exchangeStatus] || OrderStatus.UNKNOWN;
  }

  /**
   * 计算差异严重程度
   */
  private calculateDiscrepancySeverity(systemStatus: OrderStatus, exchangeStatus: OrderStatus): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    // 终态状态不匹配是严重问题
    const finalStatuses = [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXPIRED, OrderStatus.FAILED];
    
    if (finalStatuses.includes(systemStatus) && finalStatuses.includes(exchangeStatus) && systemStatus !== exchangeStatus) {
      return 'CRITICAL';
    }

    if (systemStatus === OrderStatus.FILLED && exchangeStatus !== OrderStatus.FILLED) {
      return 'HIGH';
    }

    if (systemStatus === OrderStatus.CANCELLED && exchangeStatus === OrderStatus.FILLED) {
      return 'CRITICAL';
    }

    return 'MEDIUM';
  }

  /**
   * 数组分块
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 处理同步完成事件
   */
  private async handleSyncCompleted(result: SyncResult): Promise<void> {
    this.logger.info('同步完成', {
      syncedOrders: result.syncedOrders,
      failedOrders: result.failedOrders,
      duration: result.duration
    });
  }

  /**
   * 处理差异检测事件
   */
  private async handleDiscrepancyDetected(discrepancy: OrderDiscrepancy): Promise<void> {
    this.logger.warn('检测到订单差异', {
      orderId: discrepancy.orderId,
      discrepancyType: discrepancy.discrepancyType,
      severity: discrepancy.severity,
      systemStatus: discrepancy.systemStatus,
      exchangeStatus: discrepancy.exchangeStatus
    });
  }

  /**
   * 处理对账完成事件
   */
  private async handleReconciliationCompleted(result: { orderId: string; success: boolean; action: string }): Promise<void> {
    this.logger.info('对账完成', result);
  }

  // 公共API方法

  /**
   * 获取同步统计信息
   */
  getSyncStatistics(): {
    isRunning: boolean;
    lastSyncTime: Date | null;
    syncCount: number;
    config: SyncConfig;
  } {
    return {
      isRunning: this.isRunning,
      lastSyncTime: this.lastSyncTime,
      syncCount: this.syncCount,
      config: { ...this.config }
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SyncConfig>): void {
    Object.assign(this.config, newConfig);
    
    this.logger.info('订单状态同步服务配置已更新', { newConfig });

    // 如果同步间隔改变，重启自动同步
    if (newConfig.syncInterval && this.isRunning && this.config.enableAutoSync) {
      this.stopAutoSync();
      this.startAutoSync();
    }
  }

  /**
   * 销毁服务
   */
  async destroy(): Promise<void> {
    await this.stop();
    this.removeAllListeners();
    
    this.logger.info('订单状态同步服务已销毁');
  }
}
