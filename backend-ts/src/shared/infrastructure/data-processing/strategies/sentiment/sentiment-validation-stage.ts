/**
 * 情绪验证阶段
 * 验证情绪分析结果的准确性和一致性
 */

import { IProcessingStage } from '../../interfaces/IDataProcessingPipeline';
import { ILogger } from '../../../logging/interfaces';

export class SentimentValidationStage implements IProcessingStage {
  constructor(private readonly logger: ILogger) {}

  async process(data: any): Promise<any> {
    this.logger.debug('开始情绪验证阶段');

    try {
      // 验证情绪数据的完整性
      const validatedData = this.validateSentimentData(data);
      
      // 验证情绪分数的合理性
      const scoreValidatedData = this.validateSentimentScores(validatedData);
      
      // 验证情绪趋势的一致性
      const trendValidatedData = this.validateSentimentTrends(scoreValidatedData);
      
      this.logger.debug('情绪验证阶段完成', {
        originalDataPoints: data?.sentimentData?.length || 0,
        validatedDataPoints: trendValidatedData?.sentimentData?.length || 0
      });

      return trendValidatedData;
    } catch (error) {
      this.logger.error('情绪验证阶段失败', { error });
      throw error;
    }
  }

  /**
   * 验证情绪数据的完整性
   */
  private validateSentimentData(data: any): any {
    if (!data || !data.sentimentData) {
      this.logger.warn('缺少情绪数据，跳过验证');
      return data;
    }

    const validSentimentData = data.sentimentData.filter((item: any) => {
      // 检查必需字段
      if (!item.timestamp || typeof item.score !== 'number') {
        this.logger.debug('过滤无效情绪数据项', { item });
        return false;
      }

      // 检查分数范围
      if (item.score < -1 || item.score > 1) {
        this.logger.debug('过滤超出范围的情绪分数', { score: item.score });
        return false;
      }

      return true;
    });

    return {
      ...data,
      sentimentData: validSentimentData,
      validationInfo: {
        originalCount: data.sentimentData.length,
        validCount: validSentimentData.length,
        filteredCount: data.sentimentData.length - validSentimentData.length
      }
    };
  }

  /**
   * 验证情绪分数的合理性
   */
  private validateSentimentScores(data: any): any {
    if (!data.sentimentData || data.sentimentData.length === 0) {
      return data;
    }

    const scores = data.sentimentData.map((item: any) => item.score);
    const avgScore = scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length;
    const maxScore = Math.max(...scores);
    const minScore = Math.min(...scores);

    // 检查异常值
    const outliers = data.sentimentData.filter((item: any) => {
      const deviation = Math.abs(item.score - avgScore);
      return deviation > 0.8; // 偏差超过0.8的视为异常值
    });

    if (outliers.length > 0) {
      this.logger.debug('发现情绪分数异常值', {
        outliersCount: outliers.length,
        avgScore,
        maxScore,
        minScore
      });
    }

    return {
      ...data,
      sentimentAnalysis: {
        ...data.sentimentAnalysis,
        scoreStatistics: {
          average: avgScore,
          maximum: maxScore,
          minimum: minScore,
          outliers: outliers.length,
          totalDataPoints: scores.length
        }
      }
    };
  }

  /**
   * 验证情绪趋势的一致性
   */
  private validateSentimentTrends(data: any): any {
    if (!data.sentimentData || data.sentimentData.length < 2) {
      return data;
    }

    // 按时间排序
    const sortedData = [...data.sentimentData].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    // 计算趋势变化
    const trendChanges = [];
    for (let i = 1; i < sortedData.length; i++) {
      const change = sortedData[i].score - sortedData[i - 1].score;
      trendChanges.push({
        timestamp: sortedData[i].timestamp,
        change,
        direction: change > 0 ? 'positive' : change < 0 ? 'negative' : 'neutral'
      });
    }

    // 检测急剧变化
    const significantChanges = trendChanges.filter(change => Math.abs(change.change) > 0.5);
    
    if (significantChanges.length > 0) {
      this.logger.debug('检测到情绪趋势急剧变化', {
        significantChangesCount: significantChanges.length,
        totalChanges: trendChanges.length
      });
    }

    // 计算趋势稳定性
    const changeVariance = this.calculateVariance(trendChanges.map(c => c.change));
    const trendStability = changeVariance < 0.1 ? 'stable' : changeVariance < 0.3 ? 'moderate' : 'volatile';

    return {
      ...data,
      sentimentAnalysis: {
        ...data.sentimentAnalysis,
        trendValidation: {
          totalChanges: trendChanges.length,
          significantChanges: significantChanges.length,
          trendStability,
          changeVariance,
        }
      }
    };
  }

  /**
   * 计算方差
   */
  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }
}
