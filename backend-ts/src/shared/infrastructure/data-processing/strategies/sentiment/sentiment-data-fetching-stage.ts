/**
 * 情绪数据获取阶段 - V3.0策略模式实现
 * 并行调用所有情绪数据适配器，聚合原始数据
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../../di/types/index';
import { IProcessingStage, ProcessingContext } from '../../interfaces/IDataProcessingPipeline';
import { ILogger } from '../../../logging/interfaces';
import { TradingSymbol } from '../../../../../contexts/market-data/domain/value-objects/trading-symbol';

/**
 * 情绪数据聚合结果接口
 */
export interface SentimentDataAggregation {
  sentiCryptData: any;
  fearGreedData: any;
  timestamp: Date;
  symbol: TradingSymbol;
  dataQuality: {
    sentiCryptAvailable: boolean;
    fearGreedAvailable: boolean;
    overallQuality: number;
  };
  metadata: {
    fetchDuration: number;
    sources: string[];
    errors: string[];
  };
}

/**
 * 情绪数据获取阶段实现
 */
@injectable()
export class SentimentDataFetchingStage implements IProcessingStage {
  readonly stageName = 'sentiment-data-fetching';

  constructor(
    @inject(TYPES.Logger)
    private readonly logger: ILogger,

    @inject(TYPES.MarketData.SentiCryptAdapter)
    private readonly sentiCryptAdapter: any, // 使用any类型避免循环依赖

    @inject(TYPES.MarketData.FearGreedAdapter)
    private readonly fearGreedAdapter: any // 使用any类型避免循环依赖
  ) {}

  /**
   * 执行情绪数据获取处理
   */
  async process(
    context: ProcessingContext, 
    previousStageOutput: any
  ): Promise<SentimentDataAggregation> {
    const startTime = performance.now();
    const symbol = this.extractSymbol(context, previousStageOutput);
    const sources: string[] = [];
    const errors: string[] = [];

    this.logger.info('开始情绪数据获取阶段', {
      symbol: symbol.symbol,
      context: {
        source: context.source,
        dataType: context.dataType,
        businessSystem: context.businessSystem
      }
    });

    try {
      // 并行获取所有情绪数据源
      const [sentiCryptResult, fearGreedResult] = await Promise.allSettled([
        this.fetchSentiCryptData(symbol),
        this.fetchFearGreedData()
      ]);

      // 处理SentiCrypt数据结果
      let sentiCryptData = null;
      let sentiCryptAvailable = false;
      if (sentiCryptResult.status === 'fulfilled') {
        sentiCryptData = sentiCryptResult.value;
        sentiCryptAvailable = true;
        sources.push('senticrypt');
        this.logger.debug('SentiCrypt数据获取成功');
      } else {
        errors.push(`SentiCrypt: ${sentiCryptResult.reason}`);
        this.logger.warn('SentiCrypt数据获取失败', { error: sentiCryptResult.reason });
      }

      // 处理FearGreed数据结果
      let fearGreedData = null;
      let fearGreedAvailable = false;
      if (fearGreedResult.status === 'fulfilled') {
        fearGreedData = fearGreedResult.value;
        fearGreedAvailable = true;
        sources.push('fear-greed');
        this.logger.debug('FearGreed数据获取成功');
      } else {
        errors.push(`FearGreed: ${fearGreedResult.reason}`);
        this.logger.warn('FearGreed数据获取失败', { error: fearGreedResult.reason });
      }

      // 计算数据质量
      const availableSourcesCount = sources.length;
      const totalSourcesCount = 2; // SentiCrypt + FearGreed
      const overallQuality = availableSourcesCount / totalSourcesCount;

      // 验证至少有一个数据源可用
      if (availableSourcesCount === 0) {
        throw new Error('所有情绪数据源都不可用');
      }

      const fetchDuration = performance.now() - startTime;

      const aggregation: SentimentDataAggregation = {
        sentiCryptData,
        fearGreedData,
        timestamp: new Date(),
        symbol,
        dataQuality: {
          sentiCryptAvailable,
          fearGreedAvailable,
          overallQuality
        },
        metadata: {
          fetchDuration,
          sources,
          errors
        }
      };

      this.logger.info('情绪数据获取阶段完成', {
        symbol: symbol.symbol,
        duration: fetchDuration.toFixed(3),
        sources: sources.length,
        quality: overallQuality,
        hasErrors: errors.length > 0
      });

      return aggregation;

    } catch (error) {
      const fetchDuration = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.logger.error('情绪数据获取阶段失败', {
        symbol: symbol.symbol,
        error: errorMessage,
        duration: fetchDuration.toFixed(3)
      });

      throw new Error(`情绪数据获取失败: ${errorMessage}`);
    }
  }

  /**
   * 验证输入数据
   */
  async validateInput(context: ProcessingContext, input: any): Promise<boolean> {
    try {
      // 验证上下文必要字段
      if (!context.source || !context.dataType || !context.businessSystem) {
        this.logger.warn('情绪数据获取阶段输入验证失败：缺少必要的上下文字段');
        return false;
      }

      // 验证是否为情绪数据相关的请求
      if (!context.dataType.includes('sentiment') && !context.dataType.includes('emotion')) {
        this.logger.debug('非情绪数据请求，跳过验证');
      }

      return true;
    } catch (error) {
      this.logger.error('情绪数据获取阶段输入验证异常', { error });
      return false;
    }
  }

  /**
   * 获取阶段配置
   */
  getStageConfig(): Record<string, any> {
    return {
      stageName: this.stageName,
      description: '并行获取所有情绪数据源的原始数据',
      supportedSources: ['senticrypt', 'fear-greed'],
      timeout: 15000,
      retryCount: 2,
      parallelExecution: true,
      dataQualityThreshold: 0.5 // 至少50%的数据源可用
    };
  }

  /**
   * 获取SentiCrypt数据
   */
  private async fetchSentiCryptData(symbol: TradingSymbol): Promise<any> {
    try {
      return await this.sentiCryptAdapter.getTodaySentiment();
    } catch (error) {
      throw new Error(`SentiCrypt数据获取失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取FearGreed数据
   */
  private async fetchFearGreedData(): Promise<any> {
    try {
      return await this.fearGreedAdapter.getCurrentIndex();
    } catch (error) {
      throw new Error(`FearGreed数据获取失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 从上下文或输入中提取交易符号
   */
  private extractSymbol(context: ProcessingContext, input: any): TradingSymbol {
    // 尝试从输入中提取
    if (input && input.symbol) {
      if (input.symbol instanceof TradingSymbol) {
        return input.symbol;
      }
      if (typeof input.symbol === 'string') {
        return TradingSymbol.fromString(input.symbol);
      }
    }

    // 尝试从上下文元数据中提取
    if (context.metadata && context.metadata.symbol) {
      if (context.metadata.symbol instanceof TradingSymbol) {
        return context.metadata.symbol;
      }
      if (typeof context.metadata.symbol === 'string') {
        return TradingSymbol.fromString(context.metadata.symbol);
      }
    }

    // 默认使用BTC
    this.logger.debug('未找到指定交易符号，使用默认BTC');
    return TradingSymbol.fromString('BTCUSDT');
  }
}
