/**
 * 统一学习服务管理器
 * 整合短期和长期学习服务，消除重复代码
 */

import { PrismaClient } from '@prisma/client';
import { EventEmitter } from 'events';
import * as cron from 'node-cron';
import { injectable, inject } from 'inversify';
import { TYPES } from '../di/types';
import { ILogger } from '../logging/interfaces/basic-logger.interface';
import { AdaptiveLearningEngine } from '../../../application/adaptive-learning-engine';

/**
 * 学习时间范围类型
 */
export type LearningTimeframe = 'short-term' | 'long-term';

/**
 * 学习配置接口
 */
export interface LearningConfig {
  predictionInterval: string;
  verificationInterval: string;
  predictionHorizon: string;
  verificationDelay: string;
  verificationDelayMs: number;
}

/**
 * 预设学习配置
 */
export const LEARNING_CONFIGS: Record<LearningTimeframe, LearningConfig> = {
  'short-term': {
    predictionInterval: '*/15 * * * *', // 每15分钟
    verificationInterval: '*/5 * * * *', // 每5分钟检查验证
    predictionHorizon: '15m',
    verificationDelay: '30m',
    verificationDelayMs: 30 * 60 * 1000, // 30分钟
  },
  'long-term': {
    predictionInterval: '0 */8 * * *', // 每8小时
    verificationInterval: '0 */4 * * *', // 每4小时检查验证
    predictionHorizon: '8h',
    verificationDelay: '24h',
    verificationDelayMs: 24 * 60 * 60 * 1000, // 24小时
  }
};

/**
 * 学习服务实例
 */
class LearningServiceInstance extends EventEmitter {
  private predictionTask: any | null = null;
  private verificationTask: any | null = null;
  private isRunning = false;

  constructor(
    private readonly timeframe: LearningTimeframe,
    private readonly config: LearningConfig,
    private readonly prisma: PrismaClient,
    private readonly logger: ILogger,
    private readonly learningEngine: AdaptiveLearningEngine
  ) {
    super();
  }

  /**
   * 启动学习服务
   */
  async start(): Promise<void> {
    if (this.predictionTask || this.verificationTask) {
      this.logger.warn(`${this.timeframe}学习服务已在运行`);
      return;
    }

    this.logger.info(`🚀 启动${this.timeframe}学习服务...`);

    // 立即执行一次预测
    await this.generatePrediction();

    // 设置预测定时任务
    this.predictionTask = cron.schedule(this.config.predictionInterval, async () => {
      await this.generatePrediction();
    });

    // 设置验证定时任务
    this.verificationTask = cron.schedule(this.config.verificationInterval, async () => {
      await this.verifyPredictions();
    });

    this.isRunning = true;
    this.logger.info(`✅ ${this.timeframe}学习服务启动成功`);
  }

  /**
   * 停止学习服务
   */
  async stop(): Promise<void> {
    this.logger.info(`🛑 停止${this.timeframe}学习服务...`);

    if (this.predictionTask) {
      this.predictionTask.stop();
      this.predictionTask = null;
    }

    if (this.verificationTask) {
      this.verificationTask.stop();
      this.verificationTask = null;
    }

    this.isRunning = false;
    this.logger.info(`✅ ${this.timeframe}学习服务已停止`);
  }

  /**
   * 生成预测
   */
  private async generatePrediction(): Promise<void> {
    try {
      this.logger.info(`📊 开始${this.timeframe}预测生成...`);

      const prediction = await this.learningEngine.generatePrediction({
        timeframe: this.timeframe,
        horizon: this.config.predictionHorizon
      });

      // 保存预测结果
      await this.prisma.prediction.create({
        data: {
          timeframe: this.timeframe,
          horizon: this.config.predictionHorizon,
          prediction: prediction.prediction,
          confidence: prediction.confidence,
          features: prediction.features,
          createdAt: new Date(),
          verificationAt: new Date(Date.now() + this.config.verificationDelayMs)
        }
      });

      this.emit('predictionGenerated', {
        timeframe: this.timeframe,
        prediction
      });

      this.logger.info(`✅ ${this.timeframe}预测生成完成`);
    } catch (error) {
      this.logger.error(`❌ ${this.timeframe}预测生成失败:`, error);
      this.emit('predictionError', { timeframe: this.timeframe, error });
    }
  }

  /**
   * 验证预测
   */
  private async verifyPredictions(): Promise<void> {
    try {
      this.logger.info(`🔍 开始${this.timeframe}预测验证...`);

      const pendingPredictions = await this.prisma.prediction.findMany({
        where: {
          timeframe: this.timeframe,
          verificationAt: {
            lte: new Date()
          },
          verified: false
        }
      });

      for (const prediction of pendingPredictions) {
        const verification = await this.learningEngine.verifyPrediction(prediction);
        
        await this.prisma.prediction.update({
          where: { id: prediction.id },
          data: {
            verified: true,
            accuracy: verification.accuracy,
            actualValue: verification.actualValue,
            verifiedAt: new Date()
          }
        });

        this.emit('predictionVerified', {
          timeframe: this.timeframe,
          prediction,
          verification
        });
      }

      this.logger.info(`✅ ${this.timeframe}预测验证完成，验证了${pendingPredictions.length}个预测`);
    } catch (error) {
      this.logger.error(`❌ ${this.timeframe}预测验证失败:`, error);
      this.emit('verificationError', { timeframe: this.timeframe, error });
    }
  }

  /**
   * 获取运行状态
   */
  getStatus() {
    return {
      timeframe: this.timeframe,
      isRunning: this.isRunning,
      config: this.config
    };
  }
}

/**
 * 统一学习服务管理器
 */
@injectable()
export class UnifiedLearningServiceManager extends EventEmitter {
  private services: Map<LearningTimeframe, LearningServiceInstance> = new Map();

  constructor(
    @inject(TYPES.Database.PrismaClient) private readonly prisma: PrismaClient,
    @inject(TYPES.Shared.Logger) private readonly logger: ILogger,
    @inject(TYPES.Application.AdaptiveLearningEngine) private readonly learningEngine: AdaptiveLearningEngine
  ) {
    super();
    this.initializeServices();
  }

  /**
   * 初始化学习服务
   */
  private initializeServices(): void {
    for (const [timeframe, config] of Object.entries(LEARNING_CONFIGS)) {
      const service = new LearningServiceInstance(
        timeframe as LearningTimeframe,
        config,
        this.prisma,
        this.logger,
        this.learningEngine
      );

      // 转发事件
      service.on('predictionGenerated', (data) => this.emit('predictionGenerated', data));
      service.on('predictionVerified', (data) => this.emit('predictionVerified', data));
      service.on('predictionError', (data) => this.emit('predictionError', data));
      service.on('verificationError', (data) => this.emit('verificationError', data));

      this.services.set(timeframe as LearningTimeframe, service);
    }
  }

  /**
   * 启动指定时间范围的学习服务
   */
  async startService(timeframe: LearningTimeframe): Promise<void> {
    const service = this.services.get(timeframe);
    if (!service) {
      throw new Error(`未找到${timeframe}学习服务`);
    }
    await service.start();
  }

  /**
   * 停止指定时间范围的学习服务
   */
  async stopService(timeframe: LearningTimeframe): Promise<void> {
    const service = this.services.get(timeframe);
    if (!service) {
      throw new Error(`未找到${timeframe}学习服务`);
    }
    await service.stop();
  }

  /**
   * 启动所有学习服务
   */
  async startAll(): Promise<void> {
    this.logger.info('🚀 启动所有学习服务...');
    for (const [timeframe, service] of this.services) {
      await service.start();
    }
    this.logger.info('✅ 所有学习服务启动完成');
  }

  /**
   * 停止所有学习服务
   */
  async stopAll(): Promise<void> {
    this.logger.info('🛑 停止所有学习服务...');
    for (const [timeframe, service] of this.services) {
      await service.stop();
    }
    this.logger.info('✅ 所有学习服务已停止');
  }

  /**
   * 获取所有服务状态
   */
  getAllStatus() {
    const status: Record<string, any> = {};
    for (const [timeframe, service] of this.services) {
      status[timeframe] = service.getStatus();
    }
    return status;
  }
}
