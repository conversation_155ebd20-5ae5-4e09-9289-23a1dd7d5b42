/**
 * 统一环境变量管理器
 * 从config-validation.ts中提取的环境管理功能
 */

import { z } from 'zod';

/**
 * 环境变量验证模式
 * 整合environment.ts中的验证逻辑
 */
export const ENVIRONMENT_SCHEMA = z.object({
  // 应用配置
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.coerce.number().default(3001),
  HOST: z.string().default('0.0.0.0'),

  // 数据库配置
  DATABASE_URL: z.string().optional(),
  DB_HOST: z.string().default('localhost'),
  DB_PORT: z.coerce.number().default(5432),
  DB_NAME: z.string().default('cryptoMonitor'),
  DB_USER: z.string().default('postgres'),
  DB_PASSWORD: z.string().default('password'),

  // Redis配置
  REDIS_URL: z.string().optional(),
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.coerce.number().default(6379),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_DB: z.coerce.number().default(0),

  // 外部API配置
  BINANCE_API_URL: z.string().default('https://api.binance.com'),
  BINANCE_API_KEY: z.string().optional(),
  BINANCE_SECRET_KEY: z.string().optional(),
  COINBASE_API_URL: z.string().default('https://api.exchange.coinbase.com'),

  // 凭证加密配置
  CREDENTIAL_ENCRYPTION_KEY: z.string().optional(),

  // LLM API配置
  OPENAI_API_KEY: z.string().optional(),
  OPENAI_BASE_URL: z.string().default('https://api.openai.com'),
  ANTHROPIC_API_KEY: z.string().optional(),
  ANTHROPIC_BASE_URL: z.string().default('https://api.anthropic.com'),
  GEMINI_API_KEY: z.string().optional(),

  // LLM模型配置
  DEFAULT_LLM_MODEL: z.string().default('gemini-2.0-flash'),
  LLM_TIMEOUT: z.coerce.number().default(30000),
  LLM_MAX_TOKENS: z.coerce.number().default(2000),
  LLM_TEMPERATURE: z.coerce.number().default(0.3),

  // 安全配置
  JWT_SECRET: z.string().default('your-secret-key'),
  JWT_EXPIRES_IN: z.string().default('24h'),

  // 日志配置
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug', 'silent']).default('info'),
  LOG_FORMAT: z.enum(['json', 'simple']).default('json'),

  // 监控配置
  METRICS_ENABLED: z.coerce.boolean().default(true),
  METRICS_PORT: z.coerce.number().default(9090),

  // WebSocket配置
  WS_CORS_ORIGINS: z.string().default('http://localhost:3001'),

  // 任务队列配置
  QUEUE_CONCURRENCY: z.coerce.number().default(5),
  QUEUE_MAX_ATTEMPTS: z.coerce.number().default(3),

  // 缓存配置
  CACHE_TTL: z.coerce.number().default(30),

  // 限流配置
  RATE_LIMIT_MAX: z.coerce.number().default(100),
  RATE_LIMIT_WINDOW: z.coerce.number().default(60000),

  // API配置
  API_BASE_URL: z.string().default('http://localhost:3001'),

  // 测试相关配置
  BINANCE_TESTNET: z.string().optional(),
  VITEST: z.string().optional(),
  API_KEY_SECRET: z.string().optional(),
  LLM_CONFIG_ENCRYPTION_KEY: z.string().optional(),
  TEST_API_KEY_PREFIX: z.string().optional(),
  TEST_SECRET_KEY_PREFIX: z.string().optional(),
  COHERE_API_KEY: z.string().optional()
});

/**
 * 环境变量类型
 */
export type Environment = z.infer<typeof ENVIRONMENT_SCHEMA>;

/**
 * 验证环境变量
 */
export function validateEnvironment(env: Record<string, any> = process.env): Environment {
  const result = ENVIRONMENT_SCHEMA.safeParse(env);

  if (!result.success) {
    console.error('❌ 环境变量验证失败:');
    console.error(result.error.format());
    throw new Error('环境变量验证失败');
  }

  return result.data;
}

/**
 * 获取数据库连接URL
 */
export function getDatabaseUrl(env: Environment): string {
  if (env.DATABASE_URL) {
    return env.DATABASE_URL;
  }

  if (env.NODE_ENV === 'test') {
    return 'file:./test.db';
  }

  // 开发环境默认使用SQLite
  if (env.NODE_ENV === 'development') {
    return 'file:./dev.db';
  }

  return `postgresql://${env.DB_USER}:${env.DB_PASSWORD}@${env.DB_HOST}:${env.DB_PORT}/${env.DB_NAME}`;
}

/**
 * 获取Redis连接URL
 */
export function getRedisUrl(env: Environment): string {
  if (env.REDIS_URL) {
    return env.REDIS_URL;
  }

  const auth = env.REDIS_PASSWORD ? `:${env.REDIS_PASSWORD}@` : '';
  return `redis://${auth}${env.REDIS_HOST}:${env.REDIS_PORT}/${env.REDIS_DB}`;
}

/**
 * 检查是否为开发环境
 */
export function isDevelopment(env: Environment): boolean {
  return env.NODE_ENV === 'development';
}

/**
 * 检查是否为生产环境
 */
export function isProduction(env: Environment): boolean {
  return env.NODE_ENV === 'production';
}

/**
 * 检查是否为测试环境
 */
export function isTest(env: Environment): boolean {
  return env.NODE_ENV === 'test';
}
