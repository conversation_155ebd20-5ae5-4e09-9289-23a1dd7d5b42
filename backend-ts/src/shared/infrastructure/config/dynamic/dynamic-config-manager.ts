/**
 * 动态配置管理器实现
 * 提供配置订阅、变更推送等功能
 */

import { injectable, inject } from 'inversify';
import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { IBasicLogger } from '../../logging/interfaces/index';
import { UnifiedErrorHandler } from '../../error/unified-error-handler';
import { TYPES } from '../../di/types';
import {
  DynamicConfigManager,
  ConfigSubscription,
  ConfigChange,
  ConfigChangeCallback,
  SubscriptionStats,
  RealTimeConfigPushService
} from './dynamic-config.interface';

@injectable()
export class DynamicConfigManagerImpl implements DynamicConfigManager {
  private readonly eventEmitter = new EventEmitter();
  private readonly subscriptions: Map<string, ConfigSubscription> = new Map();
  private readonly patternSubscriptions: Map<string, Set<string>> = new Map(); // pattern -> subscriptionIds
  private readonly changeHistory: ConfigChange[] = [];
  private readonly maxHistorySize = 1000;

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler,
    @inject('RealTimeConfigPushService') private readonly pushService: RealTimeConfigPushService
  ) {
    this.setupEventListeners();
  }

  /**
   * 订阅配置变更
   */
  subscribe(pattern: string, callback: ConfigChangeCallback): ConfigSubscription {
    try {
      const subscription: ConfigSubscription = {
        id: uuidv4(),
        pattern,
        callback,
        createdAt: new Date(),
        triggerCount: 0,
        active: true
      };

      this.subscriptions.set(subscription.id, subscription);

      // 添加到模式订阅映射
      if (!this.patternSubscriptions.has(pattern)) {
        this.patternSubscriptions.set(pattern, new Set());
      }
      this.patternSubscriptions.get(pattern)!.add(subscription.id);

      this.logger.debug('配置订阅已创建', { 
        subscriptionId: subscription.id, 
        pattern 
      });

      this.eventEmitter.emit('subscriptionCreated', subscription);

      return subscription;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'DynamicConfigManager.subscribe',
        pattern
      });
      throw error;
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(subscription: ConfigSubscription): void {
    try {
      const existingSubscription = this.subscriptions.get(subscription.id);
      if (!existingSubscription) {
        this.logger.warn('尝试取消不存在的订阅', { subscriptionId: subscription.id });
        return;
      }

      // 标记为非活跃
      existingSubscription.active = false;

      // 从模式订阅映射中移除
      const patternSubs = this.patternSubscriptions.get(subscription.pattern);
      if (patternSubs) {
        patternSubs.delete(subscription.id);
        if (patternSubs.size === 0) {
          this.patternSubscriptions.delete(subscription.pattern);
        }
      }

      // 从订阅映射中移除
      this.subscriptions.delete(subscription.id);

      this.logger.debug('配置订阅已取消', { 
        subscriptionId: subscription.id, 
        pattern: subscription.pattern 
      });

      this.eventEmitter.emit('subscriptionCancelled', subscription);
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'DynamicConfigManager.unsubscribe',
        subscriptionId: subscription.id
      });
    }
  }

  /**
   * 推送配置变更
   */
  async pushConfigChange(change: ConfigChange): Promise<void> {
    try {
      this.logger.info('推送配置变更', { 
        key: change.key, 
        changeType: change.changeType 
      });

      // 添加到历史记录
      this.addToHistory(change);

      // 触发匹配的订阅
      await this.triggerMatchingSubscriptions(change);

      // 通过推送服务广播
      await this.pushService.broadcast(change);

      this.eventEmitter.emit('configChanged', change);
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'DynamicConfigManager.pushConfigChange',
        configKey: change.key
      });
      throw error;
    }
  }

  /**
   * 批量推送配置变更
   */
  async pushConfigChanges(changes: ConfigChange[]): Promise<void> {
    try {
      this.logger.info('批量推送配置变更', { count: changes.length });

      const promises = changes.map(change => this.pushConfigChange(change));
      await Promise.allSettled(promises);

      this.logger.info('批量配置变更推送完成', { count: changes.length });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'DynamicConfigManager.pushConfigChanges',
        changesCount: changes.length
      });
      throw error;
    }
  }

  /**
   * 获取活跃订阅数量
   */
  getActiveSubscriptions(): number {
    return Array.from(this.subscriptions.values())
      .filter(sub => sub.active).length;
  }

  /**
   * 获取订阅统计信息
   */
  getSubscriptionStats(): SubscriptionStats {
    const allSubscriptions = Array.from(this.subscriptions.values());
    const activeSubscriptions = allSubscriptions.filter(sub => sub.active);
    const inactiveSubscriptions = allSubscriptions.filter(sub => !sub.active);

    const subscriptionsByPattern: Record<string, number> = {};
    for (const [pattern, subIds] of this.patternSubscriptions) {
      subscriptionsByPattern[pattern] = subIds.size;
    }

    const totalTriggers = allSubscriptions.reduce((sum, sub) => sum + sub.triggerCount, 0);
    const averageTriggersPerSubscription = allSubscriptions.length > 0 ? 
      totalTriggers / allSubscriptions.length : 0;

    return {
      totalSubscriptions: allSubscriptions.length,
      activeSubscriptions: activeSubscriptions.length,
      inactiveSubscriptions: inactiveSubscriptions.length,
      subscriptionsByPattern,
      totalTriggers,
      averageTriggersPerSubscription
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.eventEmitter.on('subscriptionCreated', (subscription) => {
      this.logger.debug('订阅创建事件', { subscriptionId: subscription.id });
    });

    this.eventEmitter.on('subscriptionCancelled', (subscription) => {
      this.logger.debug('订阅取消事件', { subscriptionId: subscription.id });
    });

    this.eventEmitter.on('configChanged', (change) => {
      this.logger.debug('配置变更事件', { key: change.key });
    });
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(change: ConfigChange): void {
    this.changeHistory.push(change);

    // 保持历史记录大小限制
    if (this.changeHistory.length > this.maxHistorySize) {
      this.changeHistory.splice(0, this.changeHistory.length - this.maxHistorySize);
    }
  }

  /**
   * 触发匹配的订阅
   */
  private async triggerMatchingSubscriptions(change: ConfigChange): Promise<void> {
    const matchingSubscriptions = this.findMatchingSubscriptions(change.key);

    const promises = matchingSubscriptions.map(async (subscription) => {
      try {
        // 更新触发统计
        subscription.triggerCount++;
        subscription.lastTriggered = new Date();

        // 调用回调函数
        await subscription.callback(change);

        this.logger.debug('订阅回调已触发', { 
          subscriptionId: subscription.id, 
          configKey: change.key 
        });
      } catch (error) {
        this.logger.error('订阅回调执行失败', { 
          subscriptionId: subscription.id, 
          configKey: change.key,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * 查找匹配的订阅
   */
  private findMatchingSubscriptions(configKey: string): ConfigSubscription[] {
    const matchingSubscriptions: ConfigSubscription[] = [];

    for (const [pattern, subscriptionIds] of this.patternSubscriptions) {
      if (this.isPatternMatch(pattern, configKey)) {
        for (const subscriptionId of subscriptionIds) {
          const subscription = this.subscriptions.get(subscriptionId);
          if (subscription && subscription.active) {
            matchingSubscriptions.push(subscription);
          }
        }
      }
    }

    return matchingSubscriptions;
  }

  /**
   * 检查模式是否匹配
   */
  private isPatternMatch(pattern: string, configKey: string): boolean {
    try {
      // 支持通配符模式
      if (pattern === '*') {
        return true; // 匹配所有
      }

      if (pattern.includes('*')) {
        // 转换为正则表达式
        const regexPattern = pattern
          .replace(/\./g, '\\.')
          .replace(/\*/g, '.*');
        const regex = new RegExp(`^${regexPattern}$`);
        return regex.test(configKey);
      }

      // 精确匹配
      return pattern === configKey;
    } catch (error) {
      this.logger.error('模式匹配检查失败', { pattern, configKey, error });
      return false;
    }
  }

  /**
   * 获取变更历史
   */
  getChangeHistory(limit?: number): ConfigChange[] {
    const history = [...this.changeHistory].reverse(); // 最新的在前
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * 清理非活跃订阅
   */
  cleanupInactiveSubscriptions(): number {
    let cleanedCount = 0;

    for (const [subscriptionId, subscription] of this.subscriptions) {
      if (!subscription.active) {
        this.subscriptions.delete(subscriptionId);
        cleanedCount++;
      }
    }

    // 清理模式订阅映射中的无效引用
    for (const [pattern, subscriptionIds] of this.patternSubscriptions) {
      const validIds = new Set<string>();
      for (const id of subscriptionIds) {
        if (this.subscriptions.has(id)) {
          validIds.add(id);
        }
      }
      
      if (validIds.size === 0) {
        this.patternSubscriptions.delete(pattern);
      } else {
        this.patternSubscriptions.set(pattern, validIds);
      }
    }

    if (cleanedCount > 0) {
      this.logger.info('清理非活跃订阅完成', { cleanedCount });
    }

    return cleanedCount;
  }

  /**
   * 获取订阅详情
   */
  getSubscriptionDetails(subscriptionId: string): ConfigSubscription | undefined {
    return this.subscriptions.get(subscriptionId);
  }

  /**
   * 获取模式订阅
   */
  getPatternSubscriptions(pattern: string): ConfigSubscription[] {
    const subscriptionIds = this.patternSubscriptions.get(pattern) || new Set();
    const subscriptions: ConfigSubscription[] = [];

    for (const id of subscriptionIds) {
      const subscription = this.subscriptions.get(id);
      if (subscription) {
        subscriptions.push(subscription);
      }
    }

    return subscriptions;
  }

  /**
   * 暂停订阅
   */
  pauseSubscription(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.active = false;
      this.logger.debug('订阅已暂停', { subscriptionId });
      return true;
    }
    return false;
  }

  /**
   * 恢复订阅
   */
  resumeSubscription(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.active = true;
      this.logger.debug('订阅已恢复', { subscriptionId });
      return true;
    }
    return false;
  }

  /**
   * 获取配置变更统计
   */
  getChangeStats(timeRange?: { start: Date; end: Date }): {
    totalChanges: number;
    changesByType: Record<string, number>;
    changesByKey: Record<string, number>;
    mostActiveKeys: Array<{ key: string; count: number }>;
  } {
    let changes = this.changeHistory;

    if (timeRange) {
      changes = changes.filter(change => 
        change.timestamp >= timeRange.start && change.timestamp <= timeRange.end
      );
    }

    const changesByType: Record<string, number> = {};
    const changesByKey: Record<string, number> = {};

    for (const change of changes) {
      changesByType[change.changeType] = (changesByType[change.changeType] || 0) + 1;
      changesByKey[change.key] = (changesByKey[change.key] || 0) + 1;
    }

    const mostActiveKeys = Object.entries(changesByKey)
      .map(([key, count]) => ({ key, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalChanges: changes.length,
      changesByType,
      changesByKey,
      mostActiveKeys
    };
  }
}
