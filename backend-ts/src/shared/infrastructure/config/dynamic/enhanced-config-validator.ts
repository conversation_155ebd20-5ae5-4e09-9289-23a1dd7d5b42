/**
 * 增强配置验证器实现
 * 提供更完善的配置验证和依赖检查功能
 */

import { injectable, inject } from 'inversify';
import { z } from 'zod';
import { IBasicLogger } from '../../logging/interfaces/basic-logger.interface';
import { UnifiedErrorHandler } from '../../error/unified-error-handler';
import { TYPES } from '../../di/types';
import {
  EnhancedConfigValidator,
  ValidationResult,
  ValidationContext,
  BatchValidationResult,
  DependencyValidationResult,
  CompatibilityValidationResult,
  CustomValidator,
  ValidationRule,
  ConfigItem,
  ConfigChange
} from './dynamic-config.interface';

@injectable()
export class EnhancedConfigValidatorImpl implements EnhancedConfigValidator {
  private readonly customValidators: Map<string, CustomValidator> = new Map();
  private readonly validationRules: Map<string, ValidationRule[]> = new Map();
  private readonly dependencyGraph: Map<string, Set<string>> = new Map(); // key -> dependencies
  private readonly reverseDependencyGraph: Map<string, Set<string>> = new Map(); // key -> dependents

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler
  ) {
    this.initializeBuiltInValidators();
    this.initializeDependencyGraph();
  }

  /**
   * 验证配置值
   */
  async validate(key: string, value: any, context?: ValidationContext): Promise<ValidationResult> {
    try {
      this.logger.debug('开始验证配置', { key, context });

      const result: ValidationResult = {
        isValid: true,
        value,
        errors: [],
        warnings: [],
        suggestions: []
      };

      // 获取验证规则
      const rules = this.getValidationRules(key);
      
      // 执行验证规则
      for (const rule of rules) {
        if (!rule.enabled) continue;

        const ruleResult = await this.executeValidationRule(rule, value, context);
        
        // 合并结果
        if (!ruleResult.isValid) {
          result.isValid = false;
        }
        
        result.errors.push(...ruleResult.errors);
        result.warnings.push(...ruleResult.warnings);
        result.suggestions.push(...ruleResult.suggestions);
        
        // 使用验证后的值
        if (ruleResult.value !== undefined) {
          result.value = ruleResult.value;
        }
      }

      // 执行自定义验证器
      const customValidator = this.customValidators.get(key);
      if (customValidator) {
        const customResult = await customValidator.validate(value, context);
        
        if (!customResult.isValid) {
          result.isValid = false;
        }
        
        result.errors.push(...customResult.errors);
        result.warnings.push(...customResult.warnings);
        result.suggestions.push(...customResult.suggestions);
      }

      this.logger.debug('配置验证完成', { 
        key, 
        isValid: result.isValid,
        errorsCount: result.errors.length,
        warningsCount: result.warnings.length
      });

      return result;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'EnhancedConfigValidator.validate',
        configKey: key
      });

      return {
        isValid: false,
        errors: [`验证过程中发生错误: ${error instanceof Error ? error.message : String(error)}`],
        warnings: [],
        suggestions: []
      };
    }
  }

  /**
   * 批量验证配置
   */
  async validateBatch(configs: ConfigItem[]): Promise<BatchValidationResult> {
    try {
      this.logger.info('开始批量验证配置', { count: configs.length });

      const results: Record<string, ValidationResult> = {};
      let validConfigs = 0;
      let invalidConfigs = 0;
      let warningConfigs = 0;

      // 并行验证所有配置
      const validationPromises = configs.map(async (config) => {
        const result = await this.validate(config.key, config.value);
        results[config.key] = result;

        if (result.isValid) {
          validConfigs++;
        } else {
          invalidConfigs++;
        }

        if (result.warnings.length > 0) {
          warningConfigs++;
        }

        return { key: config.key, result };
      });

      await Promise.allSettled(validationPromises);

      const batchResult: BatchValidationResult = {
        overallValid: invalidConfigs === 0,
        results,
        summary: {
          totalConfigs: configs.length,
          validConfigs,
          invalidConfigs,
          warningConfigs
        }
      };

      this.logger.info('批量验证完成', batchResult.summary);

      return batchResult;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'EnhancedConfigValidator.validateBatch',
        configsCount: configs.length
      });
      throw error;
    }
  }

  /**
   * 验证配置依赖
   */
  async validateDependencies(key: string, value: any): Promise<DependencyValidationResult> {
    try {
      this.logger.debug('验证配置依赖', { key });

      const result: DependencyValidationResult = {
        isValid: true,
        missingDependencies: [],
        conflictingDependencies: [],
        circularDependencies: []
      };

      // 检查缺失的依赖
      const dependencies = this.dependencyGraph.get(key) || new Set();
      for (const depKey of dependencies) {
        if (!await this.configExists(depKey)) {
          result.missingDependencies.push(depKey);
          result.isValid = false;
        }
      }

      // 检查循环依赖
      const circularDeps = this.detectCircularDependencies(key);
      if (circularDeps.length > 0) {
        result.circularDependencies = circularDeps;
        result.isValid = false;
      }

      // 检查冲突的依赖
      const conflicts = await this.checkDependencyConflicts(key, value);
      if (conflicts.length > 0) {
        result.conflictingDependencies = conflicts;
        result.isValid = false;
      }

      this.logger.debug('依赖验证完成', { 
        key, 
        isValid: result.isValid,
        missingCount: result.missingDependencies.length,
        conflictsCount: result.conflictingDependencies.length,
        circularCount: result.circularDependencies.length
      });

      return result;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'EnhancedConfigValidator.validateDependencies',
        configKey: key
      });
      throw error;
    }
  }

  /**
   * 验证配置兼容性
   */
  async validateCompatibility(changes: ConfigChange[]): Promise<CompatibilityValidationResult> {
    try {
      this.logger.info('验证配置兼容性', { changesCount: changes.length });

      const result: CompatibilityValidationResult = {
        isCompatible: true,
        incompatibleChanges: [],
        warnings: []
      };

      // 检查每个变更的兼容性
      for (const change of changes) {
        const incompatibilities = await this.checkChangeCompatibility(change);
        
        if (incompatibilities.length > 0) {
          result.isCompatible = false;
          result.incompatibleChanges.push(...incompatibilities);
        }
      }

      // 检查变更之间的相互影响
      const crossChangeIssues = await this.checkCrossChangeCompatibility(changes);
      if (crossChangeIssues.length > 0) {
        result.isCompatible = false;
        result.incompatibleChanges.push(...crossChangeIssues);
      }

      this.logger.info('兼容性验证完成', { 
        isCompatible: result.isCompatible,
        incompatibleCount: result.incompatibleChanges.length
      });

      return result;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'EnhancedConfigValidator.validateCompatibility',
        changesCount: changes.length
      });
      throw error;
    }
  }

  /**
   * 注册自定义验证器
   */
  registerValidator(key: string, validator: CustomValidator): void {
    try {
      this.customValidators.set(key, validator);
      this.logger.debug('自定义验证器已注册', { 
        key, 
        validatorName: validator.name 
      });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'EnhancedConfigValidator.registerValidator',
        configKey: key,
        validatorName: validator.name
      });
    }
  }

  /**
   * 获取验证规则
   */
  getValidationRules(key: string): ValidationRule[] {
    return this.validationRules.get(key) || [];
  }

  // ==================== 私有方法 ====================

  /**
   * 初始化内置验证器
   */
  private initializeBuiltInValidators(): void {
    // 数据库配置验证规则
    this.validationRules.set('database.host', [
      {
        name: 'hostname_format',
        description: '验证主机名格式',
        type: 'schema',
        rule: z.string().min(1).max(255),
        priority: 1,
        enabled: true
      }
    ]);

    this.validationRules.set('database.port', [
      {
        name: 'port_range',
        description: '验证端口范围',
        type: 'schema',
        rule: z.number().int().min(1).max(65535),
        priority: 1,
        enabled: true
      }
    ]);

    // API配置验证规则
    this.validationRules.set('api.rateLimit', [
      {
        name: 'rate_limit_range',
        description: '验证速率限制范围',
        type: 'schema',
        rule: z.number().int().min(1).max(10000),
        priority: 1,
        enabled: true
      }
    ]);

    // 交易配置验证规则
    this.validationRules.set('trading.maxPositions', [
      {
        name: 'max_positions_range',
        description: '验证最大持仓数量',
        type: 'schema',
        rule: z.number().int().min(1).max(100),
        priority: 1,
        enabled: true
      }
    ]);

    this.validationRules.set('trading.riskLimit', [
      {
        name: 'risk_limit_range',
        description: '验证风险限制',
        type: 'schema',
        rule: z.number().min(0).max(1),
        priority: 1,
        enabled: true
      }
    ]);
  }

  /**
   * 初始化依赖图
   */
  private initializeDependencyGraph(): void {
    // 定义配置依赖关系
    this.addDependency('database.connectionString', ['database.host', 'database.port', 'database.username']);
    this.addDependency('trading.strategy', ['trading.maxPositions', 'trading.riskLimit']);
    this.addDependency('api.authentication', ['api.jwtSecret', 'api.tokenExpiry']);
    this.addDependency('llm.provider', ['llm.apiKey', 'llm.baseUrl']);
  }

  /**
   * 添加依赖关系
   */
  private addDependency(key: string, dependencies: string[]): void {
    this.dependencyGraph.set(key, new Set(dependencies));
    
    // 更新反向依赖图
    for (const dep of dependencies) {
      if (!this.reverseDependencyGraph.has(dep)) {
        this.reverseDependencyGraph.set(dep, new Set());
      }
      this.reverseDependencyGraph.get(dep)!.add(key);
    }
  }

  /**
   * 执行验证规则
   */
  private async executeValidationRule(
    rule: ValidationRule, 
    value: any, 
    context?: ValidationContext
  ): Promise<ValidationResult> {
    try {
      switch (rule.type) {
        case 'schema':
          return this.executeSchemaValidation(rule, value);
        case 'custom':
          return this.executeCustomValidation(rule, value, context);
        case 'dependency':
          return this.executeDependencyValidation(rule, value);
        case 'compatibility':
          return this.executeCompatibilityValidation(rule, value);
        default:
          return {
            isValid: true,
            errors: [],
            warnings: [`未知的验证规则类型: ${rule.type}`],
            suggestions: []
          };
      }
    } catch (error) {
      return {
        isValid: false,
        errors: [`验证规则执行失败: ${error instanceof Error ? error.message : String(error)}`],
        warnings: [],
        suggestions: []
      };
    }
  }

  /**
   * 执行Schema验证
   */
  private executeSchemaValidation(rule: ValidationRule, value: any): ValidationResult {
    try {
      const schema = rule.rule as z.ZodSchema;
      const result = schema.safeParse(value);

      if (result.success) {
        return {
          isValid: true,
          value: result.data,
          errors: [],
          warnings: [],
          suggestions: []
        };
      } else {
        return {
          isValid: false,
          errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
          warnings: [],
          suggestions: []
        };
      }
    } catch (error) {
      return {
        isValid: false,
        errors: [`Schema验证失败: ${error instanceof Error ? error.message : String(error)}`],
        warnings: [],
        suggestions: []
      };
    }
  }

  /**
   * 执行自定义验证
   */
  private async executeCustomValidation(
    rule: ValidationRule, 
    value: any, 
    context?: ValidationContext
  ): Promise<ValidationResult> {
    // 自定义验证逻辑的占位符
    return {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };
  }

  /**
   * 执行依赖验证
   */
  private async executeDependencyValidation(rule: ValidationRule, value: any): Promise<ValidationResult> {
    // 依赖验证逻辑的占位符
    return {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };
  }

  /**
   * 执行兼容性验证
   */
  private async executeCompatibilityValidation(rule: ValidationRule, value: any): Promise<ValidationResult> {
    // 兼容性验证逻辑的占位符
    return {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };
  }

  /**
   * 检查配置是否存在
   */
  private async configExists(key: string): Promise<boolean> {
    // 这里应该调用实际的配置管理器来检查配置是否存在
    // 暂时返回true作为占位符
    return true;
  }

  /**
   * 检测循环依赖
   */
  private detectCircularDependencies(key: string, visited: Set<string> = new Set(), path: string[] = []): string[][] {
    if (visited.has(key)) {
      const cycleStart = path.indexOf(key);
      if (cycleStart >= 0) {
        return [path.slice(cycleStart).concat(key)];
      }
      return [];
    }

    visited.add(key);
    path.push(key);

    const dependencies = this.dependencyGraph.get(key) || new Set();
    const cycles: string[][] = [];

    for (const dep of dependencies) {
      const depCycles = this.detectCircularDependencies(dep, new Set(visited), [...path]);
      cycles.push(...depCycles);
    }

    return cycles;
  }

  /**
   * 检查依赖冲突
   */
  private async checkDependencyConflicts(key: string, value: any): Promise<Array<{
    key: string;
    expectedValue: any;
    actualValue: any;
    reason: string;
  }>> {
    // 依赖冲突检查的占位符
    return [];
  }

  /**
   * 检查变更兼容性
   */
  private async checkChangeCompatibility(change: ConfigChange): Promise<Array<{
    change: ConfigChange;
    reason: string;
    severity: 'low' | 'medium' | 'high';
    suggestion?: string;
  }>> {
    const incompatibilities: Array<{
      change: ConfigChange;
      reason: string;
      severity: 'low' | 'medium' | 'high';
      suggestion?: string;
    }> = [];

    // 检查关键配置的变更
    if (this.isCriticalConfig(change.key)) {
      incompatibilities.push({
        change,
        reason: '关键配置变更可能影响系统稳定性',
        severity: 'high',
        suggestion: '建议在维护窗口期间进行变更'
      });
    }

    // 检查数据类型变更
    if (change.oldValue !== null && change.newValue !== null) {
      const oldType = typeof change.oldValue;
      const newType = typeof change.newValue;
      
      if (oldType !== newType) {
        incompatibilities.push({
          change,
          reason: `数据类型从 ${oldType} 变更为 ${newType}`,
          severity: 'medium',
          suggestion: '确保所有使用此配置的组件都支持新的数据类型'
        });
      }
    }

    return incompatibilities;
  }

  /**
   * 检查跨变更兼容性
   */
  private async checkCrossChangeCompatibility(changes: ConfigChange[]): Promise<Array<{
    change: ConfigChange;
    reason: string;
    severity: 'low' | 'medium' | 'high';
    suggestion?: string;
  }>> {
    // 跨变更兼容性检查的占位符
    return [];
  }

  /**
   * 检查是否为关键配置
   */
  private isCriticalConfig(key: string): boolean {
    const criticalConfigs = [
      'database.host',
      'database.port',
      'database.connectionString',
      'api.jwtSecret',
      'trading.riskLimit'
    ];

    return criticalConfigs.includes(key) || key.startsWith('security.');
  }
}
