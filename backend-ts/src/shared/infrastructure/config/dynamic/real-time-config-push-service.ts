/**
 * 实时配置推送服务实现
 * 支持WebSocket和SSE的实时配置推送机制
 */

import { injectable, inject } from 'inversify';
import { EventEmitter } from 'events';
import WebSocket from 'ws';
import { v4 as uuidv4 } from 'uuid';
import { IBasicLogger } from '../../logging/interfaces/basic-logger.interface';
import { UnifiedErrorHandler } from '../../error/unified-error-handler';
import { TYPES } from '../../di/types';
import {
  RealTimeConfigPushService,
  ConfigConnection,
  ConfigChange,
  ConnectionStatus
} from './dynamic-config.interface';

@injectable()
export class RealTimeConfigPushServiceImpl implements RealTimeConfigPushService {
  private readonly eventEmitter = new EventEmitter();
  private readonly connections: Map<string, ConfigConnection> = new Map();
  private readonly subscriptionPatterns: Map<string, Set<string>> = new Map(); // pattern -> clientIds
  private wsServer?: WebSocket.Server;
  private isRunning = false;
  private readonly heartbeatInterval = 30000; // 30秒
  private heartbeatTimer?: NodeJS.Timeout;

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler
  ) {
    this.setupEventListeners();
  }

  /**
   * 启动推送服务
   */
  async start(): Promise<void> {
    try {
      if (this.isRunning) {
        this.logger.warn('实时配置推送服务已在运行');
        return;
      }

      this.logger.info('启动实时配置推送服务...');

      // 启动WebSocket服务器
      await this.startWebSocketServer();

      // 启动心跳检测
      this.startHeartbeat();

      this.isRunning = true;
      this.logger.info('实时配置推送服务启动成功');
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'RealTimeConfigPushService.start'
      });
      throw error;
    }
  }

  /**
   * 停止推送服务
   */
  async stop(): Promise<void> {
    try {
      if (!this.isRunning) {
        this.logger.warn('实时配置推送服务未在运行');
        return;
      }

      this.logger.info('停止实时配置推送服务...');

      // 停止心跳检测
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = undefined;
      }

      // 关闭所有连接
      await this.closeAllConnections();

      // 关闭WebSocket服务器
      if (this.wsServer) {
        this.wsServer.close();
        this.wsServer = undefined;
      }

      this.isRunning = false;
      this.logger.info('实时配置推送服务已停止');
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'RealTimeConfigPushService.stop'
      });
      throw error;
    }
  }

  /**
   * 添加客户端连接
   */
  addClient(clientId: string, connection: ConfigConnection): void {
    try {
      this.connections.set(clientId, {
        ...connection,
        lastActivity: new Date()
      });

      // 注册订阅模式
      connection.subscriptions.forEach(pattern => {
        if (!this.subscriptionPatterns.has(pattern)) {
          this.subscriptionPatterns.set(pattern, new Set());
        }
        this.subscriptionPatterns.get(pattern)!.add(clientId);
      });

      this.logger.info('客户端连接已添加', { 
        clientId, 
        type: connection.type,
        subscriptions: connection.subscriptions.length 
      });

      this.eventEmitter.emit('clientConnected', { clientId, connection });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'RealTimeConfigPushService.addClient',
        clientId
      });
    }
  }

  /**
   * 移除客户端连接
   */
  removeClient(clientId: string): void {
    try {
      const connection = this.connections.get(clientId);
      if (!connection) {
        this.logger.warn('尝试移除不存在的客户端', { clientId });
        return;
      }

      // 从订阅模式中移除
      connection.subscriptions.forEach(pattern => {
        const clients = this.subscriptionPatterns.get(pattern);
        if (clients) {
          clients.delete(clientId);
          if (clients.size === 0) {
            this.subscriptionPatterns.delete(pattern);
          }
        }
      });

      // 关闭连接
      this.closeConnection(connection);

      this.connections.delete(clientId);

      this.logger.info('客户端连接已移除', { clientId });
      this.eventEmitter.emit('clientDisconnected', { clientId });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'RealTimeConfigPushService.removeClient',
        clientId
      });
    }
  }

  /**
   * 向指定客户端推送配置
   */
  async pushToClient(clientId: string, change: ConfigChange): Promise<void> {
    try {
      const connection = this.connections.get(clientId);
      if (!connection) {
        this.logger.warn('客户端不存在，无法推送配置', { clientId });
        return;
      }

      await this.sendToConnection(connection, change);
      
      // 更新最后活动时间
      connection.lastActivity = new Date();

      this.logger.debug('配置已推送到客户端', { clientId, configKey: change.key });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'RealTimeConfigPushService.pushToClient',
        clientId,
        configKey: change.key
      });
    }
  }

  /**
   * 向所有客户端广播配置变更
   */
  async broadcast(change: ConfigChange): Promise<void> {
    try {
      const promises: Promise<void>[] = [];

      for (const [clientId, connection] of this.connections) {
        promises.push(
          this.sendToConnection(connection, change).catch(error => {
            this.logger.error('向客户端广播失败', { clientId, error });
          })
        );
      }

      await Promise.allSettled(promises);

      this.logger.info('配置变更已广播', { 
        configKey: change.key, 
        clientCount: this.connections.size 
      });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'RealTimeConfigPushService.broadcast',
        configKey: change.key
      });
    }
  }

  /**
   * 向匹配模式的客户端推送配置
   */
  async pushToPattern(pattern: string, change: ConfigChange): Promise<void> {
    try {
      const clientIds = this.subscriptionPatterns.get(pattern);
      if (!clientIds || clientIds.size === 0) {
        this.logger.debug('没有客户端订阅此模式', { pattern });
        return;
      }

      const promises: Promise<void>[] = [];

      for (const clientId of clientIds) {
        const connection = this.connections.get(clientId);
        if (connection) {
          promises.push(
            this.sendToConnection(connection, change).catch(error => {
              this.logger.error('向模式订阅客户端推送失败', { clientId, pattern, error });
            })
          );
        }
      }

      await Promise.allSettled(promises);

      this.logger.info('配置变更已推送到模式订阅客户端', { 
        pattern, 
        configKey: change.key, 
        clientCount: clientIds.size 
      });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'RealTimeConfigPushService.pushToPattern',
        pattern,
        configKey: change.key
      });
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): ConnectionStatus {
    const connectionsByType: Record<string, number> = {};
    let totalSubscriptions = 0;

    for (const connection of this.connections.values()) {
      connectionsByType[connection.type] = (connectionsByType[connection.type] || 0) + 1;
      totalSubscriptions += connection.subscriptions.length;
    }

    return {
      totalConnections: this.connections.size,
      activeConnections: this.getActiveConnectionsCount(),
      connectionsByType,
      totalSubscriptions,
      averageSubscriptionsPerConnection: this.connections.size > 0 ? 
        totalSubscriptions / this.connections.size : 0
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.eventEmitter.on('clientConnected', (event) => {
      this.logger.debug('客户端连接事件', event);
    });

    this.eventEmitter.on('clientDisconnected', (event) => {
      this.logger.debug('客户端断开事件', event);
    });
  }

  /**
   * 启动WebSocket服务器
   */
  private async startWebSocketServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.wsServer = new WebSocket.Server({ 
          port: 8081, // 配置端口
          path: '/config-updates'
        });

        this.wsServer.on('connection', (ws, req) => {
          const clientId = uuidv4();
          const url = new URL(req.url!, `http://${req.headers.host}`);
          const subscriptions = url.searchParams.get('subscriptions')?.split(',') || [];

          const connection: ConfigConnection = {
            id: clientId,
            type: 'websocket',
            connection: ws,
            subscriptions,
            lastActivity: new Date(),
            metadata: {
              userAgent: req.headers['user-agent'],
              ip: req.socket.remoteAddress
            }
          };

          this.addClient(clientId, connection);

          ws.on('message', (message) => {
            try {
              const data = JSON.parse(message.toString());
              this.handleClientMessage(clientId, data);
            } catch (error) {
              this.logger.error('解析客户端消息失败', { clientId, error });
            }
          });

          ws.on('close', () => {
            this.removeClient(clientId);
          });

          ws.on('error', (error) => {
            this.logger.error('WebSocket连接错误', { clientId, error });
            this.removeClient(clientId);
          });

          // 发送连接确认
          ws.send(JSON.stringify({
            type: 'connection_established',
            clientId,
            timestamp: new Date().toISOString()
          }));
        });

        this.wsServer.on('listening', () => {
          this.logger.info('WebSocket服务器已启动', { port: 8081 });
          resolve();
        });

        this.wsServer.on('error', (error) => {
          this.logger.error('WebSocket服务器错误', { error });
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 处理客户端消息
   */
  private handleClientMessage(clientId: string, data: any): void {
    try {
      switch (data.type) {
        case 'subscribe':
          this.handleSubscribe(clientId, data.pattern);
          break;
        case 'unsubscribe':
          this.handleUnsubscribe(clientId, data.pattern);
          break;
        case 'ping':
          this.handlePing(clientId);
          break;
        default:
          this.logger.warn('未知的客户端消息类型', { clientId, type: data.type });
      }
    } catch (error) {
      this.logger.error('处理客户端消息失败', { clientId, error });
    }
  }

  /**
   * 处理订阅请求
   */
  private handleSubscribe(clientId: string, pattern: string): void {
    const connection = this.connections.get(clientId);
    if (!connection) return;

    if (!connection.subscriptions.includes(pattern)) {
      connection.subscriptions.push(pattern);
      
      if (!this.subscriptionPatterns.has(pattern)) {
        this.subscriptionPatterns.set(pattern, new Set());
      }
      this.subscriptionPatterns.get(pattern)!.add(clientId);

      this.logger.debug('客户端订阅模式', { clientId, pattern });
    }
  }

  /**
   * 处理取消订阅请求
   */
  private handleUnsubscribe(clientId: string, pattern: string): void {
    const connection = this.connections.get(clientId);
    if (!connection) return;

    const index = connection.subscriptions.indexOf(pattern);
    if (index > -1) {
      connection.subscriptions.splice(index, 1);

      const clients = this.subscriptionPatterns.get(pattern);
      if (clients) {
        clients.delete(clientId);
        if (clients.size === 0) {
          this.subscriptionPatterns.delete(pattern);
        }
      }

      this.logger.debug('客户端取消订阅模式', { clientId, pattern });
    }
  }

  /**
   * 处理心跳请求
   */
  private handlePing(clientId: string): void {
    const connection = this.connections.get(clientId);
    if (!connection) return;

    connection.lastActivity = new Date();

    if (connection.type === 'websocket') {
      const ws = connection.connection as WebSocket;
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'pong',
          timestamp: new Date().toISOString()
        }));
      }
    }
  }

  /**
   * 向连接发送消息
   */
  private async sendToConnection(connection: ConfigConnection, change: ConfigChange): Promise<void> {
    try {
      const message = {
        type: 'config_change',
        data: change,
        timestamp: new Date().toISOString()
      };

      switch (connection.type) {
        case 'websocket':
          const ws = connection.connection as WebSocket;
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
          }
          break;
        case 'sse':
          // SSE实现
          const res = connection.connection;
          res.write(`data: ${JSON.stringify(message)}\n\n`);
          break;
        default:
          this.logger.warn('不支持的连接类型', { type: connection.type });
      }
    } catch (error) {
      this.logger.error('发送消息到连接失败', { 
        connectionId: connection.id, 
        error 
      });
      throw error;
    }
  }

  /**
   * 关闭连接
   */
  private closeConnection(connection: ConfigConnection): void {
    try {
      switch (connection.type) {
        case 'websocket':
          const ws = connection.connection as WebSocket;
          if (ws.readyState === WebSocket.OPEN) {
            ws.close();
          }
          break;
        case 'sse':
          const res = connection.connection;
          if (!res.destroyed) {
            res.end();
          }
          break;
      }
    } catch (error) {
      this.logger.error('关闭连接失败', { 
        connectionId: connection.id, 
        error 
      });
    }
  }

  /**
   * 关闭所有连接
   */
  private async closeAllConnections(): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const connection of this.connections.values()) {
      promises.push(
        new Promise<void>((resolve) => {
          try {
            this.closeConnection(connection);
          } catch (error) {
            this.logger.error('关闭连接失败', { error });
          } finally {
            resolve();
          }
        })
      );
    }

    await Promise.allSettled(promises);
    this.connections.clear();
    this.subscriptionPatterns.clear();
  }

  /**
   * 启动心跳检测
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      this.performHeartbeatCheck();
    }, this.heartbeatInterval);
  }

  /**
   * 执行心跳检测
   */
  private performHeartbeatCheck(): void {
    const now = Date.now();
    const timeout = 30000; // 30秒超时

    const expiredClients: string[] = [];

    for (const [clientId, connection] of this.connections) {
      if (now - connection.lastActivity.getTime() > timeout) {
        expiredClients.push(clientId);
      }
    }

    // 移除过期连接
    expiredClients.forEach(clientId => {
      this.logger.info('移除过期连接', { clientId });
      this.removeClient(clientId);
    });

    if (expiredClients.length > 0) {
      this.logger.info('心跳检测完成', { 
        expiredConnections: expiredClients.length,
        activeConnections: this.connections.size 
      });
    }
  }

  /**
   * 获取活跃连接数量
   */
  private getActiveConnectionsCount(): number {
    let activeCount = 0;
    for (const connection of this.connections.values()) {
      // 检查连接是否活跃（最近5分钟内有活动）
      const lastActivity = connection.lastActivity;
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;

      if (lastActivity.getTime() > fiveMinutesAgo) {
        activeCount++;
      }
    }
    return activeCount;
  }
}
