/**
 * 配置版本管理器实现
 * 提供配置的版本控制和回滚机制
 */

import { injectable, inject } from 'inversify';
import { v4 as uuidv4 } from 'uuid';
import { PrismaClient } from '@prisma/client';
import { IBasicLogger } from '../../logging/interfaces/index';
import { UnifiedErrorHandler } from '../../error/unified-error-handler';
import { MultiTierCacheService } from '../../ai/multi-tier-cache-service';
import { TYPES } from '../../di/types';
import {
  ConfigVersionManager,
  ConfigSnapshot,
  ConfigHistory,
  RollbackResult,
  VersionComparison,
  VersionDetails,
  CleanupResult
} from './dynamic-config.interface';

@injectable()
export class ConfigVersionManagerImpl implements ConfigVersionManager {
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24小时
  private readonly CACHE_PREFIX = 'config:version:';
  private currentVersion: string = '1.0.0';

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler,
    @inject(TYPES.Shared.MultiTierCacheService) private readonly cache: MultiTierCacheService,
    @inject(TYPES.Shared.Database) private readonly prisma: PrismaClient
  ) {}

  /**
   * 创建配置快照
   */
  async createSnapshot(description?: string): Promise<ConfigSnapshot> {
    try {
      this.logger.info('创建配置快照', { description });

      // 获取当前所有配置
      const currentConfigs = await this.getCurrentConfigs();

      const snapshot: ConfigSnapshot = {
        id: uuidv4(),
        description: description || `自动快照 - ${new Date().toISOString()}`,
        createdAt: new Date(),
        createdBy: 'system', // 可以从上下文获取用户信息
        configs: currentConfigs,
        metadata: {
          configCount: Object.keys(currentConfigs).length,
          createdBy: 'system'
        }
      };

      // 保存到数据库
      await this.saveSnapshotToDatabase(snapshot);

      // 缓存快照
      await this.cache.set(
        `${this.CACHE_PREFIX}snapshot:${snapshot.id}`,
        snapshot,
        { ttl: this.CACHE_TTL }
      );

      this.logger.info('配置快照创建完成', { 
        snapshotId: snapshot.id,
        configCount: Object.keys(currentConfigs).length
      });

      return snapshot;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ConfigVersionManager.createSnapshot',
        description
      });
      throw error;
    }
  }

  /**
   * 获取配置历史
   */
  async getHistory(key?: string, limit: number = 10): Promise<ConfigHistory[]> {
    return this.getConfigHistory(key, limit);
  }

  /**
   * 获取配置历史（内部实现）
   */
  async getConfigHistory(key?: string, limit: number = 10): Promise<any[]> {
    try {
      this.logger.debug('获取配置历史', { key, limit });

      // 尝试从缓存获取
      const cacheKey = `${this.CACHE_PREFIX}history:${key || 'all'}:${limit}`;
      let history: any[] = await this.cache.get(cacheKey) || [];

      if (history.length === 0) {
        // 从数据库获取
        history = await this.getHistoryFromDatabase(key, limit);
        
        // 缓存结果
        await this.cache.set(cacheKey, history, { ttl: this.CACHE_TTL });
      }

      this.logger.debug('配置历史获取完成', { 
        key, 
        historyCount: history.length 
      });

      return history;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ConfigVersionManager.getHistory',
        configKey: key,
        limit
      });
      throw error;
    }
  }

  /**
   * 回滚到指定版本
   */
  async rollbackToVersion(versionId: string): Promise<RollbackResult> {
    try {
      this.logger.info('开始回滚到指定版本', { versionId });

      const result: RollbackResult = {
        success: false,
        rolledBackConfigs: [],
        failedConfigs: [],
        versionId,
        timestamp: new Date()
      };

      // 获取版本详情
      const versionDetails = await this.getVersionDetails(versionId);
      if (!versionDetails) {
        throw new Error(`版本不存在: ${versionId}`);
      }

      // 创建回滚前的快照
      const preRollbackSnapshot = await this.createSnapshot(`回滚前快照 - ${versionId}`);

      // 执行回滚
      const rollbackPromises = Object.entries(versionDetails.configs).map(async ([key, value]) => {
        try {
          await this.rollbackSingleConfig(key, value);
          result.rolledBackConfigs.push(key);
          return { key, success: true };
        } catch (error) {
          result.failedConfigs.push({
            key,
            error: error instanceof Error ? error.message : String(error)
          });
          return { key, success: false, error };
        }
      });

      const rollbackResults = await Promise.allSettled(rollbackPromises);

      result.success = result.failedConfigs.length === 0;

      // 记录回滚历史
      await this.recordRollbackHistory(versionId, result, preRollbackSnapshot.id);

      this.logger.info('版本回滚完成', {
        versionId,
        success: result.success,
        rolledBackCount: result.rolledBackConfigs.length,
        failedCount: result.failedConfigs.length
      });

      return result;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ConfigVersionManager.rollbackToVersion',
        versionId
      });
      throw error;
    }
  }

  /**
   * 回滚到指定快照
   */
  async rollbackToSnapshot(snapshotId: string): Promise<RollbackResult> {
    try {
      this.logger.info('开始回滚到指定快照', { snapshotId });

      const result: RollbackResult = {
        success: false,
        rolledBackConfigs: [],
        failedConfigs: [],
        snapshotId,
        timestamp: new Date()
      };

      // 获取快照
      const snapshot = await this.getSnapshot(snapshotId);
      if (!snapshot) {
        throw new Error(`快照不存在: ${snapshotId}`);
      }

      // 创建回滚前的快照
      const preRollbackSnapshot = await this.createSnapshot(`回滚前快照 - ${snapshotId}`);

      // 执行回滚
      const rollbackPromises = Object.entries(snapshot.configs).map(async ([key, value]) => {
        try {
          await this.rollbackSingleConfig(key, value);
          result.rolledBackConfigs.push(key);
          return { key, success: true };
        } catch (error) {
          result.failedConfigs.push({
            key,
            error: error instanceof Error ? error.message : String(error)
          });
          return { key, success: false, error };
        }
      });

      await Promise.allSettled(rollbackPromises);

      result.success = result.failedConfigs.length === 0;

      // 记录回滚历史
      await this.recordSnapshotRollbackHistory(snapshotId, result, preRollbackSnapshot.id);

      this.logger.info('快照回滚完成', {
        snapshotId,
        success: result.success,
        rolledBackCount: result.rolledBackConfigs.length,
        failedCount: result.failedConfigs.length
      });

      return result;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ConfigVersionManager.rollbackToSnapshot',
        snapshotId
      });
      throw error;
    }
  }

  /**
   * 比较两个版本
   */
  async compareVersions(versionA: string, versionB: string): Promise<VersionComparison> {
    try {
      this.logger.debug('比较版本', { versionA, versionB });

      const [detailsA, detailsB] = await Promise.all([
        this.getVersionDetails(versionA),
        this.getVersionDetails(versionB)
      ]);

      if (!detailsA || !detailsB) {
        throw new Error('版本不存在');
      }

      const comparison: VersionComparison = {
        versionA,
        versionB,
        differences: [],
        summary: {
          totalChanges: 0,
          addedConfigs: 0,
          removedConfigs: 0,
          modifiedConfigs: 0
        }
      };

      // 获取所有配置键
      const allKeys = new Set([
        ...Object.keys(detailsA.configs),
        ...Object.keys(detailsB.configs)
      ]);

      // 比较每个配置
      for (const key of allKeys) {
        const valueA = detailsA.configs[key];
        const valueB = detailsB.configs[key];

        if (valueA === undefined && valueB !== undefined) {
          // 在B中添加的配置
          comparison.differences.push({
            key,
            valueA: undefined,
            valueB,
            changeType: 'added'
          });
          comparison.summary.addedConfigs++;
        } else if (valueA !== undefined && valueB === undefined) {
          // 在B中移除的配置
          comparison.differences.push({
            key,
            valueA,
            valueB: undefined,
            changeType: 'removed'
          });
          comparison.summary.removedConfigs++;
        } else if (JSON.stringify(valueA) !== JSON.stringify(valueB)) {
          // 修改的配置
          comparison.differences.push({
            key,
            valueA,
            valueB,
            changeType: 'modified'
          });
          comparison.summary.modifiedConfigs++;
        }
      }

      comparison.summary.totalChanges = comparison.differences.length;

      this.logger.debug('版本比较完成', {
        versionA,
        versionB,
        totalChanges: comparison.summary.totalChanges
      });

      return comparison;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ConfigVersionManager.compareVersions',
        versionA,
        versionB
      });
      throw error;
    }
  }

  /**
   * 获取版本详情
   */
  async getVersionDetails(versionId: string): Promise<VersionDetails | null> {
    try {
      this.logger.debug('获取版本详情', { versionId });

      // 尝试从缓存获取
      const cacheKey = `${this.CACHE_PREFIX}version:${versionId}`;
      let details: VersionDetails | null = await this.cache.get(cacheKey);

      if (!details) {
        // 从数据库获取
        details = await this.getVersionDetailsFromDatabase(versionId);
        
        if (details) {
          // 缓存版本详情
        await this.cache.set(cacheKey, details, { ttl: this.CACHE_TTL });
        }
      }

      return details;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ConfigVersionManager.getVersionDetails',
        versionId
      });
      return null;
    }
  }

  /**
   * 清理旧版本
   */
  async cleanupOldVersions(retentionDays: number): Promise<CleanupResult> {
    try {
      this.logger.info('开始清理旧版本', { retentionDays });

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const result: CleanupResult = {
        cleanedVersions: 0,
        cleanedSnapshots: 0,
        freedSpace: 0,
        retentionDays,
        timestamp: new Date()
      };

      // 清理旧的配置历史
      const cleanedVersions = await this.cleanupOldVersionsFromDatabase(cutoffDate);
      result.cleanedVersions = cleanedVersions;

      // 清理旧的快照
      const cleanedSnapshots = await this.cleanupOldSnapshotsFromDatabase(cutoffDate);
      result.cleanedSnapshots = cleanedSnapshots;

      // 清理缓存
      await this.cleanupVersionCache();

      this.logger.info('旧版本清理完成', result);

      return result;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ConfigVersionManager.cleanupOldVersions',
        retentionDays
      });
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 获取当前所有配置
   */
  private async getCurrentConfigs(): Promise<Record<string, any>> {
    try {
      const configs = await this.prisma.configItems.findMany({
        select: {
          key: true,
          value: true
        }
      });

      const result: Record<string, any> = {};
      for (const config of configs) {
        try {
          result[config.key] = JSON.parse(config.value);
        } catch (error) {
          result[config.key] = config.value;
        }
      }

      return result;
    } catch (error) {
      this.logger.error('获取当前配置失败', { error });
      return {};
    }
  }

  /**
   * 保存快照到数据库
   */
  private async saveSnapshotToDatabase(snapshot: ConfigSnapshot): Promise<void> {
    try {
      await this.prisma.configSnapshots.create({
        data: {
          id: snapshot.id,
          description: snapshot.description,
          createdAt: snapshot.createdAt,
          createdBy: snapshot.createdBy || 'system',
          configs: JSON.stringify(snapshot.configs),
          metadata: JSON.stringify(snapshot.metadata || {})
        }
      });
    } catch (error) {
      this.logger.error('保存快照到数据库失败', { 
        snapshotId: snapshot.id, 
        error 
      });
      throw error;
    }
  }

  /**
   * 从数据库获取历史记录
   */
  private async getHistoryFromDatabase(key?: string, limit: number = 10): Promise<any[]> {
    try {
      const whereClause = key ? { key } : {};
      
      const histories = await this.prisma.configHistory.findMany({
        where: whereClause,
        orderBy: { changedAt: 'desc' },
        take: limit
      });

      return histories.map(history => ({
        id: history.id,
        key: history.key,
        oldValue: this.parseJsonSafely(history.oldValue),
        newValue: this.parseJsonSafely(history.newValue),
        changeType: history.changeType as any,
        changedAt: history.changedAt,
        changedBy: history.changedBy || undefined,
        source: 'system', // 默认来源
        reason: undefined, // 数据库中没有reason字段
        version: 1 // 默认版本号
      }));
    } catch (error) {
      this.logger.error('从数据库获取历史记录失败', { key, error });
      return [];
    }
  }

  /**
   * 获取快照
   */
  private async getSnapshot(snapshotId: string): Promise<ConfigSnapshot | null> {
    try {
      // 尝试从缓存获取
      const cacheKey = `${this.CACHE_PREFIX}snapshot:${snapshotId}`;
      let snapshot: ConfigSnapshot | null = await this.cache.get(cacheKey);

      if (!snapshot) {
        // 从数据库获取
        const snapshotData = await this.prisma.configSnapshots.findUnique({
          where: { id: snapshotId }
        });

        if (snapshotData) {
          snapshot = {
            id: snapshotData.id,
            description: snapshotData.description,
            createdAt: snapshotData.createdAt,
            createdBy: snapshotData.createdBy || undefined,
            configs: JSON.parse(snapshotData.configs),
            metadata: JSON.parse(snapshotData.metadata || '{}')
          };

          // 缓存结果
          await this.cache.set(cacheKey, snapshot, { ttl: this.CACHE_TTL });
        }
      }

      return snapshot;
    } catch (error) {
      this.logger.error('获取快照失败', { snapshotId, error });
      return null;
    }
  }

  /**
   * 回滚单个配置
   */
  private async rollbackSingleConfig(key: string, value: any): Promise<void> {
    try {
      await this.prisma.configItems.upsert({
        where: { key },
        update: {
          value: JSON.stringify(value),
          lastUpdated: new Date()
        },
        create: {
          key,
          value: JSON.stringify(value),
          type: 'string', // 简化类型处理
          description: `回滚的配置项: ${key}`,
          lastUpdated: new Date(),
          version: 1
        }
      });

      // 清除相关缓存
      await this.cache.delete(`config:${key}`);
    } catch (error) {
      this.logger.error('回滚单个配置失败', { key, error });
      throw error;
    }
  }

  /**
   * 记录回滚历史
   */
  private async recordRollbackHistory(
    versionId: string, 
    result: RollbackResult, 
    preRollbackSnapshotId: string
  ): Promise<void> {
    try {
      await this.prisma.configRollbackHistory.create({
        data: {
          configKey: `version_${versionId}`,
          fromValue: preRollbackSnapshotId,
          toValue: versionId,
          rolledBackBy: 'system',
          reason: `Rollback operation: ${result.success ? 'success' : 'failed'}`
        }
      });
    } catch (error) {
      this.logger.error('记录回滚历史失败', { versionId, error });
    }
  }

  /**
   * 记录快照回滚历史
   */
  private async recordSnapshotRollbackHistory(
    snapshotId: string, 
    result: RollbackResult, 
    preRollbackSnapshotId: string
  ): Promise<void> {
    try {
      await this.prisma.configSnapshotRollbackHistory.create({
        data: {
          snapshotId: snapshotId,
          rolledBackBy: 'system',
          reason: `Snapshot rollback operation: ${result.success ? 'success' : 'failed'}`
        }
      });
    } catch (error) {
      this.logger.error('记录快照回滚历史失败', { snapshotId, error });
    }
  }

  /**
   * 从数据库获取版本详情
   */
  private async getVersionDetailsFromDatabase(versionId: string): Promise<VersionDetails | null> {
    try {
      // 这里需要根据实际的版本存储结构来实现
      // 暂时返回null作为占位符
      return null;
    } catch (error) {
      this.logger.error('从数据库获取版本详情失败', { versionId, error });
      return null;
    }
  }

  /**
   * 从数据库清理旧版本
   */
  private async cleanupOldVersionsFromDatabase(cutoffDate: Date): Promise<number> {
    try {
      const result = await this.prisma.configHistory.deleteMany({
        where: {
          changedAt: {
            lt: cutoffDate
          }
        }
      });

      return result.count;
    } catch (error) {
      this.logger.error('清理旧版本失败', { cutoffDate, error });
      return 0;
    }
  }

  /**
   * 从数据库清理旧快照
   */
  private async cleanupOldSnapshotsFromDatabase(cutoffDate: Date): Promise<number> {
    try {
      const result = await this.prisma.configSnapshots.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        }
      });

      return result.count;
    } catch (error) {
      this.logger.error('清理旧快照失败', { cutoffDate, error });
      return 0;
    }
  }

  /**
   * 清理版本缓存
   */
  private async cleanupVersionCache(): Promise<void> {
    try {
      // 由于MultiTierCacheService没有deletePattern方法，我们暂时跳过缓存清理
      // 或者可以实现一个简单的清理逻辑
      this.logger.debug('版本缓存清理已跳过 - MultiTierCacheService不支持模式删除');
    } catch (error) {
      this.logger.error('清理版本缓存失败', { error });
    }
  }

  /**
   * 安全解析JSON
   */
  private parseJsonSafely(value: string | null): any {
    if (!value) return null;
    
    try {
      return JSON.parse(value);
    } catch (error) {
      return value;
    }
  }
}
