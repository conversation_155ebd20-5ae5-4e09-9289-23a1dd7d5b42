/**
 * 动态配置管理接口
 * 提供实时配置推送、配置订阅、配置验证等功能
 */

/**
 * 动态配置管理器接口
 */
export interface DynamicConfigManager {
  /**
   * 订阅配置变更
   */
  subscribe(pattern: string, callback: ConfigChangeCallback): ConfigSubscription;

  /**
   * 取消订阅
   */
  unsubscribe(subscription: ConfigSubscription): void;

  /**
   * 推送配置变更
   */
  pushConfigChange(change: ConfigChange): Promise<void>;

  /**
   * 批量推送配置变更
   */
  pushConfigChanges(changes: ConfigChange[]): Promise<void>;

  /**
   * 获取活跃订阅数量
   */
  getActiveSubscriptions(): number;

  /**
   * 获取订阅统计信息
   */
  getSubscriptionStats(): SubscriptionStats;
}

/**
 * 实时配置推送服务接口
 */
export interface RealTimeConfigPushService {
  /**
   * 启动推送服务
   */
  start(): Promise<void>;

  /**
   * 停止推送服务
   */
  stop(): Promise<void>;

  /**
   * 添加客户端连接
   */
  addClient(clientId: string, connection: ConfigConnection): void;

  /**
   * 移除客户端连接
   */
  removeClient(clientId: string): void;

  /**
   * 向指定客户端推送配置
   */
  pushToClient(clientId: string, change: ConfigChange): Promise<void>;

  /**
   * 向所有客户端广播配置变更
   */
  broadcast(change: ConfigChange): Promise<void>;

  /**
   * 向匹配模式的客户端推送配置
   */
  pushToPattern(pattern: string, change: ConfigChange): Promise<void>;

  /**
   * 获取连接状态
   */
  getConnectionStatus(): ConnectionStatus;
}

/**
 * 增强配置验证器接口
 */
export interface EnhancedConfigValidator {
  /**
   * 验证配置值
   */
  validate(key: string, value: any, context?: ValidationContext): Promise<ValidationResult>;

  /**
   * 批量验证配置
   */
  validateBatch(configs: ConfigItem[]): Promise<BatchValidationResult>;

  /**
   * 验证配置依赖
   */
  validateDependencies(key: string, value: any): Promise<DependencyValidationResult>;

  /**
   * 验证配置兼容性
   */
  validateCompatibility(changes: ConfigChange[]): Promise<CompatibilityValidationResult>;

  /**
   * 注册自定义验证规则
   */
  registerValidator(key: string, validator: CustomValidator): void;

  /**
   * 获取验证规则
   */
  getValidationRules(key: string): ValidationRule[];
}

/**
 * 配置版本管理器接口
 */
export interface ConfigVersionManager {
  /**
   * 创建配置快照
   */
  createSnapshot(description?: string): Promise<ConfigSnapshot>;

  /**
   * 获取配置历史
   */
  getHistory(key?: string, limit?: number): Promise<ConfigHistory[]>;

  /**
   * 回滚到指定版本
   */
  rollbackToVersion(versionId: string): Promise<RollbackResult>;

  /**
   * 回滚到指定快照
   */
  rollbackToSnapshot(snapshotId: string): Promise<RollbackResult>;

  /**
   * 比较两个版本
   */
  compareVersions(versionA: string, versionB: string): Promise<VersionComparison>;

  /**
   * 获取版本详情
   */
  getVersionDetails(versionId: string): Promise<VersionDetails>;

  /**
   * 清理旧版本
   */
  cleanupOldVersions(retentionDays: number): Promise<CleanupResult>;
}

/**
 * 配置监控器接口
 */
export interface ConfigMonitor {
  /**
   * 开始监控
   */
  startMonitoring(): void;

  /**
   * 停止监控
   */
  stopMonitoring(): void;

  /**
   * 获取监控指标
   */
  getMetrics(): ConfigMetrics;

  /**
   * 获取配置使用统计
   */
  getUsageStats(): ConfigUsageStats;

  /**
   * 检查配置健康状态
   */
  checkHealth(): Promise<ConfigHealthStatus>;
}

// ==================== 数据结构定义 ====================

/**
 * 配置变更回调函数
 */
export type ConfigChangeCallback = (change: ConfigChange) => void | Promise<void>;

/**
 * 配置订阅
 */
export interface ConfigSubscription {
  id: string;
  pattern: string;
  callback: ConfigChangeCallback;
  createdAt: Date;
  lastTriggered?: Date;
  triggerCount: number;
  active: boolean;
}

/**
 * 配置变更
 */
export interface ConfigChange {
  id: string;
  key: string;
  oldValue: any;
  newValue: any;
  changeType: 'create' | 'update' | 'delete';
  source: string;
  userId?: string;
  timestamp: Date;
  reason?: string;
  metadata?: Record<string, any>;
}

/**
 * 订阅统计信息
 */
export interface SubscriptionStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  inactiveSubscriptions: number;
  subscriptionsByPattern: Record<string, number>;
  totalTriggers: number;
  averageTriggersPerSubscription: number;
}

/**
 * 配置连接
 */
export interface ConfigConnection {
  id: string;
  type: 'websocket' | 'sse' | 'polling';
  connection: any; // WebSocket | Response | etc.
  subscriptions: string[];
  lastActivity: Date;
  metadata?: Record<string, any>;
}

/**
 * 连接状态
 */
export interface ConnectionStatus {
  totalConnections: number;
  activeConnections: number;
  connectionsByType: Record<string, number>;
  totalSubscriptions: number;
  averageSubscriptionsPerConnection: number;
}

/**
 * 验证上下文
 */
export interface ValidationContext {
  userId?: string;
  source?: string;
  environment?: string;
  dependencies?: Record<string, any>;
  metadata?: Record<string, any>;
}

/**
 * 验证结果
 */
export interface ValidationResult {
  isValid: boolean;
  value?: any;
  errors: string[];
  warnings: string[];
  suggestions: string[];
  metadata?: Record<string, any>;
}

/**
 * 批量验证结果
 */
export interface BatchValidationResult {
  overallValid: boolean;
  results: Record<string, ValidationResult>;
  summary: {
    totalConfigs: number;
    validConfigs: number;
    invalidConfigs: number;
    warningConfigs: number;
  };
}

/**
 * 依赖验证结果
 */
export interface DependencyValidationResult {
  isValid: boolean;
  missingDependencies: string[];
  conflictingDependencies: Array<{
    key: string;
    expectedValue: any;
    actualValue: any;
    reason: string;
  }>;
  circularDependencies: string[][];
}

/**
 * 兼容性验证结果
 */
export interface CompatibilityValidationResult {
  isCompatible: boolean;
  incompatibleChanges: Array<{
    change: ConfigChange;
    reason: string;
    severity: 'low' | 'medium' | 'high';
    suggestion?: string;
  }>;
  warnings: string[];
}

/**
 * 自定义验证器
 */
export interface CustomValidator {
  name: string;
  description: string;
  validate: (value: any, context?: ValidationContext) => Promise<ValidationResult> | ValidationResult;
  priority?: number;
}

/**
 * 验证规则
 */
export interface ValidationRule {
  name: string;
  description: string;
  type: 'schema' | 'custom' | 'dependency' | 'compatibility';
  rule: any;
  priority: number;
  enabled: boolean;
}

/**
 * 配置快照
 */
export interface ConfigSnapshot {
  id: string;
  description: string;
  createdAt: Date;
  createdBy?: string;
  configs: Record<string, any>;
  metadata?: Record<string, any>;
}

/**
 * 配置历史
 */
export interface ConfigHistory {
  id: string;
  key: string;
  oldValue: any;
  newValue: any;
  changeType: 'create' | 'update' | 'delete';
  changedAt: Date;
  changedBy?: string;
  source: string;
  reason?: string;
  version: number;
}

/**
 * 回滚结果
 */
export interface RollbackResult {
  success: boolean;
  rolledBackConfigs: string[];
  failedConfigs: Array<{
    key: string;
    error: string;
  }>;
  snapshotId?: string;
  versionId?: string;
  timestamp: Date;
}

/**
 * 版本比较
 */
export interface VersionComparison {
  versionA: string;
  versionB: string;
  differences: Array<{
    key: string;
    valueA: any;
    valueB: any;
    changeType: 'added' | 'removed' | 'modified';
  }>;
  summary: {
    totalChanges: number;
    addedConfigs: number;
    removedConfigs: number;
    modifiedConfigs: number;
  };
}

/**
 * 版本详情
 */
export interface VersionDetails {
  versionId: string;
  createdAt: Date;
  createdBy?: string;
  description?: string;
  configs: Record<string, any>;
  changes: ConfigHistory[];
  metadata?: Record<string, any>;
}

/**
 * 清理结果
 */
export interface CleanupResult {
  cleanedVersions: number;
  cleanedSnapshots: number;
  freedSpace: number; // bytes
  retentionDays: number;
  timestamp: Date;
}

/**
 * 配置指标
 */
export interface ConfigMetrics {
  totalConfigs: number;
  changesInLastHour: number;
  changesInLastDay: number;
  changesInLastWeek: number;
  mostChangedConfigs: Array<{
    key: string;
    changeCount: number;
  }>;
  averageChangeFrequency: number;
  errorRate: number;
  validationFailureRate: number;
}

/**
 * 配置使用统计
 */
export interface ConfigUsageStats {
  mostAccessedConfigs: Array<{
    key: string;
    accessCount: number;
    lastAccessed: Date;
  }>;
  leastAccessedConfigs: Array<{
    key: string;
    accessCount: number;
    lastAccessed: Date;
  }>;
  configsByCategory: Record<string, number>;
  accessPatterns: Array<{
    pattern: string;
    frequency: number;
  }>;
}

/**
 * 配置健康状态
 */
export interface ConfigHealthStatus {
  overall: 'healthy' | 'warning' | 'critical';
  checks: Array<{
    name: string;
    status: 'pass' | 'warning' | 'fail';
    message: string;
    details?: any;
  }>;
  recommendations: string[];
  lastChecked: Date;
}

/**
 * 配置项
 */
export interface ConfigItem {
  key: string;
  value: any;
  metadata?: Record<string, any>;
}
