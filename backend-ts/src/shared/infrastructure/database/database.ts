import { PrismaClient } from '@prisma/client';
import { getEnvironmentManager } from '../config/config-validation';
import { getLogger } from '../../../config/logging';

/**
 * 全局唯一的PrismaClient实例
 * 严禁在项目中任何地方使用 new PrismaClient()
 * 必须通过 getGlobalPrismaClient() 获取实例
 */
let GLOBAL_PRISMA_CLIENT: PrismaClient | null = null;

/**
 * 获取全局唯一的PrismaClient实例
 * 这是整个应用程序访问数据库的唯一合法方式
 *
 * @returns {PrismaClient} 全局唯一的PrismaClient实例
 */
export function getGlobalPrismaClient(): PrismaClient {
  if (!GLOBAL_PRISMA_CLIENT) {
    const envManager = getEnvironmentManager();
    const dbConfig = envManager.getDatabaseConfig();

    // 构建优化的数据库URL，包含连接池配置
    const optimizedUrl = buildOptimizedDatabaseUrl(dbConfig);

    GLOBAL_PRISMA_CLIENT = new PrismaClient({
      log: envManager.isDevelopment()
        ? ['info', 'warn', 'error']  // 移除 'query' 日志，减少冗长输出
        : ['error'],
      datasources: {
        db: {
          url: optimizedUrl,
        },
      },
    });

    console.log('✅ 全局唯一PrismaClient实例已创建，应用性能优化配置');
    console.log(`   连接池大小: ${dbConfig.connectionPoolSize || 20}`);
    console.log(`   查询超时: ${dbConfig.queryTimeout || 10000}ms`);
  }

  return GLOBAL_PRISMA_CLIENT;
}

/**
 * 构建优化的数据库URL，包含性能配置参数
 */
function buildOptimizedDatabaseUrl(dbConfig: any): string {
  const url = new URL(dbConfig.url);

  // 添加连接池配置
  url.searchParams.set('connection_limit', String(dbConfig.connectionPoolSize || 20));
  url.searchParams.set('pool_timeout', String(dbConfig.queryTimeout || 10000));
  url.searchParams.set('connect_timeout', '10');
  url.searchParams.set('socket_timeout', '30');

  // 添加性能优化参数
  url.searchParams.set('statement_cache_size', '100');
  url.searchParams.set('prepared_statement_cache_queries', '100');

  return url.toString();
}

/**
 * 关闭全局PrismaClient连接
 */
export async function closeGlobalPrismaClient(): Promise<void> {
  if (GLOBAL_PRISMA_CLIENT) {
    await GLOBAL_PRISMA_CLIENT.$disconnect();
    GLOBAL_PRISMA_CLIENT = null;
    console.log('✅ 全局PrismaClient连接已关闭');
  }
}

/**
 * @deprecated 使用 DatabaseHealthProvider 替代
 * 数据库健康检查 - 已迁移到统一监控系统
 */
export async function checkDatabaseHealth(): Promise<boolean> {
  console.warn('checkDatabaseHealth 已废弃，请使用 DatabaseHealthProvider');
  try {
    const client = getGlobalPrismaClient();
    await client.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('数据库健康检查失败:', error);
    return false;
  }
}

/**
 * @deprecated 使用 DatabaseHealthProvider.getDatabaseStats() 替代
 * 获取数据库统计信息 - 已迁移到统一监控系统
 */
export async function getDatabaseStats(): Promise<any> {
  console.warn('getDatabaseStats 已废弃，请使用 DatabaseHealthProvider.getDatabaseStats()');
  try {
    const client = getGlobalPrismaClient();
    const stats = {
      symbols: await client.symbols.count(),
      priceData: await client.priceData.count(),
      historicalData: await client.historicalData.count(),
      timestamp: new Date().toISOString(),
    };
    return stats;
  } catch (error) {
    console.error('获取数据库统计信息失败:', error);
    throw error;
  }
}

/**
 * 设置数据库连接
 */
export async function setupDatabase(): Promise<void> {
  const globalClient = getGlobalPrismaClient();

  try {
    await globalClient.$connect();
    console.log('✅ 数据库连接设置成功（使用全局实例）');
  } catch (error) {
    console.error('❌ 数据库连接设置失败:', error);
    throw error;
  }

  const logger = getLogger();
  logger.info('✅ 数据库初始化完成');
}

/**
 * @deprecated 使用 getGlobalPrismaClient() 替代
 * 获取数据库客户端
 */
export function getDatabase(): PrismaClient {
  return getGlobalPrismaClient();
}
