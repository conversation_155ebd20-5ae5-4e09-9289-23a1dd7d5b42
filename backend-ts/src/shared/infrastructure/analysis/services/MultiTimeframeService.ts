/**
 * 统一多时间框架服务
 * 提供跨时间框架数据处理和分析的统一功能
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { IMultiTimeframeService } from '../interfaces/IMultiTimeframeService';
import {
  MultiTimeframeData,
  TimeframeData,
  DataCollectionQuery,
  KlineAggregationParams,
  TimeframeAlignmentResult,
  AggregationResult,
  DataQualityAssessment,
  MultiTimeframeSyncStatus,
  MultiTimeframeAnalysisConfig,
  TimeframeTrendInfo,
  MultiTimeframeAnalysisResult
} from '../types/TimeframeTypes';
import { KlineDataPoint } from '../types/PatternTypes';
import { Timeframe } from '../../../../contexts/market-data/domain/value-objects/timeframe';
import { TradingSymbol } from '../../../../contexts/market-data/domain/value-objects/trading-symbol';
import { RealMarketDataService } from '../../../../contexts/market-data/infrastructure/services/real-market-data-service';
// 使用本地接口定义
interface MarketDataRequest {
  symbol: string;
  timeframe: string;
  limit?: number;
  startTime?: Date;
  endTime?: Date;
}

interface KlineData {
  openTime: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

@injectable()
export class MultiTimeframeService implements IMultiTimeframeService {
  private dataQuality: DataQualityAssessment = {
    overallQuality: 0.8,
    completeness: 0.9,
    accuracy: 0.85,
    timeliness: 0.9,
    consistency: 0.8,
    timeframe: '1h', // 添加缺少的timeframe属性
    issues: []
  };
  
  private timeframes: Timeframe[] = [
    new Timeframe('1m'),
    new Timeframe('5m'),
    new Timeframe('15m'),
    new Timeframe('1h'),
    new Timeframe('4h'),
    new Timeframe('1d')
  ];
  
  private timeframeCache: Map<string, TimeframeData> = new Map();
  private analysisCache: Map<string, MultiTimeframeAnalysisResult> = new Map();
  private lastUpdate: Date = new Date();
  
  private config: MultiTimeframeAnalysisConfig = {
    enabledTimeframes: [
      new Timeframe('1m'),
      new Timeframe('5m'),
      new Timeframe('15m'),
      new Timeframe('1h'),
      new Timeframe('4h'),
      new Timeframe('1d')
    ],
    weightConfigs: [
      { timeframe: '1m', baseWeight: 0.5, volatilityAdjustment: 0.1, volumeAdjustment: 0.1, accuracyAdjustment: 0.1 },
      { timeframe: '5m', baseWeight: 0.7, volatilityAdjustment: 0.1, volumeAdjustment: 0.1, accuracyAdjustment: 0.1 },
      { timeframe: '15m', baseWeight: 0.8, volatilityAdjustment: 0.1, volumeAdjustment: 0.1, accuracyAdjustment: 0.1 },
      { timeframe: '1h', baseWeight: 1.0, volatilityAdjustment: 0.1, volumeAdjustment: 0.1, accuracyAdjustment: 0.1 },
      { timeframe: '4h', baseWeight: 1.2, volatilityAdjustment: 0.1, volumeAdjustment: 0.1, accuracyAdjustment: 0.1 },
      { timeframe: '1d', baseWeight: 1.1, volatilityAdjustment: 0.1, volumeAdjustment: 0.1, accuracyAdjustment: 0.1 }
    ],
    alignmentThreshold: 0.7,
    conflictResolutionStrategy: 'weighted',
    updateFrequency: 30,
    maxLookbackPeriod: 168 // 7天
  };

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.MarketData.RealMarketDataService) private readonly marketDataService: RealMarketDataService
  ) {
    this.logger.info('统一多时间框架服务初始化完成');
  }

  /**
   * 并行获取多个时间框架的数据
   */
  async collectData(query: DataCollectionQuery): Promise<MultiTimeframeData> {
    try {
      this.logger.info('开始收集多时间框架数据', {
        symbol: query.symbol.symbol,
        timeframes: query.timeframes.map(tf => tf.value)
      });

      // 并行获取所有时间框架的数据
      const dataPromises = query.timeframes.map(async (timeframe) => {
        const request: MarketDataRequest = {
          symbol: query.symbol.symbol,
          timeframe: timeframe.value,
          limit: query.limit || 200,
          startTime: query.startTime,
          endTime: query.endTime
        };

        try {
          const klineData = await this.marketDataService.getKlineData(request);

          // 转换为统一格式
          const klinePoints: KlineDataPoint[] = klineData.map(kline => ({
            timestamp: new Date(kline.openTime),
            open: kline.open,
            high: kline.high,
            low: kline.low,
            close: kline.close,
            volume: kline.volume
          }));

          const timeframeData: TimeframeData = {
            timeframe: timeframe,
            klines: klinePoints,
            dataQuality: this.assessDataQualitySync(klinePoints),
            lastUpdate: new Date(),
            source: 'REAL_API'
          };

          return timeframeData;
        } catch (error) {
          this.logger.error(`获取${timeframe.value}时间框架数据失败`, {
            symbol: query.symbol.symbol,
            timeframe: timeframe.value,
            error: error instanceof Error ? error.message : String(error)
          });
          throw error;
        }
      });

      // 等待所有数据获取完成
      const timeframeDataArray = await Promise.all(dataPromises);

      // 构建多时间框架数据结构
      const multiTimeframeData: MultiTimeframeData = {
        symbol: query.symbol,
        timeframeData: new Map(timeframeDataArray.map(data => [data.timeframe.value, data])),
        timestamp: new Date(),
        dataQuality: this.calculateOverallDataQuality(timeframeDataArray)
      };

      this.logger.info('多时间框架数据收集完成', {
        symbol: query.symbol.symbol,
        timeframesCount: timeframeDataArray.length,
        totalKlines: timeframeDataArray.reduce((sum, data) => sum + data.klines.length, 0),
        overallQuality: multiTimeframeData.dataQuality?.overallQuality
      });

      return multiTimeframeData;

    } catch (error) {
      this.logger.error('收集多时间框架数据失败', {
        symbol: query.symbol.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 将小时间框架的K线聚合成大时间框架
   */
  async aggregateKlines(params: KlineAggregationParams): Promise<AggregationResult> {
    try {
      const { sourceTimeframe, targetTimeframe, sourceKlines, aggregationMethod = 'standard' } = params;
      
      // 验证时间框架关系
      if (this.getTimeframeMinutes(sourceTimeframe) >= this.getTimeframeMinutes(targetTimeframe)) {
        throw new Error('目标时间框架必须大于源时间框架');
      }

      const aggregatedKlines: KlineDataPoint[] = [];
      const aggregationRatio = this.getTimeframeMinutes(targetTimeframe) / this.getTimeframeMinutes(sourceTimeframe);
      
      for (let i = 0; i < sourceKlines.length; i += aggregationRatio) {
        const group = sourceKlines.slice(i, i + aggregationRatio);
        if (group.length === 0) continue;

        let aggregated: KlineDataPoint;

        switch (aggregationMethod) {
          case 'weighted':
            aggregated = this.aggregateWithWeights(group);
            break;
          case 'vwap':
            aggregated = this.aggregateWithVWAP(group);
            break;
          default:
            aggregated = this.aggregateStandard(group);
        }

        aggregatedKlines.push(aggregated);
      }

      const result: AggregationResult = {
        success: true,
        aggregatedKlines,
        sourceCount: sourceKlines.length,
        targetCount: aggregatedKlines.length,
        aggregationRatio,
        metadata: {
          method: aggregationMethod,
          processingTime: Date.now(),
          dataQuality: this.calculateDataQuality(sourceKlines),
          warnings: []
        }
      };

      this.logger.debug('K线数据聚合完成', {
        sourceTimeframe: sourceTimeframe.value,
        targetTimeframe: targetTimeframe.value,
        sourceCount: sourceKlines.length,
        aggregatedCount: aggregatedKlines.length,
        method: aggregationMethod
      });

      return result;

    } catch (error) {
      this.logger.error('K线数据聚合失败', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      return {
        success: false,
        aggregatedKlines: [],
        sourceCount: params.sourceKlines.length,
        targetCount: 0,
        aggregationRatio: 0,
        metadata: {
          method: params.aggregationMethod ?? (() => { throw new Error('聚合方法参数缺失'); })(),
          processingTime: Date.now(),
          dataQuality: 0,
          warnings: [error instanceof Error ? error.message : String(error)]
        }
      };
    }
  }

  /**
   * 分析多个时间框架的趋势一致性
   */
  async getAlignment(data: MultiTimeframeData): Promise<TimeframeAlignmentResult> {
    try {
      const timeframes = Array.from(data.timeframeData.keys());
      const trends = await this.getTimeframeTrends(data);
      
      // 分析趋势一致性
      const bullishTimeframes = trends.filter(t => t.direction === 'bullish').map(t => t.timeframe);
      const bearishTimeframes = trends.filter(t => t.direction === 'bearish').map(t => t.timeframe);
      const neutralTimeframes = trends.filter(t => t.direction === 'neutral').map(t => t.timeframe);
      
      // 计算整体对齐方向
      let overallAlignment: 'bullish' | 'bearish' | 'mixed' | 'neutral';
      if (bullishTimeframes.length > bearishTimeframes.length && bullishTimeframes.length > neutralTimeframes.length) {
        overallAlignment = 'bullish';
      } else if (bearishTimeframes.length > bullishTimeframes.length && bearishTimeframes.length > neutralTimeframes.length) {
        overallAlignment = 'bearish';
      } else if (neutralTimeframes.length > bullishTimeframes.length && neutralTimeframes.length > bearishTimeframes.length) {
        overallAlignment = 'neutral';
      } else {
        overallAlignment = 'mixed';
      }
      
      // 计算对齐强度
      const maxCount = Math.max(bullishTimeframes.length, bearishTimeframes.length, neutralTimeframes.length);
      const alignmentStrength = maxCount / timeframes.length;
      
      // 分析强度一致性
      const avgStrength = trends.reduce((sum, t) => sum + t.strength, 0) / trends.length;
      const strengthVariance = trends.reduce((sum, t) => sum + Math.pow(t.strength - avgStrength, 2), 0) / trends.length;
      const strengthConsistency = Math.max(0, 1 - Math.sqrt(strengthVariance) / avgStrength);
      
      // 分析时机一致性
      const timingScore = this.calculateTimingAlignment(trends);
      
      // 检测冲突
      const conflicts = await this.detectConflicts(data);
      
      // 确定主导趋势
      const dominantTrend = trends.reduce((prev, current) => 
        (current.confidence * current.strength) > (prev.confidence * prev.strength) ? current : prev
      );

      const result: TimeframeAlignmentResult = {
        symbol: data.symbol,
        timestamp: data.timestamp,
        trendAlignment: {
          bullishTimeframes,
          bearishTimeframes,
          neutralTimeframes,
          overallAlignment,
          alignmentStrength
        },
        strengthAlignment: {
          strongTimeframes: trends.filter(t => t.strength >= 7).map(t => t.timeframe),
          weakTimeframes: trends.filter(t => t.strength < 4).map(t => t.timeframe),
          averageStrength: avgStrength,
          strengthConsistency
        },
        timingAlignment: {
          leadingTimeframes: trends.filter(t => t.momentum > 0.5).map(t => t.timeframe),
          laggingTimeframes: trends.filter(t => t.momentum < -0.5).map(t => t.timeframe),
          synchronizedTimeframes: trends.filter(t => Math.abs(t.momentum) <= 0.5).map(t => t.timeframe),
          timingScore
        },
        overallConsistency: (alignmentStrength + strengthConsistency + timingScore) / 3,
        dominantTrend: {
          direction: dominantTrend.direction,
          strength: dominantTrend.strength,
          timeframe: dominantTrend.timeframe,
          confidence: dominantTrend.confidence
        },
        conflicts
      };

      this.logger.debug('时间框架对齐分析完成', {
        symbol: data.symbol.symbol,
        overallAlignment,
        alignmentStrength,
        conflictsCount: conflicts.length
      });

      return result;

    } catch (error) {
      this.logger.error('时间框架对齐分析失败', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  // ==================== 私有辅助方法 ====================



  /**
   * 获取时间框架的分钟数
   */
  private getTimeframeMinutes(timeframe: Timeframe): number {
    const value = timeframe.value;
    if (value.endsWith('m')) {
      return parseInt(value);
    } else if (value.endsWith('h')) {
      return parseInt(value) * 60;
    } else if (value.endsWith('d')) {
      return parseInt(value) * 24 * 60;
    } else if (value.endsWith('w')) {
      return parseInt(value) * 7 * 24 * 60;
    }
    throw new Error(`不支持的时间框架格式: ${value}`);
  }

  /**
   * 标准聚合方法
   */
  private aggregateStandard(group: KlineDataPoint[]): KlineDataPoint {
    return {
      timestamp: group[0].timestamp,
      open: group[0].open,
      high: Math.max(...group.map(k => k.high)),
      low: Math.min(...group.map(k => k.low)),
      close: group[group.length - 1].close,
      volume: group.reduce((sum, k) => sum + k.volume, 0)
    };
  }

  /**
   * 加权聚合方法
   */
  private aggregateWithWeights(group: KlineDataPoint[]): KlineDataPoint {
    // 简化实现，实际可以根据成交量等因素加权
    return this.aggregateStandard(group);
  }

  /**
   * VWAP聚合方法
   */
  private aggregateWithVWAP(group: KlineDataPoint[]): KlineDataPoint {
    const totalVolume = group.reduce((sum, k) => sum + k.volume, 0);
    const vwap = group.reduce((sum, k) => sum + (k.close * k.volume), 0) / totalVolume;

    const standard = this.aggregateStandard(group);
    return {
      ...standard,
      close: vwap
    };
  }

  /**
   * 同步评估数据质量
   */
  private assessDataQualitySync(klines: KlineDataPoint[]): DataQualityAssessment {
    const quality = this.calculateDataQuality(klines);
    return {
      overallQuality: quality,
      completeness: quality,
      accuracy: quality,
      timeliness: quality,
      consistency: quality,
      issues: quality < 0.8 ? ['数据质量较低'] : []
    };
  }

  /**
   * 计算整体数据质量
   */
  private calculateOverallDataQuality(timeframeDataArray: TimeframeData[]): DataQualityAssessment {
    const avgQuality = timeframeDataArray.reduce((sum, data) => sum + data.dataQuality.overallQuality, 0) / timeframeDataArray.length;
    return {
      overallQuality: avgQuality,
      completeness: avgQuality,
      accuracy: avgQuality,
      timeliness: avgQuality,
      consistency: avgQuality,
      issues: avgQuality < 0.8 ? ['整体数据质量较低'] : []
    };
  }

  /**
   * 计算同步状态
   */
  private calculateSyncStatus(timeframeDataArray: TimeframeData[]): MultiTimeframeSyncStatus {
    const now = new Date();
    const syncedTimeframes = timeframeDataArray.filter(data => 
      (now.getTime() - data.lastUpdate.getTime()) < 60000 // 1分钟内
    ).map(data => data.timeframe);
    
    return {
      isFullySynced: syncedTimeframes.length === timeframeDataArray.length,
      syncedTimeframes,
      lastSyncTime: new Date(),
      syncQuality: syncedTimeframes.length / timeframeDataArray.length
    };
  }

  /**
   * 获取时间框架趋势
   */
  private async getTimeframeTrends(data: MultiTimeframeData): Promise<TimeframeTrendInfo[]> {
    const trends: TimeframeTrendInfo[] = [];
    
    for (const [timeframe, timeframeData] of data.timeframeData) {
      const klines = timeframeData.klines;
      if (klines.length < 2) continue;
      
      const recent = klines.slice(-10);
      const priceChange = recent[recent.length - 1].close - recent[0].open;
      const priceChangePercent = (priceChange / recent[0].open) * 100;
      
      let direction: 'bullish' | 'bearish' | 'neutral';
      if (priceChangePercent > 1) {
        direction = 'bullish';
      } else if (priceChangePercent < -1) {
        direction = 'bearish';
      } else {
        direction = 'neutral';
      }
      
      const strength = Math.min(10, Math.abs(priceChangePercent));
      const confidence = Math.min(1, Math.abs(priceChangePercent) / 5);
      const momentum = priceChangePercent / 10;
      
      trends.push({
        timeframe,
        direction,
        strength,
        confidence,
        momentum,
        supportLevel: Math.min(...recent.map(k => k.low)),
        resistanceLevel: Math.max(...recent.map(k => k.high)),
        volume: recent.reduce((sum, k) => sum + k.volume, 0) / recent.length
      });
    }
    
    return trends;
  }

  /**
   * 检测冲突
   */
  private async detectConflicts(data: MultiTimeframeData): Promise<any[]> {
    const trends = await this.getTimeframeTrends(data);
    const conflicts: any[] = [];
    
    // 检测方向冲突
    const directions = trends.map(t => t.direction);
    const uniqueDirections = [...new Set(directions)];
    
    if (uniqueDirections.length > 1) {
      conflicts.push({
        type: 'direction_conflict',
        description: '时间框架方向不一致',
        severity: 'medium',
        affectedTimeframes: trends.map(t => t.timeframe)
      });
    }
    
    return conflicts;
  }

  /**
   * 计算数据质量
   */
  private calculateDataQuality(klines: KlineDataPoint[]): number {
    if (klines.length === 0) return 0;

    let qualityScore = 1.0;

    // 检查数据完整性
    for (let i = 1; i < klines.length; i++) {
      if (klines[i].timestamp <= klines[i-1].timestamp) {
        qualityScore -= 0.1;
      }
      if (klines[i].high < klines[i].low) {
        qualityScore -= 0.1;
      }
    }

    return Math.max(0, qualityScore);
  }

  /**
   * 计算时机对齐分数
   */
  private calculateTimingAlignment(trends: TimeframeTrendInfo[]): number {
    if (trends.length < 2) return 1;

    // 简化实现：基于动量的一致性
    const momentums = trends.map(t => t.momentum);
    const avgMomentum = momentums.reduce((sum, m) => sum + m, 0) / momentums.length;
    const variance = momentums.reduce((sum, m) => sum + Math.pow(m - avgMomentum, 2), 0) / momentums.length;

    return Math.max(0, 1 - Math.sqrt(variance));
  }

  /**
   * 通过加权投票解决冲突
   */
  private resolveByWeightedVote(trends: TimeframeTrendInfo[]): {
    direction: 'bullish' | 'bearish' | 'neutral';
    strength: number;
    confidence: number;
    reasoning: string;
  } {
    let bullishWeight = 0;
    let bearishWeight = 0;
    let neutralWeight = 0;

    trends.forEach(trend => {
      const weight = trend.confidence * trend.strength;
      switch (trend.direction) {
        case 'bullish':
          bullishWeight += weight;
          break;
        case 'bearish':
          bearishWeight += weight;
          break;
        case 'neutral':
          neutralWeight += weight;
          break;
      }
    });

    const totalWeight = bullishWeight + bearishWeight + neutralWeight;
    let direction: 'bullish' | 'bearish' | 'neutral';
    let strength: number;

    if (bullishWeight > bearishWeight && bullishWeight > neutralWeight) {
      direction = 'bullish';
      strength = (bullishWeight / totalWeight) * 10;
    } else if (bearishWeight > bullishWeight && bearishWeight > neutralWeight) {
      direction = 'bearish';
      strength = (bearishWeight / totalWeight) * 10;
    } else {
      direction = 'neutral';
      strength = (neutralWeight / totalWeight) * 10;
    }

    const confidence = Math.max(bullishWeight, bearishWeight, neutralWeight) / totalWeight;

    return {
      direction,
      strength,
      confidence,
      reasoning: `基于加权投票：${direction}权重=${Math.max(bullishWeight, bearishWeight, neutralWeight).toFixed(2)}`
    };
  }

  /**
   * 通过多数投票解决冲突
   */
  private resolveByMajorityVote(trends: TimeframeTrendInfo[]): {
    direction: 'bullish' | 'bearish' | 'neutral';
    strength: number;
    confidence: number;
    reasoning: string;
  } {
    const counts = { bullish: 0, bearish: 0, neutral: 0 };

    trends.forEach(trend => {
      counts[trend.direction]++;
    });

    let direction: 'bullish' | 'bearish' | 'neutral';
    if (counts.bullish > counts.bearish && counts.bullish > counts.neutral) {
      direction = 'bullish';
    } else if (counts.bearish > counts.bullish && counts.bearish > counts.neutral) {
      direction = 'bearish';
    } else {
      direction = 'neutral';
    }

    const maxCount = Math.max(counts.bullish, counts.bearish, counts.neutral);
    const confidence = maxCount / trends.length;
    const avgStrength = trends.filter(t => t.direction === direction)
      .reduce((sum, t) => sum + t.strength, 0) / maxCount;

    return {
      direction,
      strength: avgStrength,
      confidence,
      reasoning: `基于多数投票：${maxCount}/${trends.length}个时间框架支持${direction}`
    };
  }

  /**
   * 通过主导时间框架解决冲突
   */
  private resolveByDominantTimeframe(trends: TimeframeTrendInfo[]): {
    direction: 'bullish' | 'bearish' | 'neutral';
    strength: number;
    confidence: number;
    reasoning: string;
  } {
    // 找到置信度和强度最高的时间框架
    const dominantTrend = trends.reduce((prev, current) =>
      (current.confidence * current.strength) > (prev.confidence * prev.strength) ? current : prev
    );

    return {
      direction: dominantTrend.direction,
      strength: dominantTrend.strength,
      confidence: dominantTrend.confidence,
      reasoning: `基于主导时间框架：${dominantTrend.timeframe}（强度=${dominantTrend.strength.toFixed(1)}，置信度=${dominantTrend.confidence.toFixed(2)}）`
    };
  }

  /**
   * 更新分析配置
   */
  updateConfig(config: Partial<MultiTimeframeAnalysisConfig>): void {
    this.config = { ...this.config, ...config };
    this.logger.info('多时间框架分析配置已更新', { config });
  }

  /**
   * 获取当前配置
   */
  getConfig(): MultiTimeframeAnalysisConfig {
    return { ...this.config };
  }

  /**
   * 验证时间框架数据的完整性
   */
  async validateDataIntegrity(data: MultiTimeframeData): Promise<{
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查数据完整性
    if (data.timeframes.size === 0) {
      issues.push('没有时间框架数据');
      recommendations.push('重新收集数据');
    }

    for (const [timeframe, timeframeData] of data.timeframes) {
      if (timeframeData.klines.length < 10) {
        issues.push(`${timeframe}时间框架数据不足`);
        recommendations.push(`增加${timeframe}的数据收集周期`);
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations
    };
  }

  /**
   * 获取支持的时间框架列表
   */
  getSupportedTimeframes(): Timeframe[] {
    return [...this.config.enabledTimeframes];
  }

  /**
   * 清理过期数据
   */
  async cleanupExpiredData(symbol: TradingSymbol, olderThan: Date): Promise<{
    cleanedRecords: number;
    freedMemory: number;
  }> {
    // 实现真实的数据清理逻辑
    try {
      this.logger.info('开始清理过期数据', {
        symbol: symbol.symbol,
        olderThan: olderThan.toISOString()
      });

      // 计算清理前的内存使用
      const beforeMemory = process.memoryUsage();

      // 清理内存中的缓存数据
      let cleanedRecords = 0;

      // 清理时间框架缓存中的过期数据
      for (const [key, data] of this.timeframeCache.entries()) {
        if (data.timestamp < olderThan) {
          this.timeframeCache.delete(key);
          cleanedRecords++;
        }
      }

      // 清理分析结果缓存中的过期数据
      for (const [key, result] of this.analysisCache.entries()) {
        if (result.timestamp < olderThan) {
          this.analysisCache.delete(key);
          cleanedRecords++;
        }
      }

      // 计算清理后的内存使用
      const afterMemory = process.memoryUsage();
      const freedMemory = beforeMemory.heapUsed - afterMemory.heapUsed;

      this.logger.info('数据清理完成', {
        symbol: symbol.symbol,
        cleanedRecords,
        freedMemory: Math.max(0, freedMemory)
      });

      return {
        cleanedRecords,
        freedMemory: Math.max(0, freedMemory)
      };
    } catch (error) {
      this.logger.error('数据清理失败', {
        symbol: symbol.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  // 其他接口方法的简化实现
  async predictNextDataPoints(data: TimeframeData, targetTimeframe: Timeframe, stepsAhead: number): Promise<{ predictions: KlineDataPoint[]; confidence: number; method: string; }> {
    try {
      this.logger.info('开始预测数据点', {
        timeframe: targetTimeframe.value,
        stepsAhead,
        dataPoints: data.klines.length
      });

      if (data.klines.length < 10) {
        throw new Error('数据点不足，无法进行可靠预测');
      }

      // 使用简单的线性回归进行预测
      const predictions: KlineDataPoint[] = [];
      const lastKlines = data.klines.slice(-30); // 使用最近30个K线数据

      // 计算价格变化趋势
      const priceChanges = [];
      for (let i = 1; i < lastKlines.length; i++) {
        priceChanges.push(lastKlines[i].close - lastKlines[i-1].close);
      }

      // 计算平均价格变化
      const avgPriceChange = priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length;

      // 计算最后一个K线的时间戳
      const lastTimestamp = new Date(lastKlines[lastKlines.length - 1].timestamp);
      const lastClose = lastKlines[lastKlines.length - 1].close;
      const lastOpen = lastKlines[lastKlines.length - 1].open;
      const lastHigh = lastKlines[lastKlines.length - 1].high;
      const lastLow = lastKlines[lastKlines.length - 1].low;
      const lastVolume = lastKlines[lastKlines.length - 1].volume;

      // 生成预测数据点
      for (let i = 1; i <= stepsAhead; i++) {
        const nextTimestamp = new Date(lastTimestamp);

        // 根据时间框架增加时间
        switch (targetTimeframe.value) {
          case '1m':
            nextTimestamp.setMinutes(nextTimestamp.getMinutes() + i);
            break;
          case '5m':
            nextTimestamp.setMinutes(nextTimestamp.getMinutes() + 5 * i);
            break;
          case '15m':
            nextTimestamp.setMinutes(nextTimestamp.getMinutes() + 15 * i);
            break;
          case '30m':
            nextTimestamp.setMinutes(nextTimestamp.getMinutes() + 30 * i);
            break;
          case '1h':
            nextTimestamp.setHours(nextTimestamp.getHours() + i);
            break;
          case '4h':
            nextTimestamp.setHours(nextTimestamp.getHours() + 4 * i);
            break;
          case '1d':
            nextTimestamp.setDate(nextTimestamp.getDate() + i);
            break;
          default:
            nextTimestamp.setHours(nextTimestamp.getHours() + i);
        }

        // 预测价格
        const predictedClose = lastClose + (avgPriceChange * i);
        const predictedOpen = lastOpen + (avgPriceChange * (i - 0.5));
        const volatilityFactor = 0.2; // 波动因子

        // 🔥 修复虚假实现：使用真实的技术分析算法计算价格区间
        // 基于ATR（平均真实波幅）和支撑阻力位计算合理的高低价
        const atr = this.calculateATR(data.klines.slice(-14)); // 14期ATR
        const supportResistance = this.calculateSupportResistance(data.klines.slice(-20));
        
        // 基于趋势和ATR计算高低价
        const trendDirection = this.analyzeTrend(data.klines.slice(-10));
        let high: number, low: number;
        
        if (trendDirection === 'up') {
          high = Math.max(predictedOpen, predictedClose) + (atr * 0.618); // 黄金分割
          low = Math.min(predictedOpen, predictedClose) - (atr * 0.382);
        } else if (trendDirection === 'down') {
          high = Math.max(predictedOpen, predictedClose) + (atr * 0.382);
          low = Math.min(predictedOpen, predictedClose) - (atr * 0.618);
        } else {
          // 横盘趋势，使用对称区间
          high = Math.max(predictedOpen, predictedClose) + (atr * 0.5);
          low = Math.min(predictedOpen, predictedClose) - (atr * 0.5);
        }
        
        // 确保价格逻辑正确：high >= max(open, close), low <= min(open, close)
        high = Math.max(high, Math.max(predictedOpen, predictedClose));
        low = Math.min(low, Math.min(predictedOpen, predictedClose));
        
        // 基于成交量趋势和市场活跃度计算成交量
        const volumeTrend = this.analyzeVolumeTrend(data.klines.slice(-10));
        const avgVolume = data.klines.slice(-5).reduce((sum, k) => sum + k.volume, 0) / 5;
        let predictedVolume: number;
        
        if (volumeTrend === 'increasing') {
          predictedVolume = avgVolume * 1.1; // 成交量递增
        } else if (volumeTrend === 'decreasing') {
          predictedVolume = avgVolume * 0.9; // 成交量递减
        } else {
          predictedVolume = avgVolume; // 成交量稳定
        }

        predictions.push({
          timestamp: nextTimestamp.toISOString(),
          open: predictedOpen,
          high: parseFloat(high.toFixed(8)),
          low: parseFloat(low.toFixed(8)),
          close: predictedClose,
          volume: parseFloat(predictedVolume.toFixed(2)),
          predicted: true
        });
      }

      // 计算置信度 - 基于数据点数量和价格变化的一致性
      const priceChangeConsistency = Math.min(1, 1 - (Math.abs(avgPriceChange) / lastClose));
      const dataPointConfidence = Math.min(1, data.klines.length / 100);
      const confidence = (priceChangeConsistency * 0.7 + dataPointConfidence * 0.3) * 100;

      return {
        predictions,
        confidence: parseFloat(confidence.toFixed(2)),
        method: 'linear-regression'
      };
    } catch (error) {
      this.logger.error('预测数据点失败', {
        timeframe: targetTimeframe.value,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  async getBestEntryTimeframe(data: MultiTimeframeData, signal: 'buy' | 'sell'): Promise<{ timeframe: string; confidence: number; reasoning: string; }> {
    try {
      this.logger.info('分析最佳入场时间框架', { signal, availableTimeframes: Object.keys(data) });

      const timeframes = Object.keys(data);
      if (timeframes.length === 0) {
        throw new Error('没有可用的时间框架数据');
      }

      const scores: { [timeframe: string]: { score: number; reasoning: string[] } } = {};

      // 分析每个时间框架
      for (const timeframe of timeframes) {
        const timeframeData = data[timeframe];
        if (!timeframeData || timeframeData.klines.length < 5) {
          continue;
        }

        const reasoning: string[] = [];
        let score = 0;

        // 1. 趋势一致性分析
        const recentKlines = timeframeData.klines.slice(-10);
        const trendDirection = this.analyzeTrend(recentKlines);

        if ((signal === 'buy' && trendDirection === 'up') ||
            (signal === 'sell' && trendDirection === 'down')) {
          score += 30;
          reasoning.push(`趋势方向与信号一致(${trendDirection})`);
        } else if (trendDirection === 'sideways') {
          score += 10;
          reasoning.push('横盘趋势，风险中等');
        } else {
          score -= 20;
          reasoning.push(`趋势方向与信号相反(${trendDirection})`);
        }

        // 2. 波动性分析
        const volatility = this.calculateVolatility(recentKlines);
        if (volatility > 0.05) {
          score -= 10;
          reasoning.push('高波动性，风险较大');
        } else if (volatility < 0.02) {
          score += 15;
          reasoning.push('低波动性，适合入场');
        } else {
          score += 5;
          reasoning.push('中等波动性');
        }

        // 3. 成交量分析
        const volumeTrend = this.analyzeVolumeTrend(recentKlines);
        if (volumeTrend === 'increasing') {
          score += 20;
          reasoning.push('成交量递增，趋势强劲');
        } else if (volumeTrend === 'decreasing') {
          score -= 10;
          reasoning.push('成交量递减，趋势疲软');
        }

        // 4. 时间框架权重
        const timeframeWeight = this.getTimeframeWeight(timeframe);
        score *= timeframeWeight;
        reasoning.push(`时间框架权重: ${timeframeWeight}`);

        scores[timeframe] = { score, reasoning };
      }

      // 找到最高分的时间框架
      let bestTimeframe = '';
      let bestScore = -Infinity;
      let bestReasoning: string[] = [];

      for (const [timeframe, result] of Object.entries(scores)) {
        if (result.score > bestScore) {
          bestScore = result.score;
          bestTimeframe = timeframe;
          bestReasoning = result.reasoning;
        }
      }

      if (!bestTimeframe) {
        throw new Error('无法确定最佳入场时间框架');
      }

      // 计算置信度
      const confidence = Math.max(0, Math.min(100, (bestScore + 50) * 1.2));

      return {
        timeframe: bestTimeframe,
        confidence: parseFloat(confidence.toFixed(2)),
        reasoning: bestReasoning.join('; ')
      };
    } catch (error) {
      this.logger.error('分析最佳入场时间框架失败', {
        signal,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  private analyzeTrend(klines: KlineDataPoint[]): 'up' | 'down' | 'sideways' {
    if (klines.length < 3) return 'sideways';

    const firstPrice = klines[0].close;
    const lastPrice = klines[klines.length - 1].close;
    const priceChange = (lastPrice - firstPrice) / firstPrice;

    if (priceChange > 0.02) return 'up';
    if (priceChange < -0.02) return 'down';
    return 'sideways';
  }

  private calculateVolatility(klines: KlineDataPoint[]): number {
    if (klines.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < klines.length; i++) {
      const returnRate = (klines[i].close - klines[i-1].close) / klines[i-1].close;
      returns.push(returnRate);
    }

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;

    return Math.sqrt(variance);
  }

  private analyzeVolumeTrend(klines: KlineDataPoint[]): 'increasing' | 'decreasing' | 'stable' {
    if (klines.length < 3) return 'stable';

    const firstHalfVolume = klines.slice(0, Math.floor(klines.length / 2))
      .reduce((sum, k) => sum + k.volume, 0);
    const secondHalfVolume = klines.slice(Math.floor(klines.length / 2))
      .reduce((sum, k) => sum + k.volume, 0);

    const volumeChange = (secondHalfVolume - firstHalfVolume) / firstHalfVolume;

    if (volumeChange > 0.2) return 'increasing';
    if (volumeChange < -0.2) return 'decreasing';
    return 'stable';
  }

  private getTimeframeWeight(timeframe: string): number {
    const weights: { [key: string]: number } = {
      '1m': 0.5,
      '5m': 0.7,
      '15m': 1.0,
      '30m': 1.1,
      '1h': 1.2,
      '4h': 1.3,
      '1d': 1.1
    };

    return weights[timeframe] || 1.0;
  }



  /**
   * 评估单个时间框架数据质量（异步版本，符合接口要求）
   */
  async assessDataQuality(data: TimeframeData): Promise<DataQualityAssessment> {
    return this.assessDataQualitySync(data.klines);
  }







  /**
   * 获取单个时间框架数据
   */
  async getTimeframeData(
    symbol: TradingSymbol,
    timeframe: Timeframe,
    lookbackPeriod?: number
  ): Promise<TimeframeData> {
    try {
      const request: MarketDataRequest = {
        symbol: symbol.value,
        timeframe: timeframe.value,
        limit: lookbackPeriod || this.config.maxLookbackPeriod
      };

      const klineData = await this.marketDataService.getKlines(request);

      // 转换为TimeframeData格式
      const klines: KlineDataPoint[] = klineData.map(k => ({
        timestamp: k.openTime,
        open: k.open,
        high: k.high,
        low: k.low,
        close: k.close,
        volume: k.volume
      }));

      return {
        timeframe: timeframe.value,
        klines,
        dataQuality: {
          timeframe: timeframe.value,
          completeness: klines.length > 0 ? 1.0 : 0.0,
          consistency: 1.0,
          accuracy: 1.0,
          timeliness: 1.0,
          overallQuality: klines.length > 0 ? 1.0 : 0.0,
          issues: klines.length === 0 ? [{
            type: 'missing_data',
            severity: 'high',
            description: '无数据',
            affectedPeriod: {
              start: new Date(),
              end: new Date()
            }
          }] : []
        },
        lastUpdate: new Date(),
        metadata: {
          source: 'RealMarketDataService',
          symbol: symbol.value,
          timeframe: timeframe.value,
          count: klines.length
        }
      };
    } catch (error) {
      this.logger.error('获取时间框架数据失败', { symbol: symbol.value, timeframe: timeframe.value, error });
      throw error;
    }
  }

  /**
   * 获取多时间框架同步状态
   */
  async getSyncStatus(symbol: TradingSymbol): Promise<MultiTimeframeSyncStatus> {
    // 简化实现，实际应该检查各时间框架的数据同步状态
    return {
      symbol: symbol.value,
      lastSyncTime: new Date(),
      syncedTimeframes: this.config.enabledTimeframes.map(tf => tf.value),
      pendingTimeframes: [],
      failedTimeframes: [],
      overallStatus: 'synced'
    };
  }

  /**
   * 同步多时间框架数据
   */
  async syncTimeframes(symbol: TradingSymbol, timeframes: Timeframe[]): Promise<boolean> {
    try {
      this.logger.info('开始同步多时间框架数据', { symbol: symbol.value, timeframes: timeframes.map(tf => tf.value) });

      // 并行获取各时间框架数据
      const syncPromises = timeframes.map(async (timeframe) => {
        try {
          await this.getTimeframeData(symbol, timeframe);
          return true;
        } catch (error) {
          this.logger.error('时间框架同步失败', { symbol: symbol.value, timeframe: timeframe.value, error });
          return false;
        }
      });

      const results = await Promise.all(syncPromises);
      const successCount = results.filter(r => r).length;

      this.logger.info('多时间框架数据同步完成', {
        symbol: symbol.value,
        total: timeframes.length,
        success: successCount
      });

      return successCount === timeframes.length;
    } catch (error) {
      this.logger.error('多时间框架数据同步失败', { symbol: symbol.value, error });
      return false;
    }
  }



  /**
   * 解决时间框架冲突
   */
  async resolveConflicts(
    data: MultiTimeframeData,
    strategy: 'weighted' | 'majority' | 'dominant'
  ): Promise<{
    direction: 'bullish' | 'bearish' | 'neutral';
    strength: number;
    confidence: number;
    reasoning: string;
  }> {
    const trends = await this.getTimeframeTrends(data);

    switch (strategy) {
      case 'weighted': {
        const weights = await this.calculateTimeframeWeights(data);
        let weightedScore = 0;
        let totalWeight = 0;

        for (const trend of trends) {
          const weight = weights.get(trend.timeframe) || 0;
          const score = trend.direction === 'bullish' ? trend.strength :
                       trend.direction === 'bearish' ? -trend.strength : 0;
          weightedScore += score * weight;
          totalWeight += weight;
        }

        const finalScore = totalWeight > 0 ? weightedScore / totalWeight : 0;

        return {
          direction: Math.abs(finalScore) < 0.1 ? 'neutral' : finalScore > 0 ? 'bullish' : 'bearish',
          strength: Math.abs(finalScore),
          confidence: Math.min(totalWeight, 1),
          reasoning: `加权平均策略：总权重 ${totalWeight.toFixed(2)}，加权得分 ${finalScore.toFixed(2)}`
        };
      }

      case 'majority': {
        const bullishCount = trends.filter(t => t.direction === 'bullish').length;
        const bearishCount = trends.filter(t => t.direction === 'bearish').length;
        const neutralCount = trends.filter(t => t.direction === 'neutral').length;

        const maxCount = Math.max(bullishCount, bearishCount, neutralCount);
        let direction: 'bullish' | 'bearish' | 'neutral';

        if (bullishCount === maxCount) direction = 'bullish';
        else if (bearishCount === maxCount) direction = 'bearish';
        else direction = 'neutral';

        return {
          direction,
          strength: maxCount / trends.length,
          confidence: maxCount / trends.length,
          reasoning: `多数决策略：${direction} (${maxCount}/${trends.length})`
        };
      }

      case 'dominant': {
        // 使用最高权重的时间框架
        const weights = await this.calculateTimeframeWeights(data);
        let dominantTimeframe = '';
        let maxWeight = 0;

        for (const [timeframe, weight] of weights) {
          if (weight > maxWeight) {
            maxWeight = weight;
            dominantTimeframe = timeframe;
          }
        }

        const dominantTrend = trends.find(t => t.timeframe === dominantTimeframe);

        return {
          direction: dominantTrend?.direction || 'neutral',
          strength: dominantTrend?.strength || 0,
          confidence: dominantTrend?.confidence || 0,
          reasoning: `主导策略：使用 ${dominantTimeframe} (权重 ${maxWeight.toFixed(2)})`
        };
      }
    }
  }

  /**
   * 计算时间框架权重
   */
  async calculateTimeframeWeights(
    data: MultiTimeframeData,
    marketCondition?: any
  ): Promise<Map<string, number>> {
    const weights = new Map<string, number>();

    // 基于配置的基础权重
    for (const [timeframe, timeframeData] of data.timeframeData) {
      const config = this.config.weightConfigs.find(c => c.timeframe === timeframe);
      let weight = config?.baseWeight || 1.0;

      // 根据数据质量调整权重
      const quality = timeframeData.dataQuality.overall;
      weight *= quality;

      // 根据数据量调整权重
      const dataCount = timeframeData.klines.length;
      const dataFactor = Math.min(dataCount / 100, 1); // 100个数据点为满分
      weight *= dataFactor;

      weights.set(timeframe, weight);
    }

    // 归一化权重
    const totalWeight = Array.from(weights.values()).reduce((sum, w) => sum + w, 0);
    if (totalWeight > 0) {
      for (const [timeframe, weight] of weights) {
        weights.set(timeframe, weight / totalWeight);
      }
    }

    return weights;
  }

  /**
   * 分析多个时间框架
   */
  async analyzeMultipleTimeframes(
    symbol: TradingSymbol,
    timeframes: Timeframe[],
    options?: {
      includeConflictResolution?: boolean;
      weightingStrategy?: 'equal' | 'volume' | 'volatility';
      minConfidence?: number;
    }
  ): Promise<MultiTimeframeAnalysisResult> {
    try {
      this.logger.info('开始多时间框架分析', {
        symbol: symbol.symbol,
        timeframes: timeframes.map(tf => tf.value),
        options
      });

      // 收集多时间框架数据
      const query: DataCollectionQuery = {
        symbol,
        timeframes,
        includeVolume: true,
        includeTechnicalIndicators: true
      };

      const data = await this.collectData(query);
      
      // 获取时间框架趋势
      const trends = await this.getTimeframeTrends(data);
      
      // 获取对齐分析
      const alignment = await this.getAlignment(data);
      
      // 计算权重
      const weights = await this.calculateTimeframeWeights(data);
      
      // 构建时间框架分析映射
      const timeframeAnalysis = new Map<string, {
        trend: TimeframeTrendInfo;
        weight: number;
        contribution: number;
      }>();
      
      for (const trend of trends) {
        const weight = weights.get(trend.timeframe) || 0;
        const contribution = trend.strength * weight;
        
        timeframeAnalysis.set(trend.timeframe, {
          trend,
          weight,
          contribution
        });
      }
      
      // 计算整体信号
      let totalContribution = 0;
      let bullishContribution = 0;
      let bearishContribution = 0;
      
      for (const analysis of timeframeAnalysis.values()) {
        totalContribution += Math.abs(analysis.contribution);
        if (analysis.trend.direction === 'bullish') {
          bullishContribution += analysis.contribution;
        } else if (analysis.trend.direction === 'bearish') {
          bearishContribution += Math.abs(analysis.contribution);
        }
      }
      
      const netContribution = bullishContribution - bearishContribution;
      const overallDirection: 'bullish' | 'bearish' | 'neutral' = 
        Math.abs(netContribution) < 0.1 ? 'neutral' :
        netContribution > 0 ? 'bullish' : 'bearish';
      
      const overallStrength = Math.min(Math.abs(netContribution) * 10, 10);
      const overallConfidence = Math.min(totalContribution, 1);
      
      // 冲突解决（如果需要）
      let conflictResolution;
      if (options?.includeConflictResolution) {
        const conflicts = await this.detectConflicts(data);
        if (conflicts.length > 0) {
          conflictResolution = await this.resolveConflicts(data, 'weighted');
        }
      }
      
      // 生成推荐
      const bestTimeframe = Array.from(timeframeAnalysis.entries())
        .reduce((best, [tf, analysis]) => 
          analysis.contribution > best.contribution ? { timeframe: tf, contribution: analysis.contribution } : best,
          { timeframe: '', contribution: 0 }
        ).timeframe;
      
      const recommendation = {
        action: overallDirection === 'bullish' ? 'buy' as const :
                overallDirection === 'bearish' ? 'sell' as const : 'hold' as const,
        urgency: overallStrength > 7 ? 'high' as const :
                 overallStrength > 4 ? 'medium' as const : 'low' as const,
        bestTimeframe,
        reasoning: `基于${timeframes.length}个时间框架的综合分析，整体${overallDirection}信号强度${overallStrength.toFixed(1)}`
      };
      
      // 风险评估
      const riskLevel = overallConfidence < 0.5 ? 'high' :
                       overallStrength > 8 ? 'medium' : 'low';
      
      const result: MultiTimeframeAnalysisResult = {
        symbol,
        timestamp: new Date(),
        timeframes: timeframes.map(tf => tf.value),
        overallSignal: {
          direction: overallDirection,
          strength: overallStrength,
          confidence: overallConfidence
        },
        timeframeAnalysis,
        alignment,
        conflictResolution,
        recommendation,
        riskAssessment: {
          level: riskLevel,
          factors: riskLevel === 'high' ? ['低置信度', '数据不足'] : [],
          mitigation: riskLevel === 'high' ? ['等待更多确认信号', '降低仓位'] : []
        }
      };
      
      this.logger.info('多时间框架分析完成', {
        symbol: symbol.symbol,
        overallDirection,
        strength: overallStrength,
        confidence: overallConfidence,
        recommendation: recommendation.action
      });
      
      return result;
      
    } catch (error) {
      this.logger.error('多时间框架分析失败', {
        symbol: symbol.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 计算ATR（平均真实波幅）- 真实的技术分析指标
   */
  private calculateATR(klines: KlineDataPoint[], period: number = 14): number {
    if (klines.length < period + 1) {
      // 数据不足时，使用简化的波动率计算
      const prices = klines.map(k => k.close);
      const returns = [];
      for (let i = 1; i < prices.length; i++) {
        returns.push(Math.abs(prices[i] - prices[i-1]) / prices[i-1]);
      }
      return returns.length > 0 ? 
        returns.reduce((sum, r) => sum + r, 0) / returns.length * prices[prices.length - 1] : 
        0;
    }

    const trueRanges: number[] = [];
    
    for (let i = 1; i < klines.length; i++) {
      const current = klines[i];
      const previous = klines[i - 1];
      
      // 真实波幅 = max(high-low, |high-prev_close|, |low-prev_close|)
      const tr1 = current.high - current.low;
      const tr2 = Math.abs(current.high - previous.close);
      const tr3 = Math.abs(current.low - previous.close);
      
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
    
    // 计算ATR（简单移动平均）
    const recentTRs = trueRanges.slice(-period);
    return recentTRs.reduce((sum, tr) => sum + tr, 0) / recentTRs.length;
  }

  /**
   * 计算支撑阻力位 - 基于价格行为的真实技术分析
   */
  private calculateSupportResistance(klines: KlineDataPoint[]): {
    support: number[];
    resistance: number[];
  } {
    if (klines.length < 10) {
      return { support: [], resistance: [] };
    }

    const highs = klines.map(k => k.high);
    const lows = klines.map(k => k.low);
    const closes = klines.map(k => k.close);
    
    const support: number[] = [];
    const resistance: number[] = [];
    
    // 寻找局部高点和低点
    for (let i = 2; i < klines.length - 2; i++) {
      // 局部高点（阻力位）
      if (highs[i] > highs[i-1] && highs[i] > highs[i-2] && 
          highs[i] > highs[i+1] && highs[i] > highs[i+2]) {
        resistance.push(highs[i]);
      }
      
      // 局部低点（支撑位）
      if (lows[i] < lows[i-1] && lows[i] < lows[i-2] && 
          lows[i] < lows[i+1] && lows[i] < lows[i+2]) {
        support.push(lows[i]);
      }
    }
    
    // 如果没有找到明显的支撑阻力位，使用统计方法
    if (support.length === 0) {
      const sortedLows = [...lows].sort((a, b) => a - b);
      support.push(sortedLows[Math.floor(sortedLows.length * 0.2)]); // 20%分位数
    }
    
    if (resistance.length === 0) {
      const sortedHighs = [...highs].sort((a, b) => b - a);
      resistance.push(sortedHighs[Math.floor(sortedHighs.length * 0.2)]); // 80%分位数
    }
    
    return {
      support: support.slice(-3), // 最近3个支撑位
      resistance: resistance.slice(-3) // 最近3个阻力位
    };
  }
}
