/**
 * 数据质量仪表板服务
 * 提供数据质量的可视化和管理功能
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../di/types';
import { IBasicLogger } from '../logging/interfaces/basic-logger.interface';
import { EnhancedDataQualityMonitor, DataQualityReport, QualityTrendAnalysis } from './unified-data-quality-monitor';
import { SecureIdGenerator } from '../utils/secure-id-generator';

/**
 * 仪表板配置
 */
export interface DashboardConfig {
  refreshInterval: number;
  maxDisplayItems: number;
  enableRealTimeUpdates: boolean;
  enableAlerts: boolean;
  alertThresholds: {
    criticalScore: number;
    anomalyCount: number;
  };
}

/**
 * 仪表板数据
 */
export interface DashboardData {
  overview: {
    totalSymbols: number;
    averageQualityScore: number;
    activeAnomalies: number;
    lastUpdateTime: Date;
  };
  qualityDistribution: {
    excellent: number;
    good: number;
    fair: number;
    poor: number;
    critical: number;
  };
  topIssues: Array<{
    type: string;
    count: number;
    severity: string;
    affectedSymbols: string[];
  }>;
  recentReports: DataQualityReport[];
  trendAnalysis: QualityTrendAnalysis[];
  alerts: Array<{
    id: string;
    type: 'QUALITY_DEGRADATION' | 'ANOMALY_SPIKE' | 'DATA_MISSING';
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    message: string;
    timestamp: Date;
    affectedSymbols: string[];
  }>;
}

/**
 * 质量警报
 */
export interface QualityAlert {
  id: string;
  type: 'QUALITY_DEGRADATION' | 'ANOMALY_SPIKE' | 'DATA_MISSING' | 'THRESHOLD_BREACH';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  message: string;
  timestamp: Date;
  affectedSymbols: string[];
  metadata: any;
  isResolved: boolean;
  resolvedAt?: Date;
}

/**
 * 数据质量仪表板
 */
@injectable()
export class DataQualityDashboard {
  private readonly config: DashboardConfig;
  private readonly alerts: Map<string, QualityAlert> = new Map();
  private dashboardTimer: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.EnhancedDataQualityMonitor) private readonly qualityMonitor: EnhancedDataQualityMonitor
  ) {
    this.config = {
      refreshInterval: 30000, // 30秒刷新
      maxDisplayItems: 50,
      enableRealTimeUpdates: true,
      enableAlerts: true,
      alertThresholds: {
        criticalScore: 0.4,
        anomalyCount: 5
      }
    };
  }

  /**
   * 启动仪表板
   */
  start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.logger.info('启动数据质量仪表板', {
      refreshInterval: this.config.refreshInterval,
      realTimeUpdates: this.config.enableRealTimeUpdates
    });

    if (this.config.enableRealTimeUpdates) {
      this.startPeriodicUpdate();
    }
  }

  /**
   * 停止仪表板
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.dashboardTimer) {
      clearInterval(this.dashboardTimer);
      this.dashboardTimer = null;
    }

    this.logger.info('停止数据质量仪表板');
  }

  /**
   * 获取仪表板数据
   */
  async getDashboardData(symbols?: string[]): Promise<DashboardData> {
    try {
      const startTime = Date.now();

      // 获取质量统计
      const stats = this.qualityMonitor.getQualityStatistics();

      // 获取最近报告
      const recentReports = this.getRecentReports(symbols);

      // 获取趋势分析
      const trendAnalysis = await this.getTrendAnalysis(symbols);

      // 构建概览数据
      const overview = {
        totalSymbols: this.getTotalSymbols(symbols),
        averageQualityScore: stats.averageScore,
        activeAnomalies: this.getActiveAnomaliesCount(recentReports),
        lastUpdateTime: new Date()
      };

      // 构建质量分布
      const qualityDistribution = {
        excellent: stats.qualityDistribution.EXCELLENT || 0,
        good: stats.qualityDistribution.GOOD || 0,
        fair: stats.qualityDistribution.FAIR || 0,
        poor: stats.qualityDistribution.POOR || 0,
        critical: stats.qualityDistribution.CRITICAL || 0
      };

      // 处理顶级问题
      const topIssues = this.processTopIssues(stats.topIssues, symbols);

      // 获取活跃警报
      const alerts = this.getActiveAlerts();

      const dashboardData: DashboardData = {
        overview,
        qualityDistribution,
        topIssues,
        recentReports: recentReports.slice(0, this.config.maxDisplayItems),
        trendAnalysis,
        alerts
      };

      // 检查并生成新警报
      if (this.config.enableAlerts) {
        await this.checkAndGenerateAlerts(dashboardData);
      }

      const processingTime = Date.now() - startTime;
      this.logger.debug('仪表板数据生成完成', {
        symbols: symbols?.length || 'all',
        processingTime,
        reportsCount: recentReports.length,
        alertsCount: alerts.length
      });

      return dashboardData;

    } catch (error) {
      this.logger.error('获取仪表板数据失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取最近的质量报告
   */
  private getRecentReports(symbols?: string[]): DataQualityReport[] {
    const allReports: DataQualityReport[] = [];

    if (symbols) {
      symbols.forEach(symbol => {
        const reports = this.qualityMonitor.getQualityHistory(symbol);
        allReports.push(...reports);
      });
    } else {
      // 获取所有符号的报告（这里需要实现获取所有符号的逻辑）
      // 暂时返回空数组
    }

    // 按时间戳排序，最新的在前
    return allReports
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, this.config.maxDisplayItems);
  }

  /**
   * 获取趋势分析
   */
  private async getTrendAnalysis(symbols?: string[]): Promise<QualityTrendAnalysis[]> {
    const trends: QualityTrendAnalysis[] = [];

    if (symbols) {
      for (const symbol of symbols.slice(0, 10)) { // 限制最多10个符号
        try {
          const trend = await this.qualityMonitor.getQualityTrend(symbol);
          trends.push(trend);
        } catch (error) {
          this.logger.warn('获取符号趋势失败', { symbol, error });
        }
      }
    }

    return trends;
  }

  /**
   * 获取总符号数
   */
  private getTotalSymbols(symbols?: string[]): number {
    return symbols ? symbols.length : 0; // 这里需要实现获取总符号数的逻辑
  }

  /**
   * 获取活跃异常数量
   */
  private getActiveAnomaliesCount(reports: DataQualityReport[]): number {
    return reports.reduce((count, report) => count + report.anomalies.length, 0);
  }

  /**
   * 处理顶级问题
   */
  private processTopIssues(topIssues: Array<{ type: string; count: number; severity: string }>, symbols?: string[]): Array<{
    type: string;
    count: number;
    severity: string;
    affectedSymbols: string[];
  }> {
    return topIssues.map(issue => ({
      ...issue,
      affectedSymbols: symbols ? symbols.slice(0, 5) : [] // 示例：显示前5个受影响的符号
    }));
  }

  /**
   * 获取活跃警报
   */
  private getActiveAlerts(): Array<{
    id: string;
    type: 'QUALITY_DEGRADATION' | 'ANOMALY_SPIKE' | 'DATA_MISSING';
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    message: string;
    timestamp: Date;
    affectedSymbols: string[];
  }> {
    return Array.from(this.alerts.values())
      .filter(alert => !alert.isResolved)
      .map(alert => ({
        id: alert.id,
        type: alert.type,
        severity: alert.severity,
        message: alert.message,
        timestamp: alert.timestamp,
        affectedSymbols: alert.affectedSymbols
      }))
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, this.config.maxDisplayItems);
  }

  /**
   * 检查并生成警报
   */
  private async checkAndGenerateAlerts(dashboardData: DashboardData): Promise<void> {
    // 检查质量评分警报
    if (dashboardData.overview.averageQualityScore < this.config.alertThresholds.criticalScore) {
      this.generateAlert({
        type: 'QUALITY_DEGRADATION',
        severity: 'CRITICAL',
        title: '整体数据质量严重下降',
        message: `平均质量评分降至 ${(dashboardData.overview.averageQualityScore * 100).toFixed(1)}%`,
        affectedSymbols: [],
        metadata: { averageScore: dashboardData.overview.averageQualityScore }
      });
    }

    // 检查异常数量警报
    if (dashboardData.overview.activeAnomalies > this.config.alertThresholds.anomalyCount) {
      this.generateAlert({
        type: 'ANOMALY_SPIKE',
        severity: 'HIGH',
        title: '异常数量激增',
        message: `检测到 ${dashboardData.overview.activeAnomalies} 个活跃异常`,
        affectedSymbols: [],
        metadata: { anomalyCount: dashboardData.overview.activeAnomalies }
      });
    }

    // 检查数据缺失警报
    const missingDataSymbols = this.detectMissingData(dashboardData.recentReports);
    if (missingDataSymbols.length > 0) {
      this.generateAlert({
        type: 'DATA_MISSING',
        severity: 'MEDIUM',
        title: '数据缺失检测',
        message: `${missingDataSymbols.length} 个符号存在数据缺失`,
        affectedSymbols: missingDataSymbols,
        metadata: { missingSymbols: missingDataSymbols }
      });
    }
  }

  /**
   * 生成警报
   */
  private generateAlert(alertData: {
    type: 'QUALITY_DEGRADATION' | 'ANOMALY_SPIKE' | 'DATA_MISSING';
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    title: string;
    message: string;
    affectedSymbols: string[];
    metadata: any;
  }): void {
    const alertId = SecureIdGenerator.generatePrefixedId(alertData.type, 9);

    const alert: QualityAlert = {
      id: alertId,
      type: alertData.type,
      severity: alertData.severity,
      title: alertData.title,
      message: alertData.message,
      timestamp: new Date(),
      affectedSymbols: alertData.affectedSymbols,
      metadata: alertData.metadata,
      isResolved: false
    };

    this.alerts.set(alertId, alert);

    this.logger.warn('生成数据质量警报', {
      alertId,
      type: alert.type,
      severity: alert.severity,
      message: alert.message
    });
  }

  /**
   * 检测数据缺失
   */
  private detectMissingData(reports: DataQualityReport[]): string[] {
    const symbolLastSeen = new Map<string, Date>();
    const now = Date.now();
    const missingThreshold = 5 * 60 * 1000; // 5分钟

    reports.forEach(report => {
      const lastSeen = symbolLastSeen.get(report.symbol);
      if (!lastSeen || report.timestamp > lastSeen) {
        symbolLastSeen.set(report.symbol, report.timestamp);
      }
    });

    const missingSymbols: string[] = [];
    symbolLastSeen.forEach((lastSeen, symbol) => {
      if (now - lastSeen.getTime() > missingThreshold) {
        missingSymbols.push(symbol);
      }
    });

    return missingSymbols;
  }

  /**
   * 启动定期更新
   */
  private startPeriodicUpdate(): void {
    this.dashboardTimer = setInterval(() => {
      this.performPeriodicUpdate();
    }, this.config.refreshInterval);
  }

  /**
   * 执行定期更新
   */
  private async performPeriodicUpdate(): Promise<void> {
    try {
      // 清理过期警报
      this.cleanupExpiredAlerts();

      // 这里可以添加其他定期更新逻辑
      
    } catch (error) {
      this.logger.error('定期更新失败', { error });
    }
  }

  /**
   * 清理过期警报
   */
  private cleanupExpiredAlerts(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    let cleanedCount = 0;

    this.alerts.forEach((alert, id) => {
      if (now - alert.timestamp.getTime() > maxAge) {
        this.alerts.delete(id);
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      this.logger.debug('清理过期警报', { cleanedCount });
    }
  }

  /**
   * 解决警报
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId);
    if (alert && !alert.isResolved) {
      alert.isResolved = true;
      alert.resolvedAt = new Date();
      
      this.logger.info('警报已解决', { alertId, type: alert.type });
      return true;
    }
    return false;
  }

  /**
   * 获取警报详情
   */
  getAlert(alertId: string): QualityAlert | null {
    return this.alerts.get(alertId) || null;
  }

  /**
   * 获取所有警报
   */
  getAllAlerts(): QualityAlert[] {
    return Array.from(this.alerts.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * 销毁资源
   */
  destroy(): void {
    this.stop();
    this.alerts.clear();
    this.logger.info('DataQualityDashboard 资源已清理');
  }
}
