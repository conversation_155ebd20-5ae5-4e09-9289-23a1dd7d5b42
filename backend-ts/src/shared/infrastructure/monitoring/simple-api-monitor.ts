import axios, { AxiosResponse } from 'axios';
import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';

interface APIEndpoint {
  name: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  path: string;
  expectedStatus: number[];
  critical: boolean;
  timeout: number;
  description: string;
  body?: any;
  headers?: Record<string, string>;
  validation?: {
    requiredFields?: string[];
    customValidator?: string;
  };
}

interface MonitoringConfig {
  metadata?: {
    generatedAt?: string;
    totalEndpoints?: number;
    purpose?: string;
    description?: string;
    version?: string;
  };
  endpoints: APIEndpoint[];
  monitoring: {
    interval: number;
    retryAttempts: number;
    retryDelay: number;
    alertThreshold: {
      consecutiveFailures: number;
      uptimeBelow: number;
    };
  };
  notifications: {
    enabled: boolean;
    channels: string[];
    webhook?: {
      url?: string;
      enabled: boolean;
    };
  };
}

interface MonitorResult {
  endpoint: string;
  success: boolean;
  statusCode?: number;
  responseTime: number;
  error?: string;
  timestamp: Date;
}

interface CheckReport {
  summary: {
    totalEndpoints: number;
    successfulEndpoints: number;
    failedEndpoints: number;
    successRate: number;
    totalResponseTime: number;
    averageResponseTime: number;
    timestamp: Date;
  };
  results: MonitorResult[];
  groupedResults: {
    [category: string]: MonitorResult[];
  };
}

export class SimpleAPIMonitor extends EventEmitter {
  private config: MonitoringConfig;
  private stats: Map<string, {
    totalChecks: number;
    successCount: number;
    consecutiveFailures: number;
    lastSuccess?: Date;
    lastFailure?: Date;
    avgResponseTime: number;
  }> = new Map();
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private baseUrl: string;

  constructor(configPath?: string, baseUrl: string = 'http://localhost:3001') {
    super();
    this.baseUrl = baseUrl;
    this.loadConfig(configPath);
    this.initializeStats();
  }

  private loadConfig(configPath?: string): void {
    // 配置文件优先级：指定路径 > 完整配置 > 启动验证 > 基础配置
    const startupConfigPath = path.join(process.cwd(), 'config', 'api-monitoring-startup.json');
    const completeConfigPath = path.join(process.cwd(), 'config', 'api-monitoring-complete.json');
    const basicConfigPath = path.join(process.cwd(), 'config', 'api-monitoring.json');

    let filePath = configPath;
    let configType = '指定';

    if (!filePath) {
      if (fs.existsSync(completeConfigPath)) {
        filePath = completeConfigPath;
        configType = '完整';
      } else if (fs.existsSync(startupConfigPath)) {
        filePath = startupConfigPath;
        configType = '启动验证';
      } else {
        filePath = basicConfigPath;
        configType = '基础';
      }
    }

    try {
      const configData = fs.readFileSync(filePath, 'utf8');
      this.config = JSON.parse(configData);
      console.log(`✅ ${configType}API监控配置加载成功: ${this.config.endpoints.length} 个端点`);

      // 显示配置元数据
      if (this.config.metadata) {
        console.log(`📋 配置描述: ${this.config.metadata.description || '无描述'}`);
        if (this.config.metadata.generatedAt) {
          console.log(`📅 生成时间: ${new Date(this.config.metadata.generatedAt).toLocaleString()}`);
        }
      }

    } catch (error) {
      console.error('❌ 加载API监控配置失败:', error);
      // 使用默认配置
      this.config = {
        endpoints: [],
        monitoring: {
          interval: 60000,
          retryAttempts: 3,
          retryDelay: 5000,
          alertThreshold: {
            consecutiveFailures: 3,
            uptimeBelow: 95
          }
        },
        notifications: {
          enabled: true,
          channels: ['console']
        }
      };
    }
  }

  private initializeStats(): void {
    this.config.endpoints.forEach(endpoint => {
      this.stats.set(endpoint.name, {
        totalChecks: 0,
        successCount: 0,
        consecutiveFailures: 0,
        avgResponseTime: 0
      });
    });
  }

  async start(): Promise<void> {
    console.log('🚀 启动简化API监控系统...');
    console.log(`📊 监控端点数量: ${this.config.endpoints.length}`);
    
    // 立即执行一次检查
    await this.checkAllEndpoints();
    
    // 设置定期检查
    const interval = setInterval(async () => {
      await this.checkAllEndpoints();
    }, this.config.monitoring.interval);
    
    this.intervals.set('main', interval);
    
    console.log(`✅ API监控系统已启动，检查间隔: ${this.config.monitoring.interval}ms`);
  }

  stop(): void {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.clear();
    console.log('🛑 API监控系统已停止');
  }

  /**
   * 执行一次性API检查（不启动持续监控）
   */
  async runOnceCheck(): Promise<CheckReport> {
    console.log('🔍 开始执行一次性API检查...');
    console.log(`📊 检查端点数量: ${this.config.endpoints.length}`);

    const results: MonitorResult[] = [];
    const startTime = Date.now();

    // 并发检查所有端点
    const checkPromises = this.config.endpoints.map(async (endpoint) => {
      const result = await this.checkSingleEndpoint(endpoint);
      results.push(result);
      return result;
    });

    await Promise.all(checkPromises);

    const totalTime = Date.now() - startTime;

    // 生成报告
    const report = this.generateCheckReport(results, totalTime);

    console.log(`✅ 一次性API检查完成，耗时: ${totalTime}ms`);
    console.log(`📈 成功率: ${report.summary.successRate.toFixed(2)}%`);

    return report;
  }

  /**
   * 执行一次性检查并保存报告
   */
  async runOnceCheckAndSaveReport(reportDir: string = 'docs/api'): Promise<string> {
    const report = await this.runOnceCheck();
    const reportPath = await this.saveReport(report, reportDir);

    console.log(`📄 检查报告已保存: ${reportPath}`);
    return reportPath;
  }

  private async checkAllEndpoints(): Promise<void> {
    const promises = this.config.endpoints.map(endpoint => 
      this.checkEndpoint(endpoint)
    );
    
    await Promise.allSettled(promises);
    this.reportOverallStatus();
  }

  private async checkEndpoint(endpoint: APIEndpoint): Promise<void> {
    const result = await this.checkSingleEndpoint(endpoint);
    this.updateStats(endpoint.name, result);
    this.emit('check:result', result);

    if (result.success) {
      console.log(`🟢 API检查成功: ${endpoint.name} (${result.responseTime}ms)`);
    } else {
      console.log(`🔴 API检查失败: ${endpoint.name} - ${result.error} (${result.responseTime}ms)`);
    }
  }

  /**
   * 检查单个端点并返回结果（用于一次性检查）
   */
  private async checkSingleEndpoint(endpoint: APIEndpoint): Promise<MonitorResult> {
    const startTime = Date.now();

    try {
      const response = await this.makeRequest(endpoint);
      const responseTime = Date.now() - startTime;

      const isValid = this.validateResponse(response, endpoint);
      const isStatusValid = endpoint.expectedStatus.includes(response.status);
      const success = isValid && isStatusValid;

      const result: MonitorResult = {
        endpoint: endpoint.name,
        success,
        statusCode: response.status,
        responseTime,
        timestamp: new Date()
      };

      if (!success) {
        result.error = `Status: ${response.status}, Valid: ${isValid}`;
      }

      return result;

    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      return {
        endpoint: endpoint.name,
        success: false,
        statusCode: error.response?.status,
        responseTime,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  private async makeRequest(endpoint: APIEndpoint): Promise<AxiosResponse> {
    const url = `${this.baseUrl}${endpoint.path}`;
    
    const config = {
      method: endpoint.method,
      url,
      timeout: endpoint.timeout,
      headers: endpoint.headers || {},
      validateStatus: () => true // 接受所有状态码，我们自己验证
    };
    
    if (endpoint.method !== 'GET' && endpoint.body !== undefined) {
      (config as any).data = endpoint.body;
    }
    
    return axios(config);
  }

  private validateResponse(response: AxiosResponse, endpoint: APIEndpoint): boolean {
    if (!endpoint.validation) return true;
    
    const { requiredFields, customValidator } = endpoint.validation;
    
    // 自定义验证器
    if (customValidator) {
      switch (customValidator) {
        case 'auth_required':
          // 对于需要认证的API，401是正常的
          return response.status === 401 || (response.status === 200 && response.data?.success);
        case 'validation_error_ok':
          // 对于验证错误，400是正常的
          return response.status === 400 || (response.status === 200 && response.data?.success);
        default:
          return true;
      }
    }
    
    // 检查必需字段
    if (requiredFields && response.data) {
      return requiredFields.every(field => 
        response.data.hasOwnProperty(field)
      );
    }
    
    return true;
  }

  private updateStats(endpointName: string, result: MonitorResult): void {
    const stats = this.stats.get(endpointName);
    if (!stats) return;
    
    stats.totalChecks++;
    
    if (result.success) {
      stats.successCount++;
      stats.consecutiveFailures = 0;
      stats.lastSuccess = result.timestamp;
    } else {
      stats.consecutiveFailures++;
      stats.lastFailure = result.timestamp;
    }
    
    // 更新平均响应时间
    stats.avgResponseTime = (stats.avgResponseTime * (stats.totalChecks - 1) + result.responseTime) / stats.totalChecks;
  }

  private reportOverallStatus(): void {
    const totalEndpoints = this.config.endpoints.length;
    let healthyEndpoints = 0;
    
    this.stats.forEach((stats, name) => {
      const uptime = stats.totalChecks > 0 ? (stats.successCount / stats.totalChecks) * 100 : 0;
      if (uptime >= this.config.monitoring.alertThreshold.uptimeBelow) {
        healthyEndpoints++;
      }
    });
    
    const overallHealth = (healthyEndpoints / totalEndpoints) * 100;
    
    console.log(`📊 整体健康状况: ${overallHealth.toFixed(1)}% (${healthyEndpoints}/${totalEndpoints})`);
    
    if (overallHealth < this.config.monitoring.alertThreshold.uptimeBelow) {
      console.log(`🚨 警告: 整体可用性低于 ${this.config.monitoring.alertThreshold.uptimeBelow}%`);
    }
  }

  getStats(): Map<string, any> {
    return this.stats;
  }

  /**
   * 生成检查报告
   */
  private generateCheckReport(results: MonitorResult[], totalTime: number): CheckReport {
    const successfulResults = results.filter(r => r.success);
    const failedResults = results.filter(r => !r.success);

    const summary = {
      totalEndpoints: results.length,
      successfulEndpoints: successfulResults.length,
      failedEndpoints: failedResults.length,
      successRate: results.length > 0 ? (successfulResults.length / results.length) * 100 : 0,
      totalResponseTime: totalTime,
      averageResponseTime: results.length > 0 ? results.reduce((sum, r) => sum + r.responseTime, 0) / results.length : 0,
      timestamp: new Date()
    };

    // 按功能模块分组
    const groupedResults: { [category: string]: MonitorResult[] } = {};
    results.forEach(result => {
      const category = this.categorizeEndpoint(result.endpoint);
      if (!groupedResults[category]) {
        groupedResults[category] = [];
      }
      groupedResults[category].push(result);
    });

    return {
      summary,
      results,
      groupedResults
    };
  }

  /**
   * 根据端点名称分类
   */
  private categorizeEndpoint(endpointName: string): string {
    if (endpointName.includes('health')) return '系统健康检查';
    if (endpointName.includes('market-data')) return '市场数据';
    if (endpointName.includes('auth')) return '认证授权';
    if (endpointName.includes('ai') || endpointName.includes('reasoning')) return 'AI推理';
    if (endpointName.includes('trading')) return '交易执行';
    if (endpointName.includes('risk')) return '风险管理';
    if (endpointName.includes('analytics')) return '数据分析';
    if (endpointName.includes('signals')) return '交易信号';
    if (endpointName.includes('trend')) return '趋势分析';
    if (endpointName.includes('user')) return '用户管理';
    if (endpointName.includes('admin')) return '系统管理';
    return '其他接口';
  }

  /**
   * 保存报告到文件
   */
  private async saveReport(report: CheckReport, reportDir: string): Promise<string> {
    // 确保目录存在
    const fullReportDir = path.resolve(reportDir);
    if (!fs.existsSync(fullReportDir)) {
      fs.mkdirSync(fullReportDir, { recursive: true });
    }

    // 使用统一的文件名，自动覆盖之前的报告
    const fileName = 'api-health-check-latest.md';
    const filePath = path.join(fullReportDir, fileName);

    // 如果存在旧报告，先备份为历史文件
    if (fs.existsSync(filePath)) {
      const backupFileName = 'api-health-check-previous.md';
      const backupFilePath = path.join(fullReportDir, backupFileName);
      fs.copyFileSync(filePath, backupFilePath);
    }

    // 生成Markdown报告
    const markdownContent = this.generateMarkdownReport(report);

    // 写入文件
    fs.writeFileSync(filePath, markdownContent, 'utf8');

    return filePath;
  }

  /**
   * 生成Markdown格式的报告
   */
  private generateMarkdownReport(report: CheckReport): string {
    const { summary, results, groupedResults } = report;
    const timestamp = summary.timestamp.toLocaleString('zh-CN');

    let markdown = `# API健康检查报告\n\n`;

    // 添加脚本说明信息
    markdown += `## 📋 报告说明\n\n`;
    markdown += `本报告由API健康检查系统手动执行生成，用于验证所有API端点的可用性和健康状况。\n\n`;
    markdown += `> **重要**: API健康检查已改为纯手动执行模式，不再自动运行。\n\n`;
    markdown += `### 🔧 检查脚本信息\n`;
    markdown += `- **脚本位置**: \`scripts/run-api-health-check.js\`\n`;
    markdown += `- **配置文件**: \`config/api-monitoring-complete.json\`\n`;
    markdown += `- **检查方式**: 手动执行一次性全面检查所有API端点\n`;
    markdown += `- **执行模式**: 纯手动，不自动运行\n`;
    markdown += `- **报告文件**: \`docs/api/api-health-check-latest.md\` (自动覆盖)\n`;
    markdown += `- **备份文件**: \`docs/api/api-health-check-previous.md\` (上次检查备份)\n\n`;

    markdown += `### 📖 使用方法\n`;
    markdown += `\`\`\`bash\n`;
    markdown += `# 手动执行API健康检查\n`;
    markdown += `node scripts/run-api-health-check.js\n\n`;
    markdown += `# 查看帮助信息\n`;
    markdown += `node scripts/run-api-health-check.js --help\n\n`;
    markdown += `# 重新生成监控配置\n`;
    markdown += `node scripts/generate-api-monitoring-config.js\n`;
    markdown += `\`\`\`\n\n`;

    markdown += `### ⚙️ 系统配置\n`;
    markdown += `- **基础URL**: ${this.baseUrl}\n`;
    markdown += `- **超时时间**: 10秒\n`;
    markdown += `- **并发检查**: 是\n`;
    markdown += `- **智能验证**: 支持认证和参数验证错误识别\n\n`;

    markdown += `---\n\n`;

    // 检查基本信息
    markdown += `## 📊 检查基本信息\n\n`;
    markdown += `**生成时间**: ${timestamp}\n`;
    markdown += `**检查耗时**: ${summary.totalResponseTime}ms\n`;
    markdown += `**平均响应时间**: ${summary.averageResponseTime.toFixed(2)}ms\n\n`;

    // 概览统计
    markdown += `## 📊 检查概览\n\n`;
    markdown += `| 指标 | 数值 |\n`;
    markdown += `|------|------|\n`;
    markdown += `| 总端点数 | ${summary.totalEndpoints} |\n`;
    markdown += `| 成功端点 | ${summary.successfulEndpoints} |\n`;
    markdown += `| 失败端点 | ${summary.failedEndpoints} |\n`;
    markdown += `| 成功率 | ${summary.successRate.toFixed(2)}% |\n\n`;

    // 健康状态指示器
    const healthStatus = summary.successRate >= 95 ? '🟢 优秀' :
                        summary.successRate >= 80 ? '🟡 良好' :
                        summary.successRate >= 60 ? '🟠 一般' : '🔴 需要关注';
    markdown += `**整体健康状态**: ${healthStatus}\n\n`;

    // 按功能模块分组显示
    markdown += `## 📋 分模块检查结果\n\n`;
    Object.entries(groupedResults).forEach(([category, categoryResults]) => {
      const successCount = categoryResults.filter(r => r.success).length;
      const successRate = (successCount / categoryResults.length) * 100;
      const statusIcon = successRate === 100 ? '✅' : successRate >= 80 ? '⚠️' : '❌';

      markdown += `### ${statusIcon} ${category}\n\n`;
      markdown += `成功率: ${successRate.toFixed(1)}% (${successCount}/${categoryResults.length})\n\n`;

      categoryResults.forEach(result => {
        const icon = result.success ? '🟢' : '🔴';
        const status = result.success ? '成功' : '失败';
        markdown += `- ${icon} **${result.endpoint}**: ${status} (${result.responseTime}ms)`;
        if (result.error) {
          markdown += ` - ${result.error}`;
        }
        markdown += `\n`;
      });
      markdown += `\n`;
    });

    // 失败详情
    const failedResults = results.filter(r => !r.success);
    if (failedResults.length > 0) {
      markdown += `## ❌ 失败详情\n\n`;
      failedResults.forEach(result => {
        markdown += `### ${result.endpoint}\n\n`;
        markdown += `- **状态码**: ${result.statusCode || 'N/A'}\n`;
        markdown += `- **响应时间**: ${result.responseTime}ms\n`;
        markdown += `- **错误信息**: ${result.error || '未知错误'}\n`;
        markdown += `- **检查时间**: ${result.timestamp.toLocaleString('zh-CN')}\n\n`;
      });
    }

    // 建议
    markdown += `## 💡 建议\n\n`;
    if (summary.successRate === 100) {
      markdown += `🎉 所有API端点运行正常，系统健康状况良好！\n\n`;
    } else if (summary.successRate >= 80) {
      markdown += `✅ 大部分API端点运行正常，建议关注失败的端点。\n\n`;
    } else {
      markdown += `⚠️ 多个API端点存在问题，建议立即检查和修复。\n\n`;
    }

    if (summary.averageResponseTime > 5000) {
      markdown += `⚠️ 平均响应时间较长(${summary.averageResponseTime.toFixed(2)}ms)，建议优化性能。\n\n`;
    }

    markdown += `---\n\n`;

    // 添加详细的系统信息和使用说明
    markdown += `## 📚 系统信息\n\n`;

    markdown += `### 🔍 检查逻辑说明\n`;
    markdown += `1. **端点发现**: 从Swagger文档(\`/api-docs.json\`)和运行时路由自动发现所有API端点\n`;
    markdown += `2. **并发检查**: 同时检查所有端点，提高检查效率\n`;
    markdown += `3. **智能验证**: 区分真正的错误和预期的认证/参数错误\n`;
    markdown += `4. **分类统计**: 按功能模块自动分组，便于问题定位\n`;
    markdown += `5. **状态判断**: 基于HTTP状态码和响应内容进行健康状况判断\n\n`;

    markdown += `### 📈 状态码说明\n`;
    markdown += `- **2xx**: 成功响应，API正常工作\n`;
    markdown += `- **400**: 请求参数错误（通常是正常的，因为测试请求缺少必需参数）\n`;
    markdown += `- **401**: 未授权（通常是正常的，因为测试请求缺少认证信息）\n`;
    markdown += `- **404**: 端点不存在或路由配置错误\n`;
    markdown += `- **500**: 服务器内部错误，需要检查代码实现\n`;
    markdown += `- **503**: 服务不可用，可能是依赖服务问题\n\n`;

    markdown += `### 🛠️ 故障排查\n`;
    markdown += `1. **高失败率**: 检查服务器是否正常启动，依赖服务是否可用\n`;
    markdown += `2. **特定模块失败**: 检查该模块的具体实现和依赖\n`;
    markdown += `3. **超时错误**: 检查网络连接和服务器性能\n`;
    markdown += `4. **认证错误**: 确认API是否需要特殊的认证配置\n\n`;

    markdown += `### 📁 相关文件\n`;
    markdown += `- **技术文档**: \`docs/developer-guides/自动化API监控系统技术说明文档.md\`\n`;
    markdown += `- **Swagger文档**: http://localhost:3001/api-docs\n`;
    markdown += `- **完整配置**: \`config/api-monitoring-complete.json\` (${this.config.endpoints.length}个端点)\n`;
    markdown += `- **启动配置**: \`config/api-monitoring-startup.json\` (关键端点)\n\n`;

    markdown += `### 🔄 执行方式\n`;
    markdown += `- **手动执行**: 使用脚本手动获取最新状态\n`;
    markdown += `- **按需检查**: 开发者根据需要随时执行\n`;
    markdown += `- **CI/CD**: 可集成到部署流程中验证API可用性\n`;
    markdown += `- **定期检查**: 可配置定时任务定期执行健康检查\n\n`;

    markdown += `---\n\n`;
    markdown += `*本报告由API健康检查系统 v5.0.0 手动执行生成*  \n`;
    markdown += `*最后更新: ${timestamp}*  \n`;
    markdown += `*如有问题，请参考技术文档或联系开发团队*\n`;

    return markdown;
  }
}
