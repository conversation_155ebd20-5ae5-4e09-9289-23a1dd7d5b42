/**
 * API监控系统初始化器
 * 
 * 在应用启动时自动初始化和启动API监控系统
 * 确保所有API端点都被持续监控
 */

import { Container } from 'inversify';
import { Logger } from 'winston';
import { TYPES } from '../../di/types';
// 自动监控服务已移除
// import { configureMonitoringServices, startAPIMonitoring } from '../di/monitoring-container-config';

/**
 * API监控初始化器
 */
export class APIMonitoringInitializer {
  private static instance: APIMonitoringInitializer;
  private initialized = false;
  private logger?: Logger;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): APIMonitoringInitializer {
    if (!APIMonitoringInitializer.instance) {
      APIMonitoringInitializer.instance = new APIMonitoringInitializer();
    }
    return APIMonitoringInitializer.instance;
  }

  /**
   * 初始化API监控系统
   */
  async initialize(container: Container): Promise<void> {
    if (this.initialized) {
      console.log('⚠️ API监控系统已经初始化');
      return;
    }

    try {
      // 获取日志器
      this.logger = container.get<Logger>(TYPES.Logger);
      
      this.logger.info('🚀 开始初始化API监控系统...');

      // 自动监控服务已移除，改为手动执行模式
      this.logger.info('⚠️ API监控已改为手动执行模式，不再自动启动');
      this.logger.info('💡 使用 node scripts/run-api-health-check.js 手动执行检查');

      this.initialized = true;
      
      // 3. 输出启动成功信息
      this.logStartupSuccess();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      if (this.logger) {
        this.logger.error('❌ API监控系统初始化失败', { error: errorMessage });
      } else {
        console.error('❌ API监控系统初始化失败:', errorMessage);
      }
      
      throw error;
    }
  }

  /**
   * 输出启动成功信息
   */
  private logStartupSuccess(): void {
    const successMessage = `
╔══════════════════════════════════════════════════════════════╗
║                    🎯 API监控系统已启动                        ║
╠══════════════════════════════════════════════════════════════╣
║  ✅ 自动发现API端点                                           ║
║  ✅ 持续监控API可用性                                         ║
║  ✅ 实时日志输出                                              ║
║  ✅ 自动故障告警                                              ║
║  ✅ 性能指标收集                                              ║
╠══════════════════════════════════════════════════════════════╣
║  📊 监控间隔: 60秒                                            ║
║  📈 状态报告: 每5分钟                                         ║
║  🔔 关键端点故障将立即告警                                     ║
╚══════════════════════════════════════════════════════════════╝
    `;

    if (this.logger) {
      this.logger.info(successMessage);
    } else {
      console.log(successMessage);
    }
  }

  /**
   * 检查初始化状态
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 重置初始化状态（用于测试）
   */
  reset(): void {
    this.initialized = false;
    this.logger = undefined;
  }
}

/**
 * 便捷的初始化函数 - 使用一次性API检查
 */
export async function initializeAPIMonitoring(container: Container): Promise<void> {
  try {
    console.log('🔍 开始执行一次性API健康检查...');

    // 动态导入简化监控器
    const { SimpleAPIMonitor } = await import('../simple-api-monitor');

    // 创建监控器并执行一次性检查
    const monitor = new SimpleAPIMonitor();
    const reportPath = await monitor.runOnceCheckAndSaveReport('docs/api');

    console.log('✅ 一次性API健康检查完成');
    console.log(`📄 检查报告已保存到: ${reportPath}`);
  } catch (error) {
    console.error('❌ API健康检查失败:', error);
    throw error;
  }
}

/**
 * 应用启动时的API健康检查钩子
 * 在Express应用启动后调用此函数，执行一次性API健康检查
 */
export async function onApplicationStartup(container: Container): Promise<void> {
  try {
    console.log('🔧 应用启动后准备执行API健康检查...');

    // 延迟60秒启动，确保服务器完全启动并且所有路由都已注册
    setTimeout(async () => {
      try {
        await initializeAPIMonitoring(container);
        console.log('🎉 API健康检查完成');
      } catch (error) {
        console.error('💥 API健康检查失败:', error);
      }
    }, 60000);

  } catch (error) {
    console.error('💥 应用启动钩子执行失败:', error);
  }
}

/**
 * 优雅关闭API监控系统（已废弃）
 */
export async function shutdownAPIMonitoring(container: Container): Promise<void> {
  try {
    const logger = container.get<Logger>(TYPES.Logger);
    logger.info('⚠️ API监控系统已改为手动执行模式，无需关闭');

  } catch (error) {
    console.error('❌ 关闭API监控系统失败:', error);
  }
}

// 导出单例实例
export const apiMonitoringInitializer = APIMonitoringInitializer.getInstance();
