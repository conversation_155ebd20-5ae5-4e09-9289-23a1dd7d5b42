/**
 * 基础性能监控接口
 * 提供执行延迟、滑点、吞吐量等关键性能指标的监控功能
 */

/**
 * 执行延迟监控器接口
 */
export interface ExecutionLatencyMonitor {
  /**
   * 记录执行开始
   */
  recordExecutionStart(executionId: string, context: ExecutionContext): void;

  /**
   * 记录执行结束
   */
  recordExecutionEnd(executionId: string, result: ExecutionResult): void;

  /**
   * 获取延迟统计
   */
  getLatencyStats(timeRange?: TimeRange): Promise<LatencyStats>;

  /**
   * 获取延迟分布
   */
  getLatencyDistribution(timeRange?: TimeRange): Promise<LatencyDistribution>;

  /**
   * 检查延迟异常
   */
  checkLatencyAnomalies(): Promise<LatencyAnomaly[]>;
}

/**
 * 滑点监控器接口
 */
export interface SlippageMonitor {
  /**
   * 记录滑点数据
   */
  recordSlippage(slippageData: SlippageData): Promise<void>;

  /**
   * 获取滑点统计
   */
  getSlippageStats(symbol?: string, timeRange?: TimeRange): Promise<SlippageStats>;

  /**
   * 获取实时滑点
   */
  getRealTimeSlippage(symbol: string): Promise<RealTimeSlippage>;

  /**
   * 检查滑点异常
   */
  checkSlippageAnomalies(): Promise<SlippageAnomaly[]>;

  /**
   * 设置滑点阈值
   */
  setSlippageThresholds(thresholds: SlippageThresholds): void;
}

/**
 * 吞吐量监控器接口
 */
export interface ThroughputMonitor {
  /**
   * 记录操作
   */
  recordOperation(operation: OperationRecord): void;

  /**
   * 获取吞吐量统计
   */
  getThroughputStats(timeRange?: TimeRange): Promise<ThroughputStats>;

  /**
   * 获取实时吞吐量
   */
  getRealTimeThroughput(): Promise<RealTimeThroughput>;

  /**
   * 检查吞吐量异常
   */
  checkThroughputAnomalies(): Promise<ThroughputAnomaly[]>;
}

/**
 * 性能指标仪表板接口
 */
export interface PerformanceDashboard {
  /**
   * 获取仪表板数据
   */
  getDashboardData(timeRange?: TimeRange): Promise<DashboardData>;

  /**
   * 获取关键性能指标
   */
  getKPIs(): Promise<PerformanceKPIs>;

  /**
   * 获取性能趋势
   */
  getPerformanceTrends(timeRange?: TimeRange): Promise<PerformanceTrends>;

  /**
   * 生成性能报告
   */
  generatePerformanceReport(timeRange: TimeRange): Promise<PerformanceReport>;
}

/**
 * 性能告警管理器接口
 */
export interface PerformanceAlertManager {
  /**
   * 注册告警规则
   */
  registerAlertRule(rule: AlertRule): void;

  /**
   * 检查告警条件
   */
  checkAlertConditions(): Promise<Alert[]>;

  /**
   * 发送告警
   */
  sendAlert(alert: Alert): Promise<void>;

  /**
   * 获取活跃告警
   */
  getActiveAlerts(): Promise<Alert[]>;

  /**
   * 确认告警
   */
  acknowledgeAlert(alertId: string, userId: string): Promise<void>;
}

// ==================== 数据结构定义 ====================

/**
 * 执行上下文
 */
export interface ExecutionContext {
  type: 'order_placement' | 'order_cancellation' | 'market_data_fetch' | 'risk_calculation' | 'ai_reasoning';
  symbol?: string;
  accountId?: string;
  orderType?: string;
  quantity?: number;
  metadata?: Record<string, any>;
}

/**
 * 执行结果
 */
export interface ExecutionResult {
  success: boolean;
  error?: string;
  responseSize?: number;
  networkLatency?: number;
  processingTime?: number;
  metadata?: Record<string, any>;
}

/**
 * 时间范围
 */
export interface TimeRange {
  start: Date;
  end: Date;
}

/**
 * 延迟统计
 */
export interface LatencyStats {
  averageLatency: number;
  medianLatency: number;
  p95Latency: number;
  p99Latency: number;
  minLatency: number;
  maxLatency: number;
  totalOperations: number;
  timeRange: TimeRange;
  byType: Record<string, LatencyMetrics>;
}

/**
 * 延迟指标
 */
export interface LatencyMetrics {
  average: number;
  median: number;
  p95: number;
  p99: number;
  count: number;
}

/**
 * 延迟分布
 */
export interface LatencyDistribution {
  buckets: LatencyBucket[];
  totalSamples: number;
  timeRange: TimeRange;
}

/**
 * 延迟桶
 */
export interface LatencyBucket {
  range: string; // e.g., "0-10ms"
  count: number;
  percentage: number;
}

/**
 * 延迟异常
 */
export interface LatencyAnomaly {
  id: string;
  type: 'spike' | 'sustained_high' | 'timeout';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  detectedAt: Date;
  value: number;
  threshold: number;
  context: ExecutionContext;
  recommendations: string[];
}

/**
 * 滑点数据
 */
export interface SlippageData {
  orderId: string;
  symbol: string;
  orderType: 'market' | 'limit';
  side: 'buy' | 'sell';
  requestedPrice: number;
  executedPrice: number;
  slippage: number; // 滑点百分比
  slippageBps: number; // 滑点基点
  quantity: number;
  timestamp: Date;
  marketConditions: MarketConditions;
}

/**
 * 市场条件
 */
export interface MarketConditions {
  volatility: number;
  spread: number;
  volume: number;
  liquidity: 'high' | 'medium' | 'low';
  timeOfDay: string;
}

/**
 * 滑点统计
 */
export interface SlippageStats {
  averageSlippage: number;
  medianSlippage: number;
  maxSlippage: number;
  slippageDistribution: SlippageDistribution[];
  totalTrades: number;
  timeRange: TimeRange;
  bySymbol: Record<string, SlippageMetrics>;
  byOrderType: Record<string, SlippageMetrics>;
}

/**
 * 滑点指标
 */
export interface SlippageMetrics {
  average: number;
  median: number;
  max: number;
  count: number;
  totalCost: number; // 滑点总成本
}

/**
 * 滑点分布
 */
export interface SlippageDistribution {
  range: string; // e.g., "0-5bps"
  count: number;
  percentage: number;
}

/**
 * 实时滑点
 */
export interface RealTimeSlippage {
  symbol: string;
  currentSlippage: number;
  predictedSlippage: number;
  confidence: number;
  marketConditions: MarketConditions;
  lastUpdated: Date;
}

/**
 * 滑点异常
 */
export interface SlippageAnomaly {
  id: string;
  type: 'excessive_slippage' | 'negative_slippage' | 'slippage_spike';
  severity: 'low' | 'medium' | 'high' | 'critical';
  symbol: string;
  description: string;
  detectedAt: Date;
  slippage: number;
  threshold: number;
  impact: number; // 影响金额
  recommendations: string[];
}

/**
 * 滑点阈值
 */
export interface SlippageThresholds {
  warning: number; // 警告阈值（基点）
  critical: number; // 严重阈值（基点）
  bySymbol?: Record<string, { warning: number; critical: number }>;
  byOrderSize?: Array<{ minSize: number; maxSize: number; warning: number; critical: number }>;
}

/**
 * 操作记录
 */
export interface OperationRecord {
  id: string;
  type: string;
  timestamp: Date;
  duration?: number;
  success: boolean;
  metadata?: Record<string, any>;
}

/**
 * 吞吐量统计
 */
export interface ThroughputStats {
  operationsPerSecond: number;
  operationsPerMinute: number;
  operationsPerHour: number;
  totalOperations: number;
  successRate: number;
  errorRate: number;
  timeRange: TimeRange;
  byType: Record<string, ThroughputMetrics>;
}

/**
 * 吞吐量指标
 */
export interface ThroughputMetrics {
  rate: number; // 每秒操作数
  count: number;
  successRate: number;
}

/**
 * 实时吞吐量
 */
export interface RealTimeThroughput {
  currentRate: number; // 当前每秒操作数
  averageRate: number; // 平均每秒操作数
  peakRate: number; // 峰值每秒操作数
  lastMinuteOperations: number;
  lastUpdated: Date;
}

/**
 * 吞吐量异常
 */
export interface ThroughputAnomaly {
  id: string;
  type: 'low_throughput' | 'high_error_rate' | 'throughput_drop';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  detectedAt: Date;
  currentValue: number;
  expectedValue: number;
  threshold: number;
  recommendations: string[];
}

/**
 * 仪表板数据
 */
export interface DashboardData {
  overview: PerformanceOverview;
  latency: LatencyStats;
  slippage: SlippageStats;
  throughput: ThroughputStats;
  alerts: Alert[];
  trends: PerformanceTrends;
  lastUpdated: Date;
}

/**
 * 性能概览
 */
export interface PerformanceOverview {
  systemHealth: 'healthy' | 'warning' | 'critical';
  activeAlerts: number;
  criticalIssues: number;
  uptime: number; // 运行时间（秒）
  lastIncident?: Date;
}

/**
 * 关键性能指标
 */
export interface PerformanceKPIs {
  averageLatency: number;
  p95Latency: number;
  averageSlippage: number;
  throughputRate: number;
  successRate: number;
  systemAvailability: number;
  costEfficiency: number;
}

/**
 * 性能趋势
 */
export interface PerformanceTrends {
  latencyTrend: TrendData[];
  slippageTrend: TrendData[];
  throughputTrend: TrendData[];
  errorRateTrend: TrendData[];
}

/**
 * 趋势数据
 */
export interface TrendData {
  timestamp: Date;
  value: number;
  change?: number; // 相对于前一个数据点的变化
}

/**
 * 性能报告
 */
export interface PerformanceReport {
  id: string;
  generatedAt: Date;
  timeRange: TimeRange;
  summary: PerformanceSummary;
  detailedMetrics: DetailedMetrics;
  anomalies: PerformanceAnomaly[];
  recommendations: string[];
  attachments: ReportAttachment[];
}

/**
 * 性能摘要
 */
export interface PerformanceSummary {
  overallPerformance: 'excellent' | 'good' | 'fair' | 'poor';
  keyFindings: string[];
  majorIssues: string[];
  improvements: string[];
  costImpact: number;
}

/**
 * 详细指标
 */
export interface DetailedMetrics {
  latency: LatencyStats;
  slippage: SlippageStats;
  throughput: ThroughputStats;
  availability: AvailabilityMetrics;
  resourceUtilization: ResourceMetrics;
}

/**
 * 可用性指标
 */
export interface AvailabilityMetrics {
  uptime: number;
  downtime: number;
  availability: number; // 百分比
  incidents: IncidentSummary[];
}

/**
 * 事件摘要
 */
export interface IncidentSummary {
  id: string;
  startTime: Date;
  endTime?: Date;
  duration: number;
  severity: string;
  description: string;
  impact: string;
}

/**
 * 资源指标
 */
export interface ResourceMetrics {
  cpu: ResourceUsage;
  memory: ResourceUsage;
  network: ResourceUsage;
  storage: ResourceUsage;
}

/**
 * 资源使用情况
 */
export interface ResourceUsage {
  average: number;
  peak: number;
  utilization: number; // 百分比
}

/**
 * 性能异常
 */
export interface PerformanceAnomaly {
  id: string;
  type: string;
  category: 'latency' | 'slippage' | 'throughput' | 'availability' | 'resource';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  detectedAt: Date;
  resolvedAt?: Date;
  impact: AnomalyImpact;
  rootCause?: string;
  resolution?: string;
}

/**
 * 异常影响
 */
export interface AnomalyImpact {
  financial: number; // 财务影响
  operational: string; // 运营影响描述
  userExperience: 'none' | 'minor' | 'moderate' | 'severe';
  duration: number; // 持续时间（秒）
}

/**
 * 告警规则
 */
export interface AlertRule {
  id: string;
  name: string;
  description: string;
  category: 'latency' | 'slippage' | 'throughput' | 'availability';
  condition: AlertCondition;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldown: number; // 冷却时间（秒）
  actions: AlertAction[];
}

/**
 * 告警条件
 */
export interface AlertCondition {
  metric: string;
  operator: '>' | '<' | '>=' | '<=' | '==' | '!=';
  threshold: number;
  duration: number; // 持续时间（秒）
  aggregation?: 'avg' | 'max' | 'min' | 'sum' | 'count';
}

/**
 * 告警动作
 */
export interface AlertAction {
  type: 'email' | 'webhook' | 'sms' | 'slack';
  target: string;
  template?: string;
}

/**
 * 告警
 */
export interface Alert {
  id: string;
  ruleId: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'active' | 'acknowledged' | 'resolved';
  triggeredAt: Date;
  acknowledgedAt?: Date;
  acknowledgedBy?: string;
  resolvedAt?: Date;
  value: number;
  threshold: number;
  metadata?: Record<string, any>;
}

/**
 * 报告附件
 */
export interface ReportAttachment {
  id: string;
  name: string;
  type: 'chart' | 'data' | 'log';
  format: 'png' | 'pdf' | 'csv' | 'json';
  url: string;
  size: number;
}
