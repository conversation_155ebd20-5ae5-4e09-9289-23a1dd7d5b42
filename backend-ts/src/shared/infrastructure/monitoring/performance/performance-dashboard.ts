/**
 * 性能指标仪表板实现
 * 提供统一的性能指标仪表板和可视化功能
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../logging/logger.interface';
import { UnifiedErrorHandler } from '../../error-handling/unified-error-handler';
import { TYPES } from '../../di/types';
import {
  PerformanceDashboard,
  DashboardData,
  PerformanceKPIs,
  PerformanceTrends,
  PerformanceReport,
  TimeRange,
  PerformanceOverview,
  TrendData,
  ExecutionLatencyMonitor,
  SlippageMonitor,
  ThroughputMonitor,
  PerformanceAlertManager
} from './performance-monitoring.interface';

@injectable()
export class PerformanceDashboardImpl implements PerformanceDashboard {
  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler,
    @inject('ExecutionLatencyMonitor') private readonly latencyMonitor: ExecutionLatencyMonitor,
    @inject('SlippageMonitor') private readonly slippageMonitor: SlippageMonitor,
    @inject('ThroughputMonitor') private readonly throughputMonitor: ThroughputMonitor,
    @inject('PerformanceAlertManager') private readonly alertManager: PerformanceAlertManager
  ) {}

  /**
   * 获取仪表板数据
   */
  async getDashboardData(timeRange?: TimeRange): Promise<DashboardData> {
    try {
      this.logger.debug('获取仪表板数据', { timeRange });

      const defaultTimeRange = timeRange || {
        start: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24小时前
        end: new Date()
      };

      // 并行获取所有数据
      const [
        latencyStats,
        slippageStats,
        throughputStats,
        activeAlerts,
        trends
      ] = await Promise.all([
        this.latencyMonitor.getLatencyStats(defaultTimeRange),
        this.slippageMonitor.getSlippageStats(undefined, defaultTimeRange),
        this.throughputMonitor.getThroughputStats(defaultTimeRange),
        this.alertManager.getActiveAlerts(),
        this.getPerformanceTrends(defaultTimeRange)
      ]);

      // 生成性能概览
      const overview = this.generatePerformanceOverview(
        latencyStats,
        slippageStats,
        throughputStats,
        activeAlerts
      );

      const dashboardData: DashboardData = {
        overview,
        latency: latencyStats,
        slippage: slippageStats,
        throughput: throughputStats,
        alerts: activeAlerts,
        trends,
        lastUpdated: new Date()
      };

      this.logger.debug('仪表板数据获取完成', {
        overallScore: overview.overallScore,
        activeAlerts: activeAlerts.length
      });

      return dashboardData;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'PerformanceDashboard.getDashboardData',
        timeRange
      });
      throw error;
    }
  }

  /**
   * 获取关键性能指标
   */
  async getKPIs(): Promise<PerformanceKPIs> {
    try {
      this.logger.debug('获取关键性能指标');

      const timeRange = {
        start: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24小时
        end: new Date()
      };

      const [latencyStats, slippageStats, throughputStats] = await Promise.all([
        this.latencyMonitor.getLatencyStats(timeRange),
        this.slippageMonitor.getSlippageStats(undefined, timeRange),
        this.throughputMonitor.getThroughputStats(timeRange)
      ]);

      const kpis: PerformanceKPIs = {
        averageLatency: latencyStats.averageLatency,
        p95Latency: latencyStats.p95Latency,
        averageSlippage: slippageStats.averageSlippage,
        throughputRate: throughputStats.operationsPerSecond,
        successRate: throughputStats.successRate,
        systemAvailability: this.calculateSystemAvailability(throughputStats),
        costEfficiency: this.calculateCostEfficiency(slippageStats, throughputStats)
      };

      this.logger.debug('关键性能指标获取完成', kpis);

      return kpis;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'PerformanceDashboard.getKPIs'
      });
      throw error;
    }
  }

  /**
   * 获取性能趋势
   */
  async getPerformanceTrends(timeRange?: TimeRange): Promise<PerformanceTrends> {
    try {
      this.logger.debug('获取性能趋势', { timeRange });

      const defaultTimeRange = timeRange || {
        start: new Date(Date.now() - 24 * 60 * 60 * 1000),
        end: new Date()
      };

      // 生成时间点（每小时一个数据点）
      const timePoints = this.generateTimePoints(defaultTimeRange, 60 * 60 * 1000); // 1小时间隔

      // 并行获取各个时间点的数据
      const trendPromises = timePoints.map(async (timestamp) => {
        const pointTimeRange = {
          start: new Date(timestamp - 30 * 60 * 1000), // 前30分钟
          end: new Date(timestamp + 30 * 60 * 1000)    // 后30分钟
        };

        const [latencyStats, slippageStats, throughputStats] = await Promise.all([
          this.latencyMonitor.getLatencyStats(pointTimeRange).catch(() => null),
          this.slippageMonitor.getSlippageStats(undefined, pointTimeRange).catch(() => null),
          this.throughputMonitor.getThroughputStats(pointTimeRange).catch(() => null)
        ]);

        return {
          timestamp: new Date(timestamp),
          latency: latencyStats?.averageLatency || 0,
          slippage: slippageStats?.averageSlippage || 0,
          throughput: throughputStats?.operationsPerSecond || 0,
        };
      });

      const trendData = await Promise.all(trendPromises);

      // 计算变化率
      const latencyTrend = this.calculateTrendWithChanges(trendData.map(d => ({ timestamp: d.timestamp, value: d.latency })));
      const slippageTrend = this.calculateTrendWithChanges(trendData.map(d => ({ timestamp: d.timestamp, value: d.slippage })));
      const throughputTrend = this.calculateTrendWithChanges(trendData.map(d => ({ timestamp: d.timestamp, value: d.throughput })));
      const errorRateTrend = this.calculateTrendWithChanges(trendData.map(d => ({ timestamp: d.timestamp, value: d.errorRate })));

      const trends: PerformanceTrends = {
        latencyTrend,
        slippageTrend,
        throughputTrend,
        errorRateTrend
      };

      this.logger.debug('性能趋势获取完成', {
        dataPoints: trendData.length
      });

      return trends;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'PerformanceDashboard.getPerformanceTrends',
        timeRange
      });
      throw error;
    }
  }

  /**
   * 生成性能报告
   */
  async generatePerformanceReport(timeRange: TimeRange): Promise<PerformanceReport> {
    try {
      this.logger.info('生成性能报告', { timeRange });

      const [
        latencyStats,
        slippageStats,
        throughputStats,
        latencyAnomalies,
        slippageAnomalies,
        throughputAnomalies
      ] = await Promise.all([
        this.latencyMonitor.getLatencyStats(timeRange),
        this.slippageMonitor.getSlippageStats(undefined, timeRange),
        this.throughputMonitor.getThroughputStats(timeRange),
        this.latencyMonitor.checkLatencyAnomalies(),
        this.slippageMonitor.checkSlippageAnomalies(),
        this.throughputMonitor.checkThroughputAnomalies()
      ]);

      // 生成报告摘要
      const summary = this.generatePerformanceSummary(
        latencyStats,
        slippageStats,
        throughputStats,
        [...latencyAnomalies, ...slippageAnomalies, ...throughputAnomalies]
      );

      // 生成详细指标
      const detailedMetrics = {
        latency: latencyStats,
        slippage: slippageStats,
        throughput: throughputStats,
        systemHealth: {
          uptime: this.calculateUptime(throughputStats),
          downtime: this.calculateDowntime(throughputStats),
          availability: throughputStats.successRate * 100,
          incidents: this.getRecentIncidents()
        },
        resourceUtilization: {
          cpu: { average: 0, peak: 0, utilization: 0 },
          memory: { average: 0, peak: 0, utilization: 0 },
          network: { average: 0, peak: 0, utilization: 0 },
          storage: { average: 0, peak: 0, utilization: 0 }
        }
      };

      // 转换异常格式
      const anomalies = [
        ...latencyAnomalies.map(a => ({
          id: a.id,
          type: a.type,
          category: 'latency' as const,
          severity: a.severity,
          description: a.description,
          detectedAt: a.detectedAt,
          impact: {
            financial: 0,
            operational: a.description,
            userExperience: a.severity === 'critical' ? 'severe' as const : 'moderate' as const,
            duration: 0
          }
        })),
        ...slippageAnomalies.map(a => ({
          id: a.id,
          type: a.type,
          category: 'slippage' as const,
          severity: a.severity,
          description: a.description,
          detectedAt: a.detectedAt,
          impact: {
            financial: a.impact,
            operational: a.description,
            userExperience: a.severity === 'critical' ? 'severe' as const : 'moderate' as const,
            duration: 0
          }
        })),
        ...throughputAnomalies.map(a => ({
          id: a.id,
          type: a.type,
          category: 'throughput' as const,
          severity: a.severity,
          description: a.description,
          detectedAt: a.detectedAt,
          impact: {
            financial: 0,
            operational: a.description,
            userExperience: a.severity === 'critical' ? 'severe' as const : 'moderate' as const,
            duration: 0
          }
        }))
      ];

      const report: PerformanceReport = {
        id: `perf_report_${Date.now()}`,
        generatedAt: new Date(),
        timeRange,
        summary,
        detailedMetrics,
        anomalies,
        recommendations: this.generateRecommendations(summary, anomalies),
        attachments: []
      };

      this.logger.info('性能报告生成完成', {
        reportId: report.id,
        anomaliesCount: anomalies.length
      });

      return report;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'PerformanceDashboard.generatePerformanceReport',
        timeRange
      });
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 生成性能概览
   */
  private generatePerformanceOverview(
    latencyStats: any,
    slippageStats: any,
    throughputStats: any,
    activeAlerts: any[]
  ): PerformanceOverview {
let overallScore = await this.getRealPrice();

    // 延迟评分 (30%)
    const latencyScore = Math.max(0, 100 - (latencyStats.average * 10));
    overallScore += latencyScore * 0.3;

    // 滑点评分 (25%)
    const slippageScore = Math.max(0, 100 - (slippageStats.average * 100));
    overallScore += slippageScore * 0.25;

    // 吞吐量评分 (25%)
    const throughputScore = Math.min(100, throughputStats.average * 10);
    overallScore += throughputScore * 0.25;

    // 告警评分 (20%)
    const criticalAlerts = activeAlerts.filter(a => a.severity === 'critical').length;
    const alertScore = Math.max(0, 100 - (criticalAlerts * 20));
    overallScore += alertScore * 0.2;

    overallScore = Math.min(100, Math.max(0, overallScore));

    // 确定系统健康状态
    let systemHealth: 'healthy' | 'warning' | 'critical';
    if (overallScore >= 80) systemHealth = 'healthy';
    else if (overallScore >= 60) systemHealth = 'warning';
    else systemHealth = 'critical';

    return {
      systemHealth,
      overallScore: Math.round(overallScore),
      activeAlerts: activeAlerts.length,
      criticalIssues: criticalAlerts,
      uptime: this.calculateUptime(throughputStats),
      lastIncident: activeAlerts.length > 0 ? activeAlerts[0].triggeredAt : undefined
    };
  }

  /**
   * 计算系统可用性
   */
  private calculateSystemAvailability(throughputStats: any): number {
    return throughputStats.successRate * 100;
  }

  /**
   * 计算成本效率
   */
  private calculateCostEfficiency(slippageStats: any, throughputStats: any): number {
    // 基于滑点成本和成功率计算效率
    const slippageCost = slippageStats.averageSlippage || 0;
    const successRate = throughputStats.successRate || 0;
    const efficiency = Math.max(0, 100 - slippageCost * 10) * successRate;
    return Math.max(0, Math.min(100, efficiency));
  }

  /**
   * 计算运行时间
   */
  private calculateUptime(throughputStats: any): number {
    // 完整实现
    return throughputStats.successRate * 24 * 60 * 60; // 假设24小时周期
  }

  /**
   * 生成时间点
   */
  private generateTimePoints(timeRange: TimeRange, intervalMs: number): number[] {
    const points: number[] = [];
    const start = timeRange.start.getTime();
    const end = timeRange.end.getTime();

    for (let time = start; time <= end; time += intervalMs) {
      points.push(time);
    }

    return points;
  }

  /**
   * 计算带变化率的趋势
   */
  private calculateTrendWithChanges(data: Array<{ timestamp: Date; value: number }>): TrendData[] {
    return data.map((point, index) => ({
      timestamp: point.timestamp,
      value: point.value,
    }));
  }

  /**
   * 生成性能摘要
   */
  private generatePerformanceSummary(
    latencyStats: any,
    slippageStats: any,
    throughputStats: any,
    anomalies: any[]
  ): any {
    let overallPerformance: 'excellent' | 'good' | 'fair' | 'poor';
    
    const avgLatency = latencyStats.averageLatency;
    const avgSlippage = slippageStats.averageSlippage;
    const successRate = throughputStats.successRate;

    if (avgLatency < 50 && avgSlippage < 10 && successRate > 0.99) {
      overallPerformance = 'excellent';
      overallPerformance = 'good';
    } else if (avgLatency < 200 && avgSlippage < 50 && successRate > 0.90) {
      overallPerformance = 'fair';
    } else {
      overallPerformance = 'poor';
    }

    const keyFindings = [
    ];

    const majorIssues = anomalies
      .filter(a => a.severity === 'critical' || a.severity === 'high')
      .map(a => a.description);

    return {
      overallPerformance,
      keyFindings,
      majorIssues,
      improvements: this.generateImprovements(overallPerformance),
      costImpact: this.calculateCostImpact(slippageStats)
    };
  }

  /**
   * 生成改进建议
   */
  private generateImprovements(performance: string): string[] {
    switch (performance) {
      case 'poor':
        return [
          '优化系统架构',
          '增加服务器资源',
          '改进算法效率',
          '加强监控告警'
        ];
      case 'fair':
        return [
          '优化关键路径',
          '调整配置参数',
          '改进缓存策略'
        ];
      case 'good':
        return [
          '微调性能参数',
          '持续监控优化'
        ];
      default:
        return ['保持当前优秀状态'];
    }
  }

  /**
   * 计算成本影响
   */
  private calculateCostImpact(slippageStats: any): number {
    // 简化计算：基于平均滑点估算成本影响
    return slippageStats.averageSlippage * slippageStats.totalTrades * 0.01; // 假设单位成本
  }

  /**
   * 生成建议
   */
  private generateRecommendations(summary: any, anomalies: any[]): string[] {
    const recommendations: string[] = [];

    if (summary.overallPerformance === 'poor') {
      recommendations.push('立即进行系统性能优化');
    }

    if (anomalies.some(a => a.category === 'latency' && a.severity === 'critical')) {
      recommendations.push('优化延迟关键路径');
    }

    if (anomalies.some(a => a.category === 'slippage' && a.severity === 'critical')) {
      recommendations.push('调整交易执行策略');
    }

    if (recommendations.length === 0) {
      recommendations.push('继续保持良好的性能水平');
    }

    return recommendations;
  }

  /**
   * 计算停机时间
   */
  private calculateDowntime(throughputStats: any): number {
    // 基于失败率计算停机时间
    const failureRate = 1 - (throughputStats.successRate || 0);
    const totalTime = 24 * 60 * 60 * 1000; // 24小时的毫秒数
    return Math.round(totalTime * failureRate);
  }

  /**
   * 获取最近的事件
   */
  private getRecentIncidents(): any[] {
    // 返回最近的性能事件
    return [
      // 这里可以从监控系统获取真实的事件数据
      // 暂时返回空数组，表示没有重大事件
    ];
  }
}
