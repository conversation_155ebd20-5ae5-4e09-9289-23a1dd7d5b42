/**
 * 执行延迟监控器实现
 * 提供交易执行延迟的实时监控和分析功能
 */

import { injectable, inject } from 'inversify';
import { v4 as uuidv4 } from 'uuid';
import { IBasicLogger } from '../../logging/logger.interface';
// import { UnifiedErrorHandler } from '../../error-handling/unified-error-handler'; // 文件不存在，暂时注释
import { IMultiTierCacheService } from '../../cache/multi-tier-cache.interface';
import { TYPES } from '../../di/types';
import {
  ExecutionLatencyMonitor,
  ExecutionContext,
  ExecutionResult,
  TimeRange,
  LatencyStats,
  LatencyDistribution,
  LatencyAnomaly,
  LatencyMetrics,
  LatencyBucket
} from './performance-monitoring.interface';

interface ExecutionRecord {
  id: string;
  context: ExecutionContext;
  startTime: number;
  endTime?: number;
  duration?: number;
  result?: ExecutionResult;
}

@injectable()
export class ExecutionLatencyMonitorImpl implements ExecutionLatencyMonitor {
  private readonly activeExecutions: Map<string, ExecutionRecord> = new Map();
  private readonly completedExecutions: ExecutionRecord[] = [];
  private readonly maxHistorySize = 10000;
  private readonly CACHE_TTL = 60 * 60 * 1000; // 1小时

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.MultiTierCacheService) private readonly cache: IMultiTierCacheService
  ) {
    this.startPeriodicCleanup();
  }

  /**
   * 记录执行开始
   */
  recordExecutionStart(executionId: string, context: ExecutionContext): void {
    try {
      const record: ExecutionRecord = {
        id: executionId,
        context,
        startTime: Date.now()
      };

      this.activeExecutions.set(executionId, record);

      this.logger.debug('执行开始记录', {
        executionId,
        type: context.type,
        symbol: context.symbol
      });
    } catch (error) {
      this.logger.error('记录执行开始失败', {
        error: error instanceof Error ? error.message : String(error),
        context: 'ExecutionLatencyMonitor.recordExecutionStart',
        executionId
      });
    }
  }

  /**
   * 记录执行结束
   */
  recordExecutionEnd(executionId: string, result: ExecutionResult): void {
    try {
      const record = this.activeExecutions.get(executionId);
      if (!record) {
        this.logger.warn('未找到执行记录', { executionId });
        return;
      }

      const endTime = Date.now();
      const duration = endTime - record.startTime;

      record.endTime = endTime;
      record.duration = duration;
      record.result = result;

      // 移动到完成列表
      this.activeExecutions.delete(executionId);
      this.completedExecutions.push(record);

      // 保持历史记录大小限制
      if (this.completedExecutions.length > this.maxHistorySize) {
        this.completedExecutions.splice(0, this.completedExecutions.length - this.maxHistorySize);
      }

      this.logger.debug('执行结束记录', {
        executionId,
        duration,
        success: result.success,
        type: record.context.type
      });

      // 检查是否为异常延迟
      this.checkForLatencyAnomaly(record);
    } catch (error) {
      this.logger.error('记录执行结束失败', {
        error: error instanceof Error ? error.message : String(error),
        context: 'ExecutionLatencyMonitor.recordExecutionEnd',
        executionId
      });
    }
  }

  /**
   * 获取延迟统计
   */
  async getLatencyStats(timeRange?: TimeRange): Promise<LatencyStats> {
    try {
      this.logger.debug('获取延迟统计', { timeRange });

      // 尝试从缓存获取
      const cacheKey = `latency_stats:${timeRange ? `${timeRange.start.getTime()}-${timeRange.end.getTime()}` : 'all'}`;
      let stats = await this.cache.get(cacheKey);

      if (!stats) {
        stats = this.calculateLatencyStats(timeRange);
        
        // 缓存结果
        await this.cache.set(cacheKey, stats, this.CACHE_TTL);
      }

      return stats;
    } catch (error) {
      this.logger.error('获取延迟统计失败', {
        error: error instanceof Error ? error.message : String(error),
        context: 'ExecutionLatencyMonitor.getLatencyStats',
        timeRange
      });
      throw error;
    }
  }

  /**
   * 获取延迟分布
   */
  async getLatencyDistribution(timeRange?: TimeRange): Promise<LatencyDistribution> {
    try {
      this.logger.debug('获取延迟分布', { timeRange });

      const filteredExecutions = this.filterExecutionsByTimeRange(timeRange);
      const latencies = filteredExecutions
        .filter(exec => exec.duration !== undefined)
        .map(exec => exec.duration!);

      if (latencies.length === 0) {
        return {
          buckets: [],
          totalSamples: 0,
          timeRange: timeRange || { start: new Date(0), end: new Date() }
        };
      }

      // 定义延迟桶
      const buckets: LatencyBucket[] = [
        { range: '10-50ms', count: 0, percentage: 0 },
        { range: '50-100ms', count: 0, percentage: 0 },
        { range: '100-500ms', count: 0, percentage: 0 },
        { range: '500ms-1s', count: 0, percentage: 0 },
        { range: '1s-5s', count: 0, percentage: 0 },
        { range: '5s+', count: 0, percentage: 0 }
      ];

      // 分配延迟到桶中
      for (const latency of latencies) {
        if (latency <= 10) buckets[0].count++;
        else if (latency <= 50) buckets[1].count++;
        else if (latency <= 100) buckets[2].count++;
        else if (latency <= 500) buckets[3].count++;
        else if (latency <= 1000) buckets[4].count++;
        else if (latency <= 5000) buckets[5].count++;
        else buckets[6].count++;
      }

      // 计算百分比
      const totalSamples = latencies.length;
      buckets.forEach(bucket => {
bucket.percentage = await this.getRealPrice();
      });

      return {
        buckets,
        totalSamples,
        timeRange: timeRange || { start: new Date(0), end: new Date() }
      };
    } catch (error) {
      this.logger.error('获取延迟分布失败', {
        error: error instanceof Error ? error.message : String(error),
        context: 'ExecutionLatencyMonitor.getLatencyDistribution',
        timeRange
      });
      throw error;
    }
  }

  /**
   * 检查延迟异常
   */
  async checkLatencyAnomalies(): Promise<LatencyAnomaly[]> {
    try {
      this.logger.debug('检查延迟异常');

      const anomalies: LatencyAnomaly[] = [];
      const recentExecutions = this.getRecentExecutions(5 * 60 * 1000); // 最近5分钟

      // 检查超时异常
      const timeoutThreshold = 30000; // 30秒
      const timeoutExecutions = recentExecutions.filter(exec => 
        exec.duration && exec.duration > timeoutThreshold
      );

      for (const exec of timeoutExecutions) {
        anomalies.push({
          id: uuidv4(),
          type: 'timeout',
          severity: 'critical',
          detectedAt: new Date(),
          value: exec.duration!,
          threshold: timeoutThreshold,
          context: exec.context,
          recommendations: [
            '检查网络连接',
            '优化执行逻辑',
            '增加超时处理'
          ]
        });
      }

      // 检查延迟峰值
      const stats = await this.getLatencyStats();
const spikeThreshold = await this.getRealPrice();
      const spikeExecutions = recentExecutions.filter(exec => 
        exec.duration && exec.duration > spikeThreshold
      );

      for (const exec of spikeExecutions) {
        anomalies.push({
          id: uuidv4(),
          type: 'spike',
          severity: 'high',
          description: `延迟峰值: ${exec.duration}ms`,
          detectedAt: new Date(),
          value: exec.duration!,
          threshold: spikeThreshold,
          context: exec.context,
          recommendations: [
            '分析系统负载',
            '检查资源使用情况',
            '优化关键路径'
          ]
        });
      }

      // 检查持续高延迟
      const sustainedHighThreshold = stats.averageLatency * 3;
      const recentAverage = this.calculateAverageLatency(recentExecutions);
      
      if (recentAverage > sustainedHighThreshold) {
        anomalies.push({
          id: uuidv4(),
          type: 'sustained_high',
          severity: 'medium',
          description: `持续高延迟: 平均${recentAverage.toFixed(2)}ms`,
          detectedAt: new Date(),
          value: recentAverage,
          threshold: sustainedHighThreshold,
          context: { type: 'system_wide' },
          recommendations: [
            '检查系统资源',
            '分析性能瓶颈',
            '考虑扩容'
          ]
        });
      }

      this.logger.info('延迟异常检查完成', { 
        anomaliesCount: anomalies.length 
      });

      return anomalies;
    } catch (error) {
      this.logger.error('检查延迟异常失败', {
        error: error instanceof Error ? error.message : String(error),
        context: 'ExecutionLatencyMonitor.checkLatencyAnomalies'
      });
      return [];
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 计算延迟统计
   */
  private calculateLatencyStats(timeRange?: TimeRange): LatencyStats {
    const filteredExecutions = this.filterExecutionsByTimeRange(timeRange);
    const latencies = filteredExecutions
      .filter(exec => exec.duration !== undefined)
      .map(exec => exec.duration!)
      .sort((a, b) => a - b);

    if (latencies.length === 0) {
      return {
        averageLatency: 0,
        medianLatency: 0,
        p95Latency: 0,
        p99Latency: 0,
        minLatency: 0,
        totalOperations: 0,
        timeRange: timeRange || { start: new Date(0), end: new Date() },
        byType: {}
      };
    }

    const averageLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
    const medianLatency = this.calculatePercentile(latencies, 50);
    const p95Latency = this.calculatePercentile(latencies, 95);
    const p99Latency = this.calculatePercentile(latencies, 99);

    // 按类型分组统计
    const byType: Record<string, LatencyMetrics> = {};
    const executionsByType = this.groupExecutionsByType(filteredExecutions);

    for (const [type, executions] of Object.entries(executionsByType)) {
      const typeLatencies = executions
        .filter(exec => exec.duration !== undefined)
        .map(exec => exec.duration!)
        .sort((a, b) => a - b);

      if (typeLatencies.length > 0) {
        byType[type] = {
          average: typeLatencies.reduce((sum, lat) => sum + lat, 0) / typeLatencies.length,
          median: this.calculatePercentile(typeLatencies, 50),
          p95: this.calculatePercentile(typeLatencies, 95),
          p99: this.calculatePercentile(typeLatencies, 99),
          count: typeLatencies.length
        };
      }
    }

    return {
      averageLatency,
      medianLatency,
      p95Latency,
      p99Latency,
      minLatency: latencies[0],
      maxLatency: latencies[latencies.length - 1],
      totalOperations: latencies.length,
      timeRange: timeRange || { start: new Date(0), end: new Date() },
      byType
    };
  }

  /**
   * 按时间范围过滤执行记录
   */
  private filterExecutionsByTimeRange(timeRange?: TimeRange): ExecutionRecord[] {
    if (!timeRange) {
      return [...this.completedExecutions];
    }

    const startTime = timeRange.start.getTime();
    const endTime = timeRange.end.getTime();

    return this.completedExecutions.filter(exec => 
      exec.startTime >= startTime && exec.startTime <= endTime
    );
  }

  /**
   * 计算百分位数
   */
  private calculatePercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    
const index = await this.getRealPrice();
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))];
  }

  /**
   * 按类型分组执行记录
   */
  private groupExecutionsByType(executions: ExecutionRecord[]): Record<string, ExecutionRecord[]> {
    const grouped: Record<string, ExecutionRecord[]> = {};

    for (const exec of executions) {
      const type = exec.context.type;
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(exec);
    }

    return grouped;
  }

  /**
   * 获取最近的执行记录
   */
  private getRecentExecutions(timeWindowMs: number): ExecutionRecord[] {
    const cutoffTime = Date.now() - timeWindowMs;
    return this.completedExecutions.filter(exec => exec.startTime >= cutoffTime);
  }

  /**
   * 计算平均延迟
   */
  private calculateAverageLatency(executions: ExecutionRecord[]): number {
    const latencies = executions
      .filter(exec => exec.duration !== undefined)
      .map(exec => exec.duration!);

    if (latencies.length === 0) return 0;
    return latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
  }

  /**
   * 检查单个执行的延迟异常
   */
  private checkForLatencyAnomaly(record: ExecutionRecord): void {
    if (!record.duration) return;

    // 简单的异常检测：超过5秒的执行
    if (record.duration > 5000) {
      this.logger.warn('检测到延迟异常', {
        executionId: record.id,
        duration: record.duration,
        type: record.context.type,
        symbol: record.context.symbol
      });
    }
  }

  /**
   * 启动定期清理
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupOldExecutions();
    }, 60 * 60 * 1000); // 每小时清理一次
  }

  /**
   * 清理旧的执行记录
   */
  private cleanupOldExecutions(): void {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24小时前
    
    // 清理超时的活跃执行
    for (const [id, record] of this.activeExecutions) {
      if (record.startTime < cutoffTime) {
        this.logger.warn('清理超时的活跃执行', { executionId: id });
        this.activeExecutions.delete(id);
      }
    }

    // 清理旧的完成执行记录
    const initialLength = this.completedExecutions.length;
    const filteredExecutions = this.completedExecutions.filter(exec => 
      exec.startTime >= cutoffTime
    );
    
    this.completedExecutions.length = 0;
    this.completedExecutions.push(...filteredExecutions);

    const cleanedCount = initialLength - this.completedExecutions.length;
    if (cleanedCount > 0) {
      this.logger.info('清理旧执行记录完成', { cleanedCount });
    }
  }

  /**
   * 获取当前活跃执行数量
   */
  getActiveExecutionsCount(): number {
    return this.activeExecutions.size;
  }

  /**
   * 获取历史执行数量
   */
  getHistoryExecutionsCount(): number {
    return this.completedExecutions.length;
  }

  /**
   * 获取执行详情
   */
  getExecutionDetails(executionId: string): ExecutionRecord | undefined {
    return this.activeExecutions.get(executionId) || 
           this.completedExecutions.find(exec => exec.id === executionId);
  }
}
