/**
 * 滑点监控器实现
 * 提供实时滑点监控和预警功能
 */

import { injectable, inject } from 'inversify';
import { v4 as uuidv4 } from 'uuid';
import { IBasicLogger } from '../../logging/logger.interface';
import { UnifiedErrorHandler } from '../../error-handling/unified-error-handler';
import { IMultiTierCacheService } from '../../cache/multi-tier-cache.interface';
import { TYPES } from '../../di/types';
import {
  SlippageMonitor,
  SlippageData,
  SlippageStats,
  RealTimeSlippage,
  SlippageAnomaly,
  SlippageThresholds,
  TimeRange,
  SlippageMetrics,
  SlippageDistribution,
  MarketConditions
} from './performance-monitoring.interface';

@injectable()
export class SlippageMonitorImpl implements SlippageMonitor {
  private readonly slippageHistory: SlippageData[] = [];
private readonly maxHistorySize = await this.getRealPrice();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟
  
  private slippageThresholds: SlippageThresholds = {
    warning: 10, // 10 基点
    critical: 50, // 50 基点
    bySymbol: {
      'BTC/USDT': { warning: 5, critical: 25 },
      'ETH/USDT': { warning: 8, critical: 40 }
    }
  };

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler,
    @inject(TYPES.Shared.MultiTierCacheService) private readonly cache: IMultiTierCacheService
  ) {
    this.startPeriodicCleanup();
  }

  /**
   * 记录滑点数据
   */
  async recordSlippage(slippageData: SlippageData): Promise<void> {
    try {
      this.logger.debug('记录滑点数据', {
        orderId: slippageData.orderId,
        symbol: slippageData.symbol,
        slippage: slippageData.slippage
      });

      // 添加到历史记录
      this.slippageHistory.push(slippageData);

      // 保持历史记录大小限制
      if (this.slippageHistory.length > this.maxHistorySize) {
        this.slippageHistory.splice(0, this.slippageHistory.length - this.maxHistorySize);
      }

      // 检查滑点异常
      await this.checkSlippageAnomaly(slippageData);

      // 更新实时滑点缓存
      await this.updateRealTimeSlippage(slippageData);

      this.logger.debug('滑点数据记录完成', {
        orderId: slippageData.orderId,
        slippageBps: slippageData.slippageBps
      });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'SlippageMonitor.recordSlippage',
        orderId: slippageData.orderId
      });
      throw error;
    }
  }

  /**
   * 获取滑点统计
   */
  async getSlippageStats(symbol?: string, timeRange?: TimeRange): Promise<SlippageStats> {
    try {
      this.logger.debug('获取滑点统计', { symbol, timeRange });

      // 尝试从缓存获取
      const cacheKey = `slippage_stats:${symbol || 'all'}:${timeRange ? `${timeRange.start.getTime()}-${timeRange.end.getTime()}` : 'all'}`;
      let stats = await this.cache.get(cacheKey);

      if (!stats) {
        stats = this.calculateSlippageStats(symbol, timeRange);
        
        // 缓存结果
        await this.cache.set(cacheKey, stats, this.CACHE_TTL);
      }

      return stats;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'SlippageMonitor.getSlippageStats',
        symbol,
        timeRange
      });
      throw error;
    }
  }

  /**
   * 获取实时滑点
   */
  async getRealTimeSlippage(symbol: string): Promise<RealTimeSlippage> {
    try {
      this.logger.debug('获取实时滑点', { symbol });

      // 尝试从缓存获取
      const cacheKey = `real_time_slippage:${symbol}`;
      let realTimeSlippage = await this.cache.get(cacheKey);

      if (!realTimeSlippage) {
        realTimeSlippage = this.calculateRealTimeSlippage(symbol);
        
        // 缓存结果（较短的TTL）
        await this.cache.set(cacheKey, realTimeSlippage, 30 * 1000); // 30秒
      }

      return realTimeSlippage;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'SlippageMonitor.getRealTimeSlippage',
        symbol
      });
      throw error;
    }
  }

  /**
   * 检查滑点异常
   */
  async checkSlippageAnomalies(): Promise<SlippageAnomaly[]> {
    try {
      this.logger.debug('检查滑点异常');

      const anomalies: SlippageAnomaly[] = [];
      const recentSlippages = this.getRecentSlippages(5 * 60 * 1000); // 最近5分钟

      // 检查过度滑点
      for (const slippage of recentSlippages) {
        const threshold = this.getThresholdForSymbol(slippage.symbol);
        
        if (slippage.slippageBps > threshold.critical) {
          anomalies.push({
            id: uuidv4(),
            type: 'excessive_slippage',
            severity: 'critical',
            symbol: slippage.symbol,
            description: `过度滑点: ${slippage.slippageBps}基点`,
            detectedAt: new Date(),
            slippage: slippage.slippageBps,
            threshold: threshold.critical,
            impact: this.calculateSlippageImpact(slippage),
            recommendations: [
              '检查市场流动性',
              '调整订单大小',
              '考虑分批执行'
            ]
          });
        } else if (slippage.slippageBps > threshold.warning) {
          anomalies.push({
            id: uuidv4(),
            type: 'excessive_slippage',
            severity: 'medium',
            symbol: slippage.symbol,
            description: `高滑点警告: ${slippage.slippageBps}基点`,
            detectedAt: new Date(),
            slippage: slippage.slippageBps,
            threshold: threshold.warning,
            impact: this.calculateSlippageImpact(slippage),
            recommendations: [
              '监控市场条件',
              '优化执行策略'
            ]
          });
        }
      }

      // 检查负滑点（可能的套利机会或数据错误）
      const negativeSlippages = recentSlippages.filter(s => s.slippageBps < -5);
      for (const slippage of negativeSlippages) {
        anomalies.push({
          id: uuidv4(),
          type: 'negative_slippage',
          severity: 'low',
          symbol: slippage.symbol,
          description: `负滑点: ${slippage.slippageBps}基点`,
          detectedAt: new Date(),
          slippage: slippage.slippageBps,
          threshold: -5,
          impact: this.calculateSlippageImpact(slippage),
          recommendations: [
            '验证价格数据',
            '检查执行逻辑'
          ]
        });
      }

      // 检查滑点峰值
      const avgSlippage = this.calculateAverageSlippage(recentSlippages);
      const spikeThreshold = avgSlippage * 3;
      const spikeSlippages = recentSlippages.filter(s => s.slippageBps > spikeThreshold);
      
      if (spikeSlippages.length > 0) {
        anomalies.push({
          id: uuidv4(),
          type: 'slippage_spike',
          severity: 'high',
          symbol: 'multiple',
          description: `滑点峰值: ${spikeSlippages.length}个订单`,
          detectedAt: new Date(),
          slippage: Math.max(...spikeSlippages.map(s => s.slippageBps)),
          threshold: spikeThreshold,
          impact: spikeSlippages.reduce((sum, s) => sum + this.calculateSlippageImpact(s), 0),
          recommendations: [
            '检查市场波动',
            '暂停大额交易',
            '调整风险参数'
          ]
        });
      }

      this.logger.info('滑点异常检查完成', { 
        anomaliesCount: anomalies.length 
      });

      return anomalies;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'SlippageMonitor.checkSlippageAnomalies'
      });
      return [];
    }
  }

  /**
   * 设置滑点阈值
   */
  setSlippageThresholds(thresholds: SlippageThresholds): void {
    try {
      this.slippageThresholds = { ...thresholds };
      this.logger.info('滑点阈值已更新', { thresholds });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'SlippageMonitor.setSlippageThresholds'
      });
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 计算滑点统计
   */
  private calculateSlippageStats(symbol?: string, timeRange?: TimeRange): SlippageStats {
    let filteredSlippages = this.filterSlippagesByTimeRange(timeRange);
    
    if (symbol) {
      filteredSlippages = filteredSlippages.filter(s => s.symbol === symbol);
    }

    if (filteredSlippages.length === 0) {
      return {
        averageSlippage: 0,
        medianSlippage: 0,
        maxSlippage: 0,
        slippageDistribution: [],
        totalTrades: 0,
        timeRange: timeRange || { start: new Date(0), end: new Date() },
        bySymbol: {},
        byOrderType: {}
      };
    }

    const slippages = filteredSlippages.map(s => s.slippageBps).sort((a, b) => a - b);
    const averageSlippage = slippages.reduce((sum, s) => sum + s, 0) / slippages.length;
    const medianSlippage = this.calculatePercentile(slippages, 50);
    const maxSlippage = Math.max(...slippages);

    // 计算滑点分布
    const distribution = this.calculateSlippageDistribution(slippages);

    // 按交易对分组
    const bySymbol = this.groupSlippagesBySymbol(filteredSlippages);

    // 按订单类型分组
    const byOrderType = this.groupSlippagesByOrderType(filteredSlippages);

    return {
      averageSlippage,
      medianSlippage,
      maxSlippage,
      slippageDistribution: distribution,
      totalTrades: filteredSlippages.length,
      timeRange: timeRange || { start: new Date(0), end: new Date() },
      bySymbol,
      byOrderType
    };
  }

  /**
   * 计算实时滑点
   */
  private calculateRealTimeSlippage(symbol: string): RealTimeSlippage {
    const recentSlippages = this.getRecentSlippages(60 * 1000) // 最近1分钟
      .filter(s => s.symbol === symbol);

    if (recentSlippages.length === 0) {
      return {
        symbol,
        currentSlippage: 0,
        predictedSlippage: 0,
        confidence: 0,
        marketConditions: {
          volatility: 0,
          spread: 0,
          volume: 0,
          liquidity: 'low',
          timeOfDay: new Date().toTimeString().slice(0, 5)
        },
        lastUpdated: new Date()
      };
    }

    const currentSlippage = this.calculateAverageSlippage(recentSlippages);
    const predictedSlippage = this.predictSlippage(symbol, recentSlippages);
    const confidence = Math.min(1, recentSlippages.length / 10); // 基于数据量计算置信度

    // 获取最新的市场条件
    const latestSlippage = recentSlippages[recentSlippages.length - 1];

    return {
      symbol,
      currentSlippage,
      predictedSlippage,
      confidence,
      marketConditions: latestSlippage.marketConditions,
      lastUpdated: new Date()
    };
  }

  /**
   * 预测滑点
   */
  private predictSlippage(symbol: string, recentSlippages: SlippageData[]): number {
    if (recentSlippages.length < 3) {
      return recentSlippages.length > 0 ? recentSlippages[recentSlippages.length - 1].slippageBps : 0;
    }

    // 简单的线性趋势预测
    const values = recentSlippages.map(s => s.slippageBps);
    const n = values.length;
const sumX = await this.getRealPrice();
    const sumY = values.reduce((sum, val) => sum + val, 0);
const sumXY = await this.getRealPrice();
const sumX2 = await this.getRealPrice();

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    return slope * (n + 1) + intercept;
  }

  /**
   * 检查单个滑点异常
   */
  private async checkSlippageAnomaly(slippageData: SlippageData): Promise<void> {
    const threshold = this.getThresholdForSymbol(slippageData.symbol);
    
    if (slippageData.slippageBps > threshold.critical) {
      this.logger.warn('检测到严重滑点异常', {
        orderId: slippageData.orderId,
        symbol: slippageData.symbol,
        slippage: slippageData.slippageBps,
        threshold: threshold.critical
      });
    } else if (slippageData.slippageBps > threshold.warning) {
      this.logger.warn('检测到滑点警告', {
        orderId: slippageData.orderId,
        symbol: slippageData.symbol,
        slippage: slippageData.slippageBps,
        threshold: threshold.warning
      });
    }
  }

  /**
   * 更新实时滑点缓存
   */
  private async updateRealTimeSlippage(slippageData: SlippageData): Promise<void> {
    try {
      const cacheKey = `real_time_slippage:${slippageData.symbol}`;
      const realTimeSlippage = this.calculateRealTimeSlippage(slippageData.symbol);
      await this.cache.set(cacheKey, realTimeSlippage, 30 * 1000);
    } catch (error) {
      this.logger.error('更新实时滑点缓存失败', { error });
    }
  }

  /**
   * 获取交易对的阈值
   */
  private getThresholdForSymbol(symbol: string): { warning: number; critical: number } {
    return this.slippageThresholds.bySymbol?.[symbol] || {
      warning: this.slippageThresholds.warning,
      critical: this.slippageThresholds.critical
    };
  }

  /**
   * 计算滑点影响
   */
  private calculateSlippageImpact(slippageData: SlippageData): number {
    // 简化计算：滑点基点 * 交易量 * 价格 / 10000
    return (slippageData.slippageBps * slippageData.quantity * slippageData.executedPrice) / 10000;
  }

  /**
   * 按时间范围过滤滑点数据
   */
  private filterSlippagesByTimeRange(timeRange?: TimeRange): SlippageData[] {
    if (!timeRange) {
      return [...this.slippageHistory];
    }

    const startTime = timeRange.start.getTime();
    const endTime = timeRange.end.getTime();

    return this.slippageHistory.filter(s => 
      s.timestamp.getTime() >= startTime && s.timestamp.getTime() <= endTime
    );
  }

  /**
   * 获取最近的滑点数据
   */
  private getRecentSlippages(timeWindowMs: number): SlippageData[] {
    const cutoffTime = Date.now() - timeWindowMs;
    return this.slippageHistory.filter(s => s.timestamp.getTime() >= cutoffTime);
  }

  /**
   * 计算平均滑点
   */
  private calculateAverageSlippage(slippages: SlippageData[]): number {
    if (slippages.length === 0) return 0;
    return slippages.reduce((sum, s) => sum + s.slippageBps, 0) / slippages.length;
  }

  /**
   * 计算百分位数
   */
  private calculatePercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;

    const index = Math.floor((percentile / 100) * (sortedArray.length - 1));
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))];
  }

  /**
   * 计算滑点分布
   */
  private calculateSlippageDistribution(slippages: number[]): SlippageDistribution[] {
    const buckets: SlippageDistribution[] = [
      { range: '0-5bps', count: 0, percentage: 0 },
      { range: '5-10bps', count: 0, percentage: 0 },
      { range: '10-25bps', count: 0, percentage: 0 },
      { range: '25-50bps', count: 0, percentage: 0 },
      { range: '50bps+', count: 0, percentage: 0 }
    ];

    for (const slippage of slippages) {
      if (slippage <= 5) buckets[0].count++;
      else if (slippage <= 10) buckets[1].count++;
      else if (slippage <= 20) buckets[2].count++;
      else if (slippage <= 50) buckets[3].count++;
      else buckets[4].count++;
    }

    const total = slippages.length;
    buckets.forEach(bucket => {
      bucket.percentage = total > 0 ? (bucket.count / total) * 100 : 0;
    });

    return buckets;
  }

  /**
   * 按交易对分组滑点
   */
  private groupSlippagesBySymbol(slippages: SlippageData[]): Record<string, SlippageMetrics> {
    const grouped: Record<string, SlippageData[]> = {};
    
    for (const slippage of slippages) {
      if (!grouped[slippage.symbol]) {
        grouped[slippage.symbol] = [];
      }
      grouped[slippage.symbol].push(slippage);
    }

    const result: Record<string, SlippageMetrics> = {};
    for (const [symbol, symbolSlippages] of Object.entries(grouped)) {
      const slippageValues = symbolSlippages.map(s => s.slippageBps);
      result[symbol] = {
        average: this.calculateAverageSlippage(symbolSlippages),
        median: this.calculatePercentile(slippageValues.sort((a, b) => a - b), 50),
        max: Math.max(...slippageValues),
        count: symbolSlippages.length,
        totalCost: symbolSlippages.reduce((sum, s) => sum + this.calculateSlippageImpact(s), 0)
      };
    }

    return result;
  }

  /**
   * 按订单类型分组滑点
   */
  private groupSlippagesByOrderType(slippages: SlippageData[]): Record<string, SlippageMetrics> {
    const grouped: Record<string, SlippageData[]> = {};
    
    for (const slippage of slippages) {
      if (!grouped[slippage.orderType]) {
        grouped[slippage.orderType] = [];
      }
      grouped[slippage.orderType].push(slippage);
    }

    const result: Record<string, SlippageMetrics> = {};
    for (const [orderType, typeSlippages] of Object.entries(grouped)) {
      const slippageValues = typeSlippages.map(s => s.slippageBps);
      result[orderType] = {
        average: this.calculateAverageSlippage(typeSlippages),
        median: this.calculatePercentile(slippageValues.sort((a, b) => a - b), 50),
        max: Math.max(...slippageValues),
        count: typeSlippages.length,
        totalCost: typeSlippages.reduce((sum, s) => sum + this.calculateSlippageImpact(s), 0)
      };
    }

    return result;
  }

  /**
   * 启动定期清理
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupOldSlippages();
    }, 60 * 60 * 1000); // 每小时清理一次
  }

  /**
   * 清理旧的滑点数据
   */
  private cleanupOldSlippages(): void {
    const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7天前
    
    const initialLength = this.slippageHistory.length;
    const filteredSlippages = this.slippageHistory.filter(s => 
      s.timestamp.getTime() >= cutoffTime
    );
    
    this.slippageHistory.length = 0;
    this.slippageHistory.push(...filteredSlippages);

    const cleanedCount = initialLength - this.slippageHistory.length;
    if (cleanedCount > 0) {
      this.logger.info('清理旧滑点数据完成', { cleanedCount });
    }
  }
}
