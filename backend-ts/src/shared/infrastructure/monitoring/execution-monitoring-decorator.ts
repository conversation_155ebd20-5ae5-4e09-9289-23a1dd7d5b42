/**
 * 交易执行监控装饰器
 * 自动监控和记录交易执行方法的性能指标
 */

import { EnhancedTradingExecutionMonitor, ExecutionMetrics } from './enhanced-trading-execution-monitor';
import { IBasicLogger } from '../logging/interfaces/basic-logger.interface';
import { SecureIdGenerator } from '../utils/secure-id-generator';

/**
 * 监控配置选项
 */
export interface MonitoringOptions {
  operationType?: string;
  enableDetailedLogging?: boolean;
  includeArguments?: boolean;
  includeResult?: boolean;
  customMetadataExtractor?: (args: any[], result?: any) => any;
}

/**
 * 交易执行监控装饰器
 */
export function MonitorExecution(options: MonitoringOptions = {}) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const operationType = options.operationType || `${target.constructor.name}.${propertyName}`;

    descriptor.value = async function (...args: any[]) {
      const monitor = getMonitorInstance();
      const logger = getLoggerInstance();
      
      if (!monitor) {
        // 如果监控器不可用，直接执行原方法
        return await originalMethod.apply(this, args);
      }

      const operationId = generateOperationId();
      const startTime = new Date();
      let success = false;
      let errorCode: string | undefined;
      let errorMessage: string | undefined;
      let result: any;

      try {
        // 记录方法开始执行
        if (options.enableDetailedLogging && logger) {
          logger.debug(`开始执行 ${operationType}`, {
            operationId,
            arguments: options.includeArguments ? args : undefined
          });
        }

        // 执行原方法
        result = await originalMethod.apply(this, args);
        success = true;

        return result;

      } catch (error) {
        success = false;
        errorMessage = error instanceof Error ? error.message : String(error);
        
        // 尝试提取错误代码
        if (error instanceof Error && 'code' in error) {
          errorCode = (error as any).code;
        } else if (error instanceof Error && error.name) {
          errorCode = error.name;
        }

        // 记录错误
        if (logger) {
          logger.error(`执行 ${operationType} 失败`, {
            operationId,
            error: errorMessage,
            errorCode
          });
        }

        throw error;

      } finally {
        const endTime = new Date();
        const duration = endTime.getTime() - startTime.getTime();

        // 提取账户ID和符号（如果可用）
        const { accountId, symbol } = extractContextInfo(args, result);

        // 构建执行指标
        const metrics: ExecutionMetrics = {
          operationId,
          operationType: mapToStandardOperationType(operationType),
          accountId: accountId || 'unknown',
          symbol,
          startTime,
          endTime,
          duration,
          success,
          errorCode,
          errorMessage,
          metadata: {
            methodName: propertyName,
            className: target.constructor.name,
            argumentsCount: args.length,
            ...extractCustomMetadata(args, result, options.customMetadataExtractor)
          }
        };

        // 记录执行指标
        try {
          await monitor.recordExecutionMetrics(metrics);
        } catch (monitorError) {
          // 监控记录失败不应影响业务逻辑
          if (logger) {
            logger.error('记录执行监控指标失败', {
              operationId,
              monitorError: monitorError instanceof Error ? monitorError.message : String(monitorError)
            });
          }
        }

        // 详细日志记录
        if (options.enableDetailedLogging && logger) {
          logger.info(`完成执行 ${operationType}`, {
            operationId,
            duration,
            success,
            result: options.includeResult ? result : undefined
          });
        }
      }
    };

    return descriptor;
  };
}

/**
 * 批量监控装饰器 - 用于批量操作
 */
export function MonitorBatchExecution(options: MonitoringOptions & { batchSizeExtractor?: (args: any[]) => number } = {}) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const operationType = options.operationType || `${target.constructor.name}.${propertyName}`;

    descriptor.value = async function (...args: any[]) {
      const monitor = getMonitorInstance();
      const logger = getLoggerInstance();
      
      if (!monitor) {
        return await originalMethod.apply(this, args);
      }

      const operationId = generateOperationId();
      const startTime = new Date();
      let success = false;
      let errorCode: string | undefined;
      let errorMessage: string | undefined;
      let result: any;
      let batchSize = 0;

      try {
        // 提取批量大小
        if (options.batchSizeExtractor) {
          batchSize = options.batchSizeExtractor(args);
        } else {
          // 默认尝试从第一个参数提取数组长度
          if (args.length > 0 && Array.isArray(args[0])) {
            batchSize = args[0].length;
          }
        }

        if (options.enableDetailedLogging && logger) {
          logger.debug(`开始批量执行 ${operationType}`, {
            operationId,
            batchSize
          });
        }

        result = await originalMethod.apply(this, args);
        success = true;

        return result;

      } catch (error) {
        success = false;
        errorMessage = error instanceof Error ? error.message : String(error);
        errorCode = extractErrorCode(error);

        if (logger) {
          logger.error(`批量执行 ${operationType} 失败`, {
            operationId,
            batchSize,
            error: errorMessage,
            errorCode
          });
        }

        throw error;

      } finally {
        const endTime = new Date();
        const duration = endTime.getTime() - startTime.getTime();
        const { accountId, symbol } = extractContextInfo(args, result);

        const metrics: ExecutionMetrics = {
          operationId,
          operationType: mapToStandardOperationType(operationType),
          accountId: accountId || 'unknown',
          symbol,
          startTime,
          endTime,
          duration,
          success,
          errorCode,
          errorMessage,
          metadata: {
            methodName: propertyName,
            className: target.constructor.name,
            batchSize,
            isBatchOperation: true,
            ...extractCustomMetadata(args, result, options.customMetadataExtractor)
          }
        };

        try {
          await monitor.recordExecutionMetrics(metrics);
        } catch (monitorError) {
          if (logger) {
            logger.error('记录批量执行监控指标失败', {
              operationId,
              monitorError: monitorError instanceof Error ? monitorError.message : String(monitorError)
            });
          }
        }
      }
    };

    return descriptor;
  };
}

/**
 * 性能关键操作监控装饰器 - 用于性能敏感的操作
 */
export function MonitorCriticalExecution(options: MonitoringOptions & { performanceThreshold?: number } = {}) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const operationType = options.operationType || `${target.constructor.name}.${propertyName}`;
    const performanceThreshold = options.performanceThreshold || 1000; // 默认1秒阈值

    descriptor.value = async function (...args: any[]) {
      const monitor = getMonitorInstance();
      const logger = getLoggerInstance();
      
      if (!monitor) {
        return await originalMethod.apply(this, args);
      }

      const operationId = generateOperationId();
      const startTime = new Date();
      let success = false;
      let errorCode: string | undefined;
      let errorMessage: string | undefined;
      let result: any;

      try {
        result = await originalMethod.apply(this, args);
        success = true;

        return result;

      } catch (error) {
        success = false;
        errorMessage = error instanceof Error ? error.message : String(error);
        errorCode = extractErrorCode(error);

        throw error;

      } finally {
        const endTime = new Date();
        const duration = endTime.getTime() - startTime.getTime();
        const { accountId, symbol } = extractContextInfo(args, result);

        // 性能关键操作的特殊处理
        const isSlowOperation = duration > performanceThreshold;
        
        if (isSlowOperation && logger) {
          logger.warn(`性能关键操作执行缓慢 ${operationType}`, {
            operationId,
            duration,
            threshold: performanceThreshold,
            accountId
          });
        }

        const metrics: ExecutionMetrics = {
          operationId,
          operationType: mapToStandardOperationType(operationType),
          accountId: accountId || 'unknown',
          symbol,
          startTime,
          endTime,
          duration,
          success,
          errorCode,
          errorMessage,
          metadata: {
            methodName: propertyName,
            className: target.constructor.name,
            isCriticalOperation: true,
            isSlowOperation,
            performanceThreshold,
            ...extractCustomMetadata(args, result, options.customMetadataExtractor)
          }
        };

        try {
          await monitor.recordExecutionMetrics(metrics);
        } catch (monitorError) {
          if (logger) {
            logger.error('记录关键操作监控指标失败', {
              operationId,
              monitorError: monitorError instanceof Error ? monitorError.message : String(monitorError)
            });
          }
        }
      }
    };

    return descriptor;
  };
}

// 辅助函数

/**
 * 获取监控器实例
 */
function getMonitorInstance(): EnhancedTradingExecutionMonitor | null {
  // 这里应该从DI容器获取监控器实例
  // 暂时返回null，在实际使用时需要集成DI容器
  return null;
}

/**
 * 获取日志器实例
 */
function getLoggerInstance(): IBasicLogger | null {
  // 这里应该从DI容器获取日志器实例
  // 暂时返回null，在实际使用时需要集成DI容器
  return null;
}

/**
 * 生成操作ID
 */
function generateOperationId(): string {
  return SecureIdGenerator.generatePrefixedId('op', 8);
}

/**
 * 提取上下文信息
 */
function extractContextInfo(args: any[], result?: any): { accountId?: string; symbol?: string } {
  let accountId: string | undefined;
  let symbol: string | undefined;

  // 尝试从参数中提取accountId
  for (const arg of args) {
    if (arg && typeof arg === 'object') {
      if (arg.accountId) {
        accountId = arg.accountId;
      }
      if (arg.symbol || arg.symbolId) {
        symbol = arg.symbol || arg.symbolId;
      }
    } else if (typeof arg === 'string' && arg.length > 10) {
      // 可能是accountId
      if (!accountId) {
        accountId = arg;
      }
    }
  }

  // 尝试从结果中提取
  if (result && typeof result === 'object') {
    if (result.accountId && !accountId) {
      accountId = result.accountId;
    }
    if ((result.symbol || result.symbolId) && !symbol) {
      symbol = result.symbol || result.symbolId;
    }
  }

  return { accountId, symbol };
}

/**
 * 提取错误代码
 */
function extractErrorCode(error: any): string | undefined {
  if (error instanceof Error) {
    if ('code' in error) {
      return (error as any).code;
    }
    if (error.name && error.name !== 'Error') {
      return error.name;
    }
  }
  return undefined;
}

/**
 * 提取自定义元数据
 */
function extractCustomMetadata(
  args: any[], 
  result: any, 
  extractor?: (args: any[], result?: any) => any
): any {
  if (extractor) {
    try {
      return extractor(args, result);
    } catch (error) {
      // 元数据提取失败不应影响主流程
      return { extractorError: error instanceof Error ? error.message : String(error) };
    }
  }
  return {};
}

/**
 * 映射到标准操作类型
 */
function mapToStandardOperationType(operationType: string): 'ORDER_CREATION' | 'ORDER_EXECUTION' | 'POSITION_OPEN' | 'POSITION_CLOSE' | 'RISK_CHECK' | 'SYNC_OPERATION' {
  const lowerType = operationType.toLowerCase();
  
  if (lowerType.includes('order') && (lowerType.includes('create') || lowerType.includes('place'))) {
    return 'ORDER_CREATION';
  }
  if (lowerType.includes('order') && lowerType.includes('execute')) {
    return 'ORDER_EXECUTION';
  }
  if (lowerType.includes('position') && lowerType.includes('open')) {
    return 'POSITION_OPEN';
  }
  if (lowerType.includes('position') && lowerType.includes('close')) {
    return 'POSITION_CLOSE';
  }
  if (lowerType.includes('risk')) {
    return 'RISK_CHECK';
  }
  if (lowerType.includes('sync')) {
    return 'SYNC_OPERATION';
  }
  
  // 默认返回ORDER_EXECUTION
  return 'ORDER_EXECUTION';
}

/**
 * 监控器工厂函数 - 用于创建带监控的方法
 */
export function createMonitoredMethod<T extends (...args: any[]) => any>(
  originalMethod: T,
  options: MonitoringOptions & { target?: any; propertyName?: string }
): T {
  const operationType = options.operationType || 
    `${options.target?.constructor?.name || 'Unknown'}.${options.propertyName || 'unknownMethod'}`;

  return (async function (...args: any[]) {
    const monitor = getMonitorInstance();
    
    if (!monitor) {
      return await originalMethod.apply(options.target || this, args);
    }

    const operationId = generateOperationId();
    const startTime = new Date();
    let success = false;
    let errorCode: string | undefined;
    let errorMessage: string | undefined;
    let result: any;

    try {
      result = await originalMethod.apply(options.target || this, args);
      success = true;
      return result;

    } catch (error) {
      success = false;
      errorMessage = error instanceof Error ? error.message : String(error);
      errorCode = extractErrorCode(error);
      throw error;

    } finally {
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();
      const { accountId, symbol } = extractContextInfo(args, result);

      const metrics: ExecutionMetrics = {
        operationId,
        operationType: mapToStandardOperationType(operationType),
        accountId: accountId || 'unknown',
        symbol,
        startTime,
        endTime,
        duration,
        success,
        errorCode,
        errorMessage,
        metadata: {
          methodName: options.propertyName || 'unknownMethod',
          className: options.target?.constructor?.name || 'Unknown',
          ...extractCustomMetadata(args, result, options.customMetadataExtractor)
        }
      };

      try {
        await monitor.recordExecutionMetrics(metrics);
      } catch (monitorError) {
        // 静默处理监控错误
      }
    }
  }) as T;
}

export { MonitoringOptions, ExecutionMetrics };
