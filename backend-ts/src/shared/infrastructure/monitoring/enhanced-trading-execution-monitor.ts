/**
 * 增强的交易执行监控引擎
 * 提供全面的交易执行监控、性能分析、异常检测和实时告警功能
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../di/types';
import { IBasicLogger } from '../logging/interfaces/basic-logger.interface';
import { EventEmitter } from 'events';

/**
 * 执行监控指标
 */
export interface ExecutionMetrics {
  operationId: string;
  operationType: 'ORDER_CREATION' | 'ORDER_EXECUTION' | 'POSITION_OPEN' | 'POSITION_CLOSE' | 'RISK_CHECK' | 'SYNC_OPERATION';
  accountId: string;
  symbol?: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  success: boolean;
  errorCode?: string;
  errorMessage?: string;
  metadata?: {
    orderType?: string;
    quantity?: number;
    price?: number;
    executionEngine?: string;
    latency?: number;
    retryCount?: number;
    [key: string]: any;
  };
}

/**
 * 性能统计
 */
export interface PerformanceStats {
  operationType: string;
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  successRate: number;
  averageDuration: number;
  medianDuration: number;
  p95Duration: number;
  p99Duration: number;
  minDuration: number;
  maxDuration: number;
  errorRate: number;
  throughput: number; // 每秒操作数
  lastUpdated: Date;
}

/**
 * 异常检测结果
 */
export interface AnomalyDetectionResult {
  id: string;
  type: 'PERFORMANCE_DEGRADATION' | 'HIGH_ERROR_RATE' | 'LATENCY_SPIKE' | 'THROUGHPUT_DROP' | 'EXECUTION_FAILURE_PATTERN';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  affectedOperations: string[];
  detectedAt: Date;
  threshold: number;
  actualValue: number;
  metadata?: any;
}

/**
 * 告警配置
 */
export interface AlertConfig {
  enabled: boolean;
  thresholds: {
    errorRate: number;           // 错误率阈值
    averageDuration: number;     // 平均执行时间阈值（毫秒）
    p95Duration: number;         // P95执行时间阈值（毫秒）
    throughputDrop: number;      // 吞吐量下降阈值（百分比）
    consecutiveFailures: number; // 连续失败次数阈值
  };
  cooldownPeriod: number;        // 告警冷却期（毫秒）
}

/**
 * 监控配置
 */
export interface MonitoringConfig {
  enableRealTimeMonitoring: boolean;
  metricsRetentionPeriod: number;    // 指标保留期（小时）
  anomalyDetectionInterval: number;   // 异常检测间隔（毫秒）
  performanceAnalysisWindow: number;  // 性能分析窗口（分钟）
  enableDetailedLogging: boolean;
  alertConfig: AlertConfig;
}

/**
 * 增强的交易执行监控引擎
 */
@injectable()
export class EnhancedTradingExecutionMonitor extends EventEmitter {
  private readonly config: MonitoringConfig;
  private readonly metricsHistory: Map<string, ExecutionMetrics[]> = new Map();
  private readonly performanceStats: Map<string, PerformanceStats> = new Map();
  private readonly activeAlerts: Map<string, Date> = new Map();
  private monitoringTimer: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger
  ) {
    super();
    
    this.config = {
      enableRealTimeMonitoring: true,
      metricsRetentionPeriod: 24,        // 24小时
      anomalyDetectionInterval: 60000,   // 1分钟
      performanceAnalysisWindow: 15,     // 15分钟
      enableDetailedLogging: true,
      alertConfig: {
        enabled: true,
        thresholds: {
          errorRate: 0.05,               // 5%错误率
          averageDuration: 5000,         // 5秒平均执行时间
          p95Duration: 10000,            // 10秒P95执行时间
          throughputDrop: 0.3,           // 30%吞吐量下降
          consecutiveFailures: 5         // 连续5次失败
        },
        cooldownPeriod: 300000           // 5分钟冷却期
      }
    };

    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    this.on('metricsRecorded', (metrics: ExecutionMetrics) => {
      this.handleMetricsRecorded(metrics);
    });

    this.on('anomalyDetected', (anomaly: AnomalyDetectionResult) => {
      this.handleAnomalyDetected(anomaly);
    });

    this.on('alertTriggered', (alert: { type: string; message: string; severity: string; metadata?: any }) => {
      this.handleAlertTriggered(alert);
    });
  }

  /**
   * 启动监控
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('交易执行监控已在运行');
      return;
    }

    this.logger.info('启动增强的交易执行监控', {
      anomalyDetectionInterval: this.config.anomalyDetectionInterval,
      performanceAnalysisWindow: this.config.performanceAnalysisWindow
    });

    this.isRunning = true;

    if (this.config.enableRealTimeMonitoring) {
      this.startPeriodicAnalysis();
    }

    this.emit('monitoringStarted');
  }

  /**
   * 停止监控
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('停止交易执行监控');

    this.isRunning = false;
    this.stopPeriodicAnalysis();

    this.emit('monitoringStopped');
  }

  /**
   * 记录执行指标
   */
  async recordExecutionMetrics(metrics: ExecutionMetrics): Promise<void> {
    try {
      // 存储指标
      const operationKey = `${metrics.operationType}_${metrics.accountId}`;
      let history = this.metricsHistory.get(operationKey);
      
      if (!history) {
        history = [];
        this.metricsHistory.set(operationKey, history);
      }

      history.push(metrics);

      // 限制历史记录大小
      const maxHistorySize = 1000;
      if (history.length > maxHistorySize) {
        history.shift();
      }

      // 更新性能统计
      await this.updatePerformanceStats(metrics);

      // 检查实时异常
      if (this.config.enableRealTimeMonitoring) {
        await this.checkRealTimeAnomalies(metrics);
      }

      this.emit('metricsRecorded', metrics);

      if (this.config.enableDetailedLogging) {
        this.logger.debug('执行指标已记录', {
          operationId: metrics.operationId,
          operationType: metrics.operationType,
          duration: metrics.duration,
          success: metrics.success
        });
      }

    } catch (error) {
      this.logger.error('记录执行指标失败', {
        operationId: metrics.operationId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(operationType?: string): PerformanceStats[] {
    if (operationType) {
      const stats = this.performanceStats.get(operationType);
      return stats ? [stats] : [];
    }

    return Array.from(this.performanceStats.values());
  }

  /**
   * 获取操作历史
   */
  getOperationHistory(operationType: string, accountId?: string, limit: number = 100): ExecutionMetrics[] {
    const allHistory: ExecutionMetrics[] = [];

    for (const [key, history] of this.metricsHistory) {
      if (key.startsWith(operationType)) {
        if (accountId) {
          allHistory.push(...history.filter(m => m.accountId === accountId));
        } else {
          allHistory.push(...history);
        }
      }
    }

    // 按时间排序并限制数量
    return allHistory
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);
  }

  /**
   * 执行异常检测
   */
  async detectAnomalies(): Promise<AnomalyDetectionResult[]> {
    const anomalies: AnomalyDetectionResult[] = [];

    try {
      // 1. 性能退化检测
      const performanceAnomalies = await this.detectPerformanceDegradation();
      anomalies.push(...performanceAnomalies);

      // 2. 高错误率检测
      const errorRateAnomalies = await this.detectHighErrorRate();
      anomalies.push(...errorRateAnomalies);

      // 3. 延迟峰值检测
      const latencyAnomalies = await this.detectLatencySpikes();
      anomalies.push(...latencyAnomalies);

      // 4. 吞吐量下降检测
      const throughputAnomalies = await this.detectThroughputDrop();
      anomalies.push(...throughputAnomalies);

      // 5. 执行失败模式检测
      const failurePatternAnomalies = await this.detectExecutionFailurePatterns();
      anomalies.push(...failurePatternAnomalies);

      // 按严重程度排序
      anomalies.sort((a, b) => {
        const severityOrder = { 'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      });

      this.logger.info('异常检测完成', {
        totalAnomalies: anomalies.length,
        criticalAnomalies: anomalies.filter(a => a.severity === 'CRITICAL').length
      });

      // 发送异常事件
      anomalies.forEach(anomaly => {
        this.emit('anomalyDetected', anomaly);
      });

      return anomalies;

    } catch (error) {
      this.logger.error('异常检测失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * 获取监控仪表板数据
   */
  async getDashboardData(): Promise<{
    overview: {
      totalOperations: number;
      successRate: number;
      averageLatency: number;
      activeAnomalies: number;
      systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL';
    };
    performanceStats: PerformanceStats[];
    recentAnomalies: AnomalyDetectionResult[];
    operationTrends: any[];
  }> {
    try {
      const performanceStats = this.getPerformanceStats();
      const recentAnomalies = await this.detectAnomalies();

      // 计算总体指标
      const totalOperations = performanceStats.reduce((sum, stat) => sum + stat.totalOperations, 0);
      const totalSuccessful = performanceStats.reduce((sum, stat) => sum + stat.successfulOperations, 0);
      const successRate = totalOperations > 0 ? totalSuccessful / totalOperations : 1;
      const averageLatency = performanceStats.length > 0 
        ? performanceStats.reduce((sum, stat) => sum + stat.averageDuration, 0) / performanceStats.length 
        : 0;

      // 计算系统健康状态
      const criticalAnomalies = recentAnomalies.filter(a => a.severity === 'CRITICAL').length;
      const highAnomalies = recentAnomalies.filter(a => a.severity === 'HIGH').length;
      
      let systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 'HEALTHY';
      if (criticalAnomalies > 0 || successRate < 0.9) {
        systemHealth = 'CRITICAL';
      } else if (highAnomalies > 0 || successRate < 0.95) {
        systemHealth = 'WARNING';
      }

      // 生成操作趋势数据
      const operationTrends = this.generateOperationTrends();

      return {
        overview: {
          totalOperations,
          successRate,
          averageLatency,
          activeAnomalies: recentAnomalies.length,
          systemHealth
        },
        performanceStats,
        recentAnomalies: recentAnomalies.slice(0, 10),
        operationTrends
      };

    } catch (error) {
      this.logger.error('获取监控仪表板数据失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 启动定期分析
   */
  private startPeriodicAnalysis(): void {
    this.monitoringTimer = setInterval(() => {
      this.performPeriodicAnalysis();
    }, this.config.anomalyDetectionInterval);

    this.logger.info('启动定期异常检测', {
      interval: this.config.anomalyDetectionInterval
    });
  }

  /**
   * 停止定期分析
   */
  private stopPeriodicAnalysis(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
      this.logger.info('停止定期异常检测');
    }
  }

  /**
   * 执行定期分析
   */
  private async performPeriodicAnalysis(): Promise<void> {
    try {
      // 清理过期指标
      await this.cleanupExpiredMetrics();

      // 执行异常检测
      await this.detectAnomalies();

      // 更新性能统计
      await this.updateAllPerformanceStats();

    } catch (error) {
      this.logger.error('定期分析失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 更新性能统计
   */
  private async updatePerformanceStats(metrics: ExecutionMetrics): Promise<void> {
    const key = metrics.operationType;
    let stats = this.performanceStats.get(key);

    if (!stats) {
      stats = {
        operationType: metrics.operationType,
        totalOperations: 0,
        successfulOperations: 0,
        failedOperations: 0,
        successRate: 0,
        averageDuration: 0,
        medianDuration: 0,
        p95Duration: 0,
        p99Duration: 0,
        minDuration: Number.MAX_VALUE,
        maxDuration: 0,
        errorRate: 0,
        throughput: 0,
        lastUpdated: new Date()
      };
    }

    // 更新基本统计
    stats.totalOperations++;
    if (metrics.success) {
      stats.successfulOperations++;
    } else {
      stats.failedOperations++;
    }

    stats.successRate = stats.successfulOperations / stats.totalOperations;
    stats.errorRate = stats.failedOperations / stats.totalOperations;

    // 更新持续时间统计
    stats.minDuration = Math.min(stats.minDuration, metrics.duration);
    stats.maxDuration = Math.max(stats.maxDuration, metrics.duration);

    // 计算详细的持续时间统计（需要历史数据）
    const operationKey = `${metrics.operationType}_${metrics.accountId}`;
    const history = this.metricsHistory.get(operationKey) || [];
    
    if (history.length > 0) {
      const durations = history.map(m => m.duration).sort((a, b) => a - b);
      stats.averageDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
      stats.medianDuration = durations[Math.floor(durations.length / 2)];
      stats.p95Duration = durations[Math.floor(durations.length * 0.95)];
      stats.p99Duration = durations[Math.floor(durations.length * 0.99)];

      // 计算吞吐量（最近1分钟的操作数）
      const oneMinuteAgo = new Date(Date.now() - 60000);
      const recentOperations = history.filter(m => m.startTime > oneMinuteAgo);
      stats.throughput = recentOperations.length;
    }

    stats.lastUpdated = new Date();
    this.performanceStats.set(key, stats);
  }

  /**
   * 更新所有性能统计
   */
  private async updateAllPerformanceStats(): Promise<void> {
    for (const [key, history] of this.metricsHistory) {
      if (history.length > 0) {
        const latestMetrics = history[history.length - 1];
        await this.updatePerformanceStats(latestMetrics);
      }
    }
  }

  /**
   * 检查实时异常
   */
  private async checkRealTimeAnomalies(metrics: ExecutionMetrics): Promise<void> {
    const alerts: any[] = [];

    // 检查执行时间异常
    if (metrics.duration > this.config.alertConfig.thresholds.averageDuration) {
      alerts.push({
        type: 'HIGH_LATENCY',
        message: `操作执行时间过长: ${metrics.duration}ms`,
        severity: 'HIGH',
        metadata: { metrics }
      });
    }

    // 检查连续失败
    if (!metrics.success) {
      const operationKey = `${metrics.operationType}_${metrics.accountId}`;
      const history = this.metricsHistory.get(operationKey) || [];
      const recentFailures = history.slice(-this.config.alertConfig.thresholds.consecutiveFailures)
        .filter(m => !m.success).length;

      if (recentFailures >= this.config.alertConfig.thresholds.consecutiveFailures) {
        alerts.push({
          type: 'CONSECUTIVE_FAILURES',
          message: `连续${recentFailures}次操作失败`,
          severity: 'CRITICAL',
          metadata: { metrics, consecutiveFailures: recentFailures }
        });
      }
    }

    // 发送告警
    for (const alert of alerts) {
      await this.triggerAlert(alert);
    }
  }

  /**
   * 触发告警
   */
  private async triggerAlert(alert: any): Promise<void> {
    const alertKey = `${alert.type}_${alert.metadata?.metrics?.operationType || 'unknown'}`;
    const lastAlertTime = this.activeAlerts.get(alertKey);
    const now = new Date();

    // 检查冷却期
    if (lastAlertTime && (now.getTime() - lastAlertTime.getTime()) < this.config.alertConfig.cooldownPeriod) {
      return;
    }

    this.activeAlerts.set(alertKey, now);
    this.emit('alertTriggered', alert);

    this.logger.warn('触发执行监控告警', {
      type: alert.type,
      message: alert.message,
      severity: alert.severity
    });
  }

  /**
   * 检测性能退化
   */
  private async detectPerformanceDegradation(): Promise<AnomalyDetectionResult[]> {
    const anomalies: AnomalyDetectionResult[] = [];

    for (const [operationType, stats] of this.performanceStats) {
      // 检查平均执行时间是否超过阈值
      if (stats.averageDuration > this.config.alertConfig.thresholds.averageDuration) {
        anomalies.push({
          id: `perf_deg_${operationType}_${Date.now()}`,
          type: 'PERFORMANCE_DEGRADATION',
          severity: stats.averageDuration > this.config.alertConfig.thresholds.averageDuration * 2 ? 'CRITICAL' : 'HIGH',
          description: `${operationType} 操作平均执行时间过长`,
          affectedOperations: [operationType],
          detectedAt: new Date(),
          threshold: this.config.alertConfig.thresholds.averageDuration,
          actualValue: stats.averageDuration,
          metadata: { stats }
        });
      }

      // 检查P95执行时间
      if (stats.p95Duration > this.config.alertConfig.thresholds.p95Duration) {
        anomalies.push({
          id: `p95_latency_${operationType}_${Date.now()}`,
          type: 'LATENCY_SPIKE',
          severity: 'HIGH',
          description: `${operationType} 操作P95执行时间过长`,
          affectedOperations: [operationType],
          detectedAt: new Date(),
          threshold: this.config.alertConfig.thresholds.p95Duration,
          actualValue: stats.p95Duration,
          metadata: { stats }
        });
      }
    }

    return anomalies;
  }

  /**
   * 检测高错误率
   */
  private async detectHighErrorRate(): Promise<AnomalyDetectionResult[]> {
    const anomalies: AnomalyDetectionResult[] = [];

    for (const [operationType, stats] of this.performanceStats) {
      if (stats.errorRate > this.config.alertConfig.thresholds.errorRate) {
        let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM';

        if (stats.errorRate > 0.2) {
          severity = 'CRITICAL';
        } else if (stats.errorRate > 0.1) {
          severity = 'HIGH';
        }

        anomalies.push({
          id: `error_rate_${operationType}_${Date.now()}`,
          type: 'HIGH_ERROR_RATE',
          severity,
          description: `${operationType} 操作错误率过高: ${(stats.errorRate * 100).toFixed(1)}%`,
          affectedOperations: [operationType],
          detectedAt: new Date(),
          threshold: this.config.alertConfig.thresholds.errorRate,
          actualValue: stats.errorRate,
          metadata: { stats }
        });
      }
    }

    return anomalies;
  }

  /**
   * 检测延迟峰值
   */
  private async detectLatencySpikes(): Promise<AnomalyDetectionResult[]> {
    const anomalies: AnomalyDetectionResult[] = [];

    for (const [operationType, stats] of this.performanceStats) {
      // 检查最大执行时间是否异常高
      const latencyThreshold = Math.max(stats.averageDuration * 5, 10000); // 平均时间的5倍或10秒

      if (stats.maxDuration > latencyThreshold) {
        anomalies.push({
          id: `latency_spike_${operationType}_${Date.now()}`,
          type: 'LATENCY_SPIKE',
          severity: stats.maxDuration > latencyThreshold * 2 ? 'CRITICAL' : 'HIGH',
          description: `${operationType} 操作出现延迟峰值`,
          affectedOperations: [operationType],
          detectedAt: new Date(),
          threshold: latencyThreshold,
          actualValue: stats.maxDuration,
          metadata: { stats }
        });
      }
    }

    return anomalies;
  }

  /**
   * 检测吞吐量下降
   */
  private async detectThroughputDrop(): Promise<AnomalyDetectionResult[]> {
    const anomalies: AnomalyDetectionResult[] = [];

    for (const [operationType, stats] of this.performanceStats) {
      // 获取历史吞吐量数据进行比较
      const historicalThroughput = await this.getHistoricalThroughput(operationType);

      if (historicalThroughput > 0) {
        const throughputDropRatio = (historicalThroughput - stats.throughput) / historicalThroughput;

        if (throughputDropRatio > this.config.alertConfig.thresholds.throughputDrop) {
          anomalies.push({
            id: `throughput_drop_${operationType}_${Date.now()}`,
            type: 'THROUGHPUT_DROP',
            severity: throughputDropRatio > 0.5 ? 'CRITICAL' : 'HIGH',
            description: `${operationType} 操作吞吐量下降 ${(throughputDropRatio * 100).toFixed(1)}%`,
            affectedOperations: [operationType],
            detectedAt: new Date(),
            threshold: this.config.alertConfig.thresholds.throughputDrop,
            actualValue: throughputDropRatio,
            metadata: {
              currentThroughput: stats.throughput,
              historicalThroughput,
              stats
            }
          });
        }
      }
    }

    return anomalies;
  }

  /**
   * 检测执行失败模式
   */
  private async detectExecutionFailurePatterns(): Promise<AnomalyDetectionResult[]> {
    const anomalies: AnomalyDetectionResult[] = [];

    for (const [key, history] of this.metricsHistory) {
      const operationType = key.split('_')[0];

      // 检查最近的失败模式
      const recentMetrics = history.slice(-20); // 最近20个操作
      const failurePattern = this.analyzeFailurePattern(recentMetrics);

      if (failurePattern.hasPattern) {
        anomalies.push({
          id: `failure_pattern_${operationType}_${Date.now()}`,
          type: 'EXECUTION_FAILURE_PATTERN',
          severity: failurePattern.severity,
          description: failurePattern.description,
          affectedOperations: [operationType],
          detectedAt: new Date(),
          threshold: 0,
          actualValue: failurePattern.patternStrength,
          metadata: {
            pattern: failurePattern,
            recentMetrics: recentMetrics.slice(-5) // 最近5个操作
          }
        });
      }
    }

    return anomalies;
  }

  /**
   * 分析失败模式
   */
  private analyzeFailurePattern(metrics: ExecutionMetrics[]): {
    hasPattern: boolean;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    description: string;
    patternStrength: number;
  } {
    if (metrics.length < 5) {
      return { hasPattern: false, severity: 'LOW', description: '', patternStrength: 0 };
    }

    const failures = metrics.filter(m => !m.success);
    const failureRate = failures.length / metrics.length;

    // 检查连续失败
    let consecutiveFailures = 0;
    let maxConsecutiveFailures = 0;

    for (const metric of metrics.reverse()) {
      if (!metric.success) {
        consecutiveFailures++;
        maxConsecutiveFailures = Math.max(maxConsecutiveFailures, consecutiveFailures);
      } else {
        consecutiveFailures = 0;
      }
    }

    // 检查错误类型模式
    const errorTypes = failures.map(f => f.errorCode).filter(Boolean);
    const uniqueErrorTypes = new Set(errorTypes);
    const dominantErrorType = this.getMostFrequentError(errorTypes);

    let hasPattern = false;
    let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
    let description = '';
    let patternStrength = 0;

    // 高失败率模式
    if (failureRate > 0.5) {
      hasPattern = true;
      severity = failureRate > 0.8 ? 'CRITICAL' : 'HIGH';
      description = `高失败率模式: ${(failureRate * 100).toFixed(1)}%`;
      patternStrength = failureRate;
    }

    // 连续失败模式
    if (maxConsecutiveFailures >= 3) {
      hasPattern = true;
      severity = maxConsecutiveFailures >= 5 ? 'CRITICAL' : 'HIGH';
      description = `连续失败模式: ${maxConsecutiveFailures}次连续失败`;
      patternStrength = Math.max(patternStrength, maxConsecutiveFailures / 10);
    }

    // 特定错误类型模式
    if (dominantErrorType && errorTypes.filter(e => e === dominantErrorType).length >= 3) {
      hasPattern = true;
      severity = 'MEDIUM';
      description = `重复错误模式: ${dominantErrorType}`;
      patternStrength = Math.max(patternStrength, 0.5);
    }

    return { hasPattern, severity, description, patternStrength };
  }

  /**
   * 获取最频繁的错误类型
   */
  private getMostFrequentError(errorTypes: (string | undefined)[]): string | null {
    const errorCounts = new Map<string, number>();

    errorTypes.forEach(errorType => {
      if (errorType) {
        errorCounts.set(errorType, (errorCounts.get(errorType) || 0) + 1);
      }
    });

    let mostFrequent: string | null = null;
    let maxCount = 0;

    for (const [errorType, count] of errorCounts) {
      if (count > maxCount) {
        maxCount = count;
        mostFrequent = errorType;
      }
    }

    return mostFrequent;
  }

  /**
   * 获取历史吞吐量
   */
  private async getHistoricalThroughput(operationType: string): Promise<number> {
    // 计算过去一小时的平均吞吐量
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    let totalOperations = 0;
    let totalMinutes = 0;

    for (const [key, history] of this.metricsHistory) {
      if (key.startsWith(operationType)) {
        const historicalMetrics = history.filter(m => m.startTime > oneHourAgo);

        if (historicalMetrics.length > 0) {
          // 按分钟分组计算吞吐量
          const minuteGroups = new Map<string, number>();

          historicalMetrics.forEach(metric => {
            const minute = new Date(metric.startTime.getTime()).toISOString().substring(0, 16); // YYYY-MM-DDTHH:MM
            minuteGroups.set(minute, (minuteGroups.get(minute) || 0) + 1);
          });

          totalOperations += historicalMetrics.length;
          totalMinutes += minuteGroups.size;
        }
      }
    }

    return totalMinutes > 0 ? totalOperations / totalMinutes : 0;
  }

  /**
   * 生成操作趋势数据
   */
  private generateOperationTrends(): any[] {
    const trends: any[] = [];
    const now = new Date();
    const intervals = 12; // 12个5分钟间隔（1小时）

    for (let i = intervals - 1; i >= 0; i--) {
      const intervalStart = new Date(now.getTime() - (i + 1) * 5 * 60 * 1000);
      const intervalEnd = new Date(now.getTime() - i * 5 * 60 * 1000);

      const intervalData = {
        timestamp: intervalEnd,
        operations: {} as Record<string, { count: number; successRate: number; avgDuration: number }>
      };

      for (const [key, history] of this.metricsHistory) {
        const operationType = key.split('_')[0];

        const intervalMetrics = history.filter(m =>
          m.startTime >= intervalStart && m.startTime < intervalEnd
        );

        if (intervalMetrics.length > 0) {
          const successCount = intervalMetrics.filter(m => m.success).length;
          const avgDuration = intervalMetrics.reduce((sum, m) => sum + m.duration, 0) / intervalMetrics.length;

          if (!intervalData.operations[operationType]) {
            intervalData.operations[operationType] = {
              count: 0,
              successRate: 0,
              avgDuration: 0
            };
          }

          intervalData.operations[operationType].count += intervalMetrics.length;
          intervalData.operations[operationType].successRate = successCount / intervalMetrics.length;
          intervalData.operations[operationType].avgDuration = avgDuration;
        }
      }

      trends.push(intervalData);
    }

    return trends;
  }

  /**
   * 清理过期指标
   */
  private async cleanupExpiredMetrics(): Promise<void> {
    const cutoffTime = new Date(Date.now() - this.config.metricsRetentionPeriod * 60 * 60 * 1000);
    let cleanedCount = 0;

    for (const [key, history] of this.metricsHistory) {
      const originalLength = history.length;
      const filteredHistory = history.filter(m => m.startTime > cutoffTime);

      if (filteredHistory.length !== originalLength) {
        this.metricsHistory.set(key, filteredHistory);
        cleanedCount += originalLength - filteredHistory.length;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug('清理过期指标', {
        cleanedCount,
        cutoffTime,
        retentionPeriod: this.config.metricsRetentionPeriod
      });
    }
  }

  /**
   * 处理指标记录事件
   */
  private async handleMetricsRecorded(metrics: ExecutionMetrics): Promise<void> {
    // 可以在这里添加额外的处理逻辑
    // 例如：发送到外部监控系统、更新数据库等
  }

  /**
   * 处理异常检测事件
   */
  private async handleAnomalyDetected(anomaly: AnomalyDetectionResult): Promise<void> {
    this.logger.warn('检测到执行异常', {
      id: anomaly.id,
      type: anomaly.type,
      severity: anomaly.severity,
      description: anomaly.description
    });
  }

  /**
   * 处理告警触发事件
   */
  private async handleAlertTriggered(alert: any): Promise<void> {
    // 可以在这里集成外部告警系统
    // 例如：发送邮件、Slack通知、短信等
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<MonitoringConfig>): void {
    Object.assign(this.config, newConfig);

    this.logger.info('监控配置已更新', { newConfig });

    // 如果检测间隔改变，重启定期分析
    if (newConfig.anomalyDetectionInterval && this.isRunning) {
      this.stopPeriodicAnalysis();
      this.startPeriodicAnalysis();
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): MonitoringConfig {
    return { ...this.config };
  }

  /**
   * 获取监控状态
   */
  getMonitoringStatus(): {
    isRunning: boolean;
    totalMetrics: number;
    activeOperationTypes: number;
    lastAnalysisTime: Date | null;
    config: MonitoringConfig;
  } {
    let totalMetrics = 0;
    for (const history of this.metricsHistory.values()) {
      totalMetrics += history.length;
    }

    return {
      isRunning: this.isRunning,
      totalMetrics,
      activeOperationTypes: this.performanceStats.size,
      lastAnalysisTime: null, // 可以添加最后分析时间跟踪
      config: this.config
    };
  }

  /**
   * 销毁监控器
   */
  async destroy(): Promise<void> {
    await this.stop();
    this.metricsHistory.clear();
    this.performanceStats.clear();
    this.activeAlerts.clear();
    this.removeAllListeners();

    this.logger.info('交易执行监控器已销毁');
  }
}
