/**
 * 风险强制执行配置管理器
 * 管理风险限制规则的配置、持久化和动态更新
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../di/types';
import { IBasicLogger } from '../logging/interfaces/basic-logger.interface';
import { RiskLimitRule, RiskLimitType, RiskEnforcementAction } from './risk-enforcement-engine';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * 风险配置文件结构
 */
export interface RiskConfigFile {
  version: string;
  lastUpdated: Date;
  rules: RiskLimitRule[];
  globalSettings: {
    enableRealTimeEnforcement: boolean;
    enablePreTradeChecks: boolean;
    enablePostTradeMonitoring: boolean;
    enableEmergencyStop: boolean;
    maxViolationsPerDay: number;
    violationCooldownPeriod: number;
    enforcementMode: 'STRICT' | 'MODERATE' | 'ADVISORY';
  };
  accountSpecificSettings: Record<string, {
    customRules?: Partial<RiskLimitRule>[];
    overrideGlobalSettings?: any;
    riskProfile?: 'CONSERVATIVE' | 'MODERATE' | 'AGGRESSIVE';
  }>;
}

/**
 * 风险配置模板
 */
export interface RiskConfigTemplate {
  name: string;
  description: string;
  riskProfile: 'CONSERVATIVE' | 'MODERATE' | 'AGGRESSIVE';
  rules: Partial<RiskLimitRule>[];
  globalSettings: any;
}

/**
 * 风险强制执行配置管理器
 */
@injectable()
export class RiskEnforcementConfigManager {
  private readonly configDir: string;
  private readonly configFile: string;
  private readonly templatesDir: string;
  private currentConfig: RiskConfigFile | null = null;

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger
  ) {
    this.configDir = path.join(process.cwd(), 'config', 'risk-enforcement');
    this.configFile = path.join(this.configDir, 'risk-config.json');
    this.templatesDir = path.join(this.configDir, 'templates');
  }

  /**
   * 初始化配置管理器
   */
  async initialize(): Promise<void> {
    try {
      // 确保配置目录存在
      await fs.mkdir(this.configDir, { recursive: true });
      await fs.mkdir(this.templatesDir, { recursive: true });

      // 加载或创建配置文件
      await this.loadOrCreateConfig();

      // 创建默认模板
      await this.createDefaultTemplates();

      this.logger.info('风险强制执行配置管理器初始化完成', {
        configFile: this.configFile,
        rulesCount: this.currentConfig?.rules.length || 0
      });
    } catch (error) {
      this.logger.error('风险强制执行配置管理器初始化失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 加载或创建配置文件
   */
  private async loadOrCreateConfig(): Promise<void> {
    try {
      const configData = await fs.readFile(this.configFile, 'utf-8');
      this.currentConfig = JSON.parse(configData);
      
      // 验证配置版本
      if (!this.currentConfig?.version) {
        throw new Error('配置文件版本信息缺失');
      }

      this.logger.info('风险配置文件加载成功', {
        version: this.currentConfig.version,
        rulesCount: this.currentConfig.rules.length
      });
    } catch (error) {
      this.logger.warn('风险配置文件不存在或损坏，创建默认配置', {
        error: error instanceof Error ? error.message : String(error)
      });
      
      await this.createDefaultConfig();
    }
  }

  /**
   * 创建默认配置
   */
  private async createDefaultConfig(): Promise<void> {
    this.currentConfig = {
      version: '1.0.0',
      lastUpdated: new Date(),
      rules: this.getDefaultRiskRules(),
      globalSettings: {
        enableRealTimeEnforcement: true,
        enablePreTradeChecks: true,
        enablePostTradeMonitoring: true,
        enableEmergencyStop: true,
        maxViolationsPerDay: 10,
        violationCooldownPeriod: 300000, // 5分钟
        enforcementMode: 'STRICT'
      },
      accountSpecificSettings: {}
    };

    await this.saveConfig();
    this.logger.info('默认风险配置创建完成');
  }

  /**
   * 获取默认风险限制规则
   */
  private getDefaultRiskRules(): RiskLimitRule[] {
    return [
      {
        id: 'daily-loss-limit',
        type: RiskLimitType.DAILY_LOSS,
        name: '日损失限制',
        description: '单日损失不得超过账户资金的5%',
        threshold: 0.05,
        severity: 'CRITICAL',
        action: RiskEnforcementAction.BLOCK_NEW_ORDERS,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: [],
        timeWindow: '1d'
      },
      {
        id: 'position-size-limit',
        type: RiskLimitType.POSITION_SIZE,
        name: '单仓位大小限制',
        description: '单个仓位不得超过账户资金的20%',
        threshold: 0.20,
        severity: 'WARNING',
        action: RiskEnforcementAction.LIMIT_ORDER_SIZE,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: []
      },
      {
        id: 'total-exposure-limit',
        type: RiskLimitType.TOTAL_EXPOSURE,
        name: '总敞口限制',
        description: '总敞口不得超过账户资金的80%',
        threshold: 0.80,
        severity: 'CRITICAL',
        action: RiskEnforcementAction.REDUCE_POSITION,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: []
      },
      {
        id: 'leverage-limit',
        type: RiskLimitType.LEVERAGE,
        name: '杠杆限制',
        description: '杠杆倍数不得超过10倍',
        threshold: 10,
        severity: 'CRITICAL',
        action: RiskEnforcementAction.BLOCK_NEW_ORDERS,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: []
      },
      {
        id: 'concentration-limit',
        type: RiskLimitType.CONCENTRATION,
        name: '集中度限制',
        description: '单一资产集中度不得超过50%',
        threshold: 0.50,
        severity: 'WARNING',
        action: RiskEnforcementAction.SEND_ALERT,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: []
      },
      {
        id: 'volatility-limit',
        type: RiskLimitType.VOLATILITY,
        name: '波动率限制',
        description: '高波动率资产仓位限制',
        threshold: 0.5, // 50%年化波动率
        severity: 'WARNING',
        action: RiskEnforcementAction.LIMIT_ORDER_SIZE,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: []
      },
      {
        id: 'liquidity-limit',
        type: RiskLimitType.LIQUIDITY,
        name: '流动性限制',
        description: '低流动性资产仓位限制',
        threshold: 30, // 流动性评分低于30
        severity: 'WARNING',
        action: RiskEnforcementAction.LIMIT_ORDER_SIZE,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: []
      }
    ];
  }

  /**
   * 创建默认模板
   */
  private async createDefaultTemplates(): Promise<void> {
    const templates: RiskConfigTemplate[] = [
      {
        name: 'conservative',
        description: '保守型风险配置',
        riskProfile: 'CONSERVATIVE',
        rules: [
          { id: 'daily-loss-limit', threshold: 0.02 }, // 2%
          { id: 'position-size-limit', threshold: 0.10 }, // 10%
          { id: 'leverage-limit', threshold: 3 }, // 3倍
          { id: 'concentration-limit', threshold: 0.30 } // 30%
        ],
        globalSettings: {
          enforcementMode: 'STRICT',
          maxViolationsPerDay: 5
        }
      },
      {
        name: 'moderate',
        description: '中等风险配置',
        riskProfile: 'MODERATE',
        rules: [
          { id: 'daily-loss-limit', threshold: 0.05 }, // 5%
          { id: 'position-size-limit', threshold: 0.20 }, // 20%
          { id: 'leverage-limit', threshold: 10 }, // 10倍
          { id: 'concentration-limit', threshold: 0.50 } // 50%
        ],
        globalSettings: {
          enforcementMode: 'MODERATE',
          maxViolationsPerDay: 10
        }
      },
      {
        name: 'aggressive',
        description: '激进型风险配置',
        riskProfile: 'AGGRESSIVE',
        rules: [
          { id: 'daily-loss-limit', threshold: 0.10 }, // 10%
          { id: 'position-size-limit', threshold: 0.40 }, // 40%
          { id: 'leverage-limit', threshold: 20 }, // 20倍
          { id: 'concentration-limit', threshold: 0.70 } // 70%
        ],
        globalSettings: {
          enforcementMode: 'ADVISORY',
          maxViolationsPerDay: 20
        }
      }
    ];

    for (const template of templates) {
      const templateFile = path.join(this.templatesDir, `${template.name}.json`);
      try {
        await fs.access(templateFile);
        // 文件已存在，跳过
      } catch {
        await fs.writeFile(templateFile, JSON.stringify(template, null, 2));
        this.logger.info('创建风险配置模板', { template: template.name });
      }
    }
  }

  /**
   * 保存配置
   */
  private async saveConfig(): Promise<void> {
    if (!this.currentConfig) {
      throw new Error('没有配置可保存');
    }

    this.currentConfig.lastUpdated = new Date();
    await fs.writeFile(this.configFile, JSON.stringify(this.currentConfig, null, 2));
    
    this.logger.info('风险配置保存成功', {
      rulesCount: this.currentConfig.rules.length,
      lastUpdated: this.currentConfig.lastUpdated
    });
  }

  /**
   * 获取当前配置
   */
  getCurrentConfig(): RiskConfigFile | null {
    return this.currentConfig;
  }

  /**
   * 获取所有风险规则
   */
  getRiskRules(): RiskLimitRule[] {
    return this.currentConfig?.rules || [];
  }

  /**
   * 获取特定账户的风险规则
   */
  getAccountRiskRules(accountId: string): RiskLimitRule[] {
    if (!this.currentConfig) {
      return [];
    }

    const baseRules = this.currentConfig.rules;
    const accountSettings = this.currentConfig.accountSpecificSettings[accountId];

    if (!accountSettings?.customRules) {
      return baseRules;
    }

    // 合并基础规则和账户特定规则
    const mergedRules = [...baseRules];
    
    for (const customRule of accountSettings.customRules) {
      const existingRuleIndex = mergedRules.findIndex(r => r.id === customRule.id);
      if (existingRuleIndex >= 0) {
        // 更新现有规则
        mergedRules[existingRuleIndex] = { ...mergedRules[existingRuleIndex], ...customRule };
      } else if (customRule.id) {
        // 添加新规则
        mergedRules.push(customRule as RiskLimitRule);
      }
    }

    return mergedRules;
  }

  /**
   * 添加风险规则
   */
  async addRiskRule(rule: RiskLimitRule): Promise<void> {
    if (!this.currentConfig) {
      throw new Error('配置未初始化');
    }

    // 检查规则ID是否已存在
    const existingRule = this.currentConfig.rules.find(r => r.id === rule.id);
    if (existingRule) {
      throw new Error(`规则ID ${rule.id} 已存在`);
    }

    this.currentConfig.rules.push(rule);
    await this.saveConfig();

    this.logger.info('添加风险规则', {
      ruleId: rule.id,
      type: rule.type,
      threshold: rule.threshold
    });
  }

  /**
   * 更新风险规则
   */
  async updateRiskRule(ruleId: string, updates: Partial<RiskLimitRule>): Promise<void> {
    if (!this.currentConfig) {
      throw new Error('配置未初始化');
    }

    const ruleIndex = this.currentConfig.rules.findIndex(r => r.id === ruleId);
    if (ruleIndex === -1) {
      throw new Error(`规则ID ${ruleId} 不存在`);
    }

    this.currentConfig.rules[ruleIndex] = { ...this.currentConfig.rules[ruleIndex], ...updates };
    await this.saveConfig();

    this.logger.info('更新风险规则', { ruleId, updates });
  }

  /**
   * 删除风险规则
   */
  async removeRiskRule(ruleId: string): Promise<void> {
    if (!this.currentConfig) {
      throw new Error('配置未初始化');
    }

    const ruleIndex = this.currentConfig.rules.findIndex(r => r.id === ruleId);
    if (ruleIndex === -1) {
      throw new Error(`规则ID ${ruleId} 不存在`);
    }

    this.currentConfig.rules.splice(ruleIndex, 1);
    await this.saveConfig();

    this.logger.info('删除风险规则', { ruleId });
  }

  /**
   * 应用配置模板
   */
  async applyTemplate(templateName: string, accountId?: string): Promise<void> {
    const templateFile = path.join(this.templatesDir, `${templateName}.json`);
    
    try {
      const templateData = await fs.readFile(templateFile, 'utf-8');
      const template: RiskConfigTemplate = JSON.parse(templateData);

      if (accountId) {
        // 应用到特定账户
        await this.applyTemplateToAccount(template, accountId);
      } else {
        // 应用到全局配置
        await this.applyTemplateGlobally(template);
      }

      this.logger.info('应用风险配置模板', {
        template: templateName,
        accountId: accountId || 'global'
      });
    } catch (error) {
      this.logger.error('应用配置模板失败', {
        template: templateName,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 应用模板到特定账户
   */
  private async applyTemplateToAccount(template: RiskConfigTemplate, accountId: string): Promise<void> {
    if (!this.currentConfig) {
      throw new Error('配置未初始化');
    }

    if (!this.currentConfig.accountSpecificSettings[accountId]) {
      this.currentConfig.accountSpecificSettings[accountId] = {};
    }

    this.currentConfig.accountSpecificSettings[accountId].customRules = template.rules;
    this.currentConfig.accountSpecificSettings[accountId].riskProfile = template.riskProfile;
    this.currentConfig.accountSpecificSettings[accountId].overrideGlobalSettings = template.globalSettings;

    await this.saveConfig();
  }

  /**
   * 应用模板到全局配置
   */
  private async applyTemplateGlobally(template: RiskConfigTemplate): Promise<void> {
    if (!this.currentConfig) {
      throw new Error('配置未初始化');
    }

    // 更新全局规则
    for (const templateRule of template.rules) {
      if (templateRule.id) {
        const ruleIndex = this.currentConfig.rules.findIndex(r => r.id === templateRule.id);
        if (ruleIndex >= 0) {
          this.currentConfig.rules[ruleIndex] = { ...this.currentConfig.rules[ruleIndex], ...templateRule };
        }
      }
    }

    // 更新全局设置
    this.currentConfig.globalSettings = { ...this.currentConfig.globalSettings, ...template.globalSettings };

    await this.saveConfig();
  }

  /**
   * 获取可用模板列表
   */
  async getAvailableTemplates(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.templatesDir);
      return files.filter(f => f.endsWith('.json')).map(f => f.replace('.json', ''));
    } catch (error) {
      this.logger.error('获取模板列表失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * 备份当前配置
   */
  async backupConfig(): Promise<string> {
    if (!this.currentConfig) {
      throw new Error('配置未初始化');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(this.configDir, `backup-${timestamp}.json`);

    await fs.writeFile(backupFile, JSON.stringify(this.currentConfig, null, 2));
    
    this.logger.info('风险配置备份完成', { backupFile });
    return backupFile;
  }

  /**
   * 恢复配置
   */
  async restoreConfig(backupFile: string): Promise<void> {
    try {
      const backupData = await fs.readFile(backupFile, 'utf-8');
      const backupConfig: RiskConfigFile = JSON.parse(backupData);

      // 验证备份配置
      if (!backupConfig.version || !backupConfig.rules) {
        throw new Error('备份配置格式无效');
      }

      this.currentConfig = backupConfig;
      await this.saveConfig();

      this.logger.info('风险配置恢复完成', { backupFile });
    } catch (error) {
      this.logger.error('恢复配置失败', {
        backupFile,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}
