/**
 * 风险限制强制执行引擎
 * 扩展现有的风险管理系统，实现风险评估结果与交易执行的强制性联动
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../di/types';
import { IBasicLogger } from '../logging/interfaces/basic-logger.interface';
import { UnifiedErrorHandler } from '../error/unified-error-handler';

/**
 * 风险限制类型
 */
export enum RiskLimitType {
  POSITION_SIZE = 'POSITION_SIZE',           // 仓位大小限制
  DAILY_LOSS = 'DAILY_LOSS',                 // 日损失限制
  TOTAL_EXPOSURE = 'TOTAL_EXPOSURE',         // 总敞口限制
  LEVERAGE = 'LEVERAGE',                     // 杠杆限制
  CONCENTRATION = 'CONCENTRATION',           // 集中度限制
  VOLATILITY = 'VOLATILITY',                 // 波动率限制
  LIQUIDITY = 'LIQUIDITY',                   // 流动性限制
  CORRELATION = 'CORRELATION'                // 相关性限制
}

/**
 * 风险限制规则
 */
export interface RiskLimitRule {
  id: string;
  type: RiskLimitType;
  name: string;
  description: string;
  threshold: number;
  severity: 'WARNING' | 'CRITICAL' | 'EMERGENCY';
  action: RiskEnforcementAction;
  isActive: boolean;
  applicableAccounts: string[];
  applicableSymbols: string[];
  timeWindow?: string; // 时间窗口，如 '1d', '1h'
  metadata?: any;
}

/**
 * 风险强制执行动作
 */
export enum RiskEnforcementAction {
  BLOCK_NEW_ORDERS = 'BLOCK_NEW_ORDERS',     // 阻止新订单
  REDUCE_POSITION = 'REDUCE_POSITION',       // 减少仓位
  CLOSE_POSITION = 'CLOSE_POSITION',         // 关闭仓位
  LIMIT_ORDER_SIZE = 'LIMIT_ORDER_SIZE',     // 限制订单大小
  REQUIRE_APPROVAL = 'REQUIRE_APPROVAL',     // 需要审批
  SEND_ALERT = 'SEND_ALERT',                 // 发送警报
  EMERGENCY_STOP = 'EMERGENCY_STOP'          // 紧急停止
}

/**
 * 风险限制违规记录
 */
export interface RiskViolation {
  id: string;
  ruleId: string;
  accountId: string;
  symbol?: string;
  violationType: RiskLimitType;
  severity: 'WARNING' | 'CRITICAL' | 'EMERGENCY';
  currentValue: number;
  threshold: number;
  exceedanceRatio: number;
  timestamp: Date;
  action: RiskEnforcementAction;
  actionExecuted: boolean;
  actionResult?: any;
  metadata?: any;
}

/**
 * 交易执行检查结果
 */
export interface TradeExecutionCheckResult {
  allowed: boolean;
  blockedReasons: string[];
  warnings: string[];
  modifications: {
    maxOrderSize?: number;
    requiredApproval?: boolean;
    forcedReduction?: number;
  };
  violations: RiskViolation[];
  riskScore: number;
  enforcementActions: RiskEnforcementAction[];
}

/**
 * 风险限制强制执行配置
 */
export interface RiskEnforcementConfig {
  enableRealTimeEnforcement: boolean;
  enablePreTradeChecks: boolean;
  enablePostTradeMonitoring: boolean;
  enableEmergencyStop: boolean;
  maxViolationsPerDay: number;
  violationCooldownPeriod: number; // 毫秒
  enforcementMode: 'STRICT' | 'MODERATE' | 'ADVISORY';
}

/**
 * 风险限制强制执行引擎
 */
@injectable()
export class RiskEnforcementEngine {
  private readonly config: RiskEnforcementConfig;
  private readonly riskRules: Map<string, RiskLimitRule> = new Map();
  private readonly violations: Map<string, RiskViolation[]> = new Map();
  private readonly emergencyStops: Set<string> = new Set();
  private monitoringTimer: NodeJS.Timeout | null = null;

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler
  ) {
    this.config = {
      enableRealTimeEnforcement: true,
      enablePreTradeChecks: true,
      enablePostTradeMonitoring: true,
      enableEmergencyStop: true,
      maxViolationsPerDay: 10,
      violationCooldownPeriod: 300000, // 5分钟
      enforcementMode: 'STRICT'
    };

    this.initializeDefaultRules();
    this.startRealTimeMonitoring();
  }

  /**
   * 初始化默认风险限制规则
   */
  private initializeDefaultRules(): void {
    const defaultRules: RiskLimitRule[] = [
      {
        id: 'daily-loss-limit',
        type: RiskLimitType.DAILY_LOSS,
        name: '日损失限制',
        description: '单日损失不得超过账户资金的5%',
        threshold: 0.05,
        severity: 'CRITICAL',
        action: RiskEnforcementAction.BLOCK_NEW_ORDERS,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: [],
        timeWindow: '1d'
      },
      {
        id: 'position-size-limit',
        type: RiskLimitType.POSITION_SIZE,
        name: '单仓位大小限制',
        description: '单个仓位不得超过账户资金的20%',
        threshold: 0.20,
        severity: 'WARNING',
        action: RiskEnforcementAction.LIMIT_ORDER_SIZE,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: []
      },
      {
        id: 'total-exposure-limit',
        type: RiskLimitType.TOTAL_EXPOSURE,
        name: '总敞口限制',
        description: '总敞口不得超过账户资金的80%',
        threshold: 0.80,
        severity: 'CRITICAL',
        action: RiskEnforcementAction.REDUCE_POSITION,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: []
      },
      {
        id: 'leverage-limit',
        type: RiskLimitType.LEVERAGE,
        name: '杠杆限制',
        description: '杠杆倍数不得超过10倍',
        threshold: 10,
        severity: 'CRITICAL',
        action: RiskEnforcementAction.BLOCK_NEW_ORDERS,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: []
      },
      {
        id: 'concentration-limit',
        type: RiskLimitType.CONCENTRATION,
        name: '集中度限制',
        description: '单一资产集中度不得超过50%',
        threshold: 0.50,
        severity: 'WARNING',
        action: RiskEnforcementAction.SEND_ALERT,
        isActive: true,
        applicableAccounts: [],
        applicableSymbols: []
      }
    ];

    defaultRules.forEach(rule => {
      this.riskRules.set(rule.id, rule);
    });

    this.logger.info('默认风险限制规则初始化完成', {
      rulesCount: defaultRules.length
    });
  }

  /**
   * 启动实时监控
   */
  private startRealTimeMonitoring(): void {
    if (!this.config.enableRealTimeEnforcement) {
      return;
    }

    this.logger.info('启动风险限制实时监控');

    this.monitoringTimer = setInterval(() => {
      this.performRealTimeChecks();
    }, 30000); // 30秒检查一次
  }

  /**
   * 停止实时监控
   */
  private stopRealTimeMonitoring(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
      this.logger.info('停止风险限制实时监控');
    }
  }

  /**
   * 执行实时检查
   */
  private async performRealTimeChecks(): Promise<void> {
    try {
      // 这里可以实现实时风险检查逻辑
      // 例如：检查所有活跃账户的风险状态
      this.logger.debug('执行实时风险限制检查');
    } catch (error) {
      this.logger.error('实时风险检查失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 交易前风险检查
   */
  async checkTradeExecution(
    accountId: string,
    orderRequest: {
      symbol: string;
      side: 'BUY' | 'SELL';
      quantity: number;
      price?: number;
      orderType: string;
      leverage?: number;
    },
    currentRiskAssessment: any
  ): Promise<TradeExecutionCheckResult> {
    try {
      this.logger.info('执行交易前风险检查', {
        accountId,
        symbol: orderRequest.symbol,
        side: orderRequest.side,
        quantity: orderRequest.quantity
      });

      const result: TradeExecutionCheckResult = {
        allowed: true,
        blockedReasons: [],
        warnings: [],
        modifications: {},
        violations: [],
        riskScore: currentRiskAssessment?.riskScore || 0,
        enforcementActions: []
      };

      // 检查紧急停止状态
      if (this.emergencyStops.has(accountId)) {
        result.allowed = false;
        result.blockedReasons.push('账户处于紧急停止状态');
        result.enforcementActions.push(RiskEnforcementAction.EMERGENCY_STOP);
        return result;
      }

      // 执行各项风险限制检查
      for (const rule of this.riskRules.values()) {
        if (!rule.isActive) continue;

        // 检查规则适用性
        if (!this.isRuleApplicable(rule, accountId, orderRequest.symbol)) {
          continue;
        }

        const violation = await this.checkRiskRule(rule, accountId, orderRequest, currentRiskAssessment);
        
        if (violation) {
          result.violations.push(violation);
          
          // 根据违规严重程度和动作类型处理
          switch (violation.action) {
            case RiskEnforcementAction.BLOCK_NEW_ORDERS:
              result.allowed = false;
              result.blockedReasons.push(`违反${rule.name}：${violation.currentValue.toFixed(4)} > ${violation.threshold.toFixed(4)}`);
              result.enforcementActions.push(violation.action);
              break;
              
            case RiskEnforcementAction.LIMIT_ORDER_SIZE:
              const maxSize = this.calculateMaxAllowedSize(rule, violation, orderRequest);
              if (maxSize < orderRequest.quantity) {
                result.modifications.maxOrderSize = maxSize;
                result.warnings.push(`订单大小被限制为 ${maxSize}，原因：${rule.name}`);
                result.enforcementActions.push(violation.action);
              }
              break;
              
            case RiskEnforcementAction.REQUIRE_APPROVAL:
              result.modifications.requiredApproval = true;
              result.warnings.push(`需要风险管理审批：${rule.name}`);
              result.enforcementActions.push(violation.action);
              break;
              
            case RiskEnforcementAction.SEND_ALERT:
              result.warnings.push(`风险警告：${rule.name}`);
              result.enforcementActions.push(violation.action);
              break;
          }
        }
      }

      // 记录违规
      if (result.violations.length > 0) {
        this.recordViolations(accountId, result.violations);
      }

      this.logger.info('交易前风险检查完成', {
        accountId,
        allowed: result.allowed,
        violationsCount: result.violations.length,
        enforcementActions: result.enforcementActions
      });

      return result;

    } catch (error) {
      this.logger.error('交易前风险检查失败', {
        accountId,
        error: error instanceof Error ? error.message : String(error)
      });
      
      // 安全起见，拒绝交易
      return {
        allowed: false,
        blockedReasons: ['风险检查系统异常'],
        warnings: [],
        modifications: {},
        violations: [],
        riskScore: 100,
        enforcementActions: [RiskEnforcementAction.EMERGENCY_STOP]
      };
    }
  }

  /**
   * 检查规则适用性
   */
  private isRuleApplicable(rule: RiskLimitRule, accountId: string, symbol: string): boolean {
    // 检查账户适用性
    if (rule.applicableAccounts.length > 0 && !rule.applicableAccounts.includes(accountId)) {
      return false;
    }

    // 检查符号适用性
    if (rule.applicableSymbols.length > 0 && !rule.applicableSymbols.includes(symbol)) {
      return false;
    }

    return true;
  }

  /**
   * 检查单个风险规则
   */
  private async checkRiskRule(
    rule: RiskLimitRule,
    accountId: string,
    orderRequest: any,
    currentRiskAssessment: any
  ): Promise<RiskViolation | null> {
    try {
      let currentValue = 0;
      let violationDetected = false;

      switch (rule.type) {
        case RiskLimitType.DAILY_LOSS:
          currentValue = await this.calculateDailyLoss(accountId);
          violationDetected = currentValue > rule.threshold;
          break;

        case RiskLimitType.POSITION_SIZE:
          currentValue = await this.calculatePositionSizeRatio(accountId, orderRequest);
          violationDetected = currentValue > rule.threshold;
          break;

        case RiskLimitType.TOTAL_EXPOSURE:
          currentValue = await this.calculateTotalExposure(accountId);
          violationDetected = currentValue > rule.threshold;
          break;

        case RiskLimitType.LEVERAGE:
          currentValue = orderRequest.leverage || 1;
          violationDetected = currentValue > rule.threshold;
          break;

        case RiskLimitType.CONCENTRATION:
          currentValue = await this.calculateConcentrationRisk(accountId, orderRequest.symbol);
          violationDetected = currentValue > rule.threshold;
          break;

        default:
          return null;
      }

      if (violationDetected) {
        const violation: RiskViolation = {
          id: `${rule.id}-${accountId}-${Date.now()}`,
          ruleId: rule.id,
          accountId,
          symbol: orderRequest.symbol,
          violationType: rule.type,
          severity: rule.severity,
          currentValue,
          threshold: rule.threshold,
          exceedanceRatio: currentValue / rule.threshold,
          timestamp: new Date(),
          action: rule.action,
          actionExecuted: false
        };

        return violation;
      }

      return null;

    } catch (error) {
      this.logger.error('检查风险规则失败', {
        ruleId: rule.id,
        accountId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 计算日损失比例（使用真实数据）
   */
  private async calculateDailyLoss(accountId: string): Promise<number> {
    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      try {
        // 获取账户当日损失数据
        const account = await prisma.tradingAccounts.findUnique({
          where: { id: accountId }
        });

        if (!account) {
          throw new Error(`账户不存在: ${accountId}`);
        }

        // 计算日损失比例：当日PnL / 总余额
        const dailyLoss = account.dailyPnl.toNumber();
        const totalBalance = account.currentBalance.toNumber();

        // 只返回负损失的比例
        const lossRatio = dailyLoss < 0 ? Math.abs(dailyLoss) / totalBalance : 0;

        return Math.min(1.0, lossRatio); // 最大100%损失
      } finally {
        await prisma.$disconnect();
      }
    } catch (error) {
      this.logger.error('计算日损失比例失败', { accountId, error });
      throw new Error(`无法计算账户 ${accountId} 的日损失比例`);
    }
  }

  /**
   * 计算仓位大小比例
   */
  private async calculatePositionSizeRatio(accountId: string, orderRequest: any): Promise<number> {
    // 这里应该计算新订单后的仓位大小占账户资金的比例
    // 暂时返回模拟数据
    const orderValue = orderRequest.quantity * (orderRequest.price || 100);
    const accountBalance = 10000; // 模拟账户余额
    return orderValue / accountBalance;
  }

  /**
   * 计算总敞口（使用真实数据）
   */
  private async calculateTotalExposure(accountId: string): Promise<number> {
    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      try {
        // 获取账户和持仓信息
        const account = await prisma.tradingAccounts.findUnique({
          where: { id: accountId },
          include: {
            TradingPositions: {
              where: { status: 'OPEN' }
            }
          }
        });

        if (!account) {
          throw new Error(`账户不存在: ${accountId}`);
        }

        const totalBalance = account.currentBalance.toNumber();
        const positions = account.TradingPositions;

        // 计算总敞口：所有持仓的名义价值之和 / 账户总余额
        const totalExposure = positions.reduce((sum, position) => {
          const positionValue = position.quantity.toNumber() * (position.currentPrice?.toNumber() || position.entryPrice.toNumber());
          return sum + Math.abs(positionValue);
        }, 0);

        const exposureRatio = totalBalance > 0 ? totalExposure / totalBalance : 0;

        return Math.min(10.0, exposureRatio); // 最大1000%敞口（10倍杠杆）
      } finally {
        await prisma.$disconnect();
      }
    } catch (error) {
      this.logger.error('计算总敞口失败', { accountId, error });
      throw new Error(`无法计算账户 ${accountId} 的总敞口`);
    }
  }

  /**
   * 计算集中度风险（使用真实数据）
   */
  private async calculateConcentrationRisk(accountId: string, symbol: string): Promise<number> {
    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      try {
        // 获取账户和持仓信息
        const account = await prisma.tradingAccounts.findUnique({
          where: { id: accountId },
          include: {
            TradingPositions: {
              where: { status: 'OPEN' },
              include: { symbols: true }
            }
          }
        });

        if (!account) {
          throw new Error(`账户不存在: ${accountId}`);
        }

        const totalBalance = account.currentBalance.toNumber();
        const positions = account.TradingPositions;

        // 计算特定资产的集中度
        const symbolPositions = positions.filter(p => p.symbols?.symbol === symbol);
        const symbolExposure = symbolPositions.reduce((sum, position) => {
          const positionValue = position.quantity.toNumber() * (position.currentPrice?.toNumber() || position.entryPrice.toNumber());
          return sum + Math.abs(positionValue);
        }, 0);

        // 集中度 = 特定资产敞口 / 账户总余额
        const concentrationRatio = totalBalance > 0 ? symbolExposure / totalBalance : 0;

        return Math.min(1.0, concentrationRatio); // 最大100%集中度
      } finally {
        await prisma.$disconnect();
      }
    } catch (error) {
      this.logger.error('计算集中度风险失败', { accountId, symbol, error });
      throw new Error(`无法计算账户 ${accountId} 对 ${symbol} 的集中度风险`);
    }
  }

  /**
   * 计算最大允许订单大小
   */
  private calculateMaxAllowedSize(rule: RiskLimitRule, violation: RiskViolation, orderRequest: any): number {
    const reductionFactor = rule.threshold / violation.currentValue;
    return Math.floor(orderRequest.quantity * reductionFactor * 0.9); // 留10%安全边际
  }

  /**
   * 记录违规
   */
  private recordViolations(accountId: string, violations: RiskViolation[]): void {
    if (!this.violations.has(accountId)) {
      this.violations.set(accountId, []);
    }

    const accountViolations = this.violations.get(accountId)!;
    accountViolations.push(...violations);

    // 保持最近100条违规记录
    if (accountViolations.length > 100) {
      accountViolations.splice(0, accountViolations.length - 100);
    }

    this.logger.warn('记录风险违规', {
      accountId,
      violationsCount: violations.length,
      totalViolations: accountViolations.length
    });
  }

  /**
   * 执行风险强制行动
   */
  async executeEnforcementAction(
    accountId: string,
    action: RiskEnforcementAction,
    violation: RiskViolation,
    context?: any
  ): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      this.logger.info('执行风险强制行动', {
        accountId,
        action,
        violationType: violation.violationType,
        severity: violation.severity
      });

      switch (action) {
        case RiskEnforcementAction.BLOCK_NEW_ORDERS:
          return await this.blockNewOrders(accountId, violation);

        case RiskEnforcementAction.REDUCE_POSITION:
          return await this.reducePosition(accountId, violation, context);

        case RiskEnforcementAction.CLOSE_POSITION:
          return await this.closePosition(accountId, violation, context);

        case RiskEnforcementAction.EMERGENCY_STOP:
          return await this.emergencyStop(accountId, violation);

        case RiskEnforcementAction.SEND_ALERT:
          return await this.sendAlert(accountId, violation);

        default:
          return {
            success: false,
            message: `未知的强制执行动作: ${action}`
          };
      }
    } catch (error) {
      this.logger.error('执行风险强制行动失败', {
        accountId,
        action,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        message: '强制执行动作执行失败',
        details: { error: error instanceof Error ? error.message : String(error) }
      };
    }
  }

  /**
   * 阻止新订单
   */
  private async blockNewOrders(accountId: string, violation: RiskViolation): Promise<{ success: boolean; message: string }> {
    // 这里应该实现阻止新订单的逻辑
    // 例如：在数据库中设置账户状态、通知交易引擎等

    this.logger.warn('阻止账户新订单', {
      accountId,
      reason: violation.violationType,
      threshold: violation.threshold,
      currentValue: violation.currentValue
    });

    return {
      success: true,
      message: `账户 ${accountId} 的新订单已被阻止，原因：${violation.violationType}`
    };
  }

  /**
   * 减少仓位
   */
  private async reducePosition(
    accountId: string,
    violation: RiskViolation,
    context?: any
  ): Promise<{ success: boolean; message: string; details?: any }> {
    // 这里应该实现减少仓位的逻辑

    const reductionPercentage = Math.min(0.5, (violation.exceedanceRatio - 1) * 2); // 最多减少50%

    this.logger.warn('执行仓位减少', {
      accountId,
      symbol: violation.symbol,
      reductionPercentage,
      reason: violation.violationType
    });

    return {
      success: true,
      message: `账户 ${accountId} 的仓位已减少 ${(reductionPercentage * 100).toFixed(1)}%`,
      details: { reductionPercentage, symbol: violation.symbol }
    };
  }

  /**
   * 关闭仓位
   */
  private async closePosition(
    accountId: string,
    violation: RiskViolation,
    context?: any
  ): Promise<{ success: boolean; message: string; details?: any }> {
    // 这里应该实现关闭仓位的逻辑

    this.logger.error('执行仓位关闭', {
      accountId,
      symbol: violation.symbol,
      reason: violation.violationType,
      severity: violation.severity
    });

    return {
      success: true,
      message: `账户 ${accountId} 的 ${violation.symbol} 仓位已关闭`,
      details: { symbol: violation.symbol, reason: violation.violationType }
    };
  }

  /**
   * 紧急停止
   */
  private async emergencyStop(accountId: string, violation: RiskViolation): Promise<{ success: boolean; message: string }> {
    this.emergencyStops.add(accountId);

    this.logger.error('执行紧急停止', {
      accountId,
      reason: violation.violationType,
      severity: violation.severity
    });

    return {
      success: true,
      message: `账户 ${accountId} 已进入紧急停止状态`
    };
  }

  /**
   * 发送警报
   */
  private async sendAlert(accountId: string, violation: RiskViolation): Promise<{ success: boolean; message: string }> {
    // 这里应该实现发送警报的逻辑
    // 例如：发送邮件、短信、推送通知等

    this.logger.warn('发送风险警报', {
      accountId,
      violationType: violation.violationType,
      severity: violation.severity,
      currentValue: violation.currentValue,
      threshold: violation.threshold
    });

    return {
      success: true,
      message: `风险警报已发送给账户 ${accountId}`
    };
  }

  /**
   * 添加风险限制规则
   */
  addRiskRule(rule: RiskLimitRule): void {
    this.riskRules.set(rule.id, rule);
    this.logger.info('添加风险限制规则', {
      ruleId: rule.id,
      type: rule.type,
      threshold: rule.threshold
    });
  }

  /**
   * 更新风险限制规则
   */
  updateRiskRule(ruleId: string, updates: Partial<RiskLimitRule>): boolean {
    const rule = this.riskRules.get(ruleId);
    if (!rule) {
      return false;
    }

    Object.assign(rule, updates);
    this.riskRules.set(ruleId, rule);

    this.logger.info('更新风险限制规则', { ruleId, updates });
    return true;
  }

  /**
   * 删除风险限制规则
   */
  removeRiskRule(ruleId: string): boolean {
    const deleted = this.riskRules.delete(ruleId);
    if (deleted) {
      this.logger.info('删除风险限制规则', { ruleId });
    }
    return deleted;
  }

  /**
   * 获取所有风险限制规则
   */
  getRiskRules(): RiskLimitRule[] {
    return Array.from(this.riskRules.values());
  }

  /**
   * 获取账户违规历史
   */
  getViolationHistory(accountId: string): RiskViolation[] {
    return this.violations.get(accountId) || [];
  }

  /**
   * 清除紧急停止状态
   */
  clearEmergencyStop(accountId: string): boolean {
    const removed = this.emergencyStops.delete(accountId);
    if (removed) {
      this.logger.info('清除紧急停止状态', { accountId });
    }
    return removed;
  }

  /**
   * 检查账户是否处于紧急停止状态
   */
  isEmergencyStopped(accountId: string): boolean {
    return this.emergencyStops.has(accountId);
  }

  /**
   * 获取风险强制执行统计
   */
  getEnforcementStats(): {
    totalRules: number;
    activeRules: number;
    totalViolations: number;
    emergencyStops: number;
    violationsByType: Record<string, number>;
  } {
    const totalRules = this.riskRules.size;
    const activeRules = Array.from(this.riskRules.values()).filter(r => r.isActive).length;

    let totalViolations = 0;
    const violationsByType: Record<string, number> = {};

    this.violations.forEach(accountViolations => {
      totalViolations += accountViolations.length;
      accountViolations.forEach(violation => {
        violationsByType[violation.violationType] = (violationsByType[violation.violationType] || 0) + 1;
      });
    });

    return {
      totalRules,
      activeRules,
      totalViolations,
      emergencyStops: this.emergencyStops.size,
      violationsByType
    };
  }

  /**
   * 销毁资源
   */
  destroy(): void {
    this.stopRealTimeMonitoring();
    this.riskRules.clear();
    this.violations.clear();
    this.emergencyStops.clear();
    this.logger.info('RiskEnforcementEngine 资源已清理');
  }
}
