/**
 * 实时风险监控引擎
 * 将风险计算从批处理模式升级为实时模式，提供毫秒级风险监控能力
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../di/types';
import { IBasicLogger } from '../logging/interfaces/basic-logger.interface';
import { UnifiedErrorHandler } from '../error/unified-error-handler';
import { EventEmitter } from 'events';

/**
 * 实时风险数据
 */
export interface RealTimeRiskData {
  accountId: string;
  symbol?: string;
  timestamp: Date;
  riskScore: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | 'EMERGENCY';
  metrics: {
    var95: number;
    volatility: number;
    drawdown: number;
    exposure: number;
    leverage: number;
    liquidityScore: number;
  };
  alerts: RiskAlert[];
  trends: {
    riskScoreTrend: 'INCREASING' | 'STABLE' | 'DECREASING';
    volatilityTrend: 'INCREASING' | 'STABLE' | 'DECREASING';
    exposureTrend: 'INCREASING' | 'STABLE' | 'DECREASING';
  };
  lastUpdate: Date;
}

/**
 * 风险警报
 */
export interface RiskAlert {
  id: string;
  type: 'THRESHOLD_BREACH' | 'TREND_CHANGE' | 'ANOMALY_DETECTED' | 'CORRELATION_SPIKE';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  message: string;
  value: number;
  threshold: number;
  timestamp: Date;
  acknowledged: boolean;
}

/**
 * 实时监控配置
 */
export interface RealTimeMonitorConfig {
  updateInterval: number;           // 更新间隔（毫秒）
  alertThresholds: {
    riskScore: number;
    volatility: number;
    drawdown: number;
    exposure: number;
  };
  trendAnalysisWindow: number;      // 趋势分析窗口（数据点数量）
  enablePredictiveAlerts: boolean;  // 启用预测性警报
  enableAnomalyDetection: boolean;  // 启用异常检测
  maxHistorySize: number;           // 最大历史数据大小
}

/**
 * 风险数据流
 */
export interface RiskDataStream {
  accountId: string;
  dataPoints: RealTimeRiskData[];
  lastUpdate: Date;
  isActive: boolean;
}

/**
 * 实时风险监控引擎
 */
@injectable()
export class RealTimeRiskMonitor extends EventEmitter {
  private readonly config: RealTimeMonitorConfig;
  private readonly riskStreams: Map<string, RiskDataStream> = new Map();
  private readonly alertHistory: Map<string, RiskAlert[]> = new Map();
  private monitoringTimer: NodeJS.Timeout | null = null;
  private isMonitoring = false;

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler
  ) {
    super();
    
    this.config = {
      updateInterval: 1000,        // 1秒更新
      alertThresholds: {
        riskScore: 70,             // 风险评分阈值
        volatility: 0.5,           // 50%波动率阈值
        drawdown: 0.15,            // 15%回撤阈值
        exposure: 0.8              // 80%敞口阈值
      },
      trendAnalysisWindow: 20,     // 20个数据点
      enablePredictiveAlerts: true,
      enableAnomalyDetection: true,
      maxHistorySize: 1000         // 保留1000个历史数据点
    };

    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    this.on('riskAlert', (alert: RiskAlert) => {
      this.handleRiskAlert(alert);
    });

    this.on('riskLevelChange', (data: { accountId: string; oldLevel: string; newLevel: string }) => {
      this.handleRiskLevelChange(data);
    });

    this.on('anomalyDetected', (data: { accountId: string; anomaly: any }) => {
      this.handleAnomalyDetected(data);
    });
  }

  /**
   * 启动实时监控
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      this.logger.warn('实时风险监控已在运行');
      return;
    }

    this.logger.info('启动实时风险监控', {
      updateInterval: this.config.updateInterval,
      alertThresholds: this.config.alertThresholds
    });

    this.isMonitoring = true;
    this.monitoringTimer = setInterval(() => {
      this.performRealTimeUpdate();
    }, this.config.updateInterval);

    this.emit('monitoringStarted');
  }

  /**
   * 停止实时监控
   */
  async stopMonitoring(): Promise<void> {
    if (!this.isMonitoring) {
      return;
    }

    this.logger.info('停止实时风险监控');

    this.isMonitoring = false;
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
    }

    this.emit('monitoringStopped');
  }

  /**
   * 添加账户到实时监控
   */
  async addAccountToMonitoring(accountId: string): Promise<void> {
    if (this.riskStreams.has(accountId)) {
      this.logger.warn('账户已在监控中', { accountId });
      return;
    }

    const stream: RiskDataStream = {
      accountId,
      dataPoints: [],
      lastUpdate: new Date(),
      isActive: true
    };

    this.riskStreams.set(accountId, stream);
    this.alertHistory.set(accountId, []);

    this.logger.info('添加账户到实时监控', { accountId });
    this.emit('accountAdded', { accountId });
  }

  /**
   * 从实时监控中移除账户
   */
  async removeAccountFromMonitoring(accountId: string): Promise<void> {
    const stream = this.riskStreams.get(accountId);
    if (!stream) {
      return;
    }

    stream.isActive = false;
    this.riskStreams.delete(accountId);

    this.logger.info('从实时监控中移除账户', { accountId });
    this.emit('accountRemoved', { accountId });
  }

  /**
   * 更新账户风险数据
   */
  async updateAccountRisk(accountId: string, riskData: Partial<RealTimeRiskData>): Promise<void> {
    const stream = this.riskStreams.get(accountId);
    if (!stream || !stream.isActive) {
      // 如果账户不在监控中，自动添加
      await this.addAccountToMonitoring(accountId);
    }

    const currentStream = this.riskStreams.get(accountId)!;
    const previousData = currentStream.dataPoints[currentStream.dataPoints.length - 1];

    // 创建新的风险数据点
    const newRiskData: RealTimeRiskData = {
      accountId,
      symbol: riskData.symbol,
      timestamp: new Date(),
      riskScore: riskData.riskScore || 0,
      riskLevel: riskData.riskLevel || 'LOW',
      metrics: riskData.metrics || {
        var95: 0,
        volatility: 0,
        drawdown: 0,
        exposure: 0,
        leverage: 1,
        liquidityScore: 100
      },
      alerts: riskData.alerts || [],
      trends: this.calculateTrends(currentStream.dataPoints, riskData),
      lastUpdate: new Date()
    };

    // 添加到数据流
    currentStream.dataPoints.push(newRiskData);
    currentStream.lastUpdate = new Date();

    // 限制历史数据大小
    if (currentStream.dataPoints.length > this.config.maxHistorySize) {
      currentStream.dataPoints.shift();
    }

    // 检查风险阈值和生成警报
    await this.checkRiskThresholds(newRiskData, previousData);

    // 检查异常
    if (this.config.enableAnomalyDetection) {
      await this.detectAnomalies(accountId, newRiskData, currentStream.dataPoints);
    }

    this.emit('riskDataUpdated', { accountId, riskData: newRiskData });
  }

  /**
   * 执行实时更新
   */
  private async performRealTimeUpdate(): Promise<void> {
    try {
      const activeStreams = Array.from(this.riskStreams.values()).filter(s => s.isActive);
      
      this.logger.debug('执行实时风险更新', {
        activeStreams: activeStreams.length
      });

      // 并行更新所有活跃的风险流
      await Promise.all(activeStreams.map(stream => 
        this.updateStreamRiskData(stream)
      ));

    } catch (error) {
      this.logger.error('实时风险更新失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 更新流风险数据
   */
  private async updateStreamRiskData(stream: RiskDataStream): Promise<void> {
    try {
      // 使用真实的风险计算服务
      const realRiskData = await this.calculateRealRiskData(stream.accountId);

      await this.updateAccountRisk(stream.accountId, realRiskData);
    } catch (error) {
      this.logger.error('更新流风险数据失败', {
        accountId: stream.accountId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 计算真实风险数据（使用统一组件替换虚假实现）
   */
  private async calculateRealRiskData(accountId: string): Promise<Partial<RealTimeRiskData>> {
    try {
      this.logger.info('开始计算真实风险数据', { accountId });

      // 使用统一的交易执行服务获取真实数据
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      try {
        // 获取账户信息
        const account = await prisma.tradingAccounts.findUnique({
          where: { id: accountId },
          include: {
            TradingPositions: {
              where: { status: 'OPEN' },
              include: { symbols: true }
            }
          }
        });

        if (!account) {
          throw new Error(`账户不存在: ${accountId}`);
        }

        // 计算真实的风险指标
        const positions = account.TradingPositions;
        const totalBalance = account.currentBalance.toNumber();
        const availableBalance = account.availableBalance.toNumber();
        const totalPnl = account.totalPnl.toNumber();

        // 计算总敞口（基于真实持仓）
        const totalExposure = positions.reduce((sum, pos) => {
          return sum + (pos.quantity.toNumber() * pos.currentPrice?.toNumber() || pos.entryPrice.toNumber());
        }, 0) / totalBalance;

        // 计算当前回撤（基于真实PnL）
        const currentDrawdown = Math.max(0, -totalPnl / totalBalance);

        // 计算平均杠杆（基于真实持仓）
        const averageLeverage = positions.length > 0
          ? positions.reduce((sum, pos) => sum + pos.leverage, 0) / positions.length
          : 1;

        // 计算流动性评分（基于持仓分散度）
        const liquidityScore = this.calculateLiquidityScoreFromPositions(positions);

        // 基于真实数据计算综合风险评分
        const riskScore = this.calculateComprehensiveRiskScore({
          exposure: totalExposure,
          drawdown: currentDrawdown,
          leverage: averageLeverage,
          liquidity: liquidityScore,
          positionCount: positions.length
        });

        await prisma.$disconnect();

        return {
          riskScore,
          riskLevel: this.calculateRiskLevel(riskScore),
          metrics: {
            var95: riskScore * 0.01,
            volatility: this.estimateVolatilityFromPositions(positions),
            drawdown: currentDrawdown,
            exposure: totalExposure,
            leverage: averageLeverage,
            liquidityScore
          }
        };
      } finally {
        await prisma.$disconnect();
      }
    } catch (error) {
      this.logger.error('计算真实风险数据失败', { accountId, error });
      throw new Error(`无法计算账户 ${accountId} 的风险数据: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 计算风险等级
   */
  private calculateRiskLevel(riskScore: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | 'EMERGENCY' {
    if (riskScore < 20) return 'LOW';
    if (riskScore < 40) return 'MEDIUM';
    if (riskScore < 70) return 'HIGH';
    if (riskScore < 90) return 'CRITICAL';
    return 'EMERGENCY';
  }

  /**
   * 基于真实持仓计算流动性评分
   */
  private calculateLiquidityScoreFromPositions(positions: any[]): number {
    if (positions.length === 0) return 100;

    // 基于持仓分散度计算流动性评分
    const symbolCount = new Set(positions.map(p => p.symbols?.symbol)).size;
    const positionCount = positions.length;

    // 分散度越高，流动性评分越高
    const diversificationScore = Math.min(100, (symbolCount / Math.max(1, positionCount)) * 100);

    // 基于持仓大小调整评分
    const avgPositionSize = positions.reduce((sum, p) => sum + p.quantity.toNumber(), 0) / positions.length;
    const sizeAdjustment = Math.max(0.5, Math.min(1.5, 1 / Math.log10(avgPositionSize + 1)));

    return Math.min(100, diversificationScore * sizeAdjustment);
  }

  /**
   * 基于真实数据计算综合风险评分
   */
  private calculateComprehensiveRiskScore(params: {
    exposure: number;
    drawdown: number;
    leverage: number;
    liquidity: number;
    positionCount: number;
  }): number {
    const { exposure, drawdown, leverage, liquidity, positionCount } = params;

    // 敞口风险 (0-40分)
    const exposureRisk = Math.min(40, exposure * 100 * 0.4);

    // 回撤风险 (0-30分)
    const drawdownRisk = Math.min(30, drawdown * 100 * 0.3);

    // 杠杆风险 (0-20分)
    const leverageRisk = Math.min(20, (leverage - 1) * 4);

    // 流动性风险 (0-10分，流动性越低风险越高)
    const liquidityRisk = Math.min(10, (100 - liquidity) * 0.1);

    // 集中度风险 (0-10分，持仓越少风险越高)
    const concentrationRisk = positionCount === 0 ? 10 : Math.min(10, 10 / positionCount);

    const totalRisk = exposureRisk + drawdownRisk + leverageRisk + liquidityRisk + concentrationRisk;

    return Math.min(100, Math.max(0, totalRisk));
  }

  /**
   * 基于持仓估算波动率
   */
  private estimateVolatilityFromPositions(positions: any[]): number {
    if (positions.length === 0) return 0;

    // 基于持仓的杠杆和数量估算波动率
    const avgLeverage = positions.reduce((sum, p) => sum + p.leverage, 0) / positions.length;
    const totalValue = positions.reduce((sum, p) => sum + p.quantity.toNumber() * (p.currentPrice?.toNumber() || p.entryPrice.toNumber()), 0);

    // 简化的波动率估算：基于杠杆和持仓规模
    const baseVolatility = 0.02; // 基础波动率 2%
    const leverageMultiplier = Math.log10(avgLeverage + 1);
    const sizeMultiplier = Math.log10(totalValue / 1000 + 1);

    return Math.min(1.0, baseVolatility * (1 + leverageMultiplier + sizeMultiplier * 0.1));
  }

  /**
   * 计算趋势
   */
  private calculateTrends(
    historicalData: RealTimeRiskData[], 
    newData: Partial<RealTimeRiskData>
  ): RealTimeRiskData['trends'] {
    const windowSize = Math.min(this.config.trendAnalysisWindow, historicalData.length);
    const recentData = historicalData.slice(-windowSize);

    if (recentData.length < 2) {
      return {
        riskScoreTrend: 'STABLE',
        volatilityTrend: 'STABLE',
        exposureTrend: 'STABLE'
      };
    }

    const riskScoreTrend = this.calculateTrend(
      recentData.map(d => d.riskScore),
      newData.riskScore || 0
    );

    const volatilityTrend = this.calculateTrend(
      recentData.map(d => d.metrics.volatility),
      newData.metrics?.volatility || 0
    );

    const exposureTrend = this.calculateTrend(
      recentData.map(d => d.metrics.exposure),
      newData.metrics?.exposure || 0
    );

    return {
      riskScoreTrend,
      volatilityTrend,
      exposureTrend
    };
  }

  /**
   * 计算单个指标趋势
   */
  private calculateTrend(values: number[], newValue: number): 'INCREASING' | 'STABLE' | 'DECREASING' {
    if (values.length < 2) return 'STABLE';

    const recent = values.slice(-5); // 最近5个值
    const average = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    
    const threshold = 0.05; // 5%变化阈值
    
    if (newValue > average * (1 + threshold)) return 'INCREASING';
    if (newValue < average * (1 - threshold)) return 'DECREASING';
    return 'STABLE';
  }

  /**
   * 检查风险阈值
   */
  private async checkRiskThresholds(
    currentData: RealTimeRiskData,
    previousData?: RealTimeRiskData
  ): Promise<void> {
    const alerts: RiskAlert[] = [];

    // 检查风险评分阈值
    if (currentData.riskScore > this.config.alertThresholds.riskScore) {
      alerts.push({
        id: `risk-score-${Date.now()}`,
        type: 'THRESHOLD_BREACH',
        severity: currentData.riskScore > 90 ? 'CRITICAL' : 'HIGH',
        message: `风险评分超过阈值: ${currentData.riskScore.toFixed(2)} > ${this.config.alertThresholds.riskScore}`,
        value: currentData.riskScore,
        threshold: this.config.alertThresholds.riskScore,
        timestamp: new Date(),
        acknowledged: false
      });
    }

    // 检查波动率阈值
    if (currentData.metrics.volatility > this.config.alertThresholds.volatility) {
      alerts.push({
        id: `volatility-${Date.now()}`,
        type: 'THRESHOLD_BREACH',
        severity: 'MEDIUM',
        message: `波动率超过阈值: ${(currentData.metrics.volatility * 100).toFixed(2)}% > ${(this.config.alertThresholds.volatility * 100).toFixed(2)}%`,
        value: currentData.metrics.volatility,
        threshold: this.config.alertThresholds.volatility,
        timestamp: new Date(),
        acknowledged: false
      });
    }

    // 检查回撤阈值
    if (currentData.metrics.drawdown > this.config.alertThresholds.drawdown) {
      alerts.push({
        id: `drawdown-${Date.now()}`,
        type: 'THRESHOLD_BREACH',
        severity: 'HIGH',
        message: `回撤超过阈值: ${(currentData.metrics.drawdown * 100).toFixed(2)}% > ${(this.config.alertThresholds.drawdown * 100).toFixed(2)}%`,
        value: currentData.metrics.drawdown,
        threshold: this.config.alertThresholds.drawdown,
        timestamp: new Date(),
        acknowledged: false
      });
    }

    // 检查敞口阈值
    if (currentData.metrics.exposure > this.config.alertThresholds.exposure) {
      alerts.push({
        id: `exposure-${Date.now()}`,
        type: 'THRESHOLD_BREACH',
        severity: 'MEDIUM',
        message: `敞口超过阈值: ${(currentData.metrics.exposure * 100).toFixed(2)}% > ${(this.config.alertThresholds.exposure * 100).toFixed(2)}%`,
        value: currentData.metrics.exposure,
        threshold: this.config.alertThresholds.exposure,
        timestamp: new Date(),
        acknowledged: false
      });
    }

    // 检查风险等级变化
    if (previousData && currentData.riskLevel !== previousData.riskLevel) {
      this.emit('riskLevelChange', {
        accountId: currentData.accountId,
        oldLevel: previousData.riskLevel,
        newLevel: currentData.riskLevel
      });
    }

    // 发送警报
    for (const alert of alerts) {
      await this.addAlert(currentData.accountId, alert);
      this.emit('riskAlert', alert);
    }
  }

  /**
   * 检测异常
   */
  private async detectAnomalies(
    accountId: string,
    currentData: RealTimeRiskData,
    historicalData: RealTimeRiskData[]
  ): Promise<void> {
    if (historicalData.length < 10) {
      return; // 需要足够的历史数据
    }

    // 简单的异常检测：基于标准差
    const recentData = historicalData.slice(-20);
    const riskScores = recentData.map(d => d.riskScore);
    const mean = riskScores.reduce((sum, val) => sum + val, 0) / riskScores.length;
    const variance = riskScores.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / riskScores.length;
    const stdDev = Math.sqrt(variance);

    // 如果当前值超过3个标准差，认为是异常
    if (Math.abs(currentData.riskScore - mean) > 3 * stdDev) {
      const anomaly = {
        type: 'STATISTICAL_OUTLIER',
        value: currentData.riskScore,
        mean,
        stdDev,
        deviations: Math.abs(currentData.riskScore - mean) / stdDev
      };

      this.emit('anomalyDetected', { accountId, anomaly });

      const alert: RiskAlert = {
        id: `anomaly-${Date.now()}`,
        type: 'ANOMALY_DETECTED',
        severity: 'HIGH',
        message: `检测到风险异常: 当前值 ${currentData.riskScore.toFixed(2)} 偏离均值 ${anomaly.deviations.toFixed(2)} 个标准差`,
        value: currentData.riskScore,
        threshold: mean + 3 * stdDev,
        timestamp: new Date(),
        acknowledged: false
      };

      await this.addAlert(accountId, alert);
      this.emit('riskAlert', alert);
    }
  }

  /**
   * 添加警报
   */
  private async addAlert(accountId: string, alert: RiskAlert): Promise<void> {
    let alerts = this.alertHistory.get(accountId);
    if (!alerts) {
      alerts = [];
      this.alertHistory.set(accountId, alerts);
    }

    alerts.push(alert);

    // 限制警报历史大小
    if (alerts.length > 100) {
      alerts.shift();
    }

    this.logger.warn('生成风险警报', {
      accountId,
      alertType: alert.type,
      severity: alert.severity,
      message: alert.message
    });
  }

  /**
   * 处理风险警报
   */
  private async handleRiskAlert(alert: RiskAlert): Promise<void> {
    // 这里可以实现警报处理逻辑，如发送通知、记录日志等
    this.logger.warn('处理风险警报', {
      alertId: alert.id,
      type: alert.type,
      severity: alert.severity,
      message: alert.message
    });
  }

  /**
   * 处理风险等级变化
   */
  private async handleRiskLevelChange(data: { accountId: string; oldLevel: string; newLevel: string }): Promise<void> {
    this.logger.info('风险等级变化', data);
  }

  /**
   * 处理异常检测
   */
  private async handleAnomalyDetected(data: { accountId: string; anomaly: any }): Promise<void> {
    this.logger.warn('检测到风险异常', data);
  }

  /**
   * 获取账户当前风险数据
   */
  getCurrentRiskData(accountId: string): RealTimeRiskData | null {
    const stream = this.riskStreams.get(accountId);
    if (!stream || stream.dataPoints.length === 0) {
      return null;
    }
    return stream.dataPoints[stream.dataPoints.length - 1];
  }

  /**
   * 获取账户风险历史
   */
  getRiskHistory(accountId: string, limit?: number): RealTimeRiskData[] {
    const stream = this.riskStreams.get(accountId);
    if (!stream) {
      return [];
    }

    const dataPoints = stream.dataPoints;
    if (limit && limit > 0) {
      return dataPoints.slice(-limit);
    }
    return [...dataPoints];
  }

  /**
   * 获取账户警报历史
   */
  getAlertHistory(accountId: string, limit?: number): RiskAlert[] {
    const alerts = this.alertHistory.get(accountId) || [];
    if (limit && limit > 0) {
      return alerts.slice(-limit);
    }
    return [...alerts];
  }

  /**
   * 获取未确认的警报
   */
  getUnacknowledgedAlerts(accountId?: string): RiskAlert[] {
    if (accountId) {
      const alerts = this.alertHistory.get(accountId) || [];
      return alerts.filter(alert => !alert.acknowledged);
    }

    // 获取所有账户的未确认警报
    const allAlerts: RiskAlert[] = [];
    Array.from(this.alertHistory.values()).forEach(alerts => {
      allAlerts.push(...alerts.filter(alert => !alert.acknowledged));
    });
    return allAlerts;
  }

  /**
   * 确认警报
   */
  async acknowledgeAlert(accountId: string, alertId: string): Promise<boolean> {
    const alerts = this.alertHistory.get(accountId);
    if (!alerts) {
      return false;
    }

    const alert = alerts.find(a => a.id === alertId);
    if (!alert) {
      return false;
    }

    alert.acknowledged = true;
    this.logger.info('警报已确认', { accountId, alertId });
    return true;
  }

  /**
   * 获取监控统计信息
   */
  getMonitoringStats(): {
    isMonitoring: boolean;
    activeStreams: number;
    totalDataPoints: number;
    totalAlerts: number;
    unacknowledgedAlerts: number;
    updateInterval: number;
  } {
    let totalDataPoints = 0;
    let totalAlerts = 0;
    let unacknowledgedAlerts = 0;

    Array.from(this.riskStreams.values()).forEach(stream => {
      totalDataPoints += stream.dataPoints.length;
    });

    Array.from(this.alertHistory.values()).forEach(alerts => {
      totalAlerts += alerts.length;
      unacknowledgedAlerts += alerts.filter(a => !a.acknowledged).length;
    });

    return {
      isMonitoring: this.isMonitoring,
      activeStreams: this.riskStreams.size,
      totalDataPoints,
      totalAlerts,
      unacknowledgedAlerts,
      updateInterval: this.config.updateInterval
    };
  }

  /**
   * 更新监控配置
   */
  updateConfig(newConfig: Partial<RealTimeMonitorConfig>): void {
    Object.assign(this.config, newConfig);

    this.logger.info('实时监控配置已更新', { newConfig });

    // 如果更新间隔改变，重启监控
    if (newConfig.updateInterval && this.isMonitoring) {
      this.stopMonitoring();
      this.startMonitoring();
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): RealTimeMonitorConfig {
    return { ...this.config };
  }

  /**
   * 清理账户数据
   */
  async clearAccountData(accountId: string): Promise<void> {
    const stream = this.riskStreams.get(accountId);
    if (stream) {
      stream.dataPoints = [];
    }

    const alerts = this.alertHistory.get(accountId);
    if (alerts) {
      alerts.length = 0;
    }

    this.logger.info('账户数据已清理', { accountId });
  }

  /**
   * 获取风险趋势分析
   */
  getRiskTrendAnalysis(accountId: string, timeWindow: number = 20): {
    riskScoreTrend: {
      direction: 'INCREASING' | 'STABLE' | 'DECREASING';
      slope: number;
      confidence: number;
    };
    volatilityTrend: {
      direction: 'INCREASING' | 'STABLE' | 'DECREASING';
      slope: number;
      confidence: number;
    };
    prediction: {
      nextRiskScore: number;
      confidence: number;
    };
  } | null {
    const stream = this.riskStreams.get(accountId);
    if (!stream || stream.dataPoints.length < timeWindow) {
      return null;
    }

    const recentData = stream.dataPoints.slice(-timeWindow);

    // 计算风险评分趋势
    const riskScores = recentData.map(d => d.riskScore);
    const riskTrend = this.calculateLinearTrend(riskScores);

    // 计算波动率趋势
    const volatilities = recentData.map(d => d.metrics.volatility);
    const volatilityTrend = this.calculateLinearTrend(volatilities);

    // 简单的线性预测
    const nextRiskScore = riskScores[riskScores.length - 1] + riskTrend.slope;
    const predictionConfidence = Math.max(0, Math.min(1, 1 - Math.abs(riskTrend.slope) / 10));

    return {
      riskScoreTrend: {
        direction: riskTrend.slope > 1 ? 'INCREASING' : riskTrend.slope < -1 ? 'DECREASING' : 'STABLE',
        slope: riskTrend.slope,
        confidence: riskTrend.confidence
      },
      volatilityTrend: {
        direction: volatilityTrend.slope > 0.01 ? 'INCREASING' : volatilityTrend.slope < -0.01 ? 'DECREASING' : 'STABLE',
        slope: volatilityTrend.slope,
        confidence: volatilityTrend.confidence
      },
      prediction: {
        nextRiskScore: Math.max(0, Math.min(100, nextRiskScore)),
        confidence: predictionConfidence
      }
    };
  }

  /**
   * 计算线性趋势
   */
  private calculateLinearTrend(values: number[]): { slope: number; confidence: number } {
    if (values.length < 2) {
      return { slope: 0, confidence: 0 };
    }

    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;

    // 计算线性回归
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);

    // 计算R²作为置信度
    const meanY = sumY / n;
    const totalSumSquares = y.reduce((sum, val) => sum + Math.pow(val - meanY, 2), 0);
    const residualSumSquares = y.reduce((sum, val, i) => {
      const predicted = slope * x[i] + (sumY - slope * sumX) / n;
      return sum + Math.pow(val - predicted, 2);
    }, 0);

    const rSquared = totalSumSquares > 0 ? 1 - (residualSumSquares / totalSumSquares) : 0;

    return {
      slope,
      confidence: Math.max(0, Math.min(1, rSquared))
    };
  }

  /**
   * 导出风险数据
   */
  exportRiskData(accountId: string, format: 'json' | 'csv' = 'json'): string {
    const stream = this.riskStreams.get(accountId);
    if (!stream) {
      throw new Error(`账户 ${accountId} 不在监控中`);
    }

    if (format === 'json') {
      return JSON.stringify({
        accountId,
        exportTime: new Date(),
        dataPoints: stream.dataPoints,
        alerts: this.alertHistory.get(accountId) || []
      }, null, 2);
    } else {
      // CSV格式
      const headers = [
        'timestamp', 'riskScore', 'riskLevel', 'var95', 'volatility',
        'drawdown', 'exposure', 'leverage', 'liquidityScore'
      ];

      const rows = stream.dataPoints.map(data => [
        data.timestamp.toISOString(),
        data.riskScore,
        data.riskLevel,
        data.metrics.var95,
        data.metrics.volatility,
        data.metrics.drawdown,
        data.metrics.exposure,
        data.metrics.leverage,
        data.metrics.liquidityScore
      ]);

      return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    }
  }

  /**
   * 销毁监控器
   */
  async destroy(): Promise<void> {
    await this.stopMonitoring();
    this.riskStreams.clear();
    this.alertHistory.clear();
    this.removeAllListeners();

    this.logger.info('实时风险监控器已销毁');
  }
}
