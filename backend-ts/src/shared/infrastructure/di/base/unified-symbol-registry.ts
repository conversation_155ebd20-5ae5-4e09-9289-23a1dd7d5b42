/**
 * 统一Symbol注册表
 * 解决Symbol定义重复问题，提供统一的Symbol管理机制
 */

/**
 * Symbol注册表接口
 */
export interface ISymbolRegistry {
  register(key: string, description?: string): symbol;
  get(key: string): symbol | undefined;
  has(key: string): boolean;
  getAll(): Map<string, symbol>;
  clear(): void;
  getDescription(key: string): string | undefined;
}

/**
 * Symbol注册表实现
 */
class SymbolRegistry implements ISymbolRegistry {
  private symbols = new Map<string, symbol>();
  private descriptions = new Map<string, string>();

  /**
   * 注册Symbol
   */
  register(key: string, description?: string): symbol {
    if (this.symbols.has(key)) {
      // 返回已存在的Symbol，避免重复创建
      return this.symbols.get(key)!;
    }

    const symbol = Symbol.for(key);
    this.symbols.set(key, symbol);
    
    if (description) {
      this.descriptions.set(key, description);
    }

    return symbol;
  }

  /**
   * 获取Symbol
   */
  get(key: string): symbol | undefined {
    return this.symbols.get(key);
  }

  /**
   * 检查Symbol是否存在
   */
  has(key: string): boolean {
    return this.symbols.has(key);
  }

  /**
   * 获取所有Symbol
   */
  getAll(): Map<string, symbol> {
    return new Map(this.symbols);
  }

  /**
   * 清空注册表
   */
  clear(): void {
    this.symbols.clear();
    this.descriptions.clear();
  }

  /**
   * 获取Symbol描述
   */
  getDescription(key: string): string | undefined {
    return this.descriptions.get(key);
  }
}

/**
 * 全局Symbol注册表实例
 */
export const symbolRegistry: ISymbolRegistry = new SymbolRegistry();

/**
 * Symbol工厂函数
 * 统一创建和管理Symbol
 */
export function createSymbol(key: string, description?: string): symbol {
  return symbolRegistry.register(key, description);
}

/**
 * 获取Symbol
 */
export function getSymbol(key: string): symbol {
  const symbol = symbolRegistry.get(key);
  if (!symbol) {
    throw new Error(`Symbol not found: ${key}`);
  }
  return symbol;
}

/**
 * 安全获取Symbol（不抛出异常）
 */
export function getSymbolSafe(key: string): symbol | undefined {
  return symbolRegistry.get(key);
}

/**
 * 预定义的核心Symbol
 * 统一管理所有核心服务的Symbol
 */
export const CORE_SYMBOLS = {
  // 基础设施服务
  Logger: createSymbol('Logger', '统一日志服务'),
  Database: createSymbol('Database', 'Prisma数据库客户端'),
  Redis: createSymbol('Redis', 'Redis客户端'),
  EventBus: createSymbol('EventBus', '事件总线'),
  Environment: createSymbol('Environment', '环境配置'),
  
  // Redis相关
  RedisSubscriber: createSymbol('RedisSubscriber', 'Redis订阅客户端'),
  RedisPublisher: createSymbol('RedisPublisher', 'Redis发布客户端'),
  
  // 监控和指标
  Metrics: createSymbol('Metrics', '监控指标服务'),
  
  // 消息队列
  EventStore: createSymbol('EventStore', '事件存储'),
  MessageQueue: createSymbol('MessageQueue', '消息队列'),
} as const;

/**
 * 共享服务Symbol
 */
export const SHARED_SYMBOLS = {
  // 缓存服务
  CacheService: createSymbol('Shared.CacheService', '统一缓存服务'),
  MultiTierCacheService: createSymbol('Shared.MultiTierCacheService', '多层缓存服务'),
  
  // AI服务
  AIServiceManager: createSymbol('Shared.AIServiceManager', 'AI服务管理器'),
  AICallLogService: createSymbol('Shared.AICallLogService', 'AI调用日志服务'),
  
  // 配置管理
  ConfigManager: createSymbol('Shared.ConfigManager', '统一配置管理器'),
  EnvironmentManager: createSymbol('Shared.EnvironmentManager', '环境管理器'),
  
  // 错误处理
  ErrorHandler: createSymbol('Shared.ErrorHandler', '统一错误处理器'),
  
  // 监控服务
  HealthCheckService: createSymbol('Shared.HealthCheckService', '健康检查服务'),
  PerformanceMonitoringService: createSymbol('Shared.PerformanceMonitoringService', '性能监控服务'),
  MonitoringManager: createSymbol('Shared.MonitoringManager', '监控管理器'),
  
  // 数据库相关
  QueryManager: createSymbol('Shared.QueryManager', '查询管理器'),
  DatabaseQueryIndexOptimizer: createSymbol('Shared.DatabaseQueryIndexOptimizer', '数据库查询索引优化器'),
  RepositoryBaseService: createSymbol('Shared.RepositoryBaseService', '仓储基础服务'),
  DataMappingService: createSymbol('Shared.DataMappingService', '数据映射服务'),
  
  // WebSocket服务
  WebSocketServer: createSymbol('Shared.WebSocketServer', 'WebSocket服务器'),
  AIRealtimeService: createSymbol('Shared.AIRealtimeService', 'AI实时服务'),
  
  // 核心分析服务
  DynamicWeightingService: createSymbol('Shared.DynamicWeightingService', '动态权重分配服务'),
  PatternRecognitionService: createSymbol('Shared.PatternRecognitionService', '模式识别服务'),
  MultiTimeframeService: createSymbol('Shared.MultiTimeframeService', '多时间框架服务'),
  
  // 技术指标
  TechnicalIndicatorCalculator: createSymbol('Shared.TechnicalIndicatorCalculator', '统一技术指标计算器'),
  
  // 数据同步
  DataSyncCoordinator: createSymbol('Shared.DataSyncCoordinator', '数据同步协调器'),
  LearningSystemStarter: createSymbol('Shared.LearningSystemStarter', '学习系统启动器'),
  
  // 用户服务
  UserIdentityService: createSymbol('Shared.UserIdentityService', '用户身份服务'),
  
  // 性能优化
  ConcurrencyOptimizer: createSymbol('Shared.ConcurrencyOptimizer', '并发优化器'),
  
  // 警报系统
  AlertSystem: createSymbol('Shared.AlertSystem', '统一警报系统'),
  
  // 配置服务
  RiskConfigService: createSymbol('Shared.RiskConfigService', '风险管理配置服务'),
} as const;

/**
 * 市场数据Symbol
 */
export const MARKET_DATA_SYMBOLS = {
  // 仓储
  MarketSymbolRepository: createSymbol('MarketData.MarketSymbolRepository', '市场交易对仓储'),
  PriceDataRepository: createSymbol('MarketData.PriceDataRepository', '价格数据仓储'),
  HistoricalDataRepository: createSymbol('MarketData.HistoricalDataRepository', '历史数据仓储'),
  
  // 应用服务
  MarketDataApplicationService: createSymbol('MarketData.ApplicationService', '市场数据应用服务'),
  RealDataIntegrationService: createSymbol('MarketData.RealDataIntegrationService', '真实数据集成服务'),
  RealMarketDataService: createSymbol('MarketData.RealMarketDataService', '真实市场数据服务'),
  
  // 外部适配器
  ExchangeAdapterFactory: createSymbol('MarketData.ExchangeAdapterFactory', '交易所适配器工厂'),
  CoinMetricsAdapter: createSymbol('MarketData.CoinMetricsAdapter', 'CoinMetrics适配器'),
  SentiCryptAdapter: createSymbol('MarketData.SentiCryptAdapter', 'SentiCrypt适配器'),
  FearGreedAdapter: createSymbol('MarketData.FearGreedAdapter', '恐惧贪婪指数适配器'),
  BinanceAdapter: createSymbol('MarketData.BinanceAdapter', 'Binance适配器'),
  KrakenAdapter: createSymbol('MarketData.KrakenAdapter', 'Kraken适配器'),
  
  // WebSocket服务
  WebSocketService: createSymbol('MarketData.WebSocketService', '市场数据WebSocket服务'),
  RealWebSocketManager: createSymbol('MarketData.RealWebSocketManager', '真实WebSocket管理器'),
  MultiExchangeWebSocketManager: createSymbol('MarketData.MultiExchangeWebSocketManager', '多交易所WebSocket管理器'),
  
  // 数据处理
  DataBackfillService: createSymbol('MarketData.DataBackfillService', '数据回填服务'),
  DataUpdateService: createSymbol('MarketData.DataUpdateService', '数据更新服务'),
  RealTimeDataStreamService: createSymbol('MarketData.RealTimeDataStreamService', '实时数据流服务'),
  
  // 监控服务
  APIHealthMonitor: createSymbol('MarketData.APIHealthMonitor', 'API健康监控'),
  RealTimeDataQualityMonitor: createSymbol('MarketData.RealTimeDataQualityMonitor', '实时数据质量监控'),
  TimestampConflictDetector: createSymbol('MarketData.TimestampConflictDetector', '时间戳冲突检测器'),
  
  // 数据清洗
  StreamDataCleaningEngine: createSymbol('MarketData.StreamDataCleaningEngine', '流式数据清洗引擎'),
  
  // 路由和分发
  IntelligentExchangeRouter: createSymbol('MarketData.IntelligentExchangeRouter', '智能交易所路由器'),
  HighPerformanceDataDistributionNetwork: createSymbol('MarketData.HighPerformanceDataDistributionNetwork', '高性能数据分发网络'),
  
  // 同步服务
  MarketDataSyncService: createSymbol('MarketData.MarketDataSyncService', '市场数据同步服务'),
  
  // 控制器
  MarketDataController: createSymbol('MarketData.Controller', '市场数据控制器'),
} as const;

/**
 * 趋势分析Symbol
 */
export const TREND_ANALYSIS_SYMBOLS = {
  // 应用服务
  TrendAnalysisApplicationService: createSymbol('TrendAnalysis.TrendAnalysisApplicationService', '趋势分析应用服务'),
  
  // 领域服务
  TrendAnalysisEngine: createSymbol('TrendAnalysis.TrendAnalysisEngine', '趋势分析引擎'),
  TrendPredictionService: createSymbol('TrendAnalysis.TrendPredictionService', '趋势预测服务'),
  
  // AI增强服务
  IntelligentTrendChangeDetector: createSymbol('TrendAnalysis.IntelligentTrendChangeDetector', '智能趋势变化检测器'),
  AIEnhancedPatternAnalyzer: createSymbol('TrendAnalysis.AIEnhancedPatternAnalyzer', 'AI增强模式分析器'),
  
  // 模式识别
  HarmonicPatternRecognizer: createSymbol('TrendAnalysis.HarmonicPatternRecognizer', '和谐模式识别器'),
  ElliottWaveRecognizer: createSymbol('TrendAnalysis.ElliottWaveRecognizer', '艾略特波浪识别器'),
  GannPatternRecognizer: createSymbol('TrendAnalysis.GannPatternRecognizer', '江恩模式识别器'),
  ComprehensivePatternManager: createSymbol('TrendAnalysis.ComprehensivePatternManager', '综合模式管理器'),
  
  // 协同服务
  IntelligentCollaborationService: createSymbol('TrendAnalysis.IntelligentCollaborationService', '智能协同服务'),
  
  // 基础设施
  TrendDataRepository: createSymbol('TrendAnalysis.TrendDataRepository', '趋势数据仓储'),
  TrendCacheService: createSymbol('TrendAnalysis.TrendCacheService', '趋势缓存服务'),
  TrendCollaborationService: createSymbol('TrendAnalysis.TrendCollaborationService', '趋势协同服务'),
  
  // 性能优化
  PerformanceOptimizer: createSymbol('TrendAnalysis.PerformanceOptimizer', '性能优化器'),
  EnhancedTrendAnalysisService: createSymbol('TrendAnalysis.EnhancedTrendAnalysisService', '增强趋势分析服务'),
  UnifiedErrorHandler: createSymbol('TrendAnalysis.UnifiedErrorHandler', '统一错误处理器'),
  
  // 技术指标
  TechnicalIndicatorCalculator: createSymbol('TrendAnalysis.TechnicalIndicatorCalculator', '技术指标计算器'),
  
  // 分析模块
  FundamentalAnalysisModule: createSymbol('TrendAnalysis.FundamentalAnalysisModule', '基本面分析模块'),
  SentimentAnalysisModule: createSymbol('TrendAnalysis.SentimentAnalysisModule', '情绪分析模块'),
  QuantitativeAnalysisModule: createSymbol('TrendAnalysis.QuantitativeAnalysisModule', '量化分析模块'),
  
  // 融合协调器
  FourDimensionFusionCoordinator: createSymbol('TrendAnalysis.FourDimensionFusionCoordinator', '四维度融合协调器'),
  
  // 信号融合
  SignalFusionAdapter: createSymbol('TrendAnalysis.SignalFusionAdapter', '信号融合适配器'),
  EnhancedSignalFusionAdapter: createSymbol('TrendAnalysis.EnhancedSignalFusionAdapter', '增强信号融合适配器'),
  
  // 仓储
  MultiTimeframeTrendAnalysisRepository: createSymbol('TrendAnalysis.MultiTimeframeTrendAnalysisRepository', '多时间框架趋势分析仓储'),
  TechnicalPatternAnalysisRepository: createSymbol('TrendAnalysis.TechnicalPatternAnalysisRepository', '技术模式分析仓储'),
  KeyLevelAnalysisRepository: createSymbol('TrendAnalysis.KeyLevelAnalysisRepository', '关键水平分析仓储'),
  
  // 数据质量监控
  DataSourceHealthMonitor: createSymbol('TrendAnalysis.DataSourceHealthMonitor', '数据源健康监控'),
  DataQualityValidator: createSymbol('TrendAnalysis.DataQualityValidator', '数据质量验证器'),
  
  // 控制器
  TrendAnalysisController: createSymbol('TrendAnalysis.Controller', '趋势分析控制器'),
} as const;

/**
 * Symbol验证工具
 */
export class SymbolValidator {
  /**
   * 验证Symbol是否已注册
   */
  static validate(key: string): boolean {
    return symbolRegistry.has(key);
  }

  /**
   * 验证多个Symbol
   */
  static validateMultiple(keys: string[]): { valid: string[]; invalid: string[] } {
    const valid: string[] = [];
    const invalid: string[] = [];

    for (const key of keys) {
      if (symbolRegistry.has(key)) {
        valid.push(key);
      } else {
        invalid.push(key);
      }
    }

    return { valid, invalid };
  }

  /**
   * 获取所有已注册的Symbol
   */
  static getAllRegistered(): string[] {
    return Array.from(symbolRegistry.getAll().keys());
  }

  /**
   * 检查Symbol冲突（检查数组中的重复项）
   */
  static checkConflicts(keys: string[]): string[] {
    const seen = new Set<string>();
    const conflicts: string[] = [];

    for (const key of keys) {
      if (seen.has(key)) {
        if (!conflicts.includes(key)) {
          conflicts.push(key);
        }
      } else {
        seen.add(key);
      }
    }

    return conflicts;
  }

  /**
   * 检查新Symbol是否与已注册Symbol冲突
   */
  static checkRegistrationConflicts(newKeys: string[]): string[] {
    const conflicts: string[] = [];

    for (const key of newKeys) {
      if (symbolRegistry.has(key)) {
        conflicts.push(key);
      }
    }

    return conflicts;
  }
}
