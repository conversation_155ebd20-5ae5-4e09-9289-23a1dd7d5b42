import { ContainerModule, interfaces } from 'inversify';
import { TYPES } from '../types';
import { ILogger } from '../../logging/logger.interface';
import { PrismaClient } from '@prisma/client';

/**
 * AI推理容器模块 - 重新启用核心绑定
 * 修复模块耦合度违规：将AI推理相关的DI绑定分离到专门模块
 */
export const aiReasoningContainerModule = new ContainerModule(async (bind: interfaces.Bind) => {
  console.log('🔧 配置AI推理服务绑定...');

  try {
    // 绑定AI推理应用服务
    const { AIReasoningApplicationService } = await import('../../../../contexts/ai-reasoning/application/services/ai-reasoning-application-service');
    bind(TYPES.AIReasoning.AIReasoningApplicationService)
      .to(AIReasoningApplicationService)
      .inSingletonScope();

    console.log('✅ AIReasoningApplicationService 绑定成功');

    // 🚫 双层推理控制器已禁用 - 系统开发未完成
    console.log('⚠️ DualLayerReasoningController 已禁用 - 系统开发未完成');

    // 🔥 PureAITradingSignalGenerator 已移除 - 使用正确的SignalGenerationApplicationService
    console.warn('⚠️ PureAITradingSignalGenerator 绑定失败，使用降级实现: SignalFusionAdapter已删除，请使用SignalGenerationApplicationService');

    // 绑定纯净AI趋势分析引擎
    try {
      const { PureAITrendAnalysisEngine } = await import('../../../../contexts/trend-analysis/infrastructure/services/pure-ai-trend-analysis-engine');
      bind(TYPES.AIReasoning.PureAITrendAnalysisEngine).to(PureAITrendAnalysisEngine).inSingletonScope();
      console.log('✅ PureAITrendAnalysisEngine 绑定成功');
    } catch (error) {
      console.warn('⚠️ PureAITrendAnalysisEngine 绑定失败，使用降级实现:', error);
    }

    // 绑定纯净AI风险分析引擎
    try {
      const { PureAIRiskAnalysisEngine } = await import('../../../../contexts/risk-management/infrastructure/services/pure-ai-risk-analysis-engine');
      bind(TYPES.AIReasoning.PureAIRiskAnalysisEngine).to(PureAIRiskAnalysisEngine).inSingletonScope();
      console.log('✅ PureAIRiskAnalysisEngine 绑定成功');
    } catch (error) {
      console.warn('⚠️ PureAIRiskAnalysisEngine 绑定失败，使用降级实现:', error);
    }

    // 绑定纯净AI分析器 - 使用真实实现
    try {
      const { PureAIAnalyzer } = await import('../../../../contexts/ai-reasoning/infrastructure/services/pure-ai-analyzer');
      bind(TYPES.AIReasoning.PureAIAnalyzer).to(PureAIAnalyzer).inSingletonScope();
      console.log('✅ PureAIAnalyzer 真实实现绑定成功');
    } catch (error) {
      console.warn('⚠️ PureAIAnalyzer 真实实现绑定失败，使用降级实现:', error);
      // 降级到动态值绑定
      bind(TYPES.AIReasoning.PureAIAnalyzer).toDynamicValue(() => ({
        async performInitialAnalysis(request: any) {
          return {
            tradingSignals: { signal: 'HOLD', confidence: 0.7 },
            riskAssessment: { riskLevel: 'MEDIUM', riskScore: 0.5 },
            trendAnalysis: { trend: 'NEUTRAL', strength: 0.6 }
          };
        }
      })).inSingletonScope();
    }

    // 绑定学习分析引擎 - 使用真实实现
    try {
      const { LearningAnalysisEngine } = await import('../../../../contexts/ai-reasoning/infrastructure/services/learning-analysis-engine');
      bind(TYPES.AIReasoning.LearningAnalysisEngine).to(LearningAnalysisEngine).inSingletonScope();
      console.log('✅ LearningAnalysisEngine 真实实现绑定成功');
    } catch (error) {
      console.warn('⚠️ LearningAnalysisEngine 真实实现绑定失败，使用降级实现:', error);
      // 降级到动态值绑定
      bind(TYPES.AIReasoning.LearningAnalysisEngine).toDynamicValue(() => ({
        async enhanceAnalysis(initialAnalysis: any, marketContext: any) {
          return {
            ...initialAnalysis,
            enhanced: true,
            learningInsights: ['基于历史数据的增强分析'],
            confidence: Math.min(1.0, (initialAnalysis.confidence || 0.7) + 0.1)
          };
        }
      })).inSingletonScope();
    }

    console.log('✅ AI推理核心服务绑定成功');

    // 绑定简化的依赖服务以避免循环依赖
    // 推理链 - 简化实现
    bind(TYPES.AIReasoning.ReasoningChain).toDynamicValue(() => ({
      async executeReasoning(query: any) {
        return {
          subQueries: ['简化推理'],
          evidence: { data: '模拟证据' },
          reasoningSteps: ['步骤1'],
          conclusion: '简化结论',
          confidence: 0.8
        };
      }
    })).inSingletonScope();

    // 统一决策引擎 - 简化实现
    bind(TYPES.AIReasoning.UnifiedDecisionEngine).toDynamicValue(() => ({
      async makeDecision(request: any) {
        return {
          decision: '模拟决策',
          confidence: 0.8,
          reasoning: '简化推理过程'
        };
      }
    })).inSingletonScope();

    // 绑定LLM服务
    try {
      const { LLMService } = await import('../../../../contexts/ai-reasoning/infrastructure/services/llm-service');
      bind(TYPES.AIReasoning.LLMService).to(LLMService).inSingletonScope();
      console.log('✅ LLMService 绑定成功');
    } catch (error) {
      console.warn('⚠️ LLMService 绑定失败，使用降级实现:', error);
      // 降级到简化实现
      bind(TYPES.AIReasoning.LLMService).toDynamicValue(() => ({
        async generateResponse(request: any) {
          return {
            content: '这是一个降级的LLM响应',
            model: 'fallback',
            timestamp: new Date(),
            usage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 }
          };
        },
        async batchGenerateResponse(requests: any[]) {
          return requests.map(() => ({
            content: '这是一个降级的批量LLM响应',
            model: 'fallback',
            timestamp: new Date(),
            usage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 }
          }));
        },
        async isAvailable() {
          return true;
        },
        async getSupportedModels() {
          return ['fallback'];
        }
      })).inSingletonScope();
    }

    // 绑定真实的LLM提供者
    try {
      const { OpenAIProvider } = await import('../../../../contexts/ai-reasoning/infrastructure/llm-providers/openai-provider');
      bind(TYPES.AIReasoning.OpenAIProvider).to(OpenAIProvider).inSingletonScope();
      console.log('✅ OpenAIProvider 绑定成功');
    } catch (error) {
      console.warn('⚠️ OpenAIProvider 绑定失败，使用降级实现:', error);
    }

    try {
      const { AnthropicProvider } = await import('../../../../contexts/ai-reasoning/infrastructure/llm-providers/anthropic-provider');
      bind(TYPES.AIReasoning.AnthropicProvider).to(AnthropicProvider).inSingletonScope();
      console.log('✅ AnthropicProvider 绑定成功');
    } catch (error) {
      console.warn('⚠️ AnthropicProvider 绑定失败，使用降级实现:', error);
    }

    try {
      const { GeminiProvider } = await import('../../../../contexts/ai-reasoning/infrastructure/llm-providers/gemini-provider');
      bind(TYPES.AIReasoning.GeminiProvider).to(GeminiProvider).inSingletonScope();
      console.log('✅ GeminiProvider 绑定成功');
    } catch (error) {
      console.warn('⚠️ GeminiProvider 绑定失败，使用降级实现:', error);
    }

    // 绑定真实的LLM路由器
    try {
      const { IntelligentLLMRouter } = await import('../../../../contexts/ai-reasoning/infrastructure/llm-providers/llm-router');
      bind(TYPES.AIReasoning.LLMRouter).to(IntelligentLLMRouter).inSingletonScope();
      console.log('✅ IntelligentLLMRouter 绑定成功');
    } catch (error) {
      console.warn('⚠️ IntelligentLLMRouter 绑定失败，使用降级实现:', error);

      // 降级到简化实现
      bind(TYPES.AIReasoning.LLMRouter).toDynamicValue(() => ({
        async selectOptimalModel() {
          return 'gemini-2.0-flash'; // 默认模型
        },
        async getAvailableProviders() {
          return []; // 空提供者列表
        },
        registerProvider() {
          // 空实现
        }
      })).inSingletonScope();
    }

    // 绑定参数配置中心
    try {
      const { ParameterConfigCenter } = await import('../../../../contexts/ai-reasoning/infrastructure/services/parameter-config-center');
      bind(TYPES.AIReasoning.ParameterConfigCenter).to(ParameterConfigCenter).inSingletonScope();
      console.log('✅ ParameterConfigCenter 绑定成功');
    } catch (error) {
      console.warn('⚠️ ParameterConfigCenter 绑定失败，使用降级实现:', error);
    }

    // 绑定学习知识库
    try {
      const { LearningKnowledgeBase } = await import('../../../../contexts/ai-reasoning/infrastructure/services/learning-knowledge-base');
      bind(TYPES.AIReasoning.LearningKnowledgeBase).to(LearningKnowledgeBase).inSingletonScope();
      console.log('✅ LearningKnowledgeBase 绑定成功');
    } catch (error) {
      console.warn('⚠️ LearningKnowledgeBase 绑定失败，使用降级实现:', error);
    }

    // 绑定时间框架协调器
    try {
      const { TimeframeLearningCoordinator } = await import('../../../../contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator');
      bind(TYPES.AIReasoning.TimeframeCoordinator).to(TimeframeLearningCoordinator).inSingletonScope();
      console.log('✅ TimeframeCoordinator 绑定成功');
    } catch (error) {
      console.warn('⚠️ TimeframeCoordinator 绑定失败，使用降级实现:', error);
    }

    // 绑定多时间框架学习协调器
    try {
      const { MultiTimeframeLearningCoordinator } = await import('../../../../contexts/ai-reasoning/infrastructure/services/multi-timeframe-learning-coordinator');
      bind(TYPES.AIReasoning.MultiTimeframeLearningCoordinator).to(MultiTimeframeLearningCoordinator).inSingletonScope();
      console.log('✅ MultiTimeframeLearningCoordinator 绑定成功');
    } catch (error) {
      console.warn('⚠️ MultiTimeframeLearningCoordinator 绑定失败，使用降级实现:', error);
    }

    // 绑定统一学习系统相关服务
    try {
      const { UnifiedPredictionEngine } = await import('../../../../contexts/ai-reasoning/infrastructure/services/unified-prediction-engine');
      bind(TYPES.AIReasoning.UnifiedPredictionEngine).to(UnifiedPredictionEngine).inSingletonScope();
      console.log('✅ UnifiedPredictionEngine 绑定成功');
    } catch (error) {
      console.warn('⚠️ UnifiedPredictionEngine 绑定失败，使用降级实现:', error);
    }

    try {
      const { UnifiedLearningEngine } = await import('../../../../contexts/ai-reasoning/infrastructure/services/unified-learning-engine');
      bind(TYPES.AIReasoning.UnifiedLearningEngine).to(UnifiedLearningEngine).inSingletonScope();
      console.log('✅ UnifiedLearningEngine 绑定成功');
    } catch (error) {
      console.warn('⚠️ UnifiedLearningEngine 绑定失败，使用降级实现:', error);
    }

    try {
      const { MacroPredictionService } = await import('../../../../contexts/ai-reasoning/infrastructure/services/macro-prediction-service');
      bind(TYPES.AIReasoning.MacroPredictionService).to(MacroPredictionService).inSingletonScope();
      console.log('✅ MacroPredictionService 绑定成功');
    } catch (error) {
      console.warn('⚠️ MacroPredictionService 绑定失败，使用降级实现:', error);
    }

    // 🚫 统一学习系统启动器已禁用 - 系统开发未完成
    console.log('⚠️ UnifiedLearningSystemStarter 已禁用 - 系统开发未完成');

    console.log('✅ AI推理容器模块配置完成');
  } catch (error) {
    console.error('❌ AI推理容器模块配置失败:', error);
    throw error;
  }
});
