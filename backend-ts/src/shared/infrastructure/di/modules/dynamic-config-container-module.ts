/**
 * 动态配置管理容器模块
 * 配置动态配置相关的依赖注入绑定
 */

import { ContainerModule, interfaces } from 'inversify';
import { DYNAMIC_CONFIG_TYPES } from '../types/context-types';

export const DynamicConfigContainerModule = new ContainerModule((bind: interfaces.Bind) => {
  console.log('🔧 配置动态配置管理服务...');

  try {
    // 绑定动态配置管理器
    import('../../config/dynamic/dynamic-config-manager').then(({ DynamicConfigManagerImpl }) => {
      bind(DYNAMIC_CONFIG_TYPES.DynamicConfigManager)
        .to(DynamicConfigManagerImpl)
        .inSingletonScope();
      console.log('✅ DynamicConfigManager 绑定成功');
    }).catch(error => {
      console.error('❌ DynamicConfigManager 绑定失败:', error);
    });

    // 绑定实时配置推送服务
    import('../../config/dynamic/real-time-config-push-service').then(({ RealTimeConfigPushServiceImpl }) => {
      bind(DYNAMIC_CONFIG_TYPES.RealTimeConfigPushService)
        .to(RealTimeConfigPushServiceImpl)
        .inSingletonScope();
      console.log('✅ RealTimeConfigPushService 绑定成功');
    }).catch(error => {
      console.error('❌ RealTimeConfigPushService 绑定失败:', error);
    });

    // 绑定增强配置验证器
    import('../../config/dynamic/enhanced-config-validator').then(({ EnhancedConfigValidatorImpl }) => {
      bind(DYNAMIC_CONFIG_TYPES.EnhancedConfigValidator)
        .to(EnhancedConfigValidatorImpl)
        .inSingletonScope();
      console.log('✅ EnhancedConfigValidator 绑定成功');
    }).catch(error => {
      console.error('❌ EnhancedConfigValidator 绑定失败:', error);
    });

    // 绑定配置版本管理器
    import('../../config/dynamic/config-version-manager').then(({ ConfigVersionManagerImpl }) => {
      bind(DYNAMIC_CONFIG_TYPES.ConfigVersionManager)
        .to(ConfigVersionManagerImpl)
        .inSingletonScope();
      console.log('✅ ConfigVersionManager 绑定成功');
    }).catch(error => {
      console.error('❌ ConfigVersionManager 绑定失败:', error);
    });

    console.log('✅ 动态配置管理服务配置完成');

  } catch (error) {
    console.error('❌ 动态配置管理服务配置失败:', error);
  }
});
