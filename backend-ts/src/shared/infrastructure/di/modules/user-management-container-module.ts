import { ContainerModule, interfaces } from 'inversify';
import { PrismaClient } from '@prisma/client';
import { TYPES } from '../types';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { Environment } from '../../config/config-validation';
import { BaseContainerModule, ServiceBindingConfig, LifecycleScope } from '../base/base-container-module';
import { createStandardServiceFactory, createServiceWithOptionalDeps } from '../base/dependency-resolution-strategy';

/**
 * 用户管理容器模块
 * 使用统一的DI配置基础架构，消除重复的绑定模式
 */
class UserManagementContainerModule extends BaseContainerModule {
  getModuleName(): string {
    return '用户管理模块';
  }

  createModule(): ContainerModule {
    return new ContainerModule(async (bind: interfaces.Bind) => {
      await this.withAsyncErrorHandling(
        () => this.configureUserManagementBindings(bind),
        '用户管理服务绑定配置'
      );
    });
  }

  private async configureUserManagementBindings(bind: interfaces.Bind): Promise<void> {
    this.logger.info('🔧 配置用户管理服务绑定...');

    // 配置服务绑定
    const serviceConfigs: ServiceBindingConfig[] = [
      // 用户仓储
      {
        identifier: TYPES.UserManagement.UserRepository,
        importPath: '../../../../contexts/user-management/infrastructure/repositories/UnifiedUserRepository',
        className: 'UnifiedUserRepository',
        scope: LifecycleScope.SINGLETON
      },

      // 安全事件仓储
      {
        identifier: TYPES.UserManagement.SecurityEventRepository,
        importPath: '../../../../contexts/user-management/infrastructure/repositories/PrismaSecurityEventRepository',
        className: 'PrismaSecurityEventRepository',
        dependencies: [
          TYPES.Database,
          TYPES.Logger,
          TYPES.Shared.QueryManager,
          TYPES.Shared.RepositoryBaseService
        ],
        scope: LifecycleScope.SINGLETON
      },

      // 密码服务
      {
        identifier: TYPES.UserManagement.PasswordService,
        importPath: '../../../../contexts/user-management/infrastructure/services/PasswordService',
        className: 'PasswordService',
        dependencies: [TYPES.Logger],
        scope: LifecycleScope.SINGLETON
      },

      // JWT服务
      {
        identifier: TYPES.UserManagement.JwtService,
        importPath: '../../../../contexts/user-management/infrastructure/services/JwtService',
        className: 'JwtService',
        dependencies: [TYPES.Environment, TYPES.Logger],
        scope: LifecycleScope.SINGLETON
      },

      // 邀请码服务
      {
        identifier: TYPES.UserManagement.InvitationService,
        importPath: '../../../../contexts/user-management/infrastructure/services/InvitationService',
        className: 'InvitationService',
        dependencies: [TYPES.Database, TYPES.Logger],
        scope: LifecycleScope.SINGLETON
      }
    ];

    // 批量绑定基础服务
    await this.bindServicesInBatch(bind, serviceConfigs);

    // 配置复合服务（有复杂依赖关系的服务）
    await this.configureCompositeServices(bind);

    this.logger.info('✅ 用户管理服务绑定配置完成');
  }
  private async configureCompositeServices(bind: interfaces.Bind): Promise<void> {
    // 认证服务 - 使用异步绑定避免分层架构违规
    await this.bindAsyncService(
      bind,
      TYPES.UserManagement.AuthenticationService,
      '../../../../contexts/user-management/infrastructure/services/AuthenticationService',
      'AuthenticationService',
      [
        TYPES.UserManagement.UserRepository,
        TYPES.UserManagement.PasswordService,
        TYPES.UserManagement.JwtService,
        TYPES.UserManagement.InvitationService,
        TYPES.Database,
        TYPES.Logger
      ],
      'singleton'
    );

    // 授权服务 - 使用异步绑定避免分层架构违规
    await this.bindAsyncService(
      bind,
      TYPES.UserManagement.AuthorizationService,
      '../../../../contexts/user-management/infrastructure/services/AuthorizationService',
      'AuthorizationService',
      [TYPES.UserManagement.UserRepository, TYPES.Logger],
      'singleton'
    );

    // API密钥仓储 - 使用异步绑定避免分层架构违规
    await this.bindAsyncService(
      bind,
      TYPES.UserManagement.ApiKeyRepository,
      '../../../../contexts/user-management/infrastructure/repositories/ApiKeyRepository',
      'ApiKeyRepository',
      [TYPES.Database, TYPES.Logger],
      'singleton'
    );

    // API密钥服务 - 使用异步绑定避免分层架构违规
    await this.bindAsyncService(
      bind,
      TYPES.UserManagement.ApiKeyService,
      '../../../../contexts/user-management/application/services/ApiKeyService',
      'ApiKeyService',
      [TYPES.UserManagement.ApiKeyRepository, TYPES.Logger],
      'singleton'
    );

    // API密钥控制器 - 使用异步绑定避免分层架构违规
    await this.bindAsyncService(
      bind,
      TYPES.UserManagement.ApiKeyController,
      '../../../../contexts/user-management/presentation/http/ApiKeyController',
      'ApiKeyController',
      [TYPES.UserManagement.ApiKeyService, TYPES.Logger],
      'transient'
    );

    // 用户控制器 - 使用异步绑定避免分层架构违规
    await this.bindAsyncService(
      bind,
      TYPES.UserManagement.UserController,
      '../../../../contexts/user-management/presentation/http/controllers/UserController',
      'UserController',
      [TYPES.UserManagement.UserManagementApplicationService, TYPES.Logger],
      'transient'
    );

    // 认证控制器已移至API层统一管理，不再在此绑定
    // 参考: docs/系统调查/api-layer-investigation-report.md - 控制器重复问题已解决

    // 用户管理应用服务 - 使用异步绑定避免分层架构违规
    await this.bindAsyncService(
      bind,
      TYPES.UserManagement.UserManagementApplicationService,
      '../../../../contexts/user-management/application/services/UserManagementApplicationService',
      'UserManagementApplicationService',
      [
        TYPES.UserManagement.AuthenticationService,
        TYPES.UserManagement.AuthorizationService,
        TYPES.UserManagement.UserRepository,
        TYPES.Logger
      ],
      'singleton'
    );
  }

  private async bindServicesInBatch(bind: interfaces.Bind, configs: ServiceBindingConfig[]): Promise<void> {
    for (const config of configs) {
      await this.withAsyncErrorHandling(
        async () => {
          if (config.importPath && config.className) {
            await this.bindAsyncService(
              bind,
              config.identifier,
              config.importPath,
              config.className,
              config.dependencies,
              config.scope
            );
          } else if (config.factory) {
            this.bindDynamicValue(bind, config.identifier, config.factory, config.scope);
          } else if (config.serviceClass) {
            this.bindService(bind, config.identifier, config.serviceClass, config.scope);
          } else if (config.constantValue !== undefined) {
            this.bindConstantValue(bind, config.identifier, config.constantValue);
          }
        },
        `绑定服务: ${String(config.identifier)}`
      );
    }
  }
}

// 导出容器模块实例
const userManagementModule = new UserManagementContainerModule();
export const userManagementContainerModule = userManagementModule.createModule();


