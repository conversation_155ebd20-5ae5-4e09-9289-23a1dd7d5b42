import { ContainerModule, interfaces } from 'inversify';
import { TYPES } from '../types';

/**
 * API控制器容器模块
 * 修复模块耦合度违规：将API控制器相关的DI绑定分离到专门模块
 */
export const apiContainerModule = new ContainerModule(async (bind: interfaces.Bind) => {
  // AI分析统计控制器
  const { AIAnalyticsController } = await import('../../../../api/controllers/ai-analytics-controller');
  bind(AIAnalyticsController).toSelf();

  // 模型偏好控制器
  const { ModelPreferenceController } = await import('../../../../contexts/user-config/presentation/http/ModelPreferenceController');
  bind(ModelPreferenceController).toSelf();

  // 统计控制器
  const { StatisticsController } = await import('../../../../contexts/user-config/presentation/http/StatisticsController');
  bind(StatisticsController).toSelf();

  // Phase 4 新增控制器
  const { StrategySyncController } = await import('../../../../api/controllers/strategy-sync-controller');
  bind(TYPES.Api.StrategySyncController).to(StrategySyncController);

  const { DualTrackMonitoringController } = await import('../../../../api/controllers/dual-track-monitoring-controller');
  bind(TYPES.Api.DualTrackMonitoringController).to(DualTrackMonitoringController);

  // 生产信号控制器（已合并增强信号功能）
  const { ProductionSignalController } = await import('../../../../api/controllers/production-signal-controller');
  bind(TYPES.Api.ProductionSignalController).to(ProductionSignalController);

  // 向后兼容：EnhancedSignalController 别名指向 ProductionSignalController
  bind(TYPES.Api.EnhancedSignalController).to(ProductionSignalController);

  // 风险评估控制器
  const { RiskAssessmentController } = await import('../../../../api/controllers/risk-assessment-controller');
  bind(TYPES.Api.RiskAssessmentController).to(RiskAssessmentController);

  // 趋势分析控制器
  const { TrendAnalysisController } = await import('../../../../api/controllers/trend-analysis-controller');
  bind(TYPES.Api.TrendAnalysisController).to(TrendAnalysisController);

  // 交易执行控制器
  const { TradingExecutionController } = await import('../../../../api/controllers/trading-execution-controller');
  bind(TYPES.Api.TradingExecutionController).to(TradingExecutionController);

  // 交易信号控制器
  // const { TradingSignalsController } = await import('../../../../contexts/trading-signals/presentation/controllers/trading-signals.controller');
  // bind(TYPES.Api.TradingSignalsController).to(TradingSignalsController); // 文件不存在，暂时注释

  // 管理员路由控制器
  const { AdminController } = await import('../../../../api/controllers/admin-controller');
  bind(TYPES.Api.AdminController).to(AdminController);

  // 认证路由控制器
  const { AuthController } = await import('../../../../api/controllers/auth-controller');
  bind(TYPES.Api.AuthController).to(AuthController);

  // 统一健康路由控制器
  try {
    const { UnifiedHealthController } = await import('../../../../api/controllers/unified-health-controller');
    bind(TYPES.Api.UnifiedHealthController).to(UnifiedHealthController);
  } catch (error) {
    console.warn('统一健康控制器加载失败，可能尚未实现:', error);
  }
});
