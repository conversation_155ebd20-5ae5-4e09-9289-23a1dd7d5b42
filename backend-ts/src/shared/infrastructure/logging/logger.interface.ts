/**
 * 统一Logger接口定义
 * 解决Winston <PERSON>gger类型兼容问题
 */

import { <PERSON>gger as <PERSON><PERSON><PERSON><PERSON>, LeveledLogMethod, LogMethod } from 'winston';
import { IBasicLogger } from './interfaces/basic-logger.interface';
import { ILogger<PERSON>evelChecker } from './interfaces/logger-level-checker.interface';
import { ILoggerFactory } from './interfaces/logger-factory.interface';

// 重新导出接口
export { IBasicLogger } from './interfaces/basic-logger.interface';
export { ILoggerLevelChecker } from './interfaces/logger-level-checker.interface';
export { ILoggerFactory } from './interfaces/logger-factory.interface';

/**
 * 日志配置接口
 */
export interface ILoggerConfig {
  silent: boolean;
}

/**
 * 完整的日志接口 - 组合所有子接口
 */
export interface ILogger extends IBasicLogger, ILoggerLevelChecker, ILoggerConfig, ILoggerFactory {}

/**
 * <PERSON> Logger适配器
 * 将<PERSON>gger包装为我们的ILogger接口
 */
export class WinstonLoggerAdapter implements ILogger {
  constructor(private readonly winstonLogger: WinstonLogger) {}

  info(message: string, ...meta: any[]): void {
    this.winstonLogger.info(message, ...meta);
  }

  warn(message: string, ...meta: any[]): void {
    this.winstonLogger.warn(message, ...meta);
  }

  error(message: string, ...meta: any[]): void {
    this.winstonLogger.error(message, ...meta);
  }

  debug(message: string, ...meta: any[]): void {
    this.winstonLogger.debug(message, ...meta);
  }

  log(level: string, message: string, ...meta: any[]): void {
    this.winstonLogger.log(level, message, ...meta);
  }

  get silent(): boolean { return this.winstonLogger.silent; }

  isLevelEnabled(level: string): boolean { return this.winstonLogger.isLevelEnabled(level); }
  isDebugEnabled(): boolean { return this.winstonLogger.isDebugEnabled(); }
  isInfoEnabled(): boolean { return this.winstonLogger.isInfoEnabled(); }
  isWarnEnabled(): boolean { return this.winstonLogger.isWarnEnabled(); }
  isErrorEnabled(): boolean { return this.winstonLogger.isErrorEnabled(); }

  child(options?: object): ILogger {
    return new WinstonLoggerAdapter(this.winstonLogger.child(options));
  }
}

/**
 * 简单Logger实现
 * 用于测试和开发环境
 */
export class SimpleLogger implements ILogger {
  constructor(protected readonly name: string) {}

  info(message: string, ...meta: any[]): void {
    console.log(`ℹ️  [${this.name}] ${message}`, ...meta);
  }

  warn(message: string, ...meta: any[]): void {
    console.log(`⚠️  [${this.name}] ${message}`, ...meta);
  }

  error(message: string, ...meta: any[]): void {
    console.log(`❌ [${this.name}] ${message}`, ...meta);
  }

  debug(message: string, ...meta: any[]): void {
    console.log(`🔍 [${this.name}] ${message}`, ...meta);
  }

  log(level: string, message: string, ...meta: any[]): void {
    switch (level) {
      case 'info': this.info(message, ...meta); break;
      case 'warn': this.warn(message, ...meta); break;
      case 'error': this.error(message, ...meta); break;
      case 'debug': this.debug(message, ...meta); break;
      default: this.info(message, ...meta);
    }
  }

  get silent(): boolean { return false; }

  isLevelEnabled(level: string): boolean { return true; }
  isDebugEnabled(): boolean { return true; }
  isInfoEnabled(): boolean { return true; }
  isWarnEnabled(): boolean { return true; }
  isErrorEnabled(): boolean { return true; }

  child(options?: object): ILogger {
    // For SimpleLogger, child can return a new SimpleLogger instance or just 'this'
    // depending on whether child-specific logging is needed. For now, return 'this'.
    return this;
  }
}

/**
 * BasicLogger到ILogger的适配器
 * 用于解决类型不匹配问题
 */
export class BasicLoggerAdapter implements ILogger {
  constructor(private readonly basicLogger: IBasicLogger) {}

  info(message: string, ...meta: any[]): void {
    this.basicLogger.info(message, ...meta);
  }

  warn(message: string, ...meta: any[]): void {
    this.basicLogger.warn(message, ...meta);
  }

  error(message: string, ...meta: any[]): void {
    this.basicLogger.error(message, ...meta);
  }

  debug(message: string, ...meta: any[]): void {
    this.basicLogger.debug(message, ...meta);
  }

  log(level: string, message: string, ...meta: any[]): void {
    this.basicLogger.log(level, message, ...meta);
  }

  get silent(): boolean { return false; }

  isLevelEnabled(level: string): boolean { return true; }
  isDebugEnabled(): boolean { return true; }
  isInfoEnabled(): boolean { return true; }
  isWarnEnabled(): boolean { return true; }
  isErrorEnabled(): boolean { return true; }

  child(options?: object): ILogger {
    return this;
  }
}

/**
 * 工具函数：将IBasicLogger转换为ILogger
 */
export function adaptBasicLogger(basicLogger: IBasicLogger): ILogger {
  return new BasicLoggerAdapter(basicLogger);
}
