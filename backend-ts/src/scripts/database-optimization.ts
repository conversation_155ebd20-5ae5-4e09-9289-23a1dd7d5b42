#!/usr/bin/env tsx

import { getGlobalPrismaClient } from '../shared/infrastructure/database/database';
const prisma = getGlobalPrismaClient();

/**
 * 数据库优化脚本
 * 检查和创建必要的索引，优化查询性能
 */
class DatabaseOptimizer {
  
  /**
   * 运行数据库优化
   */
  async runOptimization(): Promise<void> {
    console.log('🚀 开始数据库优化...\n');
    
    try {
      // 1. 检查当前索引状态
      await this.checkCurrentIndexes();
      
      // 2. 创建性能关键索引
      await this.createPerformanceIndexes();
      
      // 3. 分析查询性能
      await this.analyzeQueryPerformance();
      
      // 4. 优化数据库配置
      await this.optimizeDatabaseSettings();
      
      console.log('\n✅ 数据库优化完成！');
      
    } catch (error) {
      console.error('❌ 数据库优化失败:', error);
      throw error;
    }
  }
  
  /**
   * 检查当前索引状态
   */
  private async checkCurrentIndexes(): Promise<void> {
    console.log('🔍 检查当前索引状态...');
    
    try {
      // 查询PostgreSQL索引信息
      const indexes = await prisma.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          indexname,
          indexdef
        FROM pg_indexes 
        WHERE schemaname = 'public'
        ORDER BY tablename, indexname;
      ` as any[];
      
      console.log(`   发现 ${indexes.length} 个索引`);
      
      // 按表分组显示索引
      const indexesByTable = indexes.reduce((acc: any, index: any) => {
        if (!acc[index.tablename]) {
          acc[index.tablename] = [];
        }
        acc[index.tablename].push(index.indexname);
        return acc;
      }, {});
      
      for (const [table, tableIndexes] of Object.entries(indexesByTable)) {
        console.log(`   ${table}: ${(tableIndexes as string[]).length} 个索引`);
      }
      
    } catch (error) {
      console.warn('⚠️ 无法检查索引状态:', error);
    }
  }
  
  /**
   * 创建性能关键索引
   */
  private async createPerformanceIndexes(): Promise<void> {
    console.log('\n🔧 创建性能关键索引...');
    
    const indexDefinitions = [
      // 历史数据表索引
      {
        name: 'idx_historical_data_symbol_timeframe_timestamp',
        table: 'HistoricalData',
        sql: `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_historical_data_symbol_timeframe_timestamp 
              ON "HistoricalData" ("symbolId", "timeframe", "timestamp" DESC);`
      },
      
      // 交易信号表索引
      {
        name: 'idx_trading_signals_symbol_type_timestamp',
        table: 'TradingSignals',
        sql: `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trading_signals_symbol_type_timestamp 
              ON "TradingSignals" ("symbolId", "signalTypeV2", "timestamp" DESC);`
      },
      
      // 交易信号置信度索引
      {
        name: 'idx_trading_signals_confidence',
        table: 'TradingSignals',
        sql: `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trading_signals_confidence 
              ON "TradingSignals" ("confidence") WHERE "confidence" >= 0.7;`
      },
      
      // 符号表索引
      {
        name: 'idx_symbols_symbol_exchange',
        table: 'Symbols',
        sql: `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_symbols_symbol_exchange 
              ON "Symbols" ("symbol", "exchange");`
      },
      
      // AI推理链索引
      {
        name: 'idx_ai_reasoning_chains_signal_timestamp',
        table: 'AiReasoningChains',
        sql: `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_reasoning_chains_signal_timestamp 
              ON "AiReasoningChains" ("signalId", "timestamp" DESC);`
      }
    ];
    
    for (const indexDef of indexDefinitions) {
      try {
        console.log(`   创建索引: ${indexDef.name}`);
        await prisma.$executeRawUnsafe(indexDef.sql);
        console.log(`   ✅ ${indexDef.name} 创建成功`);
      } catch (error: any) {
        if (error.message.includes('already exists')) {
          console.log(`   ℹ️ ${indexDef.name} 已存在`);
        } else {
          console.error(`   ❌ ${indexDef.name} 创建失败:`, error.message);
        }
      }
    }
  }
  
  /**
   * 分析查询性能
   */
  private async analyzeQueryPerformance(): Promise<void> {
    console.log('\n📊 分析查询性能...');
    
    try {
      // 测试关键查询的性能
      const testQueries = [
        {
          name: '获取最新历史数据',
          query: async () => {
            const start = Date.now();
            await prisma.historicalData.findMany({
              take: 100,
              orderBy: { timestamp: 'desc' }
            });
            return Date.now() - start;
          }
        },
        {
          name: '获取高置信度交易信号',
          query: async () => {
            const start = Date.now();
            await prisma.tradingSignals.findMany({
              where: { confidence: { gte: 0.7 } },
              take: 50,
              orderBy: { timestamp: 'desc' }
            });
            return Date.now() - start;
          }
        },
        {
          name: '获取符号信息',
          query: async () => {
            const start = Date.now();
            await prisma.symbols.findMany({
              take: 20
            });
            return Date.now() - start;
          }
        }
      ];
      
      for (const test of testQueries) {
        try {
          const duration = await test.query();
          const status = duration < 100 ? '✅' : duration < 500 ? '⚠️' : '❌';
          console.log(`   ${status} ${test.name}: ${duration}ms`);
        } catch (error) {
          console.log(`   ❌ ${test.name}: 查询失败`);
        }
      }
      
    } catch (error) {
      console.warn('⚠️ 查询性能分析失败:', error);
    }
  }
  
  /**
   * 优化数据库配置
   */
  private async optimizeDatabaseSettings(): Promise<void> {
    console.log('\n⚙️ 优化数据库配置...');
    
    try {
      // 设置PostgreSQL性能参数
      const optimizationQueries = [
        'SET shared_preload_libraries = \'pg_stat_statements\';',
        'SET max_connections = 100;',
        'SET shared_buffers = \'256MB\';',
        'SET effective_cache_size = \'1GB\';',
        'SET maintenance_work_mem = \'64MB\';',
        'SET checkpoint_completion_target = 0.9;',
        'SET wal_buffers = \'16MB\';',
        'SET default_statistics_target = 100;'
      ];
      
      for (const query of optimizationQueries) {
        try {
          await prisma.$executeRawUnsafe(query);
          console.log(`   ✅ 应用配置: ${query.split('=')[0].trim()}`);
        } catch (error: any) {
          // 某些配置需要重启数据库，这里只是尝试
          console.log(`   ℹ️ 配置需要重启: ${query.split('=')[0].trim()}`);
        }
      }
      
    } catch (error) {
      console.warn('⚠️ 数据库配置优化失败:', error);
    }
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const optimizer = new DatabaseOptimizer();
  
  try {
    await optimizer.runOptimization();
    process.exit(0);
  } catch (error) {
    console.error('数据库优化失败:', error);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}
