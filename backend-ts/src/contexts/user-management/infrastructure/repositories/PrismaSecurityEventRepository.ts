/**
 * <PERSON><PERSON>a 安全事件仓储实现
 */

import { injectable, inject, LazyServiceIdentifer } from 'inversify';
import { PrismaClient } from '@prisma/client';
import { ISecurityEventRepository } from '../../domain/repositories/ISecurityEventRepository';
import { SecurityEvent } from '../../domain/entities/SecurityEvent';
import { UserId } from '../../domain/value-objects/UserId';
import { SecurityEventId } from '../../domain/value-objects/SecurityEventId';
import { SecurityEventType } from '../../domain/value-objects/SecurityEventType';
import { SecurityEventCategory } from '../../domain/value-objects/SecurityEventCategory';
import { SecuritySeverity } from '../../domain/value-objects/SecuritySeverity';
import { ThreatScore } from '../../domain/value-objects/ThreatScore';
import { RiskLevel } from '../../domain/value-objects/RiskLevel';

import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { IRepositoryBaseService } from '../../../../shared/domain/repositories/repository-base.interface';
import { IBaseRepository } from '../../../../shared/domain/repositories/base-repository.interface';
import { ILogger } from '../../../../shared/infrastructure/logging/logger.interface';
import { QueryManager } from '../../../../shared/infrastructure/database/query-manager';
import { UniqueEntityId } from '../../../../shared/domain/entities/base-entity';


@injectable()
export class PrismaSecurityEventRepository implements ISecurityEventRepository, IBaseRepository<SecurityEvent> {
  private readonly baseService: IRepositoryBaseService<SecurityEvent>;

  constructor(
    @inject(new LazyServiceIdentifer(() => TYPES.Database)) private readonly prisma: PrismaClient,
    @inject(new LazyServiceIdentifer(() => TYPES.Logger)) private readonly logger: ILogger,
    @inject(new LazyServiceIdentifer(() => TYPES.Shared.QueryManager)) private readonly queryManager: QueryManager,
    @inject(new LazyServiceIdentifer(() => TYPES.Shared.RepositoryBaseService)) baseService: IRepositoryBaseService<SecurityEvent>
  ) {
    this.baseService = baseService;
  }

  // IBaseRepository接口的基本方法已在下面的具体方法中实现

  // 实现IBaseRepository接口 - findById重载
  async findById(id: UniqueEntityId): Promise<SecurityEvent | null>;
  async findById(eventId: SecurityEventId): Promise<SecurityEvent | null>;
  async findById(id: UniqueEntityId | SecurityEventId): Promise<SecurityEvent | null> {
    const eventId = id instanceof SecurityEventId ? id : SecurityEventId.fromString(id.toString());
    return this.findByEventId(eventId);
  }

  // 实现IBaseRepository接口 - findAll
  async findAll(): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findAll', async () => {
      const records = await this.prisma.securityEvents.findMany();
      return records.map(record => this.toDomain(record));
    });
  }

  // 实现IBaseRepository接口 - update
  async update(entity: SecurityEvent): Promise<void> {
    await this.save(entity); // 使用现有的save方法，它已经是upsert
  }

  // 实现IBaseRepository接口 - delete重载
  async delete(id: UniqueEntityId): Promise<void>;
  async delete(eventId: SecurityEventId): Promise<void>;
  async delete(id: UniqueEntityId | SecurityEventId): Promise<void> {
    const eventId = id instanceof SecurityEventId ? id : SecurityEventId.fromString(id.toString());
    await this.deleteSecurityEvent(eventId);
  }

  // 实现IBaseRepository接口 - exists
  async exists(id: UniqueEntityId): Promise<boolean> {
    return this.baseService.executeWithMonitoring('exists', async () => {
      const eventId = SecurityEventId.fromString(id.toString());
      const record = await this.prisma.securityEvents.findUnique({
        where: { id: eventId.getValue() }
      });
      return record !== null;
    });
  }

  /**
   * 获取表名
   */
  protected getTableName(): string {
    return 'securityEvents';
  }

  /**
   * 获取Prisma模型
   */
  protected getModel(): any {
    return this.prisma.securityEvents;
  }

  /**
   * 将数据库记录转换为领域实体
   */
  protected toDomain(record: any): SecurityEvent {
    const props = {
      id: SecurityEventId.fromString(record.id),
      userId: record.userId ? UserId.create(record.userId) : undefined,
      eventType: SecurityEventType.fromString(record.eventType),
      eventCategory: SecurityEventCategory.fromString(record.eventCategory),
      severity: SecuritySeverity.fromString(record.severity),
      description: record.description,
      ipAddress: record.ipAddress,
      userAgent: record.userAgent,
      deviceFingerprint: record.deviceFingerprint,
      geolocation: record.geolocation,
      threatScore: record.threatScore ? ThreatScore.create(record.threatScore) : undefined,
      riskLevel: record.riskLevel ? RiskLevel.fromString(record.riskLevel) : undefined,
      detectedThreats: record.detectedThreats || [],
      responseAction: record.responseAction,
      isResolved: record.isResolved,
      resolvedAt: record.resolvedAt,
      createdAt: record.createdAt,
      metadata: record.metadata
    };

    return SecurityEvent.fromPersistence(props);
  }

  /**
   * 将领域实体转换为数据库记录
   */
  protected toPersistence(entity: SecurityEvent): any {
    return entity.toPersistence();
  }

  /**
   * 保存安全事件
   */
  public async save(event: SecurityEvent): Promise<void> {
    return this.baseService.executeWithMonitoring('save', async () => {
      const data = event.toPersistence();

      await this.prisma.securityEvents.upsert({
        where: { id: data.id },
        update: {
          description: data.description,
          threatScore: data.threatScore,
          riskLevel: data.riskLevel,
          detectedThreats: data.detectedThreats,
          responseAction: data.responseAction,
          isResolved: data.isResolved,
          resolvedAt: data.resolvedAt
        },
        create: {
          id: data.id,
          userId: data.userId,
          eventType: data.eventType,
          eventCategory: data.eventCategory,
          severity: data.severity,
          description: data.description,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          deviceFingerprint: data.deviceFingerprint,
          geolocation: data.geolocation,
          threatScore: data.threatScore,
          riskLevel: data.riskLevel,
          detectedThreats: data.detectedThreats,
          responseAction: data.responseAction,
          isResolved: data.isResolved,
          resolvedAt: data.resolvedAt,
          createdAt: data.createdAt
        }
      });
    });
  }

  /**
   * 根据ID查找安全事件
   */
  public async findByEventId(eventId: SecurityEventId): Promise<SecurityEvent | null> {
    return this.baseService.executeWithMonitoring('findByEventId', async () => {
      const record = await this.prisma.securityEvents.findUnique({
        where: { id: eventId.getValue() }
      });

      if (!record) {
        return null;
      }

      return this.toDomain(record);
    });
  }

  /**
   * 根据用户ID查找安全事件
   */
  public async findByUserId(
    userId: UserId,
    limit: number = 50,
    offset: number = 0
  ): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findByUserId', async () => {
      const records = await this.prisma.securityEvents.findMany({
        where: { userId: userId.getValue() },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      });

      return records.map(record => this.toDomain(record));
    });
  }

  /**
   * 根据事件类型查找安全事件
   */
  public async findByEventType(
    eventType: SecurityEventType,
    limit: number = 50,
    offset: number = 0
  ): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findByEventType', async () => {
      const records = await this.prisma.securityEvents.findMany({
        where: { eventType: eventType.getValue() as any },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      });

      return records.map(record => this.toDomain(record));
    });
  }

  /**
   * 根据严重性级别查找安全事件
   */
  public async findBySeverity(
    severity: SecuritySeverity,
    limit: number = 50,
    offset: number = 0
  ): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findBySeverity', async () => {
      const records = await this.prisma.securityEvents.findMany({
        where: { severity: severity.getValue() },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      });

      return records.map(record => this.toDomain(record));
    });
  }

  /**
   * 查找未解决的安全事件
   */
  public async findUnresolved(
    limit: number = 50,
    offset: number = 0
  ): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findUnresolved', async () => {
      const records = await this.prisma.securityEvents.findMany({
        where: { isResolved: false },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      });

      return records.map(record => this.toDomain(record));
    });
  }

  /**
   * 查找高风险安全事件
   */
  public async findHighRiskEvents(
    limit: number = 50,
    offset: number = 0
  ): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findHighRiskEvents', async () => {
      const records = await this.prisma.securityEvents.findMany({
        where: {
          OR: [
            { severity: 'HIGH' },
            { severity: 'CRITICAL' },
            { riskLevel: 'HIGH' },
            { riskLevel: 'CRITICAL' }
          ]
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      });

      return records.map(record => this.toDomain(record));
    });
  }

  /**
   * 在时间范围内查找安全事件
   */
  public async findEventsInTimeRange(
    startTime: Date,
    endTime: Date,
    filters?: any
  ): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findEventsInTimeRange', async () => {
      const where: any = {
        createdAt: {
          gte: startTime,
          lte: endTime
        }
      };

      if (filters?.userId) {
        where.userId = filters.userId.getValue();
      }

      if (filters?.eventTypes && filters.eventTypes.length > 0) {
        where.eventType = {
          in: filters.eventTypes.map((type: SecurityEventType) => type.getValue())
        };
      }

      if (filters?.severities && filters.severities.length > 0) {
        where.severity = {
          in: filters.severities.map((severity: SecuritySeverity) => severity.getValue())
        };
      }

      if (filters?.categories && filters.categories.length > 0) {
        where.eventCategory = {
          in: filters.categories.map((category: SecurityEventCategory) => category.getValue())
        };
      }

      const records = await this.prisma.securityEvents.findMany({
        where,
        orderBy: { createdAt: 'desc' }
      });

      return records.map(record => this.toDomain(record));
    });
  }

  /**
   * 查找用户在时间范围内的事件
   */
  public async findEventsByUserInTimeRange(
    userId: UserId,
    startTime: Date,
    endTime: Date
  ): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findEventsByUserInTimeRange', async () => {
      const records = await this.prisma.securityEvents.findMany({
        where: {
          userId: userId.getValue(),
          createdAt: {
            gte: startTime,
            lte: endTime
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return records.map(record => this.toDomain(record));
    });
  }

  /**
   * 查找IP地址在时间范围内的失败登录事件
   */
  public async findFailedLoginsByIpInTimeRange(
    ipAddress: string,
    startTime: Date,
    endTime: Date
  ): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findFailedLoginsByIpInTimeRange', async () => {
      const records = await this.prisma.securityEvents.findMany({
        where: {
          ipAddress,
          eventType: 'LOGIN_FAILURE',
          createdAt: {
            gte: startTime,
            lte: endTime
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return records.map(record => this.toDomain(record));
    });
  }

  /**
   * 查找用户最近的位置事件
   */
  public async findRecentLocationEvents(
    userId: UserId,
    since: Date
  ): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findRecentLocationEvents', async () => {
      const records = await this.prisma.securityEvents.findMany({
        where: {
          userId: userId.getValue(),
          geolocation: { not: null },
          createdAt: { gte: since }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      });

      return records.map(record => this.toDomain(record));
    });
  }

  /**
   * 统计用户在时间范围内的事件数量
   */
  public async countEventsByUserInTimeRange(
    userId: UserId,
    eventType: string,
    startTime: Date,
    endTime: Date
  ): Promise<number> {
    return this.baseService.executeWithMonitoring('countEventsByUserInTimeRange', async () => {
      const where: any = {
        userId: userId.getValue(),
        createdAt: {
          gte: startTime,
          lte: endTime
        }
      };
      
      // 如果不是 'ALL'，则添加事件类型过滤
      if (eventType !== 'ALL') {
        where.eventType = eventType;
      }
      
      return this.prisma.securityEvents.count({ where });
    });
  }

  /**
   * 获取用户的活跃会话数
   */
  public async getActiveSessionCount(userId: UserId): Promise<number> {
    return this.baseService.executeWithMonitoring('getActiveSessionCount', async () => {
      // 查询用户会话表获取真实的活跃会话数
      const activeSessions = await this.prisma.userSessions.count({
        where: {
          userId: userId.getValue(),
          isActive: true,
          expiresAt: {
            gt: new Date()
          }
        }
      });
      
      return activeSessions;
    });
  }

  /**
   * 统计事件数量
   */
  public async countEvents(filters?: any): Promise<number> {
    return this.baseService.executeWithMonitoring('countEvents', async () => {
      const where: any = {};

      if (filters?.userId) {
        where.userId = filters.userId.getValue();
      }

      if (filters?.eventTypes && filters.eventTypes.length > 0) {
        where.eventType = {
          in: filters.eventTypes.map((type: SecurityEventType) => type.getValue())
        };
      }

      if (filters?.severities && filters.severities.length > 0) {
        where.severity = {
          in: filters.severities.map((severity: SecuritySeverity) => severity.getValue())
        };
      }

      if (filters?.startTime && filters?.endTime) {
        where.createdAt = {
          gte: filters.startTime,
          lte: filters.endTime
        };
      }

      return this.prisma.securityEvents.count({ where });
    });
  }

  /**
   * 删除安全事件
   */
  public async deleteSecurityEvent(eventId: SecurityEventId): Promise<void> {
    return this.baseService.executeWithMonitoring('deleteSecurityEvent', async () => {
      await this.prisma.securityEvents.delete({
        where: { id: eventId.getValue() }
      });
    });
  }

  /**
   * 删除用户的所有安全事件
   */
  public async deleteAllByUserId(userId: UserId): Promise<void> {
    return this.baseService.executeWithMonitoring('deleteAllByUserId', async () => {
      await this.prisma.securityEvents.deleteMany({
        where: { userId: userId.getValue() }
      });
    });
  }

  /**
   * 删除过期的安全事件
   */
  public async deleteExpiredEvents(expiryDate: Date): Promise<number> {
    return this.baseService.executeWithMonitoring('deleteExpiredEvents', async () => {
      const result = await this.prisma.securityEvents.deleteMany({
        where: {
          createdAt: { lt: expiryDate }
        }
      });

      return result.count;
    });
  }

  /**
   * 获取安全统计信息
   */
  public async getSecurityStatistics(timeRange: { start: Date; end: Date }): Promise<any> {
    return this.baseService.executeWithMonitoring('getSecurityStatistics', async () => {
      const totalEvents = await this.countEvents({
        startTime: timeRange.start,
        endTime: timeRange.end
      });
      
      // 获取真实的事件类型分布
      const eventsByType = await this.prisma.securityEvents.groupBy({
        by: ['eventType'],
        _count: { eventType: true },
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      });
      
      // 获取真实的严重性分布
      const eventsBySeverity = await this.prisma.securityEvents.groupBy({
        by: ['severity'],
        _count: { severity: true },
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      });
      
      // 获取未解决事件数
      const unresolvedEvents = await this.prisma.securityEvents.count({
        where: {
          isResolved: false,
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      });
      
      // 获取高风险事件数
      const highRiskEvents = await this.prisma.securityEvents.count({
        where: {
          severity: 'CRITICAL',
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      });
      
      // 获取最受攻击的用户
      const topTargetedUsers = await this.prisma.securityEvents.groupBy({
        by: ['userId'],
        _count: { userId: true },
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        },
        orderBy: {
          _count: {
            userId: 'desc'
          }
        },
        take: 10
      });
      
      return {
        totalEvents,
        eventsByType: eventsByType.reduce((acc, item) => {
          acc[item.eventType] = item._count.eventType;
          return acc;
        }, {} as Record<string, number>),
        eventsBySeverity: eventsBySeverity.reduce((acc, item) => {
          acc[item.severity] = item._count.severity;
          return acc;
        }, {} as Record<string, number>),
        eventsByCategory: {},
        unresolvedEvents,
        highRiskEvents,
        topTargetedUsers: topTargetedUsers.map(item => ({
          userId: item.userId,
          eventCount: item._count.userId
        })),
        topThreatSources: []
      };
    });
  }

  /**
   * 获取威胁趋势数据
   */
  public async getThreatTrendData(timeRange: { start: Date; end: Date }, granularity: 'hour' | 'day' | 'week' | 'month'): Promise<any[]> {
    return this.baseService.executeWithMonitoring('getThreatTrendData', async () => {
      // 根据粒度计算时间间隔
      const intervalMap = {
        hour: '1 hour',
        day: '1 day',
        week: '1 week',
        month: '1 month'
      };
      
      // 使用原生SQL查询获取真实的趋势数据
      const trendData = await this.prisma.$queryRaw`
        SELECT 
          DATE_TRUNC(${granularity}, "createdAt") as period,
          COUNT(*) as event_count,
          COUNT(CASE WHEN severity = 'CRITICAL' THEN 1 END) as critical_count,
          COUNT(CASE WHEN severity = 'HIGH' THEN 1 END) as high_count
        FROM "SecurityEvents"
        WHERE "createdAt" >= ${timeRange.start}
          AND "createdAt" <= ${timeRange.end}
        GROUP BY period
        ORDER BY period ASC
      `;
      
      return trendData;
    });
  }

  /**
   * 查找相似事件
   */
  public async findSimilarEvents(event: SecurityEvent, similarity: any, timeWindow?: number): Promise<SecurityEvent[]> {
    return this.baseService.executeWithMonitoring('findSimilarEvents', async () => {
      const timeWindowHours = timeWindow || 24;
      const timeThreshold = new Date(Date.now() - timeWindowHours * 60 * 60 * 1000);
      
      // 查找相似的安全事件
      const similarEvents = await this.prisma.securityEvents.findMany({
        where: {
          eventType: (event as any).eventType || 'UNKNOWN',
          severity: (event as any).severity || 'LOW',
          createdAt: {
            gte: timeThreshold
          },
          id: {
            not: event.getId().getValue()
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      });
      
      return similarEvents.map(eventData => SecurityEvent.fromPersistence(eventData));
    });
  }

  /**
   * 获取用户安全摘要
   */
  public async getUserSecuritySummary(userId: UserId, timeRange: { start: Date; end: Date }): Promise<any> {
    return this.baseService.executeWithMonitoring('getUserSecuritySummary', async () => {
      const totalEvents = await this.countEventsByUserInTimeRange(
        userId, 
        'ALL', 
        timeRange.start, 
        timeRange.end
      );
      
      // 获取用户事件类型分布
      const eventsByType = await this.prisma.securityEvents.groupBy({
        by: ['eventType'],
        _count: { eventType: true },
        where: {
          userId: userId.getValue(),
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      });
      
      // 获取用户事件严重性分布
      const eventsBySeverity = await this.prisma.securityEvents.groupBy({
        by: ['severity'],
        _count: { severity: true },
        where: {
          userId: userId.getValue(),
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      });
      
      // 计算风险评分（基于事件严重性和频率）
      const criticalEvents = eventsBySeverity.find(item => item.severity === 'CRITICAL')?._count.severity || 0;
      const highEvents = eventsBySeverity.find(item => item.severity === 'HIGH')?._count.severity || 0;
      const mediumEvents = eventsBySeverity.find(item => item.severity === 'MEDIUM')?._count.severity || 0;
      
      const riskScore = Math.min(100, (criticalEvents * 10) + (highEvents * 5) + (mediumEvents * 2));
      
      // 计算趋势（与前一个时间段比较）
      const previousTimeRange = {
        start: new Date(timeRange.start.getTime() - (timeRange.end.getTime() - timeRange.start.getTime())),
        end: timeRange.start
      };
      
      const previousEvents = await this.countEventsByUserInTimeRange(
        userId,
        'ALL',
        previousTimeRange.start,
        previousTimeRange.end
      );
      
      const changePercentage = previousEvents > 0 
        ? ((totalEvents - previousEvents) / previousEvents) * 100 
        : totalEvents > 0 ? 100 : 0;
      
      return {
        totalEvents,
        eventsByType: eventsByType.reduce((acc, item) => {
          acc[item.eventType] = item._count.eventType;
          return acc;
        }, {} as Record<string, number>),
        eventsBySeverity: eventsBySeverity.reduce((acc, item) => {
          acc[item.severity] = item._count.severity;
          return acc;
        }, {} as Record<string, number>),
        riskScore,
        trends: {
          increasing: changePercentage > 0,
          changePercentage: Math.round(changePercentage * 100) / 100
        }
      };
    });
  }

  /**
   * 查找异常模式
   */
  public async findAnomalousPatterns(timeRange: { start: Date; end: Date }, thresholds: any): Promise<any[]> {
    return this.baseService.executeWithMonitoring('findAnomalousPatterns', async () => {
      const patterns = [];
      
      // 检测频繁失败登录的用户
      const frequentFailures = await this.prisma.securityEvents.groupBy({
        by: ['userId'],
        _count: { userId: true },
        where: {
          eventType: 'LOGIN_FAILED',
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        },
        having: {
          userId: {
            _count: {
              gt: thresholds?.loginFailureThreshold || 5
            }
          }
        }
      });
      
      patterns.push(...frequentFailures.map(item => ({
        type: 'FREQUENT_LOGIN_FAILURES',
        userId: item.userId,
        count: item._count.userId,
        severity: 'HIGH'
      })));
      
      // 检测异常时间段的活动
      const unusualTimeActivity = await this.prisma.securityEvents.findMany({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        },
        select: {
          userId: true,
          createdAt: true,
          eventType: true
        }
      });
      
      // 分析夜间活动（22:00 - 06:00）
      const nightActivity = unusualTimeActivity.filter(event => {
        const hour = event.createdAt.getHours();
        return hour >= 22 || hour <= 6;
      });
      
      if (nightActivity.length > (thresholds?.nightActivityThreshold || 10)) {
        patterns.push({
          type: 'UNUSUAL_TIME_ACTIVITY',
          description: 'High activity during night hours',
          count: nightActivity.length,
          severity: 'MEDIUM'
        });
      }
      
      return patterns;
    });
  }

  /**
   * 获取实时监控数据
   */
  public async getRealTimeMonitoringData(): Promise<any> {
    return this.baseService.executeWithMonitoring('getRealTimeMonitoringData', async () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      
      // 获取活跃威胁数（未解决的高危事件）
      const activeThreats = await this.prisma.securityEvents.count({
        where: {
          isResolved: false,
          severity: {
            in: ['CRITICAL', 'HIGH']
          }
        }
      });
      
      // 获取关键事件数（过去24小时）
      const criticalEvents = await this.prisma.securityEvents.count({
        where: {
          severity: 'CRITICAL',
          createdAt: {
            gte: oneDayAgo
          }
        }
      });
      
      // 获取最近事件（过去1小时）
      const recentEvents = await this.prisma.securityEvents.findMany({
        where: {
          createdAt: {
            gte: oneHourAgo
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10,
        select: {
          id: true,
          eventType: true,
          severity: true,
          userId: true,
          createdAt: true,
          description: true
        }
      });
      
      // 获取过去1小时的告警数
      const alertsInLastHour = await this.prisma.securityEvents.count({
        where: {
          severity: {
            in: ['CRITICAL', 'HIGH']
          },
          createdAt: {
            gte: oneHourAgo
          }
        }
      });
      
      // 获取高风险用户（过去24小时事件最多的用户）
      const topRiskUsers = await this.prisma.securityEvents.groupBy({
        by: ['userId'],
        _count: { userId: true },
        where: {
          createdAt: {
            gte: oneDayAgo
          },
          severity: {
            in: ['CRITICAL', 'HIGH']
          }
        },
        orderBy: {
          _count: {
            userId: 'desc'
          }
        },
        take: 5
      });
      
      return {
        activeThreats,
        criticalEvents,
        recentEvents,
        alertsInLastHour,
        topRiskUsers: topRiskUsers.map(item => ({
          userId: item.userId,
          riskEventCount: item._count.userId
        }))
      };
    });
  }

  /**
   * 批量创建事件
   */
  public async bulkCreate(events: SecurityEvent[]): Promise<void> {
    return this.baseService.executeWithMonitoring('bulkCreate', async () => {
      const data = events.map(event => event.toPersistence());
      await this.prisma.securityEvents.createMany({ data });
    });
  }

  /**
   * 更新事件状态
   */
  public async updateEventStatus(eventId: SecurityEventId, isResolved: boolean, resolution?: string): Promise<void> {
    return this.baseService.executeWithMonitoring('updateEventStatus', async () => {
      await this.prisma.securityEvents.update({
        where: { id: eventId.getValue() },
        data: {
          isResolved,
          resolvedAt: isResolved ? new Date() : null,
          responseAction: resolution
        }
      });
    });
  }

  /**
   * 批量更新事件状态
   */
  public async bulkUpdateEventStatus(eventIds: SecurityEventId[], isResolved: boolean, resolution?: string): Promise<void> {
    return this.baseService.executeWithMonitoring('bulkUpdateEventStatus', async () => {
      await this.prisma.securityEvents.updateMany({
        where: {
          id: {
            in: eventIds.map(id => id.getValue())
          }
        },
        data: {
          isResolved,
          resolvedAt: isResolved ? new Date() : null,
          responseAction: resolution
        }
      });
    });
  }

}
