/**
 * 用户管理路由
 * 避免重复实现，使用统一的路由模式和错误处理
 */

import { Router } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../../../../shared/infrastructure/di/types/index';
import { UserController } from '../controllers/UserController';
// import { AuthController } from '../controllers/AuthController'; // 文件不存在，暂时注释
import { EnhancedAuthMiddleware } from '../../../../../shared/infrastructure/auth/auth-middleware';

export function createUserManagementRoutes(container: Container): Router {
  const router = Router();

  // 获取控制器实例 - 使用统一的DI容器，避免重复实例化
  const getUserController = () => container.get<UserController>(TYPES.UserManagement.UserController);
  // const getAuthController = () => container.get<AuthController>(TYPES.UserManagement.AuthController);
  const getAuthMiddleware = () => container.get<EnhancedAuthMiddleware>(TYPES.Shared.EnhancedAuthMiddleware);

  // ==================== 认证路由 ====================
  
  // 用户注册
  router.post('/register',
    async (req, res, next) => {
      try {
        // 延迟获取中间件，避免在路由注册时立即执行
        const authMiddleware = getAuthMiddleware();
        const rateLimitMiddleware = authMiddleware.rateLimitByIP(5, 60000);
        rateLimitMiddleware(req, res, async (err) => {
          if (err) return next(err);
          // const controller = getAuthController();
          return res.status(501).json({ error: 'AuthController not implemented' });
          // await controller.register(req, res);
        });
      } catch (error) {
        next(error);
      }
    }
  );

  // 用户登录
  router.post('/login',
    async (req, res, next) => {
      try {
        // 延迟获取中间件，避免在路由注册时立即执行
        const authMiddleware = getAuthMiddleware();
        const rateLimitMiddleware = authMiddleware.rateLimitByIP(10, 60000);
        rateLimitMiddleware(req, res, async (err) => {
          if (err) return next(err);
          // const controller = getAuthController();
          // await controller.login(req, res);
          return res.status(501).json({ error: 'AuthController not implemented' });
        });
      } catch (error) {
        next(error);
      }
    }
  );

  // 用户登出
  router.post('/logout',
    async (req, res, next) => {
      try {
        const authMiddleware = getAuthMiddleware();
        const jwtAuthMiddleware = authMiddleware.requireJwtAuth();
        jwtAuthMiddleware(req, res, async (err) => {
          if (err) return next(err);
          // const controller = getAuthController();
          // await controller.logout(req, res);
          return res.status(501).json({ error: 'AuthController not implemented' });
        });
      } catch (error) {
        next(error);
      }
    }
  );

  // 刷新令牌
  router.post('/refresh-token',
    (req, res, next) => getAuthMiddleware().rateLimitByIP(20, 60000)(req, res, next), // 每分钟20次
    async (req, res) => {
      // const controller = getAuthController();
      // await controller.refreshToken(req, res);
      return res.status(501).json({ error: 'AuthController not implemented' });
    }
  );

  // ==================== 用户管理路由 ====================

  // 获取用户资料
  router.get('/profile',
    (req, res, next) => getAuthMiddleware().requireJwtAuth()(req, res, next),
    async (req, res) => {
      const controller = getUserController();
      await controller.getUserProfile(req, res);
    }
  );

  // 更新用户资料
  router.put('/profile',
    (req, res, next) => getAuthMiddleware().requireJwtAuth()(req, res, next),
    (req, res, next) => getAuthMiddleware().rateLimitByUser(10, 60000)(req, res, next), // 每分钟10次
    async (req, res) => {
      const controller = getUserController();
      await controller.updateUserProfile(req, res);
    }
  );

  // 修改密码
  router.put('/password',
    (req, res, next) => getAuthMiddleware().requireJwtAuth()(req, res, next),
    (req, res, next) => getAuthMiddleware().rateLimitByUser(5, 60000)(req, res, next), // 每分钟5次
    async (req, res) => {
      const controller = getUserController();
      await controller.changePassword(req, res);
    }
  );

  // 获取用户列表（管理员功能）
  router.get('/',
    (req, res, next) => getAuthMiddleware().requireJwtAuth()(req, res, next),
    (req, res, next) => getAuthMiddleware().requireRole(['ADMIN'])(req, res, next),
    async (req, res) => {
      const controller = getUserController();
      await controller.getUserList(req, res);
    }
  );

  // 获取特定用户信息（管理员功能）
  router.get('/:userId',
    (req, res, next) => getAuthMiddleware().requireJwtAuth()(req, res, next),
    (req, res, next) => getAuthMiddleware().requireRole(['ADMIN'])(req, res, next),
    async (req, res) => {
      const controller = getUserController();
      await controller.getUserById(req, res);
    }
  );

  // 更新用户状态（管理员功能）
  router.patch('/:userId/status',
    (req, res, next) => getAuthMiddleware().requireJwtAuth()(req, res, next),
    (req, res, next) => getAuthMiddleware().requireRole(['ADMIN'])(req, res, next),
    async (req, res) => {
      const controller = getUserController();
      await controller.updateUserStatus(req, res);
    }
  );

  // 更新用户角色（管理员功能）
  router.patch('/:userId/role',
    (req, res, next) => getAuthMiddleware().requireJwtAuth()(req, res, next),
    (req, res, next) => getAuthMiddleware().requireRole(['ADMIN'])(req, res, next),
    async (req, res) => {
      const controller = getUserController();
      await controller.updateUserRole(req, res);
    }
  );

  // 🔥 健康检查重复实现已移除
  // 使用统一的HealthCheckAggregator进行健康检查
  // 健康检查端点应该在应用级别统一处理，而不是在每个模块中重复实现

  return router;
}
