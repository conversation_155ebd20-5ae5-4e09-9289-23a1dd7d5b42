/**
 * 风险评估应用服务
 * 协调风险评估的业务流程
 *
 * 重构后继承BaseApplicationService，消除请求编排模式重复
 */

import { injectable, inject, optional } from 'inversify';
import { Logger } from 'winston';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { IPureAIRiskAnalysisEngine } from '../../../ai-reasoning/domain/services/pure-ai-analysis.interface';
import { IRiskAssessmentRepository } from '../../domain/repositories/risk-assessment-repository';
import { IRiskMetricsCalculatorService } from '../../infrastructure/services/risk-metrics-calculator-service.interface';
import { IDataProcessingPipeline, ProcessingContext } from '../../../../shared/infrastructure/data-processing/interfaces/IDataProcessingPipeline';
import { RiskDataStrategy, RiskDataStrategyInput } from '../../../../shared/infrastructure/data-processing/strategies/risk/risk-data-strategy';
import { Symbol } from '../../../shared/domain/value-objects/symbol';

// 核心分析服务接口导入
import { IDynamicWeightingService } from '../../../../shared/infrastructure/analysis/interfaces/IDynamicWeightingService';
import { IPatternRecognitionService } from '../../../../shared/infrastructure/analysis/interfaces/IPatternRecognitionService';
import { IMultiTimeframeService } from '../../../../shared/infrastructure/analysis/interfaces/IMultiTimeframeService';
import { PatternType } from '../../../../shared/infrastructure/analysis/types/PatternTypes';
// Symbol is a string type, no need to import
import { RiskAssessment } from '../../domain/entities/risk-assessment';
import { Position, PositionType } from '../../domain/value-objects/position';
import { RiskLevel } from '../../domain/value-objects/risk-level';
import { RiskMetrics } from '../../domain/value-objects/risk-metrics';
import { RiskFactor, RiskFactorType, RiskFactorSeverity } from '../../domain/value-objects/risk-factor';
import { MarketDataContext } from '../../domain/value-objects/market-data-context';
import {
  IRiskAssessmentApplicationService,
  RiskAssessmentResponse
} from '../../domain/interfaces/risk-assessment-application-service.interface';
import { RealTimeRiskMonitor, RealTimeRiskData } from '../../../../shared/infrastructure/risk/real-time-risk-monitor';

// 导入基础应用服务
import {
  BaseApplicationService
} from '../../../../shared/application/base-application-service';

// 本地类型定义
interface RequestContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  timestamp: Date;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

interface BusinessRuleResult {
  passed: boolean;
  violations: string[];
}
import {
  BaseRequest,
  BaseResponse,
  IApplicationService,
  HealthCheckResult
} from '../../../../shared/application/application-service-interfaces';
import { IEventBus } from '../../../../shared/domain/events/domain-event';

/**
 * 风险评估请求 - 重构后不再直接接收marketData
 */
export interface RiskAssessmentRequest extends BaseRequest {
  portfolioId: string;
  userId: string;
  position: Position;
  symbol: string; // 用于数据获取
  analysisDepth?: 'basic' | 'standard' | 'comprehensive';
  forceRecalculation?: boolean;
}

/**
 * 风险评估响应 - 应用层内部使用
 */
export interface RiskAssessmentApplicationResponse extends BaseResponse {
  data: {
    assessment: RiskAssessment;
    isNewAssessment: boolean;
    previousAssessment?: RiskAssessment;
    riskLevelChanged: boolean;
    recommendations: string[];
    alerts: string[];
  }
}

/**
 * 批量风险评估请求
 */
export interface BatchRiskAssessmentRequest {
  portfolioId: string;
  positions: Position[];
  marketDataMap: Map<string, MarketDataContext>;
  correlationMatrix?: number[][];
}

/**
 * 批量风险评估响应
 */
export interface BatchRiskAssessmentResponse {
  portfolioAssessment: RiskAssessment;
  individualAssessments: Map<string, RiskAssessment>;
  overallRiskLevel: RiskLevel;
  criticalAlerts: string[];
  recommendations: string[];
}

/**
 * 风险评估应用服务 - 重构版
 * 遵循正确的架构设计：统一数据处理 + 量化计算 + AI辅助分析
 *
 * 重构后继承BaseApplicationService，消除请求编排模式重复
 */
@injectable()
export class RiskAssessmentApplicationService extends BaseApplicationService implements IRiskAssessmentApplicationService, IApplicationService {
  readonly serviceName = 'RiskAssessmentApplicationService';

  constructor(
    @inject(TYPES.Logger)
    logger: Logger,
    @inject(TYPES.EventBus)
    eventBus: IEventBus,
    @inject(TYPES.DataProcessing.DataProcessingPipeline)
    private readonly dataProcessingPipeline: IDataProcessingPipeline,
    @inject(TYPES.DataProcessing.RiskDataStrategy)
    private readonly riskDataStrategy: RiskDataStrategy,
    @inject(TYPES.RiskManagement.RiskMetricsCalculatorService)
    private readonly riskMetricsCalculator: IRiskMetricsCalculatorService,
    @inject(TYPES.RiskManagement.RiskAssessmentRepository)
    private readonly riskAssessmentRepository: IRiskAssessmentRepository,
    @inject(TYPES.AIReasoning.PureAIRiskAnalysisEngine) @optional()
    private readonly pureRiskEngine: IPureAIRiskAnalysisEngine | undefined,
    // 🔥 核心分析服务集成 - 修复架构违规问题
    @inject(TYPES.Shared.DynamicWeightingService) @optional()
    private readonly dynamicWeightingService: IDynamicWeightingService | undefined,
    @inject(TYPES.Shared.PatternRecognitionService) @optional()
    private readonly patternRecognitionService: IPatternRecognitionService | undefined,
    @inject(TYPES.Shared.MultiTimeframeService) @optional()
    private readonly multiTimeframeService: IMultiTimeframeService | undefined,
    // 注入实时风险监控器
    @inject(TYPES.Shared.RealTimeRiskMonitor) @optional()
    private readonly realTimeRiskMonitor: RealTimeRiskMonitor | undefined,
    // 注入真实市场数据服务
    @inject(TYPES.MarketData.RealMarketDataService) @optional()
    private readonly realMarketDataService: any | undefined
  ) {
    super(logger, eventBus);
    this.logger.info('风险评估应用服务初始化完成 - 架构重构版', {
      hasDataProcessingPipeline: !!this.dataProcessingPipeline,
      hasRiskDataStrategy: !!this.riskDataStrategy,
      hasRiskMetricsCalculator: !!this.riskMetricsCalculator,
      hasPureRiskEngine: !!this.pureRiskEngine,
      // 核心分析服务可用性检查
      hasDynamicWeightingService: !!this.dynamicWeightingService,
      hasPatternRecognitionService: !!this.patternRecognitionService,
      hasMultiTimeframeService: !!this.multiTimeframeService,
      // 实时风险监控可用性检查
      hasRealTimeRiskMonitor: !!this.realTimeRiskMonitor,
      // 市场数据服务可用性检查
      hasRealMarketDataService: !!this.realMarketDataService
    });
    
    // 警告缺失的核心分析服务
    if (!this.dynamicWeightingService) {
      this.logger.warn('⚠️ DynamicWeightingService 未注入，风险因子权重计算将使用默认策略');
    }
    if (!this.patternRecognitionService) {
      this.logger.warn('⚠️ PatternRecognitionService 未注入，风险模式识别功能将受限');
    }
    if (!this.multiTimeframeService) {
      this.logger.warn('⚠️ MultiTimeframeService 未注入，多时间框架风险分析功能将受限');
    }
    if (!this.realTimeRiskMonitor) {
      this.logger.warn('⚠️ RealTimeRiskMonitor 未注入，实时风险监控功能将受限');
    } else {
      // 启动实时风险监控
      this.initializeRealTimeMonitoring();
    }
  }

  /**
   * 执行单个资产的风险评估 - 使用统一请求编排模式
   */
  async assessRisk(request: RiskAssessmentRequest): Promise<RiskAssessmentResponse> {
    const result = await this.processRequest<RiskAssessmentRequest, RiskAssessmentApplicationResponse>(
      request,
      '风险评估',
      {
        skipValidation: false,
        skipBusinessRules: false,
        skipAdditionalData: false,
        enablePerformanceTracking: true
      }
    );

    if (!result.success) {
      throw new Error(result.error || '风险评估失败');
    }

    // 转换为接口期望的格式
    const appResponse = result.data!;
    return {
      assessment: appResponse.data.assessment,
      isNewAssessment: appResponse.data.isNewAssessment,
      previousAssessment: appResponse.data.previousAssessment,
      riskLevelChanged: appResponse.data.riskLevelChanged,
      recommendations: appResponse.data.recommendations,
      alerts: appResponse.data.alerts
    };
  }

  /**
   * 执行批量风险评估
   */
  async assessPortfolioRisk(request: BatchRiskAssessmentRequest): Promise<BatchRiskAssessmentResponse> {
    try {
      this.logger.info('开始组合风险评估', {
        portfolioId: request.portfolioId,
        positionCount: request.positions.length
      });

      // 转换MarketDataContext到MarketDataInput
      const marketDataInputMap = new Map<string, any>();
      for (const [symbol, marketData] of request.marketDataMap.entries()) {
        marketDataInputMap.set(symbol, {
          symbol: marketData.symbol,
          currentPrice: marketData.currentPrice,
          priceHistory: marketData.priceHistory.map(p => ({
            timestamp: p.timestamp,
            price: p.price,
            volume: p.volume
          })),
          volume24h: marketData.volume24h,
          marketCap: marketData.marketCap,
          bidAskSpread: marketData.orderBook ?
            (marketData.orderBook.asks[0]?.price || 0) - (marketData.orderBook.bids[0]?.price || 0) : 0,
          orderBookDepth: {
            bids: marketData.orderBook?.bids || [],
            asks: marketData.orderBook?.asks || []
          }
        });
      }

      // 使用真实的风险计算服务
      const portfolioRisk = await this.riskMetricsCalculator.calculatePortfolioRisk(
        request.positions,
        marketDataInputMap,
        request.correlationMatrix
      );

      this.logger.info('投资组合风险计算完成', {
        totalRiskScore: portfolioRisk.totalRisk.getCompositeRiskScore(),
        diversificationBenefit: portfolioRisk.diversificationBenefit,
        concentrationRisk: portfolioRisk.concentrationRisk,
        individualRiskCount: portfolioRisk.individualRisks.size
      });

      // 创建组合风险评估
      const portfolioPosition = this.aggregatePositions(request.positions);
      const portfolioRiskLevel = RiskLevel.fromScore(portfolioRisk.totalRisk.getCompositeRiskScore());

      const portfolioAssessment = RiskAssessment.create({
        portfolioId: request.portfolioId,
        position: portfolioPosition,
        riskLevel: portfolioRiskLevel,
        riskMetrics: portfolioRisk.totalRisk,
        keyRiskFactors: [], // 将在下面填充
        maxLoss: {
          var95: portfolioRisk.totalRisk.var.var95_30d,
          var99: portfolioRisk.totalRisk.var.var99_30d,
          expectedShortfall: portfolioRisk.totalRisk.cvar.cvar95_30d
        },
        recommendations: [],
        alertThresholds: [],
        contingencyPlan: [],
        confidence: 0.8
      });

      // 获取个别资产的风险评估
      const individualAssessments = new Map<string, RiskAssessment>();
      const allRiskFactors: any[] = [];

      for (const position of request.positions) {
        const marketData = request.marketDataMap.get(position.symbol);
        if (marketData) {
          const individualRisk = portfolioRisk.individualRisks.get(position.symbol);
          if (individualRisk) {
            // 使用纯净AI引擎识别风险因子
            const riskFactors = [
              RiskFactor.create(
                '市场波动风险',
                RiskFactorType.MARKET_RISK,
                RiskFactorSeverity.MEDIUM,
                0.5,
                0.5,
                '当前市场存在一定波动性'
              )
            ];

            const assessment = RiskAssessment.create({
              portfolioId: request.portfolioId,
              position,
              riskLevel: RiskLevel.fromScore(individualRisk.getCompositeRiskScore()),
              riskMetrics: individualRisk,
              keyRiskFactors: riskFactors.slice(0, 3),
              maxLoss: {
                var95: individualRisk.var.var95_30d,
                var99: individualRisk.var.var99_30d,
                expectedShortfall: individualRisk.cvar.cvar95_30d
              },
              recommendations: this.generateRecommendations(
                RiskLevel.fromScore(individualRisk.getCompositeRiskScore()),
                riskFactors,
                position
              ),
              alertThresholds: this.generateAlertThresholds(individualRisk),
              contingencyPlan: this.generateContingencyPlan(
                RiskLevel.fromScore(individualRisk.getCompositeRiskScore()),
                riskFactors
              ),
              confidence: this.calculateConfidence(individualRisk, riskFactors)
            });

            individualAssessments.set(position.symbol, assessment);
            allRiskFactors.push(...riskFactors);
          }
        }
      }

      // 更新组合评估的风险因子和建议
      const topRiskFactors = this.aggregateRiskFactors(allRiskFactors).slice(0, 5);

      portfolioAssessment.updateAssessment(
        portfolioRiskLevel,
        portfolioRisk.totalRisk,
        topRiskFactors,
        {
          var95: portfolioRisk.totalRisk.var.var95_30d,
          var99: portfolioRisk.totalRisk.var.var99_30d,
          expectedShortfall: portfolioRisk.totalRisk.cvar.cvar95_30d
        },
        this.generatePortfolioRecommendations(portfolioRiskLevel, topRiskFactors, request.positions),
        this.calculatePortfolioConfidence(portfolioRisk, topRiskFactors)
      );

      // 保存评估结果
      await this.riskAssessmentRepository.save(portfolioAssessment, request.portfolioId);
      await this.riskAssessmentRepository.saveBatch(Array.from(individualAssessments.values()));

      // 生成关键预警
      const criticalAlerts = this.generateCriticalAlerts(portfolioAssessment, individualAssessments);

      this.logger.info('组合风险评估完成', {
        portfolioId: request.portfolioId,
        overallRiskLevel: portfolioRiskLevel.level,
        criticalAlertsCount: criticalAlerts.length
      });

      return {
        portfolioAssessment,
        individualAssessments,
        overallRiskLevel: portfolioRiskLevel,
        criticalAlerts,
        recommendations: portfolioAssessment.recommendations
      };
    } catch (error) {
      this.logger.error('组合风险评估失败', {
        portfolioId: request.portfolioId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }





  // 私有辅助方法
  private generateRecommendations(
    riskLevel: RiskLevel,
    riskFactors: any[],
    position: Position
  ): string[] {
    const recommendations: string[] = [];

    if (riskLevel.isCritical()) {
      recommendations.push('立即考虑减仓或平仓以控制风险');
      recommendations.push('设置紧急止损机制');
      recommendations.push('密切监控市场动态');
    } else if (riskLevel.isHigh()) {
      recommendations.push('考虑适当减少仓位规模');
      recommendations.push('加强风险监控频率');
      recommendations.push('准备应急处理方案');
    } else if (riskLevel.isMedium()) {
      recommendations.push('保持当前仓位，定期评估风险');
      recommendations.push('关注主要风险因子变化');
    } else {
      recommendations.push('当前风险水平可接受');
      recommendations.push('继续按计划执行投资策略');
    }

    // 根据具体风险因子添加建议
    for (const factor of riskFactors.slice(0, 3)) {
      if (factor.mitigationMeasures && Array.isArray(factor.mitigationMeasures)) {
        recommendations.push(...factor.mitigationMeasures.slice(0, 2));
      }
    }

    return [...new Set(recommendations)]; // 去重
  }

  private generateAlertThresholds(riskMetrics: any): Array<{
    metric: string;
    threshold: number;
    action: string;
  }> {
    return [
      {
        metric: '波动率',
        threshold: riskMetrics.volatility.annualized * 1.5,
        action: '发送高波动率预警'
      },
      {
        metric: 'VaR',
        threshold: riskMetrics.var.var95_30d * 1.2,
        action: '发送VaR超限预警'
      },
      {
        metric: '回撤',
        threshold: riskMetrics.maxDrawdown.current + 0.05,
        action: '发送回撤预警'
      }
    ];
  }

  private generateContingencyPlan(riskLevel: RiskLevel, riskFactors: any[]): string[] {
    const plan: string[] = [];

    if (riskLevel.isHigh() || riskLevel.isCritical()) {
      plan.push('准备紧急减仓方案');
      plan.push('联系风险管理团队');
      plan.push('评估对冲策略');
    }

    plan.push('监控关键风险指标');
    plan.push('准备市场沟通方案');

    return plan;
  }

  private calculateConfidence(riskMetrics: any, riskFactors: any[]): number {
    // 基于数据质量和模型可靠性计算置信度
    let confidence = 0.8;

    // 根据数据完整性调整
    if (riskMetrics.calculatedAt && !riskMetrics.isStale(30)) {
      confidence += 0.1;
    }

    // 根据风险因子数量调整
    if (riskFactors.length >= 3) {
      confidence += 0.05;
    }

    return Math.min(0.95, confidence);
  }

  private generateAlerts(assessment: RiskAssessment): string[] {
    const alerts: string[] = [];

    if (assessment.requiresImmediateAttention()) {
      alerts.push(`风险等级为${assessment.riskLevel.getDescription()}，需要立即关注`);
    }

    if (!assessment.isValid()) {
      alerts.push('风险评估已过期，建议重新评估');
    }

    return alerts;
  }

  private aggregatePositions(positions: Position[]): Position {
    if (positions.length === 0) {
      throw new Error('持仓列表不能为空');
    }

    if (positions.length === 1) {
      return positions[0];
    }

    // 计算投资组合的总市值
    const totalMarketValue = positions.reduce((sum, pos) => sum + pos.marketValue, 0);
    
    // 计算加权平均价格
    const weightedAveragePrice = positions.reduce((sum, pos) => {
      const weight = pos.marketValue / totalMarketValue;
      return sum + (pos.averagePrice * weight);
    }, 0);

    // 计算加权当前价格
    const weightedCurrentPrice = positions.reduce((sum, pos) => {
      const weight = pos.marketValue / totalMarketValue;
      return sum + (pos.currentPrice * weight);
    }, 0);

    // 计算总数量（按价值加权）
    const totalQuantity = positions.reduce((sum, pos) => sum + pos.quantity, 0);

    // 计算总未实现盈亏
    const totalUnrealizedPnL = positions.reduce((sum, pos) => sum + pos.unrealizedPnL, 0);

    // 计算加权未实现盈亏百分比
    const weightedUnrealizedPnLPercent = positions.reduce((sum, pos) => {
      const weight = pos.marketValue / totalMarketValue;
      return sum + (pos.unrealizedPnLPercent * weight);
    }, 0);

    // 计算总成本基础
    const totalCostBasis = positions.reduce((sum, pos) => sum + pos.costBasis, 0);

    // 计算加权杠杆（如果有）
    const positionsWithLeverage = positions.filter(pos => pos.leverage && pos.leverage > 0);
    const weightedLeverage = positionsWithLeverage.length > 0 
      ? positionsWithLeverage.reduce((sum, pos) => {
          const weight = pos.marketValue / totalMarketValue;
          return sum + ((pos.leverage || 1) * weight);
        }, 0)
      : undefined;

    // 计算总保证金使用（如果有）
    const totalMarginUsed = positions.reduce((sum, pos) => {
      return sum + (pos.marginUsed || 0);
    }, 0);

    // 找到最早的入场时间
    const earliestEntryTime = positions.reduce((earliest, pos) => {
      return pos.entryTime < earliest ? pos.entryTime : earliest;
    }, positions[0].entryTime);

    // 找到最新的更新时间
    const latestUpdateTime = positions.reduce((latest, pos) => {
      return pos.lastUpdated > latest ? pos.lastUpdated : latest;
    }, positions[0].lastUpdated);

    // 创建聚合的投资组合持仓
    // 使用特殊的符号标识这是一个投资组合聚合
    const portfolioSymbol = `PORTFOLIO_${positions.map(p => p.symbol).join('_')}`;
    
    try {
      return Position.create(
        portfolioSymbol,
        PositionType.LONG, // 投资组合默认为多头
        totalQuantity,
        weightedAveragePrice,
        weightedCurrentPrice,
        earliestEntryTime,
        {
          leverage: weightedLeverage,
          marginUsed: totalMarginUsed > 0 ? totalMarginUsed : undefined,
          liquidationPrice: undefined, // 投资组合层面不计算清算价格
          stopLoss: undefined, // 投资组合层面不设置止损
          takeProfit: undefined // 投资组合层面不设置止盈
        }
      );
    } catch (error) {
      this.logger.error('创建聚合投资组合持仓失败', {
        error: error instanceof Error ? error.message : String(error),
        positionsCount: positions.length,
        totalMarketValue,
        weightedAveragePrice,
        weightedCurrentPrice
      });
      
      // 如果创建失败，返回市值最大的持仓作为代表
      const largestPosition = positions.reduce((largest, current) => {
        return current.marketValue > largest.marketValue ? current : largest;
      }, positions[0]);
      
      this.logger.warn('使用市值最大的持仓作为投资组合代表', {
        selectedSymbol: largestPosition.symbol,
        marketValue: largestPosition.marketValue
      });
      
      return largestPosition;
    }
  }

  private aggregateRiskFactors(allFactors: any[]): any[] {
    // 聚合和去重风险因子
    const factorMap = new Map();
    
    for (const factor of allFactors) {
      const key = `${factor.type}_${factor.name}`;
      if (!factorMap.has(key) || factorMap.get(key).getRiskScore() < factor.getRiskScore()) {
        factorMap.set(key, factor);
      }
    }

    return Array.from(factorMap.values()).sort((a, b) => b.getRiskScore() - a.getRiskScore());
  }

  private generatePortfolioRecommendations(
    riskLevel: RiskLevel,
    riskFactors: any[],
    positions: Position[]
  ): string[] {
    const recommendations = this.generateRecommendations(riskLevel, riskFactors, positions[0]);
    
    // 添加组合特定的建议
    if (positions.length === 1) {
      recommendations.push('考虑增加投资品种以分散风险');
    }

    return recommendations;
  }

  private generateCriticalAlerts(
    portfolioAssessment: RiskAssessment,
    individualAssessments: Map<string, RiskAssessment>
  ): string[] {
    const alerts: string[] = [];

    if (portfolioAssessment.requiresImmediateAttention()) {
      alerts.push(`组合整体风险等级为${portfolioAssessment.riskLevel.getDescription()}`);
    }

    for (const [symbol, assessment] of individualAssessments) {
      if (assessment.requiresImmediateAttention()) {
        alerts.push(`${symbol} 风险等级为${assessment.riskLevel.getDescription()}`);
      }
    }

    return alerts;
  }

  // ==================== 新增的用例编排层辅助方法 ====================

  /**
   * 创建数据处理策略输入
   */
  private createStrategyInput(request: RiskAssessmentRequest): RiskDataStrategyInput {
    return {
      symbol: new Symbol(request.symbol),
      analysisDepth: request.analysisDepth || 'standard'
    };
  }

  /**
   * 创建数据处理上下文
   */
  private createProcessingContext(request: RiskAssessmentRequest): ProcessingContext {
    return {
      source: 'risk-management',
      dataType: 'risk-assessment',
      businessSystem: 'risk-management',
      metadata: {
        portfolioId: request.portfolioId,
        symbol: request.symbol,
        userId: request.userId,
        analysisDepth: request.analysisDepth || 'standard',
        requestId: `risk_${request.portfolioId}_${Date.now()}`
      }
    };
  }

  /**
   * 验证风险评估请求
   */
  private validateRiskAssessmentRequest(request: RiskAssessmentRequest): void {
    if (!request.portfolioId || typeof request.portfolioId !== 'string') {
      throw new Error('Invalid portfolioId parameter');
    }
    if (!request.userId || typeof request.userId !== 'string') {
      throw new Error('Invalid userId parameter');
    }
    if (!request.position?.symbol) {
      throw new Error('Invalid position parameter');
    }
    if (!request.symbol) {
      throw new Error('Invalid symbol parameter');
    }
  }

  /**
   * 应用风险业务规则 V2 - 基于量化计算结果
   */
  private async applyRiskBusinessRulesV2(
    riskMetrics: RiskMetrics,
    aiInsights: any,
    position: Position,
    portfolioId: string
  ): Promise<any> {
    // 🔥 使用核心分析服务进行动态权重计算
    let riskScore = await this.calculateEnhancedRiskScore(riskMetrics, position);
    
    // 基于组合规模调整风险等级
    if (position.marketValue > 1000000) { // 大额持仓
      riskScore += 10; // 增加风险评分
    }

    // 基于持仓时间调整风险评估
    const holdingDays = this.calculateHoldingDays(position);
    if (holdingDays > 90) {
      riskScore += 5; // 长期持仓增加风险
    }

    // 基于流动性风险调整
    if (riskMetrics.liquidityRisk.liquidityScore < 50) {
      riskScore += 15; // 流动性不足增加风险
    }

    // 基于波动率调整
    if (riskMetrics.volatility.annualized > 0.5) {
      riskScore += 10; // 高波动率增加风险
    }

    // 确保风险评分在合理范围内
    riskScore = Math.max(0, Math.min(100, riskScore));

    const riskLevel = RiskLevel.fromScore(riskScore);
    
    // 🔥 使用核心分析服务增强风险因子识别
    const riskFactors = await this.generateEnhancedRiskFactors(riskMetrics, position);
    
    // 如果有AI洞察，合并AI识别的风险因子
    if (aiInsights?.keyRiskFactors) {
      riskFactors.push(...aiInsights.keyRiskFactors.slice(0, 2));
    }

    return {
      riskLevel: riskLevel.level,
      riskScore,
      riskFactors: riskFactors.slice(0, 5), // 最多5个风险因子
      recommendations: this.generateRecommendations(riskLevel, riskFactors, position),
      alertThresholds: this.generateAlertThresholds(riskMetrics),
      contingencyPlans: this.generateContingencyPlan(riskLevel, riskFactors)
    };
  }

  /**
   * 🔥 使用核心分析服务进行增强的风险评分计算
   * 集成动态权重服务，根据市场条件调整各风险因子权重
   */
  private async calculateEnhancedRiskScore(riskMetrics: RiskMetrics, position: Position): Promise<number> {
    // 如果动态权重服务可用，使用智能权重计算
    if (this.dynamicWeightingService) {
      try {
        const riskFactorTrends = [
          {
            timeframe: '1d',
            direction: riskMetrics.var.var95_30d > 0.05 ? 'bearish' : 'bullish',
            strength: Math.min(riskMetrics.var.var95_30d * 10, 1),
            confidence: 0.8,
            volume: position.marketValue,
            momentum: riskMetrics.var.var95_30d > 0.05 ? -0.5 : 0.5,
            timestamp: new Date()
          },
          {
            timeframe: '7d',
            direction: riskMetrics.volatility.annualized > 0.3 ? 'bearish' : 'bullish',
            strength: Math.min(riskMetrics.volatility.annualized, 1),
            confidence: 0.7,
            volume: position.marketValue,
            momentum: riskMetrics.volatility.annualized > 0.3 ? -0.3 : 0.3,
            timestamp: new Date()
          }
        ];

        const dynamicWeights = await this.dynamicWeightingService.calculateDynamicWeights(
          riskFactorTrends,
          {
            type: 'RANGING',
            volatility: riskMetrics.volatility.annualized,
            volume: position.marketValue,
            trend: 'sideways',
            strength: 0.5
          }
        );

        this.logger.info('使用动态权重计算风险评分', {
          symbol: position.symbol,
          dynamicWeights: dynamicWeights.weights
        });

        // 使用动态权重计算评分
        let score = 0;
        const varScore = Math.min(100, riskMetrics.var.var95_30d * 100 * 3.33);
        const volatilityScore = Math.min(100, riskMetrics.volatility.annualized * 100 * 2);
        const drawdownScore = Math.min(100, riskMetrics.maxDrawdown.current * 100 * 5);
        const liquidityScore = Math.max(0, 100 - riskMetrics.liquidityRisk.liquidityScore);
        const betaScore = Math.min(100, Math.abs(riskMetrics.beta - 1) * 100);

        // 使用时间框架权重
        const weights = dynamicWeights as any;
        score += varScore * (weights['1d'] || 0.3);
        score += volatilityScore * (weights['7d'] || 0.25);
        score += drawdownScore * (weights['30d'] || 0.2);
        score += liquidityScore * 0.15;
        score += betaScore * 0.1;

        return Math.round(score);
      } catch (error) {
        this.logger.warn('动态权重计算失败，使用默认权重', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // 回退到原有的固定权重计算
    return this.calculateQuantitativeRiskScore(riskMetrics);
  }

  /**
   * 基于量化指标计算风险评分（保留原方法作为回退）
   */
  private calculateQuantitativeRiskScore(riskMetrics: RiskMetrics): number {
    let score = 0;
    
    // VaR贡献 (30%权重)
    const varScore = Math.min(100, riskMetrics.var.var95_30d * 100 * 3.33);
    score += varScore * 0.3;
    
    // 波动率贡献 (25%权重)
    const volatilityScore = Math.min(100, riskMetrics.volatility.annualized * 100 * 2);
    score += volatilityScore * 0.25;
    
    // 最大回撤贡献 (20%权重)
    const drawdownScore = Math.min(100, riskMetrics.maxDrawdown.current * 100 * 5);
    score += drawdownScore * 0.2;
    
    // 流动性风险贡献 (15%权重)
    const liquidityScore = Math.max(0, 100 - riskMetrics.liquidityRisk.liquidityScore);
    score += liquidityScore * 0.15;
    
    // Beta贡献 (10%权重)
    const betaScore = Math.min(100, Math.abs(riskMetrics.beta - 1) * 100);
    score += betaScore * 0.1;
    
    return Math.round(score);
  }

  /**
   * 🔥 使用核心分析服务增强风险因子识别
   * 集成模式识别和多时间框架分析
   */
  private async generateEnhancedRiskFactors(riskMetrics: RiskMetrics, position: Position): Promise<string[]> {
    const factors: string[] = [];
    
    // 使用模式识别服务识别风险模式
    if (this.patternRecognitionService) {
      try {
        const klineData = [
          {
            timestamp: new Date(),
            open: position.currentPrice * 0.99,
            high: position.currentPrice * 1.01,
            low: position.currentPrice * 0.98,
            close: position.currentPrice,
            volume: position.quantity,
            symbol: position.symbol
          }
        ];

        const patterns = await this.patternRecognitionService.identifyPatterns(
          klineData,
          {
            patternTypes: [PatternType.DOUBLE_TOP, PatternType.DOUBLE_BOTTOM, PatternType.HEAD_SHOULDERS],
            confidenceThreshold: 0.7,
            includeIncomplete: false
          }
        );

        this.logger.info('模式识别完成', {
          symbol: position.symbol,
          patternsFound: patterns.length || 0
        });

        // 将识别的模式转换为风险因子
        if (patterns && patterns.length > 0) {
          for (const pattern of patterns.slice(0, 2)) {
            factors.push(`模式风险：${pattern.description || pattern.type}`);
          }
        }
      } catch (error) {
        this.logger.warn('模式识别失败', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // 使用多时间框架服务分析跨期风险
    if (this.multiTimeframeService) {
      try {
        // 创建TradingSymbol对象
        const tradingSymbol = { symbol: position.symbol } as any;

        // 创建Timeframe数组
        const timeframes = [
          { interval: '1d' },
          { interval: '7d' },
          { interval: '30d' }
        ] as any[];

        const multiTimeframeAnalysis = await this.multiTimeframeService.analyzeMultipleTimeframes(
          tradingSymbol,
          timeframes,
          {
            includeConflictResolution: true,
            weightingStrategy: 'volatility',
            minConfidence: 0.6
          }
        );

        this.logger.info('多时间框架分析完成', {
          symbol: position.symbol,
          timeframesAnalyzed: multiTimeframeAnalysis.timeframes?.length || 0
        });

        // 基于多时间框架分析结果添加风险因子
        if (multiTimeframeAnalysis.riskAssessment?.factors) {
          factors.push(...multiTimeframeAnalysis.riskAssessment.factors.slice(0, 2));
        }
      } catch (error) {
        this.logger.warn('多时间框架分析失败', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // 添加基础量化风险因子
    const quantitativeFactors = this.generateQuantitativeRiskFactors(riskMetrics, position);
    factors.push(...quantitativeFactors);
    
    return factors;
  }

  /**
   * 基于量化指标生成风险因子（保留原方法）
   */
  private generateQuantitativeRiskFactors(riskMetrics: RiskMetrics, position: Position): string[] {
    const factors: string[] = [];
    
    // 基于VaR
    if (riskMetrics.var.var95_30d > 0.2) {
      factors.push('高VaR风险：30天95%置信度下的潜在损失较大');
    }
    
    // 基于波动率
    if (riskMetrics.volatility.annualized > 0.4) {
      factors.push('高波动率风险：年化波动率超过40%');
    }
    
    // 基于最大回撤
    if (riskMetrics.maxDrawdown.current > 0.15) {
      factors.push('回撤风险：当前回撤超过15%');
    }
    
    // 基于流动性
    if (riskMetrics.liquidityRisk.liquidityScore < 60) {
      factors.push('流动性风险：市场深度不足，可能影响交易执行');
    }
    
    // 基于Beta
    if (Math.abs(riskMetrics.beta) > 1.5) {
      factors.push('市场敏感性风险：Beta值较高，对市场变化敏感');
    }
    
    // 基于相关性风险
    if (riskMetrics.correlationRisk.marketCorrelation > 0.8) {
      factors.push('高相关性风险：与市场相关性过高，缺乏分散化效果');
    }
    
    return factors;
  }

  /**
    * 计算持仓天数
    */
   private calculateHoldingDays(position: Position): number {
     const now = new Date();
     const purchaseDate = position.openedAt || position.entryTime || new Date();
     return Math.floor((now.getTime() - purchaseDate.getTime()) / (1000 * 60 * 60 * 24));
   }







   /**
    * 应用风险业务规则（保留原方法以兼容）
    */
   private applyRiskBusinessRules(
    aiAssessment: any,
    position: Position,
    portfolioId: string
  ): any {
    // 基于组合规模调整风险等级
    if (position.marketValue > 1000000) { // 大额持仓
      if (aiAssessment.riskLevel === 'MEDIUM') {
        aiAssessment.riskLevel = 'HIGH';
        aiAssessment.riskFactors.push('大额持仓增加系统性风险');
      }
    }

    // 基于持仓时间调整风险评估
    const holdingDays = this.calculateHoldingDays(position);
    if (holdingDays > 90 && aiAssessment.riskLevel === 'LOW') {
      aiAssessment.riskFactors.push('长期持仓需要关注市场变化');
    }

    return aiAssessment;
  }

  /**
   * 创建风险评估实体 - 重构版
   */
  private createRiskAssessmentEntity(
    request: RiskAssessmentRequest,
    processedAssessment: any,
    existingAssessment: RiskAssessment | null
  ): RiskAssessment {
    this.logger.info('创建风险评估实体', {
      riskScore: processedAssessment.riskScore,
      riskLevel: processedAssessment.riskLevel
    });

    // 确保 riskScore 是有效的数字
    const validRiskScore = typeof processedAssessment.riskScore === 'number' && !isNaN(processedAssessment.riskScore)
      ? processedAssessment.riskScore
      : 50; // 默认中等风险

    this.logger.info('使用的风险评分', { validRiskScore });

    const riskLevel = RiskLevel.fromScore(validRiskScore);

    // 创建完整的 RiskMetrics 对象（在实际实现中应该使用量化计算的结果）
    const riskMetrics = RiskMetrics.create({
      volatility: {
        daily: 0.02,
        weekly: 0.05,
        monthly: 0.10,
        annualized: 0.25
      },
      var: {
        var95_1d: 0.05,
        var95_7d: 0.10,
        var95_30d: 0.15,
        var99_1d: 0.08,
        var99_7d: 0.15,
        var99_30d: 0.20
      },
      cvar: {
        cvar95_1d: 0.06,
        cvar95_7d: 0.12,
        cvar95_30d: 0.18,
        cvar99_1d: 0.10,
        cvar99_7d: 0.18,
        cvar99_30d: 0.25
      },
      maxDrawdown: {
        current: processedAssessment.maxDrawdown || 0.10,
        historical: 0.20,
        duration: 30
      },
      sharpeRatio: {
        daily: 0.05,
        weekly: 0.08,
        monthly: 0.10,
        annualized: 0.15
      },
      beta: 0.8,
      liquidityRisk: {
        bidAskSpread: 0.001,
        marketDepth: 1000000,
        volumeRatio: 0.1,
        liquidityScore: 75
      },
      correlationRisk: {
        marketCorrelation: 0.6,
        liquidationDistance: 0.7,
        signalConsistency: 0.6,
        trendAlignment: 0.7
      }
    });

    return RiskAssessment.create({
      portfolioId: request.portfolioId,
      position: request.position,
      riskLevel,
      riskMetrics,
      keyRiskFactors: this.convertStringArrayToRiskFactors(processedAssessment.riskFactors?.slice(0, 5) || []),
      maxLoss: {
        var95: riskMetrics.var.var95_30d,
        var99: riskMetrics.var.var99_30d,
        expectedShortfall: riskMetrics.cvar.cvar95_30d
      },
      recommendations: processedAssessment.recommendations || [],
      alertThresholds: processedAssessment.alertThresholds || [],
      contingencyPlan: processedAssessment.contingencyPlans || [],
      confidence: 0.8
    });
  }

  /**
   * 格式化风险评估响应
   */
  private formatRiskAssessmentResponse(
    newAssessment: RiskAssessment,
    existingAssessment: RiskAssessment | null,
    riskLevelChanged: boolean
  ): RiskAssessmentResponse {
    return {
      assessment: newAssessment,
      isNewAssessment: true,
      previousAssessment: existingAssessment || undefined,
      riskLevelChanged,
      recommendations: newAssessment.recommendations,
      alerts: this.generateAlerts(newAssessment)
    };
  }



  /**
   * 将字符串数组转换为 RiskFactor 对象数组
   */
  private convertStringArrayToRiskFactors(riskFactorNames: string[]): RiskFactor[] {
    return riskFactorNames.map((name, index) => {
      // 根据风险因子名称推断类型和严重程度
      let type: RiskFactorType = RiskFactorType.MARKET_RISK;
      let severity: RiskFactorSeverity = RiskFactorSeverity.MEDIUM;

      // 简单的名称匹配逻辑
      if (name.includes('波动') || name.includes('价格')) {
        type = RiskFactorType.VOLATILITY_RISK;
      } else if (name.includes('流动性')) {
        type = RiskFactorType.LIQUIDITY_RISK;
      } else if (name.includes('集中') || name.includes('集中度')) {
        type = RiskFactorType.CONCENTRATION_RISK;
      } else if (name.includes('技术') || name.includes('系统')) {
        type = RiskFactorType.TECHNICAL_RISK;
      } else if (name.includes('监管') || name.includes('政策')) {
        type = RiskFactorType.REGULATORY_RISK;
      } else if (name.includes('信用')) {
        type = RiskFactorType.CREDIT_RISK;
      } else if (name.includes('操作')) {
        type = RiskFactorType.OPERATIONAL_RISK;
      }

      // 根据位置推断严重程度（前面的风险因子更严重）
      if (index === 0) {
        severity = RiskFactorSeverity.HIGH;
      } else if (index === 1) {
        severity = RiskFactorSeverity.MEDIUM;
      } else {
        severity = RiskFactorSeverity.LOW;
      }

      return RiskFactor.create(
        name,
        type,
        severity,
        0.5 + (index * 0.1), // impact: 0.5, 0.6, 0.7, ...
        0.3 + (index * 0.1), // probability: 0.3, 0.4, 0.5, ...
        `${name}相关的风险因子`,
        {
          indicators: [],
          mitigationMeasures: [],
          monitoringMetrics: [],
          timeHorizon: 'mediumTerm'
        }
      );
    });
  }

  /**
   * 生成风险评估（兼容方法）
   */
  async generateRiskAssessment(
    accountInfo: any,
    positions: any[],
    riskProfile: any
  ): Promise<RiskAssessment> {
    // 简化实现，使用第一个持仓进行评估
    if (positions.length === 0) {
      throw new Error('No positions provided for risk assessment');
    }

    const position = positions[0];
    const request: RiskAssessmentRequest = {
      portfolioId: accountInfo.portfolioId || 'default',
      userId: accountInfo.userId || 'default',
      position: position,
      symbol: position.symbol || 'UNKNOWN'
    };

    const response = await this.assessRisk(request);
    return response.assessment;
  }

  /**
   * 获取实时风险评估（兼容方法）
   */
  async getRealTimeRiskAssessment(
    accountId: string,
    symbol?: string
  ): Promise<RiskAssessment> {
    const assessment = await this.riskAssessmentRepository.findLatestByPortfolioId(accountId);
    if (assessment) {
      return assessment;
    }

    // 如果没有找到，创建一个默认的评估
    const defaultPosition = Position.create(
      symbol || 'DEFAULT',
      'long' as any,
      1,
      100,
      100,
      new Date(),
      {
        leverage: 1,
        marginUsed: 0,
        liquidationPrice: 0
      }
    );

    const request: RiskAssessmentRequest = {
      portfolioId: accountId,
      userId: 'system',
      position: defaultPosition,
      symbol: symbol || 'DEFAULT'
    };

    const response = await this.assessRisk(request);
    return response.assessment;
  }

  /**
   * 更新风险配置（兼容方法）
   */
  async updateRiskProfile(
    accountId: string,
    riskProfile: any
  ): Promise<void> {
    this.logger.info('更新风险配置', { accountId, riskProfile });
    // 简化实现，仅记录日志
  }

  /**
   * 获取风险历史（兼容方法）
   */
  async getRiskHistory(
    accountId: string,
    startDate: Date,
    endDate: Date
  ): Promise<RiskAssessment[]> {
    this.logger.info('获取风险历史', { accountId, startDate, endDate });
    // 简化实现，返回空数组
    return [];
  }

  /**
   * 获取需要关注的风险评估
   */
  async getAttentionRequiredAssessments(): Promise<RiskAssessment[]> {
    this.logger.info('获取需要关注的风险评估');
    // 简化实现，返回空数组
    return [];
  }



  // ========== BaseApplicationService 抽象方法实现 ==========

  /**
   * 验证请求输入 - 实现基类抽象方法
   */
  protected async validateRequest<T>(request: T, context: RequestContext): Promise<ValidationResult> {
    const errors: string[] = [];
    const req = request as any;

    try {
      if (!req.portfolioId || typeof req.portfolioId !== 'string') {
        errors.push('投资组合ID无效或缺失');
      }
      if (!req.userId || typeof req.userId !== 'string') {
        errors.push('用户ID无效或缺失');
      }
      if (!req.position) {
        errors.push('仓位信息缺失');
      }
      if (!req.symbol || typeof req.symbol !== 'string') {
        errors.push('交易符号无效或缺失');
      }
      if (req.analysisDepth && !['basic', 'standard', 'comprehensive'].includes(req.analysisDepth)) {
        errors.push('分析深度参数无效');
      }
    } catch (error) {
      errors.push(`验证过程中发生错误: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 将请求参数转换为领域值对象 - 实现基类抽象方法
   */
  protected async convertToDomainObjects<T>(request: T, context: RequestContext): Promise<any> {
    const req = request as any;

    return {
      portfolioId: req.portfolioId,
      userId: req.userId,
      position: req.position,
      symbol: req.symbol,
      analysisDepth: req.analysisDepth ?? 'standard',
      forceRecalculation: req.forceRecalculation ?? false
    };
  }

  /**
   * 执行核心业务逻辑 - 实现基类抽象方法
   */
  protected async executeCoreBusinessLogic(domainObjects: any, context: RequestContext): Promise<any> {
    // 检查是否存在有效的评估结果（业务逻辑）
    let existingAssessment: RiskAssessment | null = null;
    if (!domainObjects.forceRecalculation) {
      existingAssessment = await this.riskAssessmentRepository.findLatestByPortfolioId(
        domainObjects.portfolioId
      );

      if (existingAssessment?.isValid()) {
        this.logger.info('使用现有的有效风险评估', {
          assessmentId: existingAssessment.id.toString(),
          assessedAt: existingAssessment.assessedAt
        });

        return {
          assessment: existingAssessment,
          isNewAssessment: false,
          riskLevelChanged: false,
          useExisting: true
        };
      }
    }

    // 通过统一数据处理管道获取风险数据
    const processedData = await this.dataProcessingPipeline.process(
      this.createStrategyInput(domainObjects),
      this.createProcessingContext(domainObjects)
    );

    // 类型断言以访问处理后的数据
    const processedResult = processedData.data as any;

    this.logger.info('数据处理完成', {
      symbol: domainObjects.symbol,
      dataQuality: processedResult?.dataQuality?.overallQuality,
      availableSources: processedResult?.metadata?.sources?.length
    });

    // 获取真实的历史市场数据
    let marketData = processedResult?.marketData;

    if (!marketData || !marketData.priceHistory || marketData.priceHistory.length < 30) {
      this.logger.info('从市场数据服务获取历史数据', {
        symbol: domainObjects.position.symbol,
        currentDataPoints: marketData?.priceHistory?.length || 0
      });

      try {
        // 使用真实的市场数据服务获取历史数据
        const historicalData = await this.realMarketDataService.getKlineData({
          symbol: domainObjects.position.symbol,
          timeframe: '1h', // 1小时K线
          limit: 100   // 获取100个数据点
        });

        if (historicalData && historicalData.length >= 30) {
          marketData = {
            symbol: domainObjects.symbol,
            currentPrice: historicalData[historicalData.length - 1]?.close || 100,
            priceHistory: historicalData,
            volume24h: historicalData.reduce((sum, item) => sum + (item.volume || 0), 0)
          };

          this.logger.info('成功获取历史数据', {
            symbol: domainObjects.position.symbol,
            dataPoints: historicalData.length,
            latestPrice: marketData.currentPrice
          });
        } else {
          this.logger.warn('历史数据不足', {
            symbol: domainObjects.position.symbol,
            availableDataPoints: historicalData?.length || 0,
            requiredMinimum: 30
          });
        }
      } catch (error) {
        this.logger.error('获取历史数据失败', {
          symbol: domainObjects.position.symbol,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // 使用量化模型计算风险指标（核心计算）
    const riskMetrics = await this.riskMetricsCalculator.calculateRiskMetrics(
      domainObjects.position,
      marketData || {
        symbol: domainObjects.symbol,
        currentPrice: 100,
        priceHistory: [],
        volume24h: 1000000
      }
    );

    this.logger.info('量化风险计算完成', {
      var95_1d: riskMetrics.var.var95_1d,
      cvar95_1d: riskMetrics.cvar.cvar95_1d,
      maxDrawdown: riskMetrics.maxDrawdown.current,
      liquidityScore: riskMetrics.liquidityRisk.liquidityScore
    });

    return {
      riskMetrics,
      processedData: processedData.data,
      isNewAssessment: true
    };
  }

  /**
   * 应用业务规则后处理 - 实现基类抽象方法
   */
  protected async applyBusinessRules<T>(
    coreResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<BusinessRuleResult> {
    const warnings: string[] = [];
    const recommendations: string[] = [];

    // 如果使用现有评估，直接返回
    if (coreResult.useExisting) {
      return {
        passed: true,
        violations: warnings
      };
    }

    const req = originalRequest as any;

    // AI辅助分析（可选，基于量化计算结果）
    let aiInsights: any = null;
    if (this.pureRiskEngine) {
      try {
        aiInsights = await this.pureRiskEngine.generatePureRiskAssessment({
          position: req.position,
          marketData: coreResult.marketData,
          tradingSignals: coreResult.tradingSignals,
          trendAnalysis: coreResult.trendAnalysis
        });
        this.logger.info('AI辅助分析完成', {
          aiRiskLevel: aiInsights.riskLevel,
          identifiedFactors: aiInsights.keyRiskFactors?.length
        });
      } catch (error) {
        this.logger.warn('AI辅助分析失败，使用量化结果', {
          error: error instanceof Error ? error.message : String(error)
        });
        warnings.push('AI辅助分析不可用，仅使用量化分析结果');
      }
    }

    // 应用业务规则和后处理（基于量化计算结果）
    const processedAssessment = await this.applyRiskBusinessRulesV2(
      coreResult.riskMetrics,
      aiInsights,
      req.position,
      req.portfolioId
    );

    // 🔥 更新实时风险监控数据
    await this.updateRealTimeRiskMonitoring(
      req.portfolioId,
      processedAssessment,
      coreResult.riskMetrics,
      req.position
    );

    return {
      passed: true,
      violations: warnings
    };
  }

  /**
   * 获取可选的附加数据 - 实现基类抽象方法
   */
  protected async enrichWithAdditionalData<T>(
    processedResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<any> {
    // 如果使用现有评估，直接返回
    if (processedResult.useExisting) {
      return processedResult;
    }

    const enrichedResult = { ...processedResult };

    // 生成预警信息
    try {
      enrichedResult.alerts = this.generateAlerts(processedResult.processedAssessment);
    } catch (error) {
      this.logger.warn('生成预警信息失败', { error: error instanceof Error ? error.message : String(error) });
      enrichedResult.alerts = [];
    }

    return enrichedResult;
  }

  /**
   * 格式化响应DTO - 实现基类抽象方法
   */
  protected async formatResponse<T>(
    enrichedResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<any> {
    const req = originalRequest as any;

    if (enrichedResult.useExisting) {
      return {
        success: true,
        message: '使用现有风险评估',
        timestamp: new Date(),
        requestId: context.requestId,
        data: {
          assessment: enrichedResult.assessment,
          isNewAssessment: enrichedResult.isNewAssessment,
          riskLevelChanged: enrichedResult.riskLevelChanged,
          recommendations: enrichedResult.assessment.recommendations,
          alerts: this.generateAlerts(enrichedResult.assessment)
        }
      };
    }

    return {
      success: true,
      message: '风险评估完成',
      timestamp: new Date(),
      requestId: context.requestId,
      data: {
        assessment: enrichedResult.processedAssessment,
        isNewAssessment: enrichedResult.isNewAssessment,
        riskLevelChanged: true, // 新评估默认为变化
        recommendations: enrichedResult.processedAssessment.recommendations,
        alerts: enrichedResult.alerts || []
      }
    };
  }

  // 健康检查已移至统一健康检查聚合器 - 避免重复实现
  // 使用 RiskManagementHealthProvider 替代

  /**
   * 总结请求内容 - 重写基类方法
   */
  protected summarizeRequest<T>(request: T): any {
    const req = request as any;
    return {
      portfolioId: req.portfolioId,
      symbol: req.symbol,
      analysisDepth: req.analysisDepth,
      forceRecalculation: req.forceRecalculation,
      positionSize: req.position?.size
    };
  }

  /**
   * 总结响应内容 - 重写基类方法
   */
  protected summarizeResponse<T>(response: T): any {
    const resp = response as any;
    return {
      success: resp.success,
      isNewAssessment: resp.data?.isNewAssessment,
      riskLevel: resp.data?.assessment?.riskLevel?.level,
      riskScore: resp.data?.assessment?.riskScore,
      alertsCount: resp.data?.alerts?.length || 0,
      recommendationsCount: resp.data?.recommendations?.length || 0
    };
  }

  /**
   * 计算投资组合置信度
   */
  private calculatePortfolioConfidence(
    portfolioRisk: {
      totalRisk: RiskMetrics;
      individualRisks: Map<string, RiskMetrics>;
      diversificationBenefit: number;
      concentrationRisk: number;
    },
    riskFactors: any[]
  ): number {
    try {
      // 基于数据质量和风险因子的置信度计算
      let confidence = 0.8; // 基础置信度
      
      // 根据个别风险数据的完整性调整
      const dataCompleteness = portfolioRisk.individualRisks.size > 0 ? 1.0 : 0.5;
      confidence *= dataCompleteness;
      
      // 根据多元化收益调整（多元化程度越高，置信度越高）
      const diversificationBonus = Math.min(0.2, portfolioRisk.diversificationBenefit * 0.3);
      confidence += diversificationBonus;
      
      // 根据集中度风险调整（集中度越高，置信度越低）
      const concentrationPenalty = Math.min(0.2, portfolioRisk.concentrationRisk * 0.2);
      confidence -= concentrationPenalty;
      
      // 根据风险因子数量调整
      const riskFactorAdjustment = Math.min(0.1, riskFactors.length * 0.02);
      confidence += riskFactorAdjustment;
      
      // 确保置信度在合理范围内
      return Math.max(0.3, Math.min(0.95, confidence));
    } catch (error) {
      this.logger.error('计算投资组合置信度失败', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      return 0.6; // 返回保守的默认值
    }
  }

  /**
   * 初始化实时风险监控
   */
  private async initializeRealTimeMonitoring(): Promise<void> {
    if (!this.realTimeRiskMonitor) {
      return;
    }

    try {
      await this.realTimeRiskMonitor.startMonitoring();
      this.logger.info('实时风险监控已启动');

      // 设置事件监听器
      this.realTimeRiskMonitor.on('riskAlert', (alert) => {
        this.logger.warn('实时风险警报', {
          alertId: alert.id,
          type: alert.type,
          severity: alert.severity,
          message: alert.message
        });
      });

      this.realTimeRiskMonitor.on('riskLevelChange', (data) => {
        this.logger.info('风险等级变化', data);
      });

    } catch (error) {
      this.logger.error('初始化实时风险监控失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 更新实时风险监控数据
   */
  private async updateRealTimeRiskMonitoring(
    accountId: string,
    assessment: any,
    riskMetrics: any,
    position: any
  ): Promise<void> {
    if (!this.realTimeRiskMonitor) {
      return;
    }

    try {
      // 确保账户在监控中
      await this.realTimeRiskMonitor.addAccountToMonitoring(accountId);

      // 构建实时风险数据
      const realTimeRiskData: Partial<RealTimeRiskData> = {
        symbol: position.symbol,
        riskScore: assessment.riskLevel?.score || riskMetrics.compositeRiskScore || 0,
        riskLevel: this.mapToRealTimeRiskLevel(assessment.riskLevel?.level || 'MEDIUM'),
        metrics: {
          var95: riskMetrics.var95 || 0,
          volatility: riskMetrics.volatility || 0,
          drawdown: riskMetrics.maxDrawdown || 0,
          exposure: this.calculateExposure(position),
          leverage: position.leverage || 1,
          liquidityScore: riskMetrics.liquidityScore || 100
        },
        alerts: this.convertToRiskAlerts(assessment.alerts || [])
      };

      // 更新实时监控数据
      await this.realTimeRiskMonitor.updateAccountRisk(accountId, realTimeRiskData);

      this.logger.debug('实时风险监控数据已更新', {
        accountId,
        riskScore: realTimeRiskData.riskScore,
        riskLevel: realTimeRiskData.riskLevel
      });

    } catch (error) {
      this.logger.error('更新实时风险监控数据失败', {
        accountId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 映射风险等级到实时监控格式
   */
  private mapToRealTimeRiskLevel(level: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | 'EMERGENCY' {
    switch (level.toUpperCase()) {
      case 'LOW': return 'LOW';
      case 'MEDIUM': return 'MEDIUM';
      case 'HIGH': return 'HIGH';
      case 'CRITICAL': return 'CRITICAL';
      case 'EMERGENCY': return 'EMERGENCY';
      default: return 'MEDIUM';
    }
  }

  /**
   * 计算敞口比例
   */
  private calculateExposure(position: any): number {
    // 简化的敞口计算：仓位价值 / 账户总资产
    const positionValue = Math.abs(position.quantity * (position.averagePrice || position.currentPrice || 0));
    const accountValue = position.accountValue || 100000; // 默认账户价值
    return Math.min(1, positionValue / accountValue);
  }

  /**
   * 转换警报格式
   */
  private convertToRiskAlerts(alerts: any[]): any[] {
    return alerts.map((alert, index) => ({
      id: `alert-${Date.now()}-${index}`,
      type: 'THRESHOLD_BREACH',
      severity: alert.severity || 'MEDIUM',
      message: alert.message || alert.description || '风险警报',
      value: alert.value || 0,
      threshold: alert.threshold || 0,
      timestamp: new Date(),
      acknowledged: false
    }));
  }
}
