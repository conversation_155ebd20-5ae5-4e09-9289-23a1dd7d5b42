/**
 * 风险指标计算服务
 * 专注于风险领域特有的指标计算，不包含通用金融分析功能
 * 通用功能（如波动率、相关性、Beta、夏普比率）由核心分析服务提供
 */

import 'reflect-metadata';
import { injectable, inject, optional } from 'inversify';
import { Logger } from 'winston';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { IRiskConfigService } from '../../../../shared/infrastructure/config/risk-config.service';
import { IFinancialMetricsService } from '../../../../shared/infrastructure/analysis/interfaces/IFinancialMetricsService';

// 核心分析服务接口导入
import { IDynamicWeightingService } from '../../../../shared/infrastructure/analysis/interfaces/IDynamicWeightingService';
import { IPatternRecognitionService } from '../../../../shared/infrastructure/analysis/interfaces/IPatternRecognitionService';
import { IMultiTimeframeService } from '../../../../shared/infrastructure/analysis/interfaces/IMultiTimeframeService';
// 类型导入
import { RiskMetrics } from '../../domain/value-objects/risk-metrics';
import { RiskFactor, RiskFactorType, RiskFactorSeverity } from '../../domain/value-objects/risk-factor';
import { Position, PositionType } from '../../domain/value-objects/position';
import { IRiskMetricsCalculatorService } from './risk-metrics-calculator-service.interface';
// IRiskCalculationEngine接口已被移除，相关类型定义移至此文件
export interface MarketDataInput {
  symbol: string;
  currentPrice: number;
  priceHistory: PricePoint[];
  volume24h: number;
  marketCap?: number;
  bidAskSpread?: number;
  orderBookDepth?: {
    bids: Array<{ price: number; quantity: number }>;
    asks: Array<{ price: number; quantity: number }>;
  };
}

// 类型定义和接口

export interface PricePoint {
  timestamp: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface RiskCalculationConfig {
  confidenceLevels: number[];
  timeHorizons: number[];
  volatilityWindow: number;
  correlationWindow: number;
  liquidityThresholds: {
    high: number;
    medium: number;
    low: number;
  };
  riskFreeRate: number;
}

export enum VaRMethod {
  HISTORICAL_SIMULATION = 'historicalSimulation',
  PARAMETRIC = 'parametric',
  MONTE_CARLO = 'monteCarlo'
}

export interface StressTestScenario {
  name: string;
  priceShock: number;
  duration: number;
  description?: string;
}

export interface StressTestResult {
  scenario: string;
  potentialLoss: number;
  lossPercentage: number;
  timeToRecover: number;
  riskMetrics: RiskMetrics;
  recommendations: string[];
}

// 错位的方法定义已移除，这些方法应该在类内部定义
// 接口定义已移除，应该从独立的接口文件中导入

@injectable()
export class RiskMetricsCalculatorService implements IRiskMetricsCalculatorService {


  private readonly defaultConfig: RiskCalculationConfig = {
    confidenceLevels: [0.95, 0.99],
    timeHorizons: [1, 7, 30],
    volatilityWindow: 30,
    correlationWindow: 60,
    liquidityThresholds: {
      high: 80,
      medium: 50,
      low: 20
    },
    riskFreeRate: 0.02 // 2% 年化无风险利率
  };

  constructor(
    @inject(TYPES.Logger) private readonly logger: Logger,
    @inject(TYPES.Shared.RiskConfigService) private readonly configService: IRiskConfigService,
    @inject(TYPES.Shared.FinancialMetricsService) private readonly financialMetricsService: IFinancialMetricsService,
    @inject(TYPES.Shared.DynamicWeightingService) @optional() private readonly dynamicWeightingService?: IDynamicWeightingService,
    @inject(TYPES.Shared.PatternRecognitionService) @optional() private readonly patternRecognitionService?: IPatternRecognitionService,
    @inject(TYPES.Shared.MultiTimeframeService) @optional() private readonly multiTimeframeService?: IMultiTimeframeService
  ) {}

  /**
   * 计算完整的风险指标 - IRiskCalculationEngine接口实现
   */
  async calculateRiskMetrics(
    position: Position,
    marketData: MarketDataInput,
    config?: Partial<RiskCalculationConfig>
  ): Promise<import('../../domain/value-objects/risk-metrics').RiskMetrics> {
    try {
      this.logger.info('开始计算风险指标', {
        symbol: position.symbol,
        quantity: position.quantity,
        currentPrice: position.currentPrice
      });

      const finalConfig = { ...this.defaultConfig, ...config };

      // 提取价格数据用于核心分析服务
      const prices = marketData.priceHistory.map(p => p.close);
      const returns = await this.financialMetricsService.calculateReturns(prices, 'log');
      // 使用市场基准数据或默认市场收益率
      const marketPrices = (marketData as any).marketBenchmark?.map((p: any) => p.close) || prices;
      const marketReturns = await this.financialMetricsService.calculateReturns(marketPrices, 'log');

      // 并行计算各项风险指标
      const [
        volatility,
        varMetrics,
        cvarMetrics,
        maxDrawdownResult,
        sharpeRatio,
        beta,
        liquidityRisk
      ] = await Promise.all([
        this.financialMetricsService.calculateVolatility(prices),
        this.calculateVaR(position, marketData.priceHistory, finalConfig.confidenceLevels, finalConfig.timeHorizons, VaRMethod.HISTORICAL_SIMULATION),
        this.calculateCVaR(position, marketData.priceHistory, finalConfig.confidenceLevels, finalConfig.timeHorizons),
        this.calculateMaxDrawdown(marketData.priceHistory),
        this.financialMetricsService.calculateSharpeRatio(returns, finalConfig.riskFreeRate),
        this.financialMetricsService.calculateBeta(returns, marketReturns),
        this.calculateLiquidityRisk(marketData)
      ]);

      // 计算相关性风险
      const marketCorrelation = await this.financialMetricsService.calculateCorrelation(returns, marketReturns);
      
      const maxDrawdown = {
        current: maxDrawdownResult.current,
        historical: maxDrawdownResult.historical,
        duration: maxDrawdownResult.duration
      };
      
      // 计算风险因子
      const riskFactors = await this.identifyRiskFactors(position, marketData, {
        volatility: { annualized: volatility },
        var: varMetrics,
        maxDrawdown,
        liquidityRisk
      } as any);

      const riskMetrics = RiskMetrics.create({
        volatility: {
          daily: volatility,
          weekly: volatility * Math.sqrt(7),
          monthly: volatility * Math.sqrt(30),
          annualized: volatility * Math.sqrt(365)
        },
        var: varMetrics,
        cvar: cvarMetrics,
        maxDrawdown,
        sharpeRatio: {
          daily: sharpeRatio,
          weekly: sharpeRatio * Math.sqrt(7),
          monthly: sharpeRatio * Math.sqrt(30),
          annualized: sharpeRatio * Math.sqrt(365)
        },
        beta,
        liquidityRisk,
        correlationRisk: {
          marketCorrelation,
          liquidationDistance: Math.abs(position.currentPrice - (position.averagePrice || position.currentPrice)) / position.currentPrice,
          signalConsistency: this.calculateSignalConsistency(position, undefined),
          trendAlignment: this.calculateTrendAlignment(position, undefined)
        }
      });

      this.logger.info('风险指标计算完成', {
        compositeScore: riskMetrics.getCompositeRiskScore(),
        volatility: volatility * Math.sqrt(365),
        var95_30d: varMetrics.var95_30d
      });

      return riskMetrics;
    } catch (error) {
      this.logger.error('风险指标计算失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 计算波动率指标 - IRiskCalculationEngine接口实现
   */
  async calculateVolatility(
    priceHistory: PricePoint[],
    windows: number[]
  ): Promise<{
    daily: number;
    weekly: number;
    monthly: number;
    annualized: number;
  }> {
    const prices = priceHistory.map(p => p.close);
    const volatility = await this.financialMetricsService.calculateVolatility(prices);
    
    return {
      daily: volatility,
      weekly: volatility * Math.sqrt(7),
      monthly: volatility * Math.sqrt(30),
      annualized: volatility * Math.sqrt(365)
    };
  }

  /**
   * 计算夏普比率 - IRiskCalculationEngine接口实现
   */
  async calculateSharpeRatio(
    returns: number[],
    riskFreeRate: number,
    periods: number[]
  ): Promise<{
    daily: number;
    weekly: number;
    monthly: number;
    annualized: number;
  }> {
    const sharpeRatio = await this.financialMetricsService.calculateSharpeRatio(returns, riskFreeRate);
    
    return {
      daily: sharpeRatio,
      weekly: sharpeRatio * Math.sqrt(7),
      monthly: sharpeRatio * Math.sqrt(30),
      annualized: sharpeRatio * Math.sqrt(365)
    };
  }

  /**
   * 计算Beta系数 - IRiskCalculationEngine接口实现
   */
  async calculateBeta(
    assetReturns: number[],
    marketReturns: number[]
  ): Promise<number> {
    return await this.financialMetricsService.calculateBeta(assetReturns, marketReturns);
  }

  /**
   * 计算相关性风险 - IRiskCalculationEngine接口实现
   */
  async calculateCorrelationRisk(
    assetReturns: number[],
    marketReturns: number[],
    portfolioWeights?: number[],
    position?: Position,
    tradingSignals?: any,
    trendAnalysis?: any
  ): Promise<{
    marketCorrelation: number;
    liquidationDistance: number;
    signalConsistency: number;
    trendAlignment: number;
  }> {
    const correlation = await this.financialMetricsService.calculateCorrelation(assetReturns, marketReturns);
    
    const liquidationDistance = position ? 
      Math.abs(position.currentPrice - (position.averagePrice || position.currentPrice)) / position.currentPrice : 0;
    
    const signalConsistency = this.calculateSignalConsistency(position, tradingSignals);
    const trendAlignment = this.calculateTrendAlignment(position, trendAnalysis);
    
    return {
      marketCorrelation: correlation,
      liquidationDistance,
      signalConsistency,
      trendAlignment
    };
  }

  /**
   * 计算组合风险指标 - IRiskCalculationEngine接口实现
   */
  async calculatePortfolioRisk(
    positions: Position[],
    marketDataMap: Map<string, MarketDataInput>,
    correlationMatrix?: number[][]
  ): Promise<{
    totalRisk: RiskMetrics;
    individualRisks: Map<string, RiskMetrics>;
    diversificationBenefit: number;
    concentrationRisk: number;
  }> {
    try {
      this.logger.info('开始计算投资组合风险', {
        positionCount: positions.length,
        hasCorrelationMatrix: !!correlationMatrix
      });

      const individualRisks = new Map<string, RiskMetrics>();
      
      // 计算每个仓位的风险指标
      for (const position of positions) {
        const marketData = marketDataMap.get(position.symbol);
        if (marketData) {
          const riskMetrics = await this.calculateRiskMetrics(position, marketData);
          individualRisks.set(position.symbol, riskMetrics);
        }
      }
      
      if (individualRisks.size === 0) {
        throw new Error('无法计算投资组合风险：没有有效的个别风险数据');
      }

      // 计算投资组合权重
      const totalValue = positions.reduce((sum, p) => sum + p.quantity * p.currentPrice, 0);
      const weights = positions.map(p => (p.quantity * p.currentPrice) / totalValue);
      
      // 计算集中度风险（赫芬达尔指数）
      const concentrationRisk = weights.reduce((sum, w) => sum + w * w, 0);
      
      // 计算多元化收益
      const diversificationBenefit = this.calculateDiversificationBenefit(
        weights, 
        correlationMatrix, 
        Array.from(individualRisks.values())
      );
      
      // 计算组合总风险指标
      const totalRisk = await this.calculateAggregatedRisk(
        positions,
        individualRisks,
        weights
      );
      
      this.logger.info('投资组合风险计算完成', {
        totalValue,
        concentrationRisk,
        diversificationBenefit,
        individualRiskCount: individualRisks.size
      });
      
      return {
        totalRisk,
        individualRisks,
        diversificationBenefit,
        concentrationRisk
      };
    } catch (error) {
      this.logger.error('投资组合风险计算失败', { 
        error: error instanceof Error ? error.message : String(error),
        positionCount: positions.length
      });
      throw error;
    }
  }

  async calculateVaR(
    position: Position,
    priceHistory: PricePoint[],
    confidenceLevels: number[],
    timeHorizons: number[],
    method: VaRMethod
  ): Promise<{
    var95_1d: number;
    var99_1d: number;
    var95_7d: number;
    var99_7d: number;
    var95_30d: number;
    var99_30d: number;
  }> {
    try {
      // 使用FinancialMetricsService计算收益率
      const returns = await this.financialMetricsService.calculateReturns(
        priceHistory.map(p => p.close),
        'log'
      );

      if (returns.length < 30) {
        this.logger.warn('历史数据不足，无法计算可靠的VaR值', {
          availableReturns: returns.length,
          requiredMinimum: 30
        });
        throw new Error(`历史数据不足：需要至少30个数据点，当前只有${returns.length}个`);
      }

      const varResults: Record<string, number> = {};

      const config = this.configService.getRiskCalculationConfig();
      const actualConfidenceLevels = confidenceLevels.length > 0 ? confidenceLevels : config.confidenceLevels;
      const actualTimeHorizons = timeHorizons.length > 0 ? timeHorizons : config.timeHorizons;
      
      for (const confidence of actualConfidenceLevels) {
        for (const horizon of actualTimeHorizons) {
          let varValue: number;

          switch (method) {
            case VaRMethod.HISTORICAL_SIMULATION:
              varValue = this.calculateHistoricalVaR(returns, confidence, horizon);
              break;
            case VaRMethod.PARAMETRIC:
              varValue = await this.calculateParametricVaR(returns, confidence, horizon);
              break;
            case VaRMethod.MONTE_CARLO:
              varValue = await this.calculateMonteCarloVaR(returns, confidence, horizon);
              break;
            default:
              varValue = this.calculateHistoricalVaR(returns, confidence, horizon);
          }

          const key = `var${Math.round(confidence * 100)}_${horizon}d`;
          varResults[key] = Math.abs(varValue); // VaR通常表示为正值
        }
      }

      return varResults as any;
    } catch (error) {
      this.logger.error('VaR计算失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async calculateMaxDrawdown(
    priceHistory: PricePoint[]
  ): Promise<{
    current: number;
    historical: number;
    duration: number;
  }> {
    try {
      const prices = priceHistory.map(p => p.close);
      const result = await this.financialMetricsService.calculateMaxDrawdown(prices);
      
      return {
        current: result.currentDrawdown,
        historical: result.maxDrawdown,
        duration: result.drawdownDuration
      };
    } catch (error) {
      this.logger.error('计算最大回撤失败', { error, priceHistoryLength: priceHistory.length });
      throw error;
    }
  }

  async calculateCVaR(
    position: Position,
    priceHistory: PricePoint[],
    confidenceLevels: number[],
    timeHorizons: number[]
  ): Promise<{
    cvar95_1d: number;
    cvar99_1d: number;
    cvar95_7d: number;
    cvar99_7d: number;
    cvar95_30d: number;
    cvar99_30d: number;
  }> {
    try {
      // 使用FinancialMetricsService计算收益率
      const returns = await this.financialMetricsService.calculateReturns(
        priceHistory.map(p => p.close),
        'log'
      );

      if (returns.length === 0) {
        this.logger.warn('历史数据不足，无法计算CVaR值');
        throw new Error('历史数据不足：无法计算CVaR，需要有效的收益率数据');
      }

      if (returns.length < 30) {
        this.logger.warn('历史数据较少，CVaR计算可能不够准确', {
          availableReturns: returns.length,
          recommendedMinimum: 30
        });
      }

      const cvarResults: Record<string, number> = {};

      for (const confidence of confidenceLevels) {
        for (const horizon of timeHorizons) {
          // 计算对应的VaR
          const varValue = this.calculateHistoricalVaR(returns, confidence, horizon);
          
          // 计算CVaR (超过VaR的损失的期望值)
          const scaledReturns = returns.map(r => r * Math.sqrt(horizon));
          const tailLosses = scaledReturns.filter(r => r <= -Math.abs(varValue));
          
          const cvarValue = tailLosses.length > 0 
            ? Math.abs(tailLosses.reduce((sum, loss) => sum + loss, 0) / tailLosses.length)
            : Math.abs(varValue);

          const key = `cvar${Math.round(confidence * 100)}_${horizon}d`;
          cvarResults[key] = cvarValue;
        }
      }

      return cvarResults as any;
    } catch (error) {
      this.logger.error('CVaR计算失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }



  async calculateLiquidityRisk(
    marketData: MarketDataInput
  ): Promise<{
    bidAskSpread: number;
    marketDepth: number;
    volumeRatio: number;
    liquidityScore: number;
  }> {
    try {
      // 买卖价差
      const bidAskSpread = marketData.bidAskSpread || 0;

      // 市场深度 (订单簿深度)
      let marketDepth = 0;
      if (marketData.orderBookDepth) {
        const totalBids = marketData.orderBookDepth.bids.reduce((sum, bid) => sum + bid.quantity, 0);
        const totalAsks = marketData.orderBookDepth.asks.reduce((sum, ask) => sum + ask.quantity, 0);
        marketDepth = (totalBids + totalAsks) / 2;
      }

      // 成交量比率 (24小时成交量 / 市值)
      const volumeRatio = marketData.marketCap ? marketData.volume24h / marketData.marketCap : 0;

      // 流动性评分 (0-100)
      let liquidityScore = 50; // 基础分数

      // 根据买卖价差调整
      if (bidAskSpread < 0.001) liquidityScore += 20;
      else if (bidAskSpread < 0.005) liquidityScore += 10;
      else if (bidAskSpread > 0.02) liquidityScore -= 20;

      // 根据成交量比率调整
      if (volumeRatio > 0.1) liquidityScore += 20;
      else if (volumeRatio > 0.05) liquidityScore += 10;
      else if (volumeRatio < 0.01) liquidityScore -= 20;

      // 根据市场深度调整
      if (marketDepth > 1000000) liquidityScore += 10;
      else if (marketDepth < 100000) liquidityScore -= 10;

      liquidityScore = Math.max(0, Math.min(100, liquidityScore));

      return {
        bidAskSpread,
        marketDepth,
        volumeRatio,
        liquidityScore
      };
    } catch (error) {
      this.logger.error('流动性风险计算失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async identifyRiskFactors(
    position: Position,
    marketData: MarketDataInput,
    riskMetrics: RiskMetrics
  ): Promise<RiskFactor[]> {
    try {
      const riskFactors: RiskFactor[] = [];

      // 市场风险因子
      if (riskMetrics.volatility.annualized > 0.5) {
        riskFactors.push(RiskFactor.create(
          '高波动率风险',
          RiskFactorType.VOLATILITY_RISK,
          riskMetrics.volatility.annualized > 0.8 ? RiskFactorSeverity.CRITICAL : RiskFactorSeverity.HIGH,
          Math.min(riskMetrics.volatility.annualized, 1.0), // 确保影响程度在0-1之间
          0.8,
          `当前年化波动率为${(riskMetrics.volatility.annualized * 100).toFixed(1)}%，远高于正常水平`,
          {
            indicators: ['年化波动率', '日波动率'],
            mitigationMeasures: ['降低仓位', '设置止损', '分散投资'],
            monitoringMetrics: ['实时波动率', '价格变动幅度'],
            timeHorizon: 'shortTerm'
          }
        ));
      }

      // VaR风险因子
      if (riskMetrics.var.var95_30d > 0.2) {
        riskFactors.push(RiskFactor.create(
          '高VaR风险',
          RiskFactorType.MARKET_RISK,
          riskMetrics.var.var95_30d > 0.3 ? RiskFactorSeverity.CRITICAL : RiskFactorSeverity.HIGH,
          riskMetrics.var.var95_30d,
          0.9,
          `30天95% VaR为${(riskMetrics.var.var95_30d * 100).toFixed(1)}%，潜在损失较大`,
          {
            indicators: ['VaR指标', '历史价格波动'],
            mitigationMeasures: ['降低杠杆', '设置严格止损', '分批建仓'],
            monitoringMetrics: ['VaR变化', '价格趋势'],
            timeHorizon: 'mediumTerm'
          }
        ));
      }

      // 流动性风险因子
      if (riskMetrics.liquidityRisk.liquidityScore < 50) {
        riskFactors.push(RiskFactor.create(
          '流动性不足风险',
          RiskFactorType.LIQUIDITY_RISK,
          riskMetrics.liquidityRisk.liquidityScore < 30 ? RiskFactorSeverity.HIGH : RiskFactorSeverity.MEDIUM,
          1 - riskMetrics.liquidityRisk.liquidityScore / 100,
          0.6,
          `流动性评分仅为${riskMetrics.liquidityRisk.liquidityScore.toFixed(0)}分，可能面临变现困难`,
          {
            indicators: ['买卖价差', '订单簿深度', '成交量'],
            mitigationMeasures: ['避免大额交易', '分批平仓', '选择高流动性时段'],
            monitoringMetrics: ['实时买卖价差', '成交量变化'],
            timeHorizon: 'immediate'
          }
        ));
      }

      // 回撤风险因子
      if (riskMetrics.maxDrawdown.current > 0.15) {
        riskFactors.push(RiskFactor.create(
          '大幅回撤风险',
          RiskFactorType.MARKET_RISK,
          riskMetrics.maxDrawdown.current > 0.25 ? RiskFactorSeverity.CRITICAL : RiskFactorSeverity.HIGH,
          riskMetrics.maxDrawdown.current,
          0.7,
          `当前回撤达到${(riskMetrics.maxDrawdown.current * 100).toFixed(1)}%，需要密切关注`,
          {
            indicators: ['最大回撤', '当前回撤', '回撤持续时间'],
            mitigationMeasures: ['考虑止损', '降低仓位', '等待反弹'],
            monitoringMetrics: ['回撤深度', '价格恢复情况'],
            timeHorizon: 'shortTerm'
          }
        ));
      }

      // 按风险评分排序
      return riskFactors.sort((a, b) => b.getRiskScore() - a.getRiskScore());
    } catch (error) {
      this.logger.error('风险因子识别失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async performStressTest(
    position: Position,
    scenarios: StressTestScenario[]
  ): Promise<StressTestResult[]> {
    try {
      this.logger.info('开始压力测试', {
        symbol: position.symbol,
        scenarioCount: scenarios.length
      });

      const results: StressTestResult[] = [];

      for (const scenario of scenarios) {
        // 计算价格冲击后的损失
        const shockedPrice = position.currentPrice * (1 + scenario.priceShock);
        const newPosition = position.updatePrice(shockedPrice);

        const potentialLoss = position.unrealizedPnL - newPosition.unrealizedPnL;
        const lossPercentage = potentialLoss / position.costBasis;

        // 估算恢复时间 (简化计算)
        const timeToRecover = Math.abs(scenario.priceShock) * 30; // 假设每1%需要30天恢复

        // 创建压力测试后的风险指标 (简化)
        const stressedRiskMetrics = RiskMetrics.create({
          volatility: {
            daily: 0.05,
            weekly: 0.05 * Math.sqrt(7),
            monthly: 0.05 * Math.sqrt(30),
            annualized: 0.05 * Math.sqrt(252)
          },
          var: {
            var95_1d: Math.abs(scenario.priceShock) * 0.8,
            var99_1d: Math.abs(scenario.priceShock) * 0.9,
            var95_7d: Math.abs(scenario.priceShock) * 0.8 * Math.sqrt(7),
            var99_7d: Math.abs(scenario.priceShock) * 0.9 * Math.sqrt(7),
            var95_30d: Math.abs(scenario.priceShock) * 0.8 * Math.sqrt(30),
            var99_30d: Math.abs(scenario.priceShock) * 0.9 * Math.sqrt(30)
          },
          cvar: {
            cvar95_1d: Math.abs(scenario.priceShock) * 0.9,
            cvar99_1d: Math.abs(scenario.priceShock) * 0.95,
            cvar95_7d: Math.abs(scenario.priceShock) * 0.9 * Math.sqrt(7),
            cvar99_7d: Math.abs(scenario.priceShock) * 0.95 * Math.sqrt(7),
            cvar95_30d: Math.abs(scenario.priceShock) * 0.9 * Math.sqrt(30),
            cvar99_30d: Math.abs(scenario.priceShock) * 0.95 * Math.sqrt(30)
          },
          maxDrawdown: {
            current: Math.abs(scenario.priceShock),
            historical: Math.abs(scenario.priceShock),
            duration: scenario.duration
          },
          sharpeRatio: { daily: -1, weekly: -1, monthly: -1, annualized: -1 },
          beta: 1.2,
          liquidityRisk: {
            bidAskSpread: 0.01,
            marketDepth: 100000,
            volumeRatio: 0.05,
            liquidityScore: 40
          },
          correlationRisk: {
            marketCorrelation: 0.8,
            liquidationDistance: 0.3, // 高风险场景下的强平距离
            signalConsistency: 0.2,    // 低信号一致性
            trendAlignment: 0.3        // 低趋势一致性
          }
        });

        const recommendations = this.generateStressTestRecommendations(scenario, lossPercentage);

        results.push({
          scenario: scenario.name,
          potentialLoss,
          lossPercentage,
          timeToRecover,
          riskMetrics: stressedRiskMetrics,
          recommendations
        });
      }

      return results.sort((a, b) => Math.abs(b.lossPercentage) - Math.abs(a.lossPercentage));
    } catch (error) {
      this.logger.error('压力测试失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  calculateLiquidationDistance(position: Position): number {
    try {
      let liquidationDistance = 0.5; // 默认中性值
      if (position?.liquidationPrice && position?.currentPrice) {
        // 计算原始距离百分比
        const distance = position.type === PositionType.LONG
          ? (position.currentPrice - position.liquidationPrice) / position.currentPrice
          : (position.liquidationPrice - position.currentPrice) / position.currentPrice;

        // 使用Position类的isNearLiquidation方法进行多级风险评估
        const isCritical = position.isNearLiquidation && position.isNearLiquidation(0.05); // 5%以内
        const isHigh = position.isNearLiquidation && position.isNearLiquidation(0.1);      // 10%以内
        const isMedium = position.isNearLiquidation && position.isNearLiquidation(0.2);    // 20%以内

        // 根据风险级别调整liquidationDistance评分
        if (isCritical) {
          liquidationDistance = Math.max(0, distance * 10); // 极高风险：0-0.5
        } else if (isHigh) {
          liquidationDistance = 0.2 + Math.max(0, (distance - 0.05) * 6); // 高风险：0.2-0.5
        } else if (isMedium) {
          liquidationDistance = 0.5 + Math.max(0, (distance - 0.1) * 5); // 中风险：0.5-1.0
        } else {
          liquidationDistance = Math.min(1.0, 0.8 + distance * 2); // 低风险：0.8-1.0
        }

        // 确保在0-1范围内
        liquidationDistance = Math.max(0, Math.min(1, liquidationDistance));

        this.logger.debug('增强强平距离风险计算', {
          currentPrice: position.currentPrice,
          liquidationPrice: position.liquidationPrice,
          rawDistance: distance,
          isCritical,
          isHigh,
          isMedium,
          liquidationDistance: liquidationDistance
        });
      }
      return liquidationDistance;
    } catch (error) {
      this.logger.warn('强平距离计算失败', { error });
      return 0.5; // 返回中性值
    }
  }

  calculateSignalConsistency(position: Position, tradingSignals?: any): number {
    try {
      // 如果没有信号数据，返回中性值
      if (!tradingSignals || !tradingSignals.signal) {
        return 0.5;
      }

      const positionType = position.type;
      const signalType = tradingSignals.signal;
      const signalStrength = tradingSignals.strength?.value || 5; // 1-10
      const signalConfidence = tradingSignals.confidence?.value || 0.5; // 0-1

      let consistencyScore = 0.5; // 默认中性

      // 判断持仓与信号的一致性
      if (positionType === PositionType.LONG) {
        if (signalType === 'STRONG_BUY' || signalType === 'BUY') {
          // 持多仓且信号为买入 - 高度一致
          consistencyScore = 0.8 + (signalStrength / 10) * 0.2; // 0.8-1.0
        } else if (signalType === 'HOLD') {
          // 持多仓且信号为持有 - 中等一致
          consistencyScore = 0.6 + (signalStrength / 10) * 0.2; // 0.6-0.8
        } else if (signalType === 'SELL' || signalType === 'STRONG_SELL') {
          // 持多仓但信号为卖出 - 背离风险
          consistencyScore = 0.3 - (signalStrength / 10) * 0.3; // 0.0-0.3
        }
      } else if (positionType === PositionType.SHORT) {
        if (signalType === 'STRONG_SELL' || signalType === 'SELL') {
          // 持空仓且信号为卖出 - 高度一致
          consistencyScore = 0.8 + (signalStrength / 10) * 0.2; // 0.8-1.0
        } else if (signalType === 'HOLD') {
          // 持空仓且信号为持有 - 中等一致
          consistencyScore = 0.6 + (signalStrength / 10) * 0.2; // 0.6-0.8
        } else if (signalType === 'BUY' || signalType === 'STRONG_BUY') {
          // 持空仓但信号为买入 - 背离风险
          consistencyScore = 0.3 - (signalStrength / 10) * 0.3; // 0.0-0.3
        }
      }

      // 根据信号置信度调整一致性评分
      const confidenceAdjustment = (signalConfidence - 0.5) * 0.2; // -0.1 to +0.1
      consistencyScore = Math.max(0, Math.min(1, consistencyScore + confidenceAdjustment));

      return consistencyScore;
    } catch (error) {
      this.logger.warn('信号一致性计算失败', { error });
      return 0.5; // 返回中性值
    }
  }

  calculateTrendAlignment(position: Position, trendAnalysis?: any): number {
    try {
      // 如果没有趋势数据，返回中性值
      if (!trendAnalysis || !trendAnalysis.trend) {
        return 0.5;
      }

      const positionType = position.type;
      const trendDirection = trendAnalysis.trend;
      const trendStrength = trendAnalysis.strength || 5; // 1-10
      const trendConfidence = trendAnalysis.confidence || 0.5; // 0-1

      let alignmentScore = 0.5; // 默认中性

      // 判断持仓与趋势的一致性
      if (positionType === PositionType.LONG) {
        if (trendDirection === 'UPTREND' || trendDirection === 'BULLISH') {
          // 持多仓且趋势向上 - 高度一致
          alignmentScore = 0.8 + (trendStrength / 10) * 0.2; // 0.8-1.0
        } else if (trendDirection === 'SIDEWAYS' || trendDirection === 'NEUTRAL') {
          // 持多仓且趋势震荡 - 中等一致
          alignmentScore = 0.5 + (trendStrength / 10) * 0.1; // 0.5-0.6
        } else if (trendDirection === 'DOWNTREND' || trendDirection === 'BEARISH') {
          // 持多仓但趋势向下 - 背离风险
          alignmentScore = 0.3 - (trendStrength / 10) * 0.3; // 0.0-0.3
        }
      } else if (positionType === PositionType.SHORT) {
        if (trendDirection === 'DOWNTREND' || trendDirection === 'BEARISH') {
          // 持空仓且趋势向下 - 高度一致
          alignmentScore = 0.8 + (trendStrength / 10) * 0.2; // 0.8-1.0
        } else if (trendDirection === 'SIDEWAYS' || trendDirection === 'NEUTRAL') {
          // 持空仓且趋势震荡 - 中等一致
          alignmentScore = 0.5 + (trendStrength / 10) * 0.1; // 0.5-0.6
        } else if (trendDirection === 'UPTREND' || trendDirection === 'BULLISH') {
          // 持空仓但趋势向上 - 背离风险
          alignmentScore = 0.3 - (trendStrength / 10) * 0.3; // 0.0-0.3
        }
      }

      // 根据趋势置信度调整一致性评分
      const confidenceAdjustment = (trendConfidence - 0.5) * 0.2; // -0.1 to +0.1
      alignmentScore = Math.max(0, Math.min(1, alignmentScore + confidenceAdjustment));

      return alignmentScore;
    } catch (error) {
      this.logger.warn('趋势一致性计算失败', { error });
      return 0.5; // 返回中性值
    }
  }

  // ============================================================================
  // 重复实现清理完成 - 所有通用计算方法已移除
  // ============================================================================
  // 
  // 已移除的重复方法（应使用FinancialMetricsService）：
  // - getReturns → FinancialMetricsService.calculateReturns
  // - generateDefaultReturns → FinancialMetricsService.generateDefaultReturns
  // - calculateStandardDeviation → FinancialMetricsService.calculateStandardDeviation
  // - calculateHistoricalVaR → 保留在此服务中（风险专用）
  // - calculateParametricVaR → 保留在此服务中（风险专用）
  // - calculateMonteCarloVaR → 保留在此服务中（风险专用）
  // - generateStressTestRecommendations → 保留在此服务中（风险专用）
  //
  // RiskMetricsCalculatorService现在专注于:
  // 1. 风险领域特有的指标计算
  // 2. VaR/CVaR等风险度量
  // 3. 风险因子识别
  // 4. 压力测试和建议生成
  // ============================================================================

  // 风险专用的私有方法（保留）
  private calculateHistoricalVaR(returns: number[], confidence: number, horizon: number): number {
    const scaledReturns = returns.map(r => r * Math.sqrt(horizon));
    const sortedReturns = scaledReturns.sort((a, b) => a - b);
    const index = Math.floor((1 - confidence) * sortedReturns.length);
    return sortedReturns[index] || 0;
  }

  private async calculateParametricVaR(returns: number[], confidence: number, horizon: number): Promise<number> {
    // 使用FinancialMetricsService获取统计数据
    const mean = returns.reduce((sum, val) => sum + val, 0) / returns.length;
    const std = await this.financialMetricsService.calculateStandardDeviation(returns);
    
    const zScore = confidence === 0.95 ? -1.645 : -2.326;
    return (mean + zScore * std) * Math.sqrt(horizon);
  }

  private async calculateMonteCarloVaR(returns: number[], confidence: number, horizon: number): Promise<number> {
    const mean = returns.reduce((sum, val) => sum + val, 0) / returns.length;
    const std = await this.financialMetricsService.calculateStandardDeviation(returns);

    const simulationResult = await this.financialMetricsService.runMonteCarloSimulation({
      simulations: 10000,
      timeSteps: horizon,
      initialValue: 1,
      drift: mean,
      volatility: std
    });
    
    const sortedSimulations = simulationResult.finalValues.sort((a, b) => a - b);
    const index = Math.floor((1 - confidence) * sortedSimulations.length);

    return sortedSimulations[index] || 0;
  }

  private generateStressTestRecommendations(
    scenario: StressTestScenario,
    lossPercentage: number
  ): string[] {
    const recommendations: string[] = [];

    if (Math.abs(lossPercentage) > 0.3) {
      recommendations.push('考虑大幅降低仓位以控制风险');
      recommendations.push('设置更严格的止损机制');
    } else if (Math.abs(lossPercentage) > 0.2) {
      recommendations.push('适当降低仓位规模');
      recommendations.push('加强风险监控');
    } else if (Math.abs(lossPercentage) > 0.1) {
      recommendations.push('保持当前仓位，密切监控市场');
    } else {
      recommendations.push('当前风险水平可接受');
    }

    if (scenario.name.includes('流动性')) {
      recommendations.push('避免在低流动性时段进行大额交易');
      recommendations.push('准备多个交易渠道以分散流动性风险');
    }

    if (scenario.name.includes('监管')) {
      recommendations.push('关注相关监管政策动态');
      recommendations.push('准备合规应对方案');
    }

    return recommendations;
  }

  /**
   * 计算多元化收益
   */
  private calculateDiversificationBenefit(
    weights: number[],
    correlationMatrix: number[][],
    individualRisks: import('../../domain/value-objects/risk-metrics').RiskMetrics[]
  ): number {
    if (weights.length < 2 || !correlationMatrix || individualRisks.length < 2) {
      return 0;
    }

    // 计算加权平均风险
    const weightedAverageRisk = weights.reduce((sum, weight, i) => {
      const riskMetrics = individualRisks[i];
      const volatility = riskMetrics.volatility.annualized;
      return sum + weight * volatility;
    }, 0);

    // 计算组合风险（考虑相关性）
    let portfolioVariance = 0;
    for (let i = 0; i < weights.length; i++) {
      for (let j = 0; j < weights.length; j++) {
        const correlation = i === j ? 1 : (correlationMatrix[i]?.[j] || 0);
        const vol_i = individualRisks[i]?.volatility.annualized || 0;
        const vol_j = individualRisks[j]?.volatility.annualized || 0;
        portfolioVariance += weights[i] * weights[j] * vol_i * vol_j * correlation;
      }
    }

    const portfolioRisk = Math.sqrt(portfolioVariance);

    // 多元化收益 = 加权平均风险 - 组合风险
    return Math.max(0, weightedAverageRisk - portfolioRisk);
  }

  /**
   * 计算聚合风险指标
   */
  private async calculateAggregatedRisk(
    positions: Position[],
    individualRisks: Map<string, RiskMetrics>,
    weights: number[]
  ): Promise<RiskMetrics> {
    // 计算加权平均的各项风险指标
    let totalWeight = 0;
    let aggregatedVolatility = 0;
    let aggregatedVar95_1d = 0;
    let aggregatedVar99_1d = 0;
    let aggregatedMaxDrawdown = 0;
    let aggregatedSharpeRatio = 0;

    positions.forEach((position, index) => {
      const risk = individualRisks.get(position.symbol);
      if (risk && weights[index]) {
        const weight = weights[index];
        totalWeight += weight;

        aggregatedVolatility += weight * risk.volatility.annualized;
        aggregatedVar95_1d += weight * risk.var.var95_1d;
        aggregatedVar99_1d += weight * risk.var.var99_1d;
        aggregatedMaxDrawdown += weight * risk.maxDrawdown.current;
        aggregatedSharpeRatio += weight * risk.sharpeRatio.annualized;
      }
    });

    // 标准化权重
    if (totalWeight > 0) {
      aggregatedVolatility /= totalWeight;
      aggregatedVar95_1d /= totalWeight;
      aggregatedVar99_1d /= totalWeight;
      aggregatedMaxDrawdown /= totalWeight;
      aggregatedSharpeRatio /= totalWeight;
    }

    // 创建聚合的风险指标
    return RiskMetrics.create({
      volatility: {
        daily: aggregatedVolatility / Math.sqrt(365),
        weekly: aggregatedVolatility / Math.sqrt(52),
        monthly: aggregatedVolatility / Math.sqrt(12),
        annualized: aggregatedVolatility
      },
      var: {
        var95_1d: aggregatedVar95_1d,
        var99_1d: aggregatedVar99_1d,
        var95_7d: aggregatedVar95_1d * Math.sqrt(7),
        var99_7d: aggregatedVar99_1d * Math.sqrt(7),
        var95_30d: aggregatedVar95_1d * Math.sqrt(30),
        var99_30d: aggregatedVar99_1d * Math.sqrt(30)
      },
      cvar: {
        cvar95_1d: aggregatedVar95_1d * 1.2,
        cvar99_1d: aggregatedVar99_1d * 1.2,
        cvar95_7d: aggregatedVar95_1d * Math.sqrt(7) * 1.2,
        cvar99_7d: aggregatedVar99_1d * Math.sqrt(7) * 1.2,
        cvar95_30d: aggregatedVar95_1d * Math.sqrt(30) * 1.2,
        cvar99_30d: aggregatedVar99_1d * Math.sqrt(30) * 1.2
      },
      maxDrawdown: {
        current: aggregatedMaxDrawdown,
        historical: aggregatedMaxDrawdown * 1.5,
        duration: 30
      },
      sharpeRatio: {
        daily: aggregatedSharpeRatio / Math.sqrt(365),
        weekly: aggregatedSharpeRatio / Math.sqrt(52),
        monthly: aggregatedSharpeRatio / Math.sqrt(12),
        annualized: aggregatedSharpeRatio
      },
      beta: 1.0,
      liquidityRisk: {
        bidAskSpread: 0.001,
        marketDepth: 1000000,
        volumeRatio: 0.1,
        liquidityScore: 80
      },
      correlationRisk: {
        marketCorrelation: 0.3,
        liquidationDistance: 0.8,
        signalConsistency: 0.2,
        trendAlignment: 0.7
      }
    });
  }
}