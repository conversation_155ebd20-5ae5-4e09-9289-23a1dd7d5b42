import { ContainerModule } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types';
import { TREND_ANALYSIS_TYPES } from './types';

// 领域服务接口
import { ITrendPredictionService } from '../../domain/services/trend-prediction.interface';

// 基础设施实现
import { TrendPredictionService } from '../services/trend-prediction';
import { UnifiedTechnicalIndicatorCalculator } from '../../../../shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';
import { FundamentalAnalysisModule } from '../modules/fundamental/fundamental-analysis-module';
import { SentimentAnalysisModule } from '../modules/sentiment/sentiment-analysis-module';
import { QuantitativeAnalysisModule } from '../modules/quantitative/quantitative-analysis-module';
import { FourDimensionFusionCoordinator } from '../modules/fusion/four-dimension-fusion-coordinator';
// 🔥 SignalFusionAdapter已删除 - 使用正确的SignalGenerationApplicationService
// import { SignalFusionAdapter, EnhancedSignalFusionAdapter } from '../adapters/signal-fusion-adapter';
import { TrendAnalysisEngine } from '../services/trend-analysis-engine';

// 简化服务实现已删除 - 使用完整版服务

/**
 * 趋势分析领域服务绑定模块
 * 降低模块耦合度：按功能域拆分绑定
 */
export const trendAnalysisDomainServicesBindings = new ContainerModule((bind) => {
  // 多时间框架处理器已完全迁移到统一的 IMultiTimeframeService
  // 所有调用方已迁移到 TYPES.Shared.MultiTimeframeService
  // 此绑定已废弃，不再需要

  // 🔥 四维度分析引擎 - 用于comprehensive分析深度
  bind(TREND_ANALYSIS_TYPES.TrendAnalysisEngine)
    .to(TrendAnalysisEngine)
    .inSingletonScope();

  // 注意：模式识别服务已迁移到统一的 IPatternRecognitionService
  // 请使用 TYPES.Shared.PatternRecognitionService 替代

  // 趋势预测服务
  bind<ITrendPredictionService>(TREND_ANALYSIS_TYPES.TrendPredictionService)
    .to(TrendPredictionService)
    .inSingletonScope();

  // 技术指标计算器（从trading-analysis迁移）- 使用统一技术指标计算器
  bind(TREND_ANALYSIS_TYPES.TechnicalIndicatorCalculator)
    .to(UnifiedTechnicalIndicatorCalculator)
    .inSingletonScope();

  // 基本面分析模块（从trading-analysis迁移）
  bind(TREND_ANALYSIS_TYPES.FundamentalAnalysisModule)
    .to(FundamentalAnalysisModule)
    .inSingletonScope();

  // 情绪分析模块（从trading-analysis迁移）
  bind(TREND_ANALYSIS_TYPES.SentimentAnalysisModule)
    .to(SentimentAnalysisModule)
    .inSingletonScope();

  // 量化分析模块（从trading-analysis迁移）
  bind(TREND_ANALYSIS_TYPES.QuantitativeAnalysisModule)
    .to(QuantitativeAnalysisModule)
    .inSingletonScope();

  // 四维度融合协调器（从trading-analysis迁移）
  bind(TREND_ANALYSIS_TYPES.FourDimensionFusionCoordinator)
    .to(FourDimensionFusionCoordinator)
    .inSingletonScope();

  // 🔥 信号融合适配器已删除 - 使用正确的SignalGenerationApplicationService
  // bind(TREND_ANALYSIS_TYPES.SignalFusionAdapter)
  //   .to(SignalFusionAdapter)
  //   .inSingletonScope();

  // 🔥 增强信号融合适配器已删除 - 使用正确的SignalGenerationApplicationService
  // bind(TREND_ANALYSIS_TYPES.EnhancedSignalFusionAdapter)
  //   .to(EnhancedSignalFusionAdapter)
  //   .inSingletonScope();
});
