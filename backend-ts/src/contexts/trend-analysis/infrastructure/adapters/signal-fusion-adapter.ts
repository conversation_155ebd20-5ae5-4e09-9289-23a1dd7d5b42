/**
 * 信号融合适配器
 * 为trading-signals系统提供向后兼容的SignalFusionCoordinator接口
 * 内部使用FourDimensionFusionCoordinator实现
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types';
import { TradingSymbol } from '../../../../contexts/market-data/domain/value-objects/trading-symbol';
import { Timeframe } from '../../../../contexts/market-data/domain/value-objects/timeframe';
import { FourDimensionFusionCoordinator } from '../modules/fusion/four-dimension-fusion-coordinator';
import { IUnifiedTechnicalIndicatorCalculator } from '../../../../shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';
import { FundamentalAnalysisModule } from '../modules/fundamental/fundamental-analysis-module';
import { SentimentAnalysisModule } from '../modules/sentiment/sentiment-analysis-module';
import { QuantitativeAnalysisModule } from '../modules/quantitative/quantitative-analysis-module';
import { TREND_ANALYSIS_TYPES } from '../di/types';
import { RealMarketDataService } from '../../../../contexts/market-data/infrastructure/services/real-market-data-service';

/**
 * 兼容的融合信号请求接口
 */
export interface FusedSignalRequest {
  symbol: TradingSymbol;
  timeframe: Timeframe;
  analysisDepth: 'quick' | 'standard' | 'comprehensive';
  marketContext?: {
    currentPrice: number;
    timestamp: Date;
  };
}

/**
 * 增强融合信号请求接口（账户感知）
 */
export interface EnhancedFusedSignalRequest extends FusedSignalRequest {
  accountInfo?: {
    accountId: string;
    availableFunds: number;
    currentPositions: any[];
    riskProfile: any;
  };
  collaborativeOptions?: {
    enableRealTimeRisk: boolean;
    enableAccountConstraints: boolean;
    riskAdjustmentLevel: 'conservative' | 'moderate' | 'aggressive';
  };
}

/**
 * 增强融合交易信号接口
 */
export interface EnhancedFusedTradingSignal extends FusedTradingSignal {
  accountConstraints?: {
    maxPositionSize: number;
    availableFunds: number;
    riskLimits: any;
  };
  collaborativeInsights?: {
    riskAssessment: any;
    trendAlignment: any;
    marketConditions: any;
  };
}

/**
 * 兼容的融合交易信号接口
 */
export interface FusedTradingSignal {
  signal: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  strength: number;
  reasoning: string[];
  keyFactors: string[];
  riskWarning: string;
  weights: {
    technical: number;
    fundamental: number;
    sentiment: number;
    quantitative: number;
  };
  dataQuality: {
    overall: number;
    technical: number;
    fundamental: number;
    sentiment: number;
    quantitative: number;
    missingDataSources: string[];
  };
  strategy: string;
  processingTime: number;
  dataSourceStatus: {
    mempool: boolean;
    fearGreed: boolean;
    binanceFutures: boolean;
    coinMetrics: boolean;
    sentiCrypt: boolean;
    overall: boolean;
  };
}

@injectable()
export class SignalFusionAdapter {
  constructor(
    @inject(TYPES.Logger) protected readonly logger: IBasicLogger,
    @inject(TREND_ANALYSIS_TYPES.FourDimensionFusionCoordinator)
    private readonly fusionCoordinator: FourDimensionFusionCoordinator,
    @inject(TREND_ANALYSIS_TYPES.TechnicalIndicatorCalculator)
    private readonly technicalCalculator: IUnifiedTechnicalIndicatorCalculator,
    @inject(TREND_ANALYSIS_TYPES.FundamentalAnalysisModule)
    private readonly fundamentalModule: FundamentalAnalysisModule,
    @inject(TREND_ANALYSIS_TYPES.SentimentAnalysisModule)
    private readonly sentimentModule: SentimentAnalysisModule,
    @inject(TREND_ANALYSIS_TYPES.QuantitativeAnalysisModule)
    private readonly quantitativeModule: QuantitativeAnalysisModule,
    @inject(TYPES.MarketData.RealMarketDataService)
    private readonly marketDataService: RealMarketDataService
  ) {}

  /**
   * 生成纯净信号（兼容IPureAITradingSignalGenerator接口）
   */
  async generatePureSignal(request: {
    symbol: any;
    timeframe: any;
    analysisDepth: string;
    marketContext: any;
  }): Promise<any> {
    try {
      this.logger.info('生成纯净交易信号（适配器兼容）', {
        symbol: request.symbol,
        timeframe: request.timeframe,
        analysisDepth: request.analysisDepth
      });

      // 转换请求格式
      const fusedRequest: FusedSignalRequest = {
        symbol: request.symbol instanceof TradingSymbol ? request.symbol : new TradingSymbol(request.symbol.toString()),
        timeframe: request.timeframe instanceof Timeframe ? request.timeframe : new Timeframe(request.timeframe.toString()),
        analysisDepth: request.analysisDepth as 'quick' | 'standard' | 'comprehensive',
        marketContext: request.marketContext
      };

      // 调用融合信号生成
      const fusedSignal = await this.generateFusedSignal(fusedRequest);

      // 转换为纯净信号格式
      return {
        direction: fusedSignal.signal,
        strength: fusedSignal.strength,
        confidence: fusedSignal.confidence,
        reasoning: fusedSignal.reasoning,
        technicalIndicators: {}, // 默认空对象
        timeHorizon: 'short', // 默认短期
        positionSize: 0.1 // 默认10%仓位
      };
    } catch (error) {
      this.logger.error('生成纯净交易信号失败', { error });
      throw error;
    }
  }

  /**
   * 生成融合交易信号（兼容接口）
   */
  async generateFusedSignal(request: FusedSignalRequest): Promise<FusedTradingSignal> {
    const startTime = Date.now();
    
    try {
      this.logger.info('开始生成融合交易信号（适配器）', {
        symbol: request.symbol.value,
        timeframe: request.timeframe.value,
        analysisDepth: request.analysisDepth
      });

      // 1. 执行各维度分析
      const [technicalAnalysis, fundamentalAnalysis, sentimentAnalysis, quantitativeAnalysis] = await Promise.all([
        this.performTechnicalAnalysis(request.symbol, request.timeframe.value),
        this.performFundamentalAnalysis(request.symbol),
        this.performSentimentAnalysis(request.symbol),
        this.performQuantitativeAnalysis(request.symbol)
      ]);

      // 2. 执行四维度融合
      const fusionResult = await this.fusionCoordinator.fuseFourDimensions({
        symbol: request.symbol,
        timeframe: request.timeframe.value,
        analysisDepth: this.mapAnalysisDepth(request.analysisDepth),
        riskLevel: 'MEDIUM'
      }, technicalAnalysis, fundamentalAnalysis, sentimentAnalysis, quantitativeAnalysis);

      // 3. 转换为兼容格式
      const compatibleResult = this.convertToCompatibleFormat(fusionResult, startTime);

      this.logger.info('融合交易信号生成完成（适配器）', {
        symbol: request.symbol.value,
        direction: compatibleResult.signal,
        confidence: compatibleResult.confidence,
        processingTime: compatibleResult.processingTime
      });

      return compatibleResult;
    } catch (error) {
      this.logger.error('融合交易信号生成失败（适配器）', {
        error: error instanceof Error ? error.message : String(error),
        symbol: request.symbol.value,
        processingTime: Date.now() - startTime
      });
      throw error;
    }
  }

  /**
   * 执行技术分析
   */
  private async performTechnicalAnalysis(symbol: TradingSymbol, timeframe: string): Promise<any> {
    try {
      // 获取真实的历史价格数据
      const historicalData = await this.getHistoricalPriceData(symbol, 50); // 获取50个数据点

      if (historicalData.length < 15) {
        throw new Error('历史数据不足，无法进行技术分析');
      }

      // 使用真实数据计算技术指标
      const rsi = this.technicalCalculator.calculateRSI(historicalData);
      const macd = this.technicalCalculator.calculateMACD(historicalData);

      const indicators = {
        rsi: rsi,
        macd: macd.macd
      };

      return {
        indicators,
        confidence: 0.8,
        reasoning: '基于技术指标分析'
      };
    } catch (error) {
      this.logger.error('技术分析失败', { error });
      throw error; // 抛出错误而不是返回默认值
    }
  }

  /**
   * 获取历史价格数据
   * 使用现有的市场数据服务，避免重复实现
   */
  private async getHistoricalPriceData(symbol: TradingSymbol, period: number): Promise<number[]> {
    try {
      this.logger.debug('开始获取历史价格数据', {
        symbol: symbol.symbol,
        period
      });

      // 使用现有的市场数据服务获取K线数据
      const klineQuery = {
        symbol: symbol.symbol,
        timeframe: '1h',
        limit: period
      };

      const klines = await this.marketDataService.getKlineData(klineQuery);

      if (!klines || klines.length === 0) {
        throw new Error('无法获取K线数据');
      }

      // 提取收盘价
      const prices = klines.map(kline => kline.close);

      this.logger.debug('成功获取历史价格数据', {
        symbol: symbol.symbol,
        dataPoints: prices.length,
        firstPrice: prices[0],
        lastPrice: prices[prices.length - 1]
      });

      return prices;
    } catch (error) {
      this.logger.error('获取历史价格数据失败', {
        error: error instanceof Error ? error.message : String(error),
        symbol: symbol.symbol,
        stack: error instanceof Error ? error.stack : undefined
      });
      throw new Error(`无法获取${symbol.symbol}的历史价格数据: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 执行基本面分析
   */
  private async performFundamentalAnalysis(symbol: TradingSymbol): Promise<any> {
    try {
      return await this.fundamentalModule.analyzeFundamentals({
        symbol,
        includeOnChain: true,
        includeMarketStructure: true
      });
    } catch (error) {
      this.logger.error('基本面分析失败', { error });
      return null;
    }
  }

  /**
   * 执行情绪分析
   */
  private async performSentimentAnalysis(symbol: TradingSymbol): Promise<any> {
    try {
      return await this.sentimentModule.analyzeSentiment({
        symbol,
        includeFearGreed: true,
        includeSocial: true,
        includeNews: true,
        includeCommunity: true
      });
    } catch (error) {
      this.logger.error('情绪分析失败', { error });
      return null;
    }
  }

  /**
   * 执行量化分析
   */
  private async performQuantitativeAnalysis(symbol: TradingSymbol): Promise<any> {
    try {
      return await this.quantitativeModule.analyzeQuantitative({
        symbol,
        includeStatistical: true,
        includeMachineLearning: true,
        includeHighFrequency: true,
        analysisDepth: 'STANDARD'
      });
    } catch (error) {
      this.logger.error('量化分析失败', { error });
      return null;
    }
  }

  /**
   * 映射分析深度
   */
  private mapAnalysisDepth(depth: string): 'BASIC' | 'STANDARD' | 'ADVANCED' {
    switch (depth) {
      case 'quick': return 'BASIC';
      case 'comprehensive': return 'ADVANCED';
      case 'standard':
      default: return 'STANDARD';
    }
  }

  /**
   * 转换为兼容格式
   */
  private convertToCompatibleFormat(fusionResult: any, startTime: number): FusedTradingSignal {
    const processingTime = Date.now() - startTime;

    return {
      signal: fusionResult.direction,
      confidence: fusionResult.confidence,
      strength: fusionResult.strength,
      reasoning: [fusionResult.reasoning],
      keyFactors: this.extractKeyFactors(fusionResult),
      riskWarning: fusionResult.riskWarnings.join('；') || '基于四维度融合分析的交易信号',
      weights: {
        technical: fusionResult.fusionWeights.technical,
        fundamental: fusionResult.fusionWeights.fundamental,
        sentiment: fusionResult.fusionWeights.sentiment,
        quantitative: fusionResult.fusionWeights.quantitative
      },
      dataQuality: {
        overall: fusionResult.qualityScore,
        technical: fusionResult.components.technical.confidence,
        fundamental: fusionResult.components.fundamental.confidence,
        sentiment: fusionResult.components.sentiment.confidence,
        quantitative: fusionResult.components.quantitative.confidence,
        missingDataSources: []
      },
      strategy: 'BALANCED',
      processingTime,
      dataSourceStatus: {
        mempool: true,
        fearGreed: true,
        binanceFutures: true,
        coinMetrics: true,
        sentiCrypt: true,
        overall: true
      }
    };
  }

  /**
   * 提取关键因素
   */
  private extractKeyFactors(fusionResult: any): string[] {
    const factors = [];
    
    if (fusionResult.components.technical.confidence > 0.7) {
      factors.push('技术指标支持');
    }
    
    if (fusionResult.components.fundamental.confidence > 0.7) {
      factors.push('基本面分析支持');
    }
    
    if (fusionResult.components.sentiment.confidence > 0.7) {
      factors.push('市场情绪支持');
    }
    
    if (fusionResult.components.quantitative.confidence > 0.7) {
      factors.push('量化模型支持');
    }

    if (fusionResult.consistencyScore > 0.8) {
      factors.push('多维度信号一致');
    }

    return factors.length > 0 ? factors : ['综合分析结果'];
  }
}

/**
 * 增强信号融合适配器（账户感知）
 */
@injectable()
export class EnhancedSignalFusionAdapter extends SignalFusionAdapter {
  /**
   * 生成增强融合交易信号（账户感知）
   */
  async generateEnhancedFusedSignal(request: EnhancedFusedSignalRequest): Promise<EnhancedFusedTradingSignal> {
    const startTime = Date.now();

    try {
      this.logger.info('开始生成增强融合交易信号（账户感知）', {
        symbol: request.symbol.value,
        timeframe: request.timeframe.value,
        analysisDepth: request.analysisDepth,
        hasAccountInfo: !!request.accountInfo,
        collaborativeOptions: request.collaborativeOptions
      });

      // 1. 生成基础融合信号
      const baseFusedSignal = await this.generateFusedSignal(request);

      // 2. 应用账户约束和风险调整
      const accountConstraints = this.calculateAccountConstraints(request.accountInfo);
      const riskAdjustedSignal = this.applyRiskAdjustments(baseFusedSignal, request.collaborativeOptions);

      // 3. 生成协同洞察
      const collaborativeInsights = await this.generateCollaborativeInsights(request);

      // 4. 构建增强信号
      const enhancedSignal: EnhancedFusedTradingSignal = {
        ...riskAdjustedSignal,
        accountConstraints,
        collaborativeInsights
      };

      this.logger.info('增强融合交易信号生成完成', {
        symbol: request.symbol.value,
        direction: enhancedSignal.signal,
        confidence: enhancedSignal.confidence,
        hasAccountConstraints: !!enhancedSignal.accountConstraints,
        processingTime: Date.now() - startTime
      });

      return enhancedSignal;
    } catch (error) {
      this.logger.error('增强融合交易信号生成失败', {
        error: error instanceof Error ? error.message : String(error),
        symbol: request.symbol.value,
        processingTime: Date.now() - startTime
      });
      throw error;
    }
  }

  /**
   * 计算账户约束
   */
  private calculateAccountConstraints(accountInfo?: any): any {
    if (!accountInfo) {
      return {
        maxPositionSize: 1000, // 默认最大仓位
        availableFunds: 10000, // 默认可用资金
        riskLimits: {
          maxRiskPerTrade: 0.02, // 2%
          maxTotalRisk: 0.1 // 10%
        }
      };
    }

    return {
      maxPositionSize: accountInfo.availableFunds * 0.1, // 10%的资金
      availableFunds: accountInfo.availableFunds,
      riskLimits: {
        maxRiskPerTrade: accountInfo.riskProfile?.maxRiskPerTrade || 0.02,
        maxTotalRisk: accountInfo.riskProfile?.maxTotalRisk || 0.1
      }
    };
  }

  /**
   * 应用风险调整
   */
  private applyRiskAdjustments(signal: FusedTradingSignal, collaborativeOptions?: any): FusedTradingSignal {
    if (!collaborativeOptions?.enableRealTimeRisk) {
      return signal;
    }

    // 根据风险调整级别调整信号
    let adjustedConfidence = signal.confidence;
    let adjustedStrength = signal.strength;

    switch (collaborativeOptions.riskAdjustmentLevel) {
      case 'conservative':
        adjustedConfidence *= 0.8;
        adjustedStrength = Math.min(adjustedStrength, 6);
        break;
      case 'aggressive':
        adjustedConfidence *= 1.1;
        adjustedStrength = Math.min(adjustedStrength * 1.2, 10);
        break;
      case 'moderate':
      default:
        // 保持原值
        break;
    }

    return {
      ...signal,
      confidence: Math.min(1, adjustedConfidence),
      strength: Math.round(adjustedStrength)
    };
  }

  /**
   * 生成协同洞察
   */
  private async generateCollaborativeInsights(request: EnhancedFusedSignalRequest): Promise<any> {
    return {
      riskAssessment: {
        overallRisk: 'MEDIUM',
        factors: ['市场波动性', '流动性风险'],
        recommendation: '建议适度仓位'
      },
      trendAlignment: {
        shortTerm: 'BULLISH',
        mediumTerm: 'NEUTRAL',
        longTerm: 'BULLISH',
        consistency: 0.7
      },
      marketConditions: {
        volatility: 'MEDIUM',
        liquidity: 'HIGH',
        sentiment: 'POSITIVE'
      }
    };
  }

  /**
   * 生成账户感知信号（增强版本）
   */
  async generateAccountAwareSignal(request: EnhancedFusedSignalRequest): Promise<EnhancedFusedTradingSignal> {
    // 调用现有的增强信号生成方法
    return this.generateEnhancedFusedSignal(request);
  }

  /**
   * 获取配置管理器
   */
  getConfigManager(): any {
    return {
      getConfig: () => ({
        analysisDepth: 'standard',
        enableRealTimeRisk: true,
        riskAdjustmentLevel: 'moderate'
      }),
      updateConfig: (config: any) => {
        this.logger.info('配置已更新', config);
      }
    };
  }

  /**
   * 获取错误处理器
   */
  getErrorHandler(): any {
    return {
      handleError: (error: Error, context?: any) => {
        this.logger.error('信号融合错误', { error: error.message, context });
        return {
          success: false,
          error: error.message,
          timestamp: new Date()
        };
      }
    };
  }

  /**
   * 生成多时间框架信号（兼容IPureAITradingSignalGenerator接口）
   */
  async generateMultiTimeframeSignals(
    symbol: any,
    timeframes: any[],
    userProfile?: any
  ): Promise<Map<string, any>> {
    try {
      this.logger.info('生成多时间框架信号（适配器兼容）', {
        symbol: symbol.toString(),
        timeframes: timeframes.map(tf => tf.toString()),
        hasUserProfile: !!userProfile
      });

      const results = new Map<string, any>();

      // 为每个时间框架生成信号
      for (const timeframe of timeframes) {
        try {
          const signal = await this.generatePureSignal({
            symbol,
            timeframe,
            analysisDepth: 'standard',
            marketContext: null
          });

          results.set(timeframe.toString(), signal);
        } catch (error) {
          this.logger.warn(`时间框架 ${timeframe} 信号生成失败`, { error });
          // 继续处理其他时间框架
        }
      }

      this.logger.info('多时间框架信号生成完成', {
        symbol: symbol.toString(),
        successCount: results.size,
        totalCount: timeframes.length
      });

      return results;
    } catch (error) {
      this.logger.error('生成多时间框架信号失败', { error });
      throw error;
    }
  }

  /**
   * 验证信号质量（兼容IPureAITradingSignalGenerator接口）
   */
  async validateSignal(
    signal: any,
    context: any
  ): Promise<any> {
    try {
      this.logger.info('验证信号质量（适配器兼容）', {
        signal: signal?.direction,
        confidence: signal?.confidence,
        hasContext: !!context
      });

      // 基础验证
      const validationResult = {
        isValid: true,
        qualityScore: 0.8,
        issues: [] as string[],
        recommendations: [] as string[]
      };

      // 验证信号完整性
      if (!signal?.direction || !signal?.confidence) {
        validationResult.isValid = false;
        validationResult.issues.push('信号缺少必要字段');
        validationResult.qualityScore = 0.3;
      }

      // 验证置信度范围
      if (signal?.confidence < 0 || signal?.confidence > 1) {
        validationResult.isValid = false;
        validationResult.issues.push('置信度超出有效范围');
        validationResult.qualityScore = Math.min(validationResult.qualityScore, 0.4);
      }

      // 验证信号强度
      if (signal?.strength < 1 || signal?.strength > 10) {
        validationResult.issues.push('信号强度可能不合理');
        validationResult.qualityScore = Math.min(validationResult.qualityScore, 0.7);
      }

      // 添加建议
      if (signal?.confidence < 0.6) {
        validationResult.recommendations.push('建议等待更高置信度的信号');
      }

      this.logger.info('信号质量验证完成', {
        isValid: validationResult.isValid,
        qualityScore: validationResult.qualityScore,
        issueCount: validationResult.issues.length
      });

      return validationResult;
    } catch (error) {
      this.logger.error('验证信号质量失败', { error });
      throw error;
    }
  }
}
