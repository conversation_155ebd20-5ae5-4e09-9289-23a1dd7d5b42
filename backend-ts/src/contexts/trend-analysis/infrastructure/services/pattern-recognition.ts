import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types';
import { SecureIdGenerator } from '../../../../shared/infrastructure/utils/secure-id-generator';
import { TradingSymbol } from '../../../../contexts/market-data/domain/value-objects/trading-symbol';
import { Timeframe } from '../../../../contexts/market-data/domain/value-objects/timeframe';
import {
  PatternType,
  PatternRecognitionResult,
  PatternSearchQuery,
  PatternStatistics,
  PatternMonitoringConfig,
  PatternAlert,
  KlineDataPoint
} from '../../../../shared/infrastructure/analysis/types/PatternTypes';
import { IPatternRecognitionService as TrendPatternRecognitionService } from '../../../../shared/infrastructure/analysis/interfaces/IPatternRecognitionService';
import { MultiTimeframeData, TimeframeData } from '../../domain/value-objects/multi-timeframe-data';
import { IPatternRecognitionService as UnifiedPatternRecognitionService } from '../../../../shared/infrastructure/analysis/interfaces/IPatternRecognitionService';
import {
  KlineDataPoint as UnifiedKlineDataPoint
} from '../../../../shared/infrastructure/analysis/types/PatternTypes';

/**
 * 趋势分析模式识别适配器
 * 重构：纯适配器实现，将统一的PatternRecognitionService适配到趋势分析领域接口
 * 移除了所有重复的私有检测方法，现在完全依赖统一服务
 */
@injectable()
export class PatternRecognitionService implements TrendPatternRecognitionService {
  private readonly monitoringTasks: Map<string, any> = new Map();
  private readonly config = {
    supportedPatterns: Object.values(PatternType),
    defaultConfidenceThreshold: 0.6,
    maxLookbackPeriod: 200,
    updateFrequency: 60 // 秒
  };

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.PatternRecognitionService) private readonly unifiedPatternService: UnifiedPatternRecognitionService
  ) {
    this.logger.info('趋势分析模式识别适配器初始化完成 - 使用统一模式识别服务');
  }

  /**
   * 识别单个时间框架的形态 - 重构：使用统一PatternRecognitionService
   */
  async recognizePatterns(
    timeframeData: TimeframeData,
    patternTypes?: PatternType[]
  ): Promise<PatternRecognitionResult[]> {
    try {
      this.logger.debug('开始识别形态 - 使用统一模式识别服务', {
        timeframe: timeframeData.timeframe.value,
        klinesCount: timeframeData.getKlineCount(),
        patternTypes: patternTypes?.length || 'all'
      });

      const klines = timeframeData.klines;

      if (klines.length < 20) {
        this.logger.warn('K线数据不足，无法识别形态', {
          timeframe: timeframeData.timeframe.value,
          klinesCount: klines.length
        });
        return [];
      }

      // 转换为统一的K线数据格式
      const unifiedKlines: UnifiedKlineDataPoint[] = klines.map(k => ({
        timestamp: k.timestamp,
        open: k.open,
        high: k.high,
        low: k.low,
        close: k.close,
        volume: k.volume
      }));

      // 使用统一的模式识别服务
      const unifiedPatterns = await this.unifiedPatternService.recognize(unifiedKlines, patternTypes);

      // 转换为趋势分析领域的格式
      const patterns: PatternRecognitionResult[] = unifiedPatterns.map(up => ({
        type: up.type as PatternType,
        name: up.name,
        description: up.description,
        timeframe: timeframeData.timeframe,
        startTime: up.startTime,
        endTime: up.endTime,
        duration: up.duration,
        keyPoints: up.keyPoints.map(kp => ({
          timestamp: kp.timestamp,
          price: kp.price,
          type: kp.type,
          description: kp.description
        })),
        confidence: up.confidence,
        completeness: up.completeness,
        clarity: up.clarity,
        signal: up.signal,
        strength: up.strength,
        reliability: up.reliability,
        priceTargets: up.priceTargets,
        volumeConfirmation: up.volumeConfirmation,
        riskRewardRatio: up.riskRewardRatio,
        estimatedTimeToTarget: up.estimatedTimeToTarget,
        marketContext: up.marketContext,
        qualityIndicators: up.qualityIndicators
      } as PatternRecognitionResult));

      // 应用置信度过滤
      const filteredPatterns = patterns.filter(p => p.confidence >= this.config.defaultConfidenceThreshold);

      this.logger.debug('形态识别完成', {
        timeframe: timeframeData.timeframe.value,
        patternsFound: filteredPatterns.length,
        highConfidencePatterns: filteredPatterns.filter(p => p.confidence >= 0.7).length
      });

      return filteredPatterns;

    } catch (error) {
      this.logger.error('形态识别失败', {
        timeframe: timeframeData.timeframe.value,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * 识别多时间框架的形态
   */
  async recognizeMultiTimeframePatterns(
    multiTimeframeData: MultiTimeframeData,
    query: PatternSearchQuery
  ): Promise<Map<string, PatternRecognitionResult[]>> {
    try {
      this.logger.info('开始多时间框架形态识别', {
        symbol: query.symbol.symbol,
        timeframes: query.timeframes.map(tf => tf.value)
      });

      const results = new Map<string, PatternRecognitionResult[]>();

      // 并行处理各时间框架
      const promises = query.timeframes.map(async (timeframe) => {
        const timeframeData = multiTimeframeData.getTimeframeData(timeframe);
        if (!timeframeData) {
          this.logger.warn('时间框架数据不存在', { timeframe: timeframe.value });
          return { timeframe: timeframe.value, patterns: [] };
        }

        const patterns = await this.recognizePatterns(timeframeData, query.patternTypes);
        
        // 应用过滤条件
        const filteredPatterns = patterns.filter(pattern => {
          if (query.minConfidence && pattern.confidence < query.minConfidence) {
            return false;
          }
          return true;
        });

        return { timeframe: timeframe.value, patterns: filteredPatterns };
      });

      const timeframeResults = await Promise.all(promises);
      
      timeframeResults.forEach(result => {
        results.set(result.timeframe, result.patterns);
      });

      this.logger.info('多时间框架形态识别完成', {
        symbol: query.symbol.symbol,
        totalPatterns: Array.from(results.values()).reduce((sum, patterns) => sum + patterns.length, 0)
      });

      return results;

    } catch (error) {
      this.logger.error('多时间框架形态识别失败', {
        symbol: query.symbol.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      return new Map();
    }
  }

  /**
   * 搜索历史形态 - 委托给统一服务
   */
  async searchPatterns(query: PatternSearchQuery): Promise<PatternRecognitionResult[]> {
    try {
      this.logger.debug('搜索历史形态', { symbol: query.symbol.symbol });
      
      // 转换查询格式并委托给统一服务
      const unifiedQuery: PatternSearchQuery = {
        symbol: query.symbol,
        timeframes: query.timeframes,
        patternTypes: query.patternTypes,
        startTime: query.startTime,
        endTime: query.endTime,
        minConfidence: query.minConfidence
      };

      const unifiedResults = await this.unifiedPatternService.searchPatterns(unifiedQuery);
      
      // 转换结果格式
      return unifiedResults.map(up => ({
        type: up.type as PatternType,
        name: up.name,
        description: up.description,
        timeframe: new Timeframe('1h'), // 默认时间框架
        startTime: up.startTime,
        endTime: up.endTime,
        duration: up.duration,
        keyPoints: up.keyPoints.map(kp => ({
          timestamp: kp.timestamp,
          price: kp.price,
          type: kp.type,
          description: kp.description
        })),
        confidence: up.confidence,
        completeness: up.completeness,
        clarity: up.clarity,
        signal: up.signal,
        strength: up.strength,
        reliability: up.reliability,
        priceTargets: up.priceTargets,
        volumeConfirmation: up.volumeConfirmation,
        riskRewardRatio: up.riskRewardRatio,
        estimatedTimeToTarget: up.estimatedTimeToTarget,
        marketContext: up.marketContext,
        qualityIndicators: up.qualityIndicators
      } as PatternRecognitionResult));

    } catch (error) {
      this.logger.error('历史形态搜索失败', { error: error instanceof Error ? error.message : String(error) });
      return [];
    }
  }

  /**
   * 验证形态完整性 - 简化实现
   */
  async validatePattern(
    pattern: PatternRecognitionResult,
    klines: KlineDataPoint[]
  ): Promise<boolean> {
    try {
      // 检查形态时间有效性
      const now = new Date();
      const patternAge = now.getTime() - pattern.endTime.getTime();
      const maxAge = 24 * 60 * 60 * 1000; // 24小时

      if (patternAge > maxAge) {
        return false;
      }

      // 检查置信度
      if (pattern.confidence < 0.6) {
        return false;
      }

      // 检查成交量确认
      if (!pattern.volumeConfirmation) {
        return false;
      }

      return true;

    } catch (error) {
      this.logger.error('形态验证失败', { error: error instanceof Error ? error.message : String(error) });
      return false;
    }
  }

  /**
   * 预测形态发展 - 简化实现
   */
  async predictPatternDevelopment(
    pattern: PatternRecognitionResult,
    currentData: TimeframeData
  ): Promise<{
    expectedCompletion: Date;
    probability: number;
    nextKeyLevels: number[];
    potentialOutcomes: Array<{
      scenario: string;
      probability: number;
      priceTarget: number;
      timeframe: string;
    }>;
  }> {
    try {
      // 简化的形态发展预测
      const expectedCompletion = new Date(Date.now() + pattern.duration * 60000);
      const probability = pattern.confidence * 0.8; // 预测概率通常低于识别置信度

      const nextKeyLevels = pattern.priceTargets.bullish.concat(pattern.priceTargets.bearish);

      const potentialOutcomes = [
        {
          scenario: '看涨突破',
          probability: pattern.signal === 'bullish' ? 0.6 : 0.3,
          priceTarget: pattern.priceTargets.bullish[0] || 0,
          timeframe: '1-3天'
        },
        {
          scenario: '看跌突破',
          probability: pattern.signal === 'bearish' ? 0.6 : 0.3,
          priceTarget: pattern.priceTargets.bearish[0] || 0,
          timeframe: '1-3天'
        }
      ];

      return {
        expectedCompletion,
        probability,
        nextKeyLevels,
        potentialOutcomes
      };

    } catch (error) {
      this.logger.error('形态发展预测失败', { error: error instanceof Error ? error.message : String(error) });
      return {
        expectedCompletion: new Date(),
        probability: 0,
        nextKeyLevels: [],
        potentialOutcomes: []
      };
    }
  }

  /**
   * 获取形态统计信息 - 委托给统一服务
   */
  async getPatternStatistics(
    timeframe: string,
    startTime?: Date,
    endTime?: Date
  ): Promise<PatternStatistics> {
    try {
      // 委托给统一服务
      const stats = await this.unifiedPatternService.getPatternStatistics(
        timeframe,
        startTime,
        endTime
      );
      return stats;
    } catch (error) {
      this.logger.error('获取形态统计失败', { error: error instanceof Error ? error.message : String(error) });
      return {
        totalPatterns: 0,
        successRate: 0,
        averageConfidence: 0,
        patternsByType: new Map<PatternType, number>(),
        timeframeDistribution: new Map<string, number>(),
        recentTrends: []
      };
    }
  }

  /**
   * 启动实时形态监控 - 简化实现
   */
  async startPatternMonitoring(config: PatternMonitoringConfig): Promise<string> {
    const monitoringId = SecureIdGenerator.generateMonitoringId();

    this.monitoringTasks.set(monitoringId, {
      config,
      startTime: new Date(),
      status: 'active'
    });

    this.logger.info('启动形态监控', { monitoringId, symbols: config.symbols });
    return monitoringId;
  }

  /**
   * 停止实时形态监控
   */
  async stopPatternMonitoring(monitoringId: string): Promise<void> {
    if (this.monitoringTasks.has(monitoringId)) {
      this.monitoringTasks.delete(monitoringId);
      this.logger.info('停止形态监控', { monitoringId });
    }
  }

  /**
   * 获取形态警报 - 简化实现
   */
  async getPatternAlerts(
    monitoringId: string
  ): Promise<PatternAlert[]> {
    // 简化实现，返回空数组
    return [];
  }

  /**
   * 比较形态相似性 - 委托给统一服务
   */
  async comparePatterns(
    pattern1: PatternRecognitionResult,
    pattern2: PatternRecognitionResult
  ): Promise<{
    similarity: number;
    commonFeatures: string[];
    differences: string[];
    recommendation: string;
  }> {
    try {
      // 简化实现：基于形态类型和置信度比较
      const typeSimilarity = pattern1.name === pattern2.name ? 1 : 0;
      const confidenceSimilarity = 1 - Math.abs(pattern1.confidence - pattern2.confidence);
      const signalSimilarity = pattern1.signal === pattern2.signal ? 1 : 0;
      
      const similarity = (typeSimilarity + confidenceSimilarity + signalSimilarity) / 3;
      
      const commonFeatures = [];
      const differences = [];
      
      if (pattern1.name === pattern2.name) {
        commonFeatures.push(`相同形态类型: ${pattern1.name}`);
      } else {
        differences.push(`形态类型不同: ${pattern1.name} vs ${pattern2.name}`);
      }
      
      if (pattern1.signal === pattern2.signal) {
        commonFeatures.push(`相同信号方向: ${pattern1.signal}`);
      } else {
        differences.push(`信号方向不同: ${pattern1.signal} vs ${pattern2.signal}`);
      }
      
      const recommendation = similarity > 0.7 ? '形态高度相似，可参考历史表现' : 
                           similarity > 0.4 ? '形态部分相似，需谨慎参考' : 
                           '形态差异较大，不建议参考';
      
      return {
        similarity,
        commonFeatures,
        differences,
        recommendation
      };
    } catch (error) {
      this.logger.error('形态比较失败', { error: error instanceof Error ? error.message : String(error) });
      return {
        similarity: 0,
        commonFeatures: [],
        differences: ['比较失败'],
        recommendation: '无法提供建议'
      };
    }
  }

  /**
   * 生成形态报告 - 简化实现
   */
  async generatePatternReport(
    patterns: PatternRecognitionResult[],
    includeCharts?: boolean
  ): Promise<{
    summary: string;
    detailedAnalysis: string;
    keyPatterns: PatternRecognitionResult[];
    tradingOpportunities: Array<{
      pattern: PatternRecognitionResult;
      action: 'buy' | 'sell' | 'wait';
      reasoning: string;
      riskReward: number;
    }>;
    riskAssessment: string;
    charts?: any[];
  }> {
    const keyPatterns = patterns.filter(p => p.confidence >= 0.7);
    const bullishPatterns = patterns.filter(p => p.signal === 'bullish');
    const bearishPatterns = patterns.filter(p => p.signal === 'bearish');

    return {
      summary: `发现${patterns.length}个形态，其中${keyPatterns.length}个高置信度形态`,
      detailedAnalysis: `看涨形态${bullishPatterns.length}个，看跌形态${bearishPatterns.length}个`,
      keyPatterns,
      tradingOpportunities: keyPatterns.map(pattern => ({
        pattern,
        action: pattern.signal === 'bullish' ? 'buy' : pattern.signal === 'bearish' ? 'sell' : 'wait',
        reasoning: `基于${pattern.name}形态，置信度${(pattern.confidence * 100).toFixed(1)}%`,
        riskReward: pattern.riskRewardRatio || 1
      })),
      riskAssessment: `整体风险水平：${keyPatterns.length > 3 ? '高' : keyPatterns.length > 1 ? '中' : '低'}`,
      charts: includeCharts ? [] : undefined
    };
  }

  /**
   * 训练形态识别模型 - 委托给统一服务
   */
  async trainPatternModel(
    trainingData: Array<{
      timeframeData: TimeframeData;
      knownPatterns: PatternRecognitionResult[];
    }>
  ): Promise<{
    modelVersion: string;
    accuracy: number;
    improvements: string[];
  }> {
    // 简化实现
    return {
      modelVersion: '1.0.0',
      accuracy: 0.85,
      improvements: ['使用统一模式识别服务']
    };
  }

  /**
   * 获取形态识别配置
   */
  getConfig(): {
    supportedPatterns: PatternType[];
    defaultConfidenceThreshold: number;
    maxLookbackPeriod: number;
    updateFrequency: number;
    volumeConfirmationRequired: boolean;
  } {
    return {
      ...this.config,
      volumeConfirmationRequired: false
    };
  }
}
