/**
 * 币安执行引擎
 * 实现IExecutionEngine接口，提供真实的币安交易功能
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { PrismaClient } from '@prisma/client';
import {
  IExecutionEngine,
  ExecutionEngineType,
  OrderExecutionRequest,
  OrderExecutionResult,
  PositionOpenRequest,
  PositionOpenResult,
  PositionCloseRequest,
  PositionCloseResult,
  AccountBalance,
  ValidationResult,
  RiskCheckResult,
  MarketDepth,
  OrderStatus,
  StopLossUpdateRequest,
  TakeProfitUpdateRequest,
  UpdateResult,
  PositionInfo
} from '../../domain/interfaces/execution-engine.interface';
import { IBinanceApiClient } from '../adapters/binance-api-client';
import { IOrderIdManager } from './order-id-manager';
import { ISecurityManager } from './security-manager';
import { ITradingLimitController } from './trading-limit-controller';
import { IEmergencyStopManager } from './emergency-stop-manager';
import {
  BinanceOrderParams,
  BinanceApiClientError,
  ApiCredentials
} from '../../domain/types/dual-track.types';

/**
 * 币安执行引擎实现
 */
@injectable()
export class BinanceEngine implements IExecutionEngine {
  readonly engineType = ExecutionEngineType.BINANCE_LIVE;
  readonly name = 'BinanceEngine';
  readonly isLive = true;

  private connected = false;
  private currentCredentials: ApiCredentials | null = null;

  constructor(
    @inject(TYPES.TradingExecution.BinanceApiClient)
    private readonly binanceClient: IBinanceApiClient,
    @inject(TYPES.TradingExecution.OrderIdManager)
    private readonly orderIdManager: IOrderIdManager,
    @inject(TYPES.TradingExecution.SecurityManager)
    private readonly securityManager: ISecurityManager,
    @inject(TYPES.TradingExecution.TradingLimitController)
    private readonly limitController: ITradingLimitController,
    @inject(TYPES.TradingExecution.EmergencyStopManager)
    private readonly emergencyStopManager: IEmergencyStopManager,
    @inject(TYPES.Logger)
    private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.Database)
    private readonly prisma: PrismaClient
  ) {}

  /**
   * 连接到币安
   */
  async connect(): Promise<void> {
    if (this.connected) {
      return;
    }

    if (!this.currentCredentials) {
      throw new BinanceApiClientError('未设置API凭证，无法连接到币安');
    }

    try {
      await this.binanceClient.initialize(this.currentCredentials);
      this.connected = true;
      
      this.logger.info('币安执行引擎连接成功', {
        engineType: this.engineType,
        testnet: this.currentCredentials.testnet
      });

    } catch (error) {
      this.logger.error('币安执行引擎连接失败', { error });
      throw error;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (!this.connected) {
      return;
    }

    try {
      await this.binanceClient.disconnect();
      this.connected = false;
      
      this.logger.info('币安执行引擎已断开连接');

    } catch (error) {
      this.logger.error('币安执行引擎断开连接失败', { error });
      throw error;
    }
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.connected && this.binanceClient.isInitialized();
  }

  /**
   * 设置API凭证
   */
  async setCredentials(credentials: ApiCredentials): Promise<void> {
    this.currentCredentials = credentials;
    
    // 如果已连接，重新初始化
    if (this.connected) {
      await this.disconnect();
      await this.connect();
    }
  }

  /**
   * 开启仓位
   */
  async openPosition(request: PositionOpenRequest): Promise<PositionOpenResult> {
    this.ensureConnected();

    try {
      // 检查紧急停止状态
      const emergencyStopActive = await this.emergencyStopManager.isEmergencyStopActive();
      if (emergencyStopActive) {
        throw new BinanceApiClientError('系统处于紧急停止状态，无法开启新仓位');
      }

      // 计算订单价值
      const orderValue = request.quantity * (request.entryPrice || 0);

      // 检查交易限额
      const limitCheck = await this.limitController.checkLimits(request.account.id, orderValue, 'OPEN_POSITION');
      if (!limitCheck.allowed) {
        const violatedRules = limitCheck.violatedRules.map(r => r.description).join(', ');
        throw new BinanceApiClientError(`违反交易限额: ${violatedRules}`);
      }

      this.logger.info('币安开启仓位', {
        symbol: request.symbolId,
        side: request.side,
        quantity: request.quantity,
        entryPrice: request.entryPrice,
        orderValue
      });

      // 构建币安订单参数
      const orderParams: BinanceOrderParams = {
        symbol: request.symbolId,
        side: request.side === 'LONG' ? 'BUY' : 'SELL',
        type: 'MARKET',
        quantity: request.quantity.toString(),
        positionSide: request.side === 'LONG' ? 'LONG' : 'SHORT'
      };

      // 生成系统订单ID
      const systemOrderId = this.orderIdManager.generateSystemOrderId();
      orderParams.newClientOrderId = systemOrderId;

      // 执行开仓订单
      const orderResult = await this.binanceClient.placeOrder(orderParams);

      // 创建订单映射
      await this.orderIdManager.createMapping({
        systemOrderId,
        exchangeOrderId: orderResult.orderId.toString(),
        symbol: request.symbolId,
        exchangeType: 'BINANCE',
        metadata: {
          orderType: 'MARKET',
          side: request.side,
          originalRequest: request
        }
      });

      // 设置止损订单
      let stopLossOrder = null;
      let stopLossSystemId = null;
      if (request.stopLoss) {
        stopLossSystemId = this.orderIdManager.generateSystemOrderId();
        const stopLossParams: BinanceOrderParams = {
          symbol: request.symbolId,
          side: request.side === 'LONG' ? 'SELL' : 'BUY',
          type: 'STOP_MARKET',
          quantity: request.quantity.toString(),
          stopPrice: request.stopLoss.toString(),
          positionSide: request.side,
          reduceOnly: true,
          newClientOrderId: stopLossSystemId
        };

        stopLossOrder = await this.binanceClient.placeOrder(stopLossParams);

        // 创建止损订单映射
        await this.orderIdManager.createMapping({
          systemOrderId: stopLossSystemId,
          exchangeOrderId: stopLossOrder.orderId.toString(),
          symbol: request.symbolId,
          exchangeType: 'BINANCE',
          metadata: {
            orderType: 'STOP_LOSS',
            parentOrderId: systemOrderId
          }
        });
      }

      // 设置止盈订单
      let takeProfitOrder = null;
      let takeProfitSystemId = null;
      if (request.takeProfit) {
        takeProfitSystemId = this.orderIdManager.generateSystemOrderId();
        const takeProfitParams: BinanceOrderParams = {
          symbol: request.symbolId,
          side: request.side === 'LONG' ? 'SELL' : 'BUY',
          type: 'TAKE_PROFIT_MARKET',
          quantity: request.quantity.toString(),
          stopPrice: request.takeProfit.toString(),
          positionSide: request.side === 'LONG' ? 'LONG' : 'SHORT',
          reduceOnly: true,
          newClientOrderId: takeProfitSystemId
        };

        takeProfitOrder = await this.binanceClient.placeOrder(takeProfitParams);

        // 创建止盈订单映射
        await this.orderIdManager.createMapping({
          systemOrderId: takeProfitSystemId,
          exchangeOrderId: takeProfitOrder.orderId.toString(),
          symbol: request.symbolId,
          exchangeType: 'BINANCE',
          metadata: {
            orderType: 'TAKE_PROFIT',
            parentOrderId: systemOrderId
          }
        });
      }

      // 构建执行结果
      const orders: OrderExecutionResult[] = [
        {
          success: true,
          orderId: systemOrderId,
          exchangeOrderId: orderResult.orderId.toString(),
          executedPrice: parseFloat(orderResult.avgPrice || orderResult.price),
          executedQuantity: parseFloat(orderResult.executedQty),
          commission: 0, // 需要从账户信息中获取
          timestamp: new Date(orderResult.updateTime),
          metadata: {
            binanceOrderId: orderResult.orderId,
            status: orderResult.status
          }
        }
      ];

      if (stopLossOrder && stopLossSystemId) {
        orders.push({
          success: true,
          orderId: stopLossSystemId,
          exchangeOrderId: stopLossOrder.orderId.toString(),
          executedPrice: parseFloat(stopLossOrder.stopPrice),
          executedQuantity: 0,
          commission: 0,
          timestamp: new Date(stopLossOrder.updateTime),
          metadata: {
            orderType: 'STOP_LOSS',
            binanceOrderId: stopLossOrder.orderId
          }
        });
      }

      if (takeProfitOrder && takeProfitSystemId) {
        orders.push({
          success: true,
          orderId: takeProfitSystemId,
          exchangeOrderId: takeProfitOrder.orderId.toString(),
          executedPrice: parseFloat(takeProfitOrder.stopPrice),
          executedQuantity: 0,
          commission: 0,
          timestamp: new Date(takeProfitOrder.updateTime),
          metadata: {
            orderType: 'TAKE_PROFIT',
            binanceOrderId: takeProfitOrder.orderId
          }
        });
      }

      // 更新交易使用量
      await this.limitController.updateUserStatus(request.account.id, orderValue, 'OPEN_POSITION');

      this.logger.info('币安仓位开启成功', {
        orderId: orderResult.orderId,
        executedPrice: orderResult.avgPrice,
        executedQuantity: orderResult.executedQty
      });

      return {
        success: true,
        orders,
        // position 需要从数据库创建，这里暂时返回 undefined
        position: undefined
      };

    } catch (error) {
      this.logger.error('币安开启仓位失败', { error, request });
      
      return {
        success: false,
        orders: [],
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 平仓
   */
  async closePosition(request: PositionCloseRequest): Promise<PositionCloseResult> {
    this.ensureConnected();

    try {
      this.logger.info('币安平仓', {
        positionId: request.positionId,
        quantity: request.quantity,
        reason: request.reason
      });

      // 首先从数据库获取仓位信息以获取symbol
      const dbPosition = await this.prisma.tradingPositions.findUnique({
        where: { id: request.positionId },
        include: { symbols: true }
      });

      if (!dbPosition) {
        throw new BinanceApiClientError('未找到仓位记录');
      }

      const symbol = dbPosition.symbols.symbol;

      // 获取币安仓位信息
      const positions = await this.binanceClient.getPositions();
      const position = positions.find(p => p.symbol === symbol);

      if (!position || parseFloat(position.positionAmt) === 0) {
        throw new BinanceApiClientError('未找到有效仓位');
      }

      const positionAmt = parseFloat(position.positionAmt);
      const closeQuantity = request.quantity || Math.abs(positionAmt);

      // 确定平仓方向（与开仓方向相反）
      const closeSide = positionAmt > 0 ? 'SELL' : 'BUY';
      const positionSide = positionAmt > 0 ? 'LONG' : 'SHORT';

      // 构建平仓订单参数
      const orderParams: BinanceOrderParams = {
        symbol: symbol,
        side: closeSide,
        type: 'MARKET',
        quantity: closeQuantity.toString(),
        positionSide: positionSide,
        reduceOnly: true
      };

      // 执行平仓订单
      const orderResult = await this.binanceClient.placeOrder(orderParams);

      const executedPrice = parseFloat(orderResult.avgPrice || orderResult.price);
      const executedQuantity = parseFloat(orderResult.executedQty);

      // 计算实现盈亏
      const entryPrice = parseFloat(position.entryPrice);
      const realizedPnl = positionAmt > 0
        ? (executedPrice - entryPrice) * executedQuantity
        : (entryPrice - executedPrice) * executedQuantity;

      this.logger.info('币安平仓成功', {
        orderId: orderResult.orderId,
        executedPrice,
        executedQuantity,
        realizedPnl
      });

      return {
        success: true,
        closedQuantity: executedQuantity,
        averageClosePrice: executedPrice,
        realizedPnl,
        commission: 0, // 需要从交易费用中获取
        timestamp: new Date(orderResult.updateTime)
      };

    } catch (error) {
      this.logger.error('币安平仓失败', { error, request });

      return {
        success: false,
        closedQuantity: 0,
        averageClosePrice: 0,
        realizedPnl: 0,
        commission: 0,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 执行订单
   */
  async executeOrder(request: OrderExecutionRequest): Promise<OrderExecutionResult> {
    this.ensureConnected();

    try {
      // 生成系统订单ID
      const systemOrderId = this.orderIdManager.generateSystemOrderId();

      const orderParams: BinanceOrderParams = {
        symbol: request.symbolId,
        side: request.side,
        type: request.orderType as any,
        quantity: request.quantity.toString(),
        price: request.price?.toString(),
        stopPrice: request.stopPrice?.toString(),
        timeInForce: request.timeInForce,
        newClientOrderId: systemOrderId
      };

      const result = await this.binanceClient.placeOrder(orderParams);

      // 创建订单映射
      await this.orderIdManager.createMapping({
        systemOrderId,
        exchangeOrderId: result.orderId.toString(),
        symbol: request.symbolId,
        exchangeType: 'BINANCE',
        metadata: {
          orderType: request.orderType,
          side: request.side,
          originalRequest: request
        }
      });

      return {
        success: true,
        orderId: systemOrderId,
        exchangeOrderId: result.orderId.toString(),
        executedPrice: parseFloat(result.avgPrice || result.price),
        executedQuantity: parseFloat(result.executedQty),
        commission: 0,
        timestamp: new Date(result.updateTime),
        metadata: {
          binanceOrderId: result.orderId,
          status: result.status
        }
      };

    } catch (error) {
      this.logger.error('币安订单执行失败', { error, request });

      return {
        success: false,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 取消订单
   */
  async cancelOrders(orderIds: string[]): Promise<{ [orderId: string]: boolean }> {
    this.ensureConnected();

    const results: { [orderId: string]: boolean } = {};

    for (const systemOrderId of orderIds) {
      try {
        // 从订单映射中获取交易所订单信息
        const mapping = await this.orderIdManager.getOrderMapping(systemOrderId);

        if (!mapping) {
          this.logger.warn('未找到订单映射', { systemOrderId });
          results[systemOrderId] = false;
          continue;
        }

        const binanceOrderId = parseInt(mapping.exchangeOrderId);

        // 取消币安订单
        const success = await this.binanceClient.cancelOrder(mapping.symbol, binanceOrderId);
        results[systemOrderId] = success;

        if (success) {
          // 移除订单映射
          await this.orderIdManager.removeMapping(systemOrderId);

          this.logger.info('订单取消成功', {
            systemOrderId,
            symbol: mapping.symbol,
            binanceOrderId
          });
        }

      } catch (error) {
        this.logger.error('取消订单失败', { error, systemOrderId });
        results[systemOrderId] = false;
      }
    }

    return results;
  }

  /**
   * 修改订单
   */
  async modifyOrder(orderId: string, newPrice?: number, newQuantity?: number): Promise<OrderExecutionResult> {
    this.ensureConnected();

    // 币安不支持直接修改订单，需要先取消再重新下单
    throw new BinanceApiClientError('币安不支持直接修改订单，请先取消再重新下单');
  }

  /**
   * 更新止损
   */
  async updateStopLoss(request: StopLossUpdateRequest): Promise<UpdateResult> {
    this.ensureConnected();

    try {
      // 实现止损更新逻辑
      return {
        success: true,
        updatedValue: request.newStopLoss,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        updatedValue: request.newStopLoss,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 更新止盈
   */
  async updateTakeProfit(request: TakeProfitUpdateRequest): Promise<UpdateResult> {
    this.ensureConnected();

    try {
      // 实现止盈更新逻辑
      return {
        success: true,
        updatedValue: request.newTakeProfit,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        updatedValue: request.newTakeProfit,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 获取持仓信息
   */
  async getPositions(accountId: string): Promise<PositionInfo[]> {
    try {
      // 首先尝试从币安API获取实时持仓数据
      this.ensureConnected();
      const binancePositions = await this.binanceClient.getPositions();

      // 过滤活跃持仓并转换为标准格式
      const activePositions = binancePositions
        .filter(pos => parseFloat(pos.positionAmt) !== 0)
        .map(pos => ({
          symbol: pos.symbol,
          side: parseFloat(pos.positionAmt) > 0 ? 'long' as const : 'short' as const,
          size: Math.abs(parseFloat(pos.positionAmt)),
          entryPrice: parseFloat(pos.entryPrice),
          currentPrice: parseFloat(pos.entryPrice),
          markPrice: parseFloat(pos.entryPrice), // 使用entryPrice作为markPrice的替代
          unrealizedPnl: parseFloat(pos.unrealizedProfit),
          realizedPnl: 0,
          percentage: 0,
          margin: Math.abs(parseFloat(pos.positionAmt)) * parseFloat(pos.entryPrice) / parseFloat(pos.leverage),
          leverage: parseFloat(pos.leverage),
          liquidationPrice: 0,
          marginType: 'isolated',
          timestamp: new Date()
        } as PositionInfo));

      this.logger.debug('获取币安持仓成功', {
        accountId,
        totalPositions: binancePositions.length,
        activePositions: activePositions.length
      });

      return activePositions;

    } catch (error) {
      this.logger.error('获取币安持仓失败', { error, accountId });

      // 抛出明确的API失败错误，不使用过时的数据库数据
      throw new Error(`币安API连接失败，无法获取实时持仓数据: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取账户余额
   */
  async getAccountBalance(accountId: string): Promise<AccountBalance> {
    try {
      // 首先尝试从币安API获取实时数据
      this.ensureConnected();
      const accountInfo = await this.binanceClient.getAccountInfo();

      const realTimeBalance = {
        totalBalance: parseFloat(accountInfo.totalWalletBalance),
        availableBalance: parseFloat(accountInfo.availableBalance),
        marginUsed: parseFloat(accountInfo.totalPositionInitialMargin),
        unrealizedPnl: parseFloat(accountInfo.totalUnrealizedProfit),
        currency: 'USDT',
        timestamp: new Date()
      };

      this.logger.debug('获取币安实时余额成功', { accountId, balance: realTimeBalance });
      return realTimeBalance;

    } catch (error) {
      this.logger.error('获取币安账户余额失败', { error, accountId });

      // 抛出明确的API失败错误，不使用过时的数据库数据
      throw new Error(`币安API连接失败，无法获取实时账户余额: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取订单状态
   */
  async getOrderStatus(systemOrderId: string): Promise<OrderStatus> {
    this.ensureConnected();

    try {
      // 从订单映射中获取交易所订单信息
      const mapping = await this.orderIdManager.getOrderMapping(systemOrderId);

      if (!mapping) {
        throw new BinanceApiClientError(`未找到订单映射: ${systemOrderId}`);
      }

      const binanceOrderId = parseInt(mapping.exchangeOrderId);

      // 获取币安订单状态
      const orderResult = await this.binanceClient.getOrderStatus(mapping.symbol, binanceOrderId);

      // 映射币安订单状态到系统状态
      const statusMapping: { [key: string]: string } = {
        'NEW': 'PENDING',
        'PARTIALLY_FILLED': 'PARTIALLY_FILLED',
        'FILLED': 'FILLED',
        'CANCELED': 'CANCELLED',
        'REJECTED': 'REJECTED',
        'EXPIRED': 'EXPIRED'
      };

      const status = (statusMapping[orderResult.status] || orderResult.status) as 'PENDING' | 'FILLED' | 'PARTIALLY_FILLED' | 'CANCELLED' | 'REJECTED';

      // 如果订单已完成或取消，清理映射
      if (['FILLED', 'CANCELLED', 'REJECTED', 'EXPIRED'].includes(status)) {
        await this.orderIdManager.removeMapping(systemOrderId);
      }

      return {
        id: systemOrderId,
        status,
        filledQuantity: parseFloat(orderResult.executedQty),
        averagePrice: parseFloat(orderResult.avgPrice || orderResult.price),
        timestamp: new Date(orderResult.updateTime)
      };

    } catch (error) {
      this.logger.error('获取订单状态失败', { error, systemOrderId });
      throw error;
    }
  }

  /**
   * 获取当前价格
   */
  async getCurrentPrice(symbol: string): Promise<number> {
    this.ensureConnected();

    try {
      return await this.binanceClient.getPrice(symbol);
    } catch (error) {
      this.logger.error('获取当前价格失败', { error, symbol });
      throw error;
    }
  }

  /**
   * 获取市场深度
   */
  async getMarketDepth(symbol: string): Promise<MarketDepth> {
    this.ensureConnected();

    try {
      const orderBook = await this.binanceClient.getOrderBook(symbol, 20);

      return {
        bids: orderBook.bids.map(([price, quantity]) => [parseFloat(price), parseFloat(quantity)]),
        asks: orderBook.asks.map(([price, quantity]) => [parseFloat(price), parseFloat(quantity)]),
        timestamp: new Date()
      };

    } catch (error) {
      this.logger.error('获取市场深度失败', { error, symbol });
      throw error;
    }
  }

  /**
   * 验证订单
   */
  async validateOrder(request: OrderExecutionRequest): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 基本验证
    if (!request.symbolId) {
      errors.push('交易对不能为空');
    }

    if (request.quantity <= 0) {
      errors.push('交易数量必须大于0');
    }

    if (request.orderType === 'LIMIT' && !request.price) {
      errors.push('限价单必须指定价格');
    }

    // 连接状态验证
    if (!this.isConnected()) {
      errors.push('币安API未连接');
    }

    // TODO: 集成交易对验证器
    // 这里应该添加交易对验证逻辑，确保只能交易BTC
    if (request.symbolId && !request.symbolId.includes('BTC')) {
      errors.push('当前配置只允许交易BTC相关交易对');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 检查风险限制
   */
  async checkRiskLimits(accountId: string): Promise<RiskCheckResult> {
    try {
      const accountInfo = await this.getAccountBalance(accountId);
      const reasons: string[] = [];

      // 检查可用余额
      if (accountInfo.availableBalance < 10) {
        reasons.push('可用余额不足');
      }

      // 检查未实现盈亏
      if (accountInfo.unrealizedPnl < -100) {
        reasons.push('未实现亏损过大');
      }

      const riskLevel = reasons.length > 0 ? 'HIGH' : 'LOW';

      return {
        canTrade: reasons.length === 0,
        riskLevel: riskLevel as any,
        reasons
      };

    } catch (error) {
      return {
        canTrade: false,
        riskLevel: 'CRITICAL',
        reasons: ['无法获取账户信息进行风险检查']
      };
    }
  }

  /**
   * 获取订单历史
   */
  async getOrderHistory(symbol: string, limit: number = 100): Promise<any[]> {
    this.ensureConnected();

    try {
      return await this.binanceClient.getAllOrders(symbol, limit);
    } catch (error) {
      this.logger.error('获取订单历史失败', { error, symbol, limit });
      throw error;
    }
  }

  /**
   * 获取交易历史
   */
  async getTradeHistory(symbol: string, limit: number = 100): Promise<any[]> {
    this.ensureConnected();

    try {
      return await this.binanceClient.getUserTrades(symbol, limit);
    } catch (error) {
      this.logger.error('获取交易历史失败', { error, symbol, limit });
      throw error;
    }
  }

  /**
   * 确保已连接
   */
  private ensureConnected(): void {
    if (!this.isConnected()) {
      throw new BinanceApiClientError('币安执行引擎未连接');
    }
  }
}
