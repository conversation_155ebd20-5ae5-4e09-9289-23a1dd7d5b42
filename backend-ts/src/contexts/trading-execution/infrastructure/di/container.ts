/**
 * 交易执行上下文的依赖注入配置
 */

import { Container } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';

// 领域服务
import { TradingStrategyEngine } from '../../domain/services/trading-strategy-engine';
import { PositionManager } from '../../domain/services/position-manager';
import { OrderExecutor } from '../../domain/services/order-executor';
import { RiskMonitor } from '../../domain/services/risk-monitor';

// 执行引擎
import { ExecutionEngineRouter } from '../../domain/services/execution-engine-router';
import { SimulationEngine } from '../../domain/services/simulation-engine';
import { BinanceEngine } from '../services/binance-engine';
import { IExecutionEngine } from '../../domain/interfaces/execution-engine.interface';

// 基础设施适配器
import { BinanceApiClient, IBinanceApiClient } from '../adapters/binance-api-client';

// 基础设施服务
import { CredentialManager, ICredentialManager } from '../services/credential-manager';
import { BinanceWebSocketService, IBinanceWebSocketService } from '../services/binance-websocket-service';



// 真实执行引擎服务
import { RealSlippageCalculator, IRealSlippageCalculator } from '../../domain/services/real-slippage-calculator';
import { RealOrderExecutionEngine, IRealOrderExecutionEngine } from '../../domain/services/real-order-execution-engine';

/**
 * 配置交易执行上下文的依赖注入
 */
export async function configureTradingExecutionContainer(container: Container): Promise<void> {
  // 领域服务
  container.bind(TYPES.TradingExecution.TradingStrategyEngine)
    .to(TradingStrategyEngine)
    .inSingletonScope();

  container.bind(TYPES.TradingExecution.PositionManager)
    .to(PositionManager)
    .inSingletonScope();

  container.bind(TYPES.TradingExecution.OrderExecutor)
    .to(OrderExecutor)
    .inSingletonScope();

  container.bind(TYPES.TradingExecution.RiskMonitor)
    .to(RiskMonitor)
    .inSingletonScope();

  // 真实执行引擎服务
  container.bind(TYPES.TradingExecution.RealSlippageCalculator)
    .to(RealSlippageCalculator)
    .inSingletonScope();

  container.bind(TYPES.TradingExecution.RealOrderExecutionEngine)
    .to(RealOrderExecutionEngine)
    .inSingletonScope();

  // 基础设施适配器
  container.bind(TYPES.TradingExecution.BinanceApiClient)
    .to(BinanceApiClient)
    .inSingletonScope();

  // 基础设施服务
  container.bind(TYPES.TradingExecution.CredentialManager)
    .to(CredentialManager)
    .inSingletonScope();

  container.bind(TYPES.TradingExecution.BinanceWebSocketService)
    .to(BinanceWebSocketService)
    .inSingletonScope();

  // 执行引擎
  container.bind(TYPES.TradingExecution.SimulationEngine)
    .to(SimulationEngine)
    .inSingletonScope();

  container.bind(TYPES.TradingExecution.BinanceEngine)
    .to(BinanceEngine)
    .inSingletonScope();

  container.bind(TYPES.TradingExecution.ExecutionEngineRouter)
    .to(ExecutionEngineRouter)
    .inSingletonScope();

  // 应用服务 - 使用异步绑定避免分层架构违规
  const { TradingExecutionApplicationService } = await import('../../application/services/trading-execution-application-service');
  container.bind(TYPES.TradingExecution.TradingExecutionApplicationService)
    .to(TradingExecutionApplicationService)
    .inSingletonScope();
}

/**
 * 初始化执行引擎路由器
 * 在容器配置完成后调用，注册所有执行引擎
 */
export function initializeExecutionEngineRouter(container: Container): void {
  const router = container.get<ExecutionEngineRouter>(TYPES.TradingExecution.ExecutionEngineRouter);
  const simulationEngine = container.get<SimulationEngine>(TYPES.TradingExecution.SimulationEngine);
  const binanceEngine = container.get<BinanceEngine>(TYPES.TradingExecution.BinanceEngine);

  // 注册模拟执行引擎
  router.registerEngine(simulationEngine);

  // 注册币安执行引擎
  router.registerEngine(binanceEngine);
}
