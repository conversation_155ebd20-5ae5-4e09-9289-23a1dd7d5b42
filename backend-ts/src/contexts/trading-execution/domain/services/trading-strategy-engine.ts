/**
 * 交易策略引擎 - 核心业务逻辑
 * 整合现有的AI分析服务，实现交易决策
 */

import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { TradingAccount } from '../entities/trading-account';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';

export interface TradingSignal {
  id: string;
  symbol: string;
  signal: 'BUY' | 'SELL' | 'HOLD' | 'NO_SIGNAL';
  confidence: number;
  strength: number;
  currentPrice: number;
  takeProfit?: number;
  stopLoss?: number;
  reasoning: string[];
  timestamp: string;
}

export interface TradingDecision {
  action: 'EXECUTE' | 'SKIP' | 'WAIT';
  reason: string;
  positionSize?: number;
  stopLoss?: number;
  takeProfit?: number;
  confidence?: number;
}

export interface ConfirmationResult {
  isValid: boolean;
  score: number;
  details: {
    signalConfidence: number;
    trendStrength: number;
    riskLevel: number;
    learningAccuracy: number;
  };
}

@injectable()
export class TradingStrategyEngine {
  constructor(
    @inject(TYPES.Logger)
    private readonly logger: Logger,
    @inject(TYPES.TradingSignals.SignalGenerationApplicationService)
    private readonly tradingSignalService: any,
    @inject(TYPES.RiskManagement.RiskAssessmentApplicationService)
    private readonly riskAssessmentService: any,
    @inject(TYPES.TrendAnalysis.TrendAnalysisApplicationService)
    private readonly trendAnalysisService: any,
    @inject(TYPES.AIReasoning.AIReasoningApplicationService)
    private readonly aiReasoningService: any
  ) {}

  /**
   * 处理最新信号并做出交易决策
   */
  async processLatestSignals(account: TradingAccount): Promise<TradingDecision> {
    try {
      this.logger.info('开始处理交易信号', { accountId: account.id });

      // 1. 检查账户状态
      if (!account.canOpenNewPosition()) {
        return {
          action: 'SKIP',
          reason: '账户风险限制：无法开新仓位'
        };
      }

      // 2. 获取最新信号（复用现有服务）
      const signal = await this.getLatestTradingSignal();
      
      if (signal.signal === 'HOLD') {
        return {
          action: 'SKIP',
          reason: '信号为HOLD，暂不交易'
        };
      }

      // 3. 多重确认
      const confirmation = await this.performMultipleConfirmation(signal);
      
      if (!confirmation.isValid) {
        return {
          action: 'SKIP',
          reason: `信号确认失败，确认分数: ${confirmation.score.toFixed(2)}`
        };
      }

      // 4. 计算仓位大小
      const positionSize = await this.calculatePositionSize(signal, account, confirmation.score);

      // 5. 计算止损止盈
      const { stopLoss, takeProfit } = await this.calculateStopLossAndTakeProfit(signal);

      return {
        action: 'EXECUTE',
        reason: `信号确认通过，确认分数: ${confirmation.score.toFixed(2)}`,
        positionSize,
        stopLoss,
        takeProfit,
        confidence: confirmation.score
      };

    } catch (error) {
      this.logger.error('处理交易信号失败', {
        error: error instanceof Error ? error.message : String(error),
        accountId: account.id
      });

      return {
        action: 'SKIP',
        reason: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 获取最新交易信号（复用现有服务）
   */
  private async getLatestTradingSignal(): Promise<TradingSignal> {
    const signal = await this.tradingSignalService.generateTradingSignal({
      symbol: 'BTC/USDT',
      timeframe: '15m',
      analysisDepth: 'comprehensive'
    });

    // 标准化信号数据格式
    return {
      id: signal.id || `signal_${Date.now()}`,
      symbol: signal.symbol || 'BTC/USDT',
      signal: signal.signal || 'HOLD',
      confidence: (signal.confidence ?? 50) / 100, // 转换为0-1范围
      strength: signal.strength ?? 0.5,
      currentPrice: signal.currentPrice ?? signal.price ?? 0,
      takeProfit: signal.takeProfit ?? signal.targetPrice,
      stopLoss: signal.stopLoss,
      reasoning: signal.reasoning ?? [],
      timestamp: signal.timestamp || new Date().toISOString()
    };
  }

  /**
   * 多重确认机制（整合现有AI服务）
   */
  private async performMultipleConfirmation(signal: TradingSignal): Promise<ConfirmationResult> {
    try {
      // 并行获取多个分析结果
      const [trendAnalysis, riskAssessment, aiReasoning] = await Promise.all([
        this.trendAnalysisService.analyzeTrend({
          symbol: 'BTC/USDT',
          analysisDepth: 'standard',
          includePatterns: true,
          includePredictions: true
        }),
        this.riskAssessmentService.assessRisk({
          portfolioId: 'mainPortfolio',
          position: {
            symbol: 'BTC/USDT',
            quantity: 0.001,
            entryPrice: signal.currentPrice,
            side: signal.signal === 'BUY' ? 'LONG' : 'SHORT'
          },
          marketData: {
            currentPrice: signal.currentPrice,
            volatility: 0.02,
            volume: 1000000
          }
        }),
        this.aiReasoningService.executeReasoning({
          query: `分析当前BTC交易信号的质量和可靠性，信号为${signal.signal}，置信度${signal.confidence}`,
          context: {
            signal,
            marketConditions: 'current'
          },
          analysisDepth: 'standard'
        })
      ]);

      // 计算各项确认分数
      const signalConfidence = signal.confidence;
      const trendStrength = this.normalizeTrendStrength(trendAnalysis);
      const riskLevel = this.normalizeRiskLevel(riskAssessment);
      const learningAccuracy = this.extractLearningAccuracy(aiReasoning);

      // 计算综合确认分数
      const score = this.calculateConfirmationScore({
        signalConfidence,
        trendStrength,
        riskLevel,
        learningAccuracy
      });

      const isValid = score >= 0.75; // 75%确认阈值

      this.logger.info('多重确认完成', {
        signalConfidence,
        trendStrength,
        riskLevel,
        learningAccuracy,
        score,
        isValid,
        signalType: signal.signal
      });

      return {
        isValid,
        score,
        details: {
          signalConfidence,
          trendStrength,
          riskLevel,
          learningAccuracy
        }
      };

    } catch (error) {
      this.logger.error('多重确认失败', { error: error instanceof Error ? error.message : String(error) });
      return {
        isValid: false,
        score: 0,
        details: {
          signalConfidence: 0,
          trendStrength: 0,
          riskLevel: 1,
          learningAccuracy: 0
        }
      };
    }
  }

  /**
   * 计算确认分数
   */
  private calculateConfirmationScore(details: ConfirmationResult['details']): number {
    // 根据用户要求，移除硬编码权重，抛出错误要求使用动态权重服务
    throw new Error('calculateConfirmationScore方法使用硬编码权重，应该使用DynamicWeightingService进行权重分配');
  }

  /**
   * 标准化趋势强度
   */
  private normalizeTrendStrength(trendAnalysis: any): number {
    if (!trendAnalysis?.strength) return 0.5;
    
    // 假设趋势强度在0-1范围内
    return Math.max(0, Math.min(1, trendAnalysis.strength));
  }

  /**
   * 标准化风险水平
   */
  private normalizeRiskLevel(riskAssessment: any): number {
    if (!riskAssessment?.assessment?.riskLevel) return 0.5;

    // 将风险等级转换为0-1数值
    const riskMap = {
      'LOW': 0.2,
      'MEDIUM': 0.5,
      'HIGH': 0.8,
      'VERY_HIGH': 1.0
    };

    return (riskMap as any)[riskAssessment.assessment.riskLevel.level] ?? 0.5;
  }

  /**
   * 提取学习系统准确性
   */
  private extractLearningAccuracy(aiReasoning: any): number {
    try {
      // 从AI推理结果中提取置信度
      if (aiReasoning?.confidence) {
        return Math.max(0, Math.min(1, aiReasoning.confidence));
      }

      // 如果有决策质量评分
      if (aiReasoning?.decision?.qualityScore) {
        return Math.max(0, Math.min(1, aiReasoning.decision.qualityScore / 100));
      }

      // 默认返回中等准确性
      return 0.75;
    } catch (error) {
      this.logger.warn('提取学习准确性失败，使用默认值', {
        error: error instanceof Error ? error.message : String(error)
      });
      return 0.75;
    }
  }

  /**
   * 计算仓位大小（固定分数法）
   */
  private async calculatePositionSize(
    signal: TradingSignal, 
    account: TradingAccount, 
    confirmationScore: number
  ): Promise<number> {
    // 基础风险：1%
    const baseRisk = account.riskSettings.riskPerTrade;
    
    // 根据确认分数调整风险（最高3%）
    const adjustedRisk = Math.min(baseRisk * (1 + confirmationScore), 0.03);
    
    // 计算风险金额
    const riskAmount = account.currentBalance * adjustedRisk;
    
    // 获取ATR止损距离
    const stopLossDistance = await this.getATRStopLossDistance(signal);
    
    // 计算仓位大小（考虑杠杆）
    const positionSize = (riskAmount / stopLossDistance) * account.riskSettings.leverage;
    
    // 限制最大投入为10美元保证金
    return Math.min(positionSize, 10);
  }

  /**
   * 获取ATR止损距离
   */
  private async getATRStopLossDistance(signal: TradingSignal): Promise<number> {
    // 这里应该从现有的price_data表获取ATR值
    // 暂时使用信号中的止损价格计算距离
    if (signal.stopLoss && signal.currentPrice) {
      return Math.abs(signal.currentPrice - signal.stopLoss);
    }
    
    // 默认使用当前价格的1.5%作为止损距离
    return signal.currentPrice * 0.015;
  }

  /**
   * 计算止损止盈价格
   */
  private async calculateStopLossAndTakeProfit(signal: TradingSignal): Promise<{
    stopLoss: number;
    takeProfit: number;
  }> {
    const currentPrice = signal.currentPrice;
    const stopLossDistance = await this.getATRStopLossDistance(signal);
    
    let stopLoss: number;
    let takeProfit: number;
    
    if (signal.signal === 'BUY') {
      stopLoss = currentPrice - stopLossDistance;
      takeProfit = currentPrice + (stopLossDistance * 2); // 1:2风险收益比
    } else {
      stopLoss = currentPrice + stopLossDistance;
      takeProfit = currentPrice - (stopLossDistance * 2);
    }
    
    return { stopLoss, takeProfit };
  }
}
