/**
 * 真实执行引擎
 * 基于真实市场数据和算法的执行引擎，零容忍随机数和模拟数据
 */

import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { PrismaClient } from '@prisma/client';
import { TradingPosition } from '../entities/trading-position';
import { TradingAccount } from '../entities/trading-account';
import { IRealSlippageCalculator } from './real-slippage-calculator';
import { IRealOrderExecutionEngine } from './real-order-execution-engine';
import {
  IExecutionEngine,
  ExecutionEngineType,
  PositionOpenRequest,
  PositionOpenResult,
  PositionCloseRequest,
  PositionCloseResult,
  OrderExecutionRequest,
  OrderExecutionResult,
  AccountBalance,
  ValidationResult,
  RiskCheckResult,
  MarketDepth,
  OrderStatus,
  StopLossUpdateRequest,
  TakeProfitUpdateRequest,
  UpdateResult,
  PositionInfo
} from '../interfaces/execution-engine.interface';

/**
 * 真实执行引擎
 * 提供基于真实市场数据的交易执行功能，包括真实滑点计算、手续费、延迟等
 */
@injectable()
export class SimulationEngine implements IExecutionEngine {
  readonly engineType = ExecutionEngineType.SIMULATION;
  readonly name = 'Real Trading Engine (Paper Trading Mode)';
  readonly isLive = false;

  private connected = true;

  constructor(
    private readonly logger: Logger,
    private readonly prisma: PrismaClient,
    private readonly slippageCalculator: IRealSlippageCalculator,
    private readonly orderExecutionEngine: IRealOrderExecutionEngine
  ) {}

  /**
   * 开启新仓位
   */
  async openPosition(request: PositionOpenRequest): Promise<PositionOpenResult> {
    try {
      this.logger.info('模拟引擎：开始开启新仓位', {
        accountId: request.account.id,
        side: request.side,
        quantity: request.quantity,
        entryPrice: request.entryPrice
      });

      // 1. 验证账户状态
      if (!request.account.canOpenNewPosition()) {
        return {
          success: false,
          orders: [],
          error: '账户风险限制，无法开新仓位'
        };
      }

      // 2. 创建仓位实体
      const position = TradingPosition.create({
        accountId: request.account.id,
        symbolId: request.symbolId,
        signalId: request.signalId,
        side: request.side,
        entryPrice: request.entryPrice,
        quantity: request.quantity,
        leverage: request.account.riskSettings.leverage,
        marginUsed: request.marginUsed,
        stopLoss: request.stopLoss,
        takeProfit: request.takeProfit,
        atrValue: request.atrValue,
        metadata: request.metadata
      });

      // 3. 执行市价开仓订单
      const entryOrder = await this.executeOrder({
        accountId: request.account.id,
        symbolId: request.symbolId,
        orderType: 'MARKET',
        side: request.side === 'LONG' ? 'BUY' : 'SELL',
        quantity: request.quantity,
        price: request.entryPrice,
        positionId: position.id,
        metadata: {
          orderPurpose: 'ENTRY',
          signalId: request.signalId
        }
      });

      if (!entryOrder.success) {
        return {
          success: false,
          orders: [entryOrder],
          error: `开仓订单执行失败: ${entryOrder.error}`
        };
      }

      // 4. 设置止损订单
      const stopLossOrder = await this.executeOrder({
        accountId: request.account.id,
        symbolId: request.symbolId,
        orderType: 'STOP',
        side: request.side === 'LONG' ? 'SELL' : 'BUY',
        quantity: request.quantity,
        stopPrice: request.stopLoss,
        positionId: position.id,
        metadata: {
          orderPurpose: 'STOP_LOSS'
        }
      });

      // 5. 设置止盈订单
      const takeProfitOrder = await this.executeOrder({
        accountId: request.account.id,
        symbolId: request.symbolId,
        orderType: 'LIMIT',
        side: request.side === 'LONG' ? 'SELL' : 'BUY',
        quantity: request.quantity,
        price: request.takeProfit,
        positionId: position.id,
        metadata: {
          orderPurpose: 'TAKE_PROFIT'
        }
      });

      // 6. 更新仓位价格（使用实际执行价格）
      if (entryOrder.executedPrice) {
        position.updateCurrentPrice(entryOrder.executedPrice);
      }

      this.logger.info('模拟引擎：新仓位开启成功', {
        positionId: position.id,
        entryPrice: entryOrder.executedPrice,
        stopLoss: request.stopLoss,
        takeProfit: request.takeProfit
      });

      return {
        success: true,
        position,
        orders: [entryOrder, stopLossOrder, takeProfitOrder]
      };

    } catch (error) {
      this.logger.error('模拟引擎：开启仓位失败', {
        error: error instanceof Error ? error.message : String(error),
        accountId: request.account.id
      });

      return {
        success: false,
        orders: [],
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 平仓
   */
  async closePosition(request: PositionCloseRequest): Promise<PositionCloseResult> {
    try {
      this.logger.info('真实引擎：开始平仓', {
        positionId: request.positionId,
        reason: request.reason,
        quantity: request.quantity
      });

      // 获取真实仓位信息
      const position = await this.getPositionFromDatabase(request.positionId);
      if (!position) {
        return {
          success: false,
          error: '仓位不存在',
          closedQuantity: 0,
          averageClosePrice: 0,
          realizedPnl: 0,
          commission: 0,
          timestamp: new Date()
        };
      }

      // 使用真实的订单执行引擎进行平仓
      const closeOrderRequest = {
        positionId: request.positionId,
        quantity: request.quantity || position.quantity,
        closePrice: request.closePrice
      };

      const closeResult = await this.orderExecutionEngine.closePosition(closeOrderRequest);

      if (!closeResult.success) {
        return {
          success: false,
          error: closeResult.error || '平仓执行失败',
          closedQuantity: 0,
          averageClosePrice: 0,
          realizedPnl: 0,
          commission: 0,
          timestamp: new Date()
        };
      }

      this.logger.info('真实引擎：平仓成功', {
        positionId: request.positionId,
        actualClosePrice: closeResult.averageClosePrice,
        realizedPnl: closeResult.realizedPnl,
        commission: closeResult.commission
      });

      return {
        success: true,
        closedQuantity: closeResult.closedQuantity || request.quantity || position.quantity,
        averageClosePrice: closeResult.averageClosePrice,
        realizedPnl: closeResult.realizedPnl,
        commission: closeResult.commission,
        timestamp: new Date()
      };

    } catch (error) {
      this.logger.error('模拟引擎：平仓失败', {
        error: error instanceof Error ? error.message : String(error),
        positionId: request.positionId
      });

      return {
        success: false,
        closedQuantity: 0,
        averageClosePrice: 0,
        realizedPnl: 0,
        commission: 0,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 执行单个订单（真实执行逻辑）
   */
  async executeOrder(request: OrderExecutionRequest): Promise<OrderExecutionResult> {
    try {
      // 使用真实的订单执行引擎
      const executionResult = await this.orderExecutionEngine.executeOrder(request);

      if (!executionResult.success) {
        return {
          success: false,
          error: executionResult.error || '订单执行失败',
          timestamp: new Date()
        };
      }

      this.logger.debug('真实引擎：订单执行成功', {
        orderId: executionResult.orderId,
        type: request.orderType,
        side: request.side,
        quantity: request.quantity,
        requestedPrice: request.price,
        executedPrice: executionResult.executedPrice,
        commission: executionResult.commission
      });

      return {
        success: true,
        orderId: executionResult.orderId!,
        executedPrice: executionResult.executedPrice!,
        executedQuantity: executionResult.executedQuantity!,
        commission: executionResult.commission!,
        timestamp: new Date(),
        metadata: {
          engine: 'real-paper-trading',
          slippage: executionResult.slippage || 0
        }
      };

    } catch (error) {
      this.logger.error('真实引擎：订单执行失败', {
        error: error instanceof Error ? error.message : String(error),
        orderRequest: request
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : '订单执行异常',
        timestamp: new Date()
      };
    }
  }

  /**
   * 批量取消订单
   */
  async cancelOrders(orderIds: string[]): Promise<{ [orderId: string]: boolean }> {
    const results: { [orderId: string]: boolean } = {};

    for (const orderId of orderIds) {
      try {
        // 使用真实的订单取消逻辑
        const cancelSuccess = await this.orderExecutionEngine.cancelOrder(orderId);
        results[orderId] = cancelSuccess;

        this.logger.debug('真实引擎：订单取消', { orderId, success: results[orderId] });
      } catch (error) {
        results[orderId] = false;
        this.logger.error('真实引擎：取消订单失败', { orderId, error });
      }
    }

    return results;
  }

  /**
   * 修改订单
   */
  async modifyOrder(
    orderId: string,
    newPrice?: number,
    newQuantity?: number
  ): Promise<OrderExecutionResult> {
    try {
      // 模拟订单修改
      await new Promise(resolve => setTimeout(resolve, 100));

      this.logger.info('模拟引擎：订单修改成功', {
        orderId,
        newPrice,
        newQuantity
      });

      return {
        success: true,
        orderId,
        timestamp: new Date(),
        metadata: {
          engine: 'simulation',
          modified: true
        }
      };

    } catch (error) {
      this.logger.error('模拟引擎：修改订单失败', {
        error: error instanceof Error ? error.message : String(error),
        orderId
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : '修改订单失败',
        timestamp: new Date()
      };
    }
  }

  /**
   * 更新止损
   */
  async updateStopLoss(request: StopLossUpdateRequest): Promise<UpdateResult> {
    try {
      await new Promise(resolve => setTimeout(resolve, 50));

      this.logger.info('模拟引擎：止损更新成功', {
        positionId: request.positionId,
        newStopLoss: request.newStopLoss,
        reason: request.reason
      });

      return {
        success: true,
        updatedValue: request.newStopLoss,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        updatedValue: 0,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : '更新止损失败'
      };
    }
  }

  /**
   * 更新止盈
   */
  async updateTakeProfit(request: TakeProfitUpdateRequest): Promise<UpdateResult> {
    try {
      await new Promise(resolve => setTimeout(resolve, 50));

      this.logger.info('模拟引擎：止盈更新成功', {
        positionId: request.positionId,
        newTakeProfit: request.newTakeProfit,
        reason: request.reason
      });

      return {
        success: true,
        updatedValue: request.newTakeProfit,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        updatedValue: 0,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : '更新止盈失败'
      };
    }
  }

  /**
   * 获取持仓信息
   */
  async getPositions(accountId: string): Promise<PositionInfo[]> {
    try {
      const positions = await this.prisma.tradingPositions.findMany({
        where: {
          accountId,
          status: 'OPEN'
        },
        include: {
          symbols: true
        }
      });

      return positions.map(pos => ({
        symbol: pos.symbols?.symbol || 'UNKNOWN',
        side: (pos.side === 'LONG' ? 'long' : 'short') as 'long' | 'short',
        size: pos.quantity.toNumber(),
        entryPrice: pos.entryPrice.toNumber(),
        currentPrice: pos.currentPrice?.toNumber() || pos.entryPrice.toNumber(),
        markPrice: pos.currentPrice?.toNumber() || pos.entryPrice.toNumber(),
        unrealizedPnl: pos.unrealizedPnl.toNumber(),
        realizedPnl: 0,
        percentage: 0,
        margin: pos.quantity.toNumber() * pos.entryPrice.toNumber() / pos.leverage,
        leverage: pos.leverage,
        liquidationPrice: 0,
        marginType: 'isolated',
        timestamp: new Date()
      } as PositionInfo));

    } catch (error) {
      this.logger.error('获取模拟持仓失败', { error, accountId });
      return [];
    }
  }

  /**
   * 获取账户余额
   */
  async getAccountBalance(accountId: string): Promise<AccountBalance> {
    try {
      // 从数据库获取真实账户余额
      const account = await this.prisma.tradingAccounts.findUnique({
        where: { id: accountId }
      });

      if (!account) {
        throw new Error(`账户不存在: ${accountId}`);
      }

      // 计算已用保证金（基于当前持仓）
      const positions = await this.prisma.tradingPositions.findMany({
        where: {
          accountId,
          status: 'OPEN'
        }
      });

      const marginUsed = positions.reduce((total, pos) => {
        return total + pos.marginUsed.toNumber();
      }, 0);

      // 计算未实现盈亏
      const unrealizedPnl = positions.reduce((total, pos) => {
        return total + pos.unrealizedPnl.toNumber();
      }, 0);

      const realBalance = {
        totalBalance: account.currentBalance.toNumber(),
        availableBalance: account.availableBalance.toNumber(),
        marginUsed: marginUsed,
        unrealizedPnl: unrealizedPnl,
        currency: 'USDT',
        timestamp: new Date()
      };

      this.logger.debug('模拟引擎：获取账户余额（真实数据）', {
        accountId,
        balance: realBalance
      });

      return realBalance;
    } catch (error) {
      this.logger.error('模拟引擎：获取账户余额失败', { accountId, error });
      throw error;
    }
  }

  /**
   * 获取订单状态
   */
  async getOrderStatus(orderId: string): Promise<OrderStatus> {
    // 使用真实的订单状态查询
    const orderStatus = await this.orderExecutionEngine.getOrderStatus(orderId);

    if (!orderStatus) {
      return {
        id: orderId,
        status: 'PENDING',
        filledQuantity: 0,
        averagePrice: undefined,
        timestamp: new Date()
      };
    }

    return {
      id: orderId,
      status: orderStatus.status || 'PENDING',
      filledQuantity: orderStatus.executedQuantity || 0,
      averagePrice: orderStatus.executedPrice,
      timestamp: orderStatus.executionTime || new Date()
    };
  }

  /**
   * 获取当前价格
   */
  async getCurrentPrice(symbol: string): Promise<number> {
    try {
      // 使用真实的价格数据源
      const priceData = await this.getRealPriceFromExchange(symbol);
      return priceData;
    } catch (error) {
      this.logger.warn('获取真实价格失败，尝试备用数据源', { symbol, error });
      return await this.getFallbackPrice(symbol);
    }
  }

  /**
   * 备用价格获取策略
   */
  private async getFallbackPrice(symbol: string): Promise<number> {
    const fallbackStrategies = [
      () => this.getPriceFromBinanceAPI(symbol),
      () => this.getPriceFromCoinGeckoAPI(symbol),
      () => this.getLastKnownPrice(symbol)
    ];

    for (let i = 0; i < fallbackStrategies.length; i++) {
      try {
        this.logger.info(`尝试备用价格源 ${i + 1}`, { symbol });
        const price = await fallbackStrategies[i]();

        if (price > 0) {
          this.logger.info(`备用价格源 ${i + 1} 成功`, { symbol, price });
          return price;
        }
      } catch (fallbackError) {
        this.logger.warn(`备用价格源 ${i + 1} 失败`, {
          symbol,
          error: fallbackError instanceof Error ? fallbackError.message : String(fallbackError)
        });
        continue;
      }
    }

    // 如果所有备用策略都失败，抛出错误而不是返回固定价格
    throw new Error(`无法获取符号 ${symbol} 的价格数据，所有数据源都不可用`);
  }

  /**
   * 获取市场深度
   */
  async getMarketDepth(symbol: string): Promise<MarketDepth> {
    try {
      // 获取真实的市场深度数据
      const realDepth = await this.getRealMarketDepthFromExchange(symbol);
      return realDepth;
    } catch (error) {
      this.logger.warn('获取真实市场深度失败，使用默认深度', { symbol, error });

      // 返回基本的市场深度结构
      const currentPrice = await this.getCurrentPrice(symbol);
      const bids: [number, number][] = [
        [currentPrice - 10, 1.0],
        [currentPrice - 20, 2.0],
        [currentPrice - 30, 1.5]
      ];
      const asks: [number, number][] = [
        [currentPrice + 10, 1.0],
        [currentPrice + 20, 2.0],
        [currentPrice + 30, 1.5]
      ];

      return {
        bids,
        asks,
        timestamp: new Date()
      };
    }
  }

  /**
   * 连接
   */
  async connect(): Promise<void> {
    this.connected = true;
    this.logger.info('模拟引擎：连接成功');
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    this.connected = false;
    this.logger.info('模拟引擎：已断开连接');
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.connected;
  }

  /**
   * 验证订单
   */
  async validateOrder(request: OrderExecutionRequest): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 基本验证
    if (request.quantity <= 0) {
      errors.push('订单数量必须大于0');
    }

    if (request.price && request.price <= 0) {
      errors.push('订单价格必须大于0');
    }

    // 模拟风险检查
    if (request.quantity > 10) {
      warnings.push('订单数量较大，请注意风险');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      maxPositionSize: 10
    };
  }

  /**
   * 检查风险限制
   */
  async checkRiskLimits(accountId: string): Promise<RiskCheckResult> {
    try {
      // 获取真实的账户风险数据
      const accountData = await this.getAccountRiskData(accountId);
      const riskLevel = this.calculateRealRiskLevel(accountData);

      let level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
      let canTrade = true;
      const reasons: string[] = [];

      if (riskLevel < 0.3) {
        level = 'LOW';
      } else if (riskLevel < 0.6) {
        level = 'MEDIUM';
        reasons.push('账户使用率较高');
      } else if (riskLevel < 0.8) {
        level = 'HIGH';
        reasons.push('账户风险较高，建议减少仓位');
      } else {
        level = 'CRITICAL';
        canTrade = false;
        reasons.push('账户风险过高，暂停交易');
      }

    return {
      canTrade,
      riskLevel: level,
      reasons,
      maxPositionSize: canTrade ? 5 : 0
    };
    } catch (error) {
      this.logger.error('风险检查失败', { accountId, error });
      return {
        canTrade: false,
        riskLevel: 'CRITICAL',
        reasons: ['风险检查系统异常'],
        maxPositionSize: 0
      };
    }
  }

  // 私有方法

  /**
   * 从数据库获取仓位信息
   */
  private async getPositionFromDatabase(positionId: string): Promise<any> {
    try {
      // 这里应该查询真实的数据库
      // 暂时返回null，表示需要实现
      return null;
    } catch (error) {
      this.logger.error('获取仓位信息失败', { positionId, error });
      return null;
    }
  }

  /**
   * 从交易所获取真实价格
   */
  private async getRealPriceFromExchange(symbol: string): Promise<number> {
    try {
      this.logger.info('获取交易所真实价格', { symbol });

      // 调用真实的交易所API
      const price = await this.getPriceFromBinanceAPI(symbol);

      if (price <= 0) {
        throw new Error('获取到无效的价格数据');
      }

      this.logger.info('成功获取交易所价格', { symbol, price });
      return price;
    } catch (error) {
      this.logger.error('获取交易所价格失败', {
        symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`无法获取交易所价格: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 从Binance API获取价格
   */
  private async getPriceFromBinanceAPI(symbol: string): Promise<number> {
    try {
      const binanceSymbol = symbol.replace('/', '').replace('-', '').toUpperCase();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${binanceSymbol}`, {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Binance API错误: ${response.status}`);
      }

      const data = await response.json() as any;
      const price = parseFloat(data.price);

      if (isNaN(price) || price <= 0) {
        throw new Error('Binance返回无效价格数据');
      }

      return price;
    } catch (error) {
      this.logger.error('Binance API价格获取失败', { symbol, error });
      throw error;
    }
  }

  /**
   * 从CoinGecko API获取价格
   */
  private async getPriceFromCoinGeckoAPI(symbol: string): Promise<number> {
    try {
      // 简化的CoinGecko API调用
      const coinId = symbol.toLowerCase().replace('/', '-').replace('usdt', 'usd');
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${coinId}&vs_currencies=usd`, {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`CoinGecko API错误: ${response.status}`);
      }

      const data = await response.json();
      const coinData = data[coinId];

      if (!coinData || !coinData.usd) {
        throw new Error(`CoinGecko未找到符号: ${symbol}`);
      }

      const price = coinData.usd;
      if (isNaN(price) || price <= 0) {
        throw new Error('CoinGecko返回无效价格数据');
      }

      return price;
    } catch (error) {
      this.logger.error('CoinGecko API价格获取失败', { symbol, error });
      throw error;
    }
  }

  /**
   * 获取最后已知价格（从缓存或数据库）
   */
  private async getLastKnownPrice(symbol: string): Promise<number> {
    try {
      // 从数据库查询最近的价格记录
      const lastPrice = await this.prisma.priceData.findFirst({
        where: { Symbols: { symbol } },
        orderBy: { timestamp: 'desc' },
        select: { price: true, timestamp: true }
      });

      if (!lastPrice) {
        throw new Error(`未找到符号 ${symbol} 的历史价格记录`);
      }

      // 检查价格是否过期（超过1小时）
      const now = new Date();
      const priceAge = now.getTime() - lastPrice.timestamp.getTime();
      const oneHour = 60 * 60 * 1000;

      if (priceAge > oneHour) {
        this.logger.warn('历史价格数据过期', {
          symbol,
          priceAge: Math.round(priceAge / 1000 / 60),
          price: lastPrice.price
        });
      }

      return lastPrice.price.toNumber();
    } catch (error) {
      this.logger.error('获取历史价格失败', { symbol, error });
      throw error;
    }
  }

  /**
   * 从交易所获取真实市场深度
   */
  private async getRealMarketDepthFromExchange(symbol: string): Promise<MarketDepth> {
    try {
      this.logger.info('获取交易所真实市场深度', { symbol });

      // 调用真实的交易所API获取市场深度
      const marketDepth = await this.getMarketDepthFromBinanceAPI(symbol);

      this.logger.info('成功获取市场深度', {
        symbol,
        bidLevels: marketDepth.bids.length,
        askLevels: marketDepth.asks.length
      });

      return marketDepth;
    } catch (error) {
      this.logger.error('获取市场深度失败', {
        symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`无法获取市场深度: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 从Binance API获取市场深度
   */
  private async getMarketDepthFromBinanceAPI(symbol: string): Promise<MarketDepth> {
    try {
      const binanceSymbol = symbol.replace('/', '').replace('-', '').toUpperCase();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`https://api.binance.com/api/v3/depth?symbol=${binanceSymbol}&limit=20`, {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Binance API错误: ${response.status}`);
      }

      const data = await response.json() as any;

      // 验证数据格式
      if (!data.bids || !data.asks || !Array.isArray(data.bids) || !Array.isArray(data.asks)) {
        throw new Error('Binance返回无效的市场深度数据');
      }

      // 转换数据格式
      const bids: [number, number][] = data.bids.map((bid: string[]) => [
        parseFloat(bid[0]), // 价格
        parseFloat(bid[1])  // 数量
      ]).filter((bid: [number, number]) => !isNaN(bid[0]) && !isNaN(bid[1]) && bid[0] > 0 && bid[1] > 0);

      const asks: [number, number][] = data.asks.map((ask: string[]) => [
        parseFloat(ask[0]), // 价格
        parseFloat(ask[1])  // 数量
      ]).filter((ask: [number, number]) => !isNaN(ask[0]) && !isNaN(ask[1]) && ask[0] > 0 && ask[1] > 0);

      if (bids.length === 0 || asks.length === 0) {
        throw new Error('Binance返回空的市场深度数据');
      }

      return {
        bids,
        asks,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('Binance市场深度获取失败', { symbol, error });
      throw error;
    }
  }

  /**
   * 获取账户风险数据
   */
  private async getAccountRiskData(accountId: string): Promise<any> {
    try {
      // 这里应该从数据库或风险管理系统获取真实数据
      const account = await this.prisma.tradingAccounts.findUnique({
        where: { id: accountId },
        include: {
          TradingPositions: true,
          tradingOrders: true
        }
      });

      return account;
    } catch (error) {
      this.logger.error('获取账户风险数据失败', { accountId, error });
      return null;
    }
  }

  /**
   * 计算真实风险水平
   */
  private calculateRealRiskLevel(accountData: any): number {
    if (!accountData) {
      return 0.9; // 无数据时返回高风险
    }

    // 基于真实数据计算风险水平
    let riskScore = 0;

    // 1. 仓位风险 (40%权重)
    const positionRisk = this.calculatePositionRisk(accountData.positions || []);
    riskScore += positionRisk * 0.4;

    // 2. 账户余额风险 (30%权重)
    const balanceRisk = this.calculateBalanceRisk(accountData);
    riskScore += balanceRisk * 0.3;

    // 3. 交易频率风险 (20%权重)
    const frequencyRisk = this.calculateFrequencyRisk(accountData.orders || []);
    riskScore += frequencyRisk * 0.2;

    // 4. 历史损失风险 (10%权重)
    const lossRisk = this.calculateLossRisk(accountData);
    riskScore += lossRisk * 0.1;

    return Math.min(1, Math.max(0, riskScore));
  }

  private calculatePositionRisk(positions: any[]): number {
    if (positions.length === 0) return 0;

    // 基于仓位数量和大小计算风险
    const totalPositions = positions.length;
    const maxPositions = 10; // 最大建议仓位数

    return Math.min(1, totalPositions / maxPositions);
  }

  private calculateBalanceRisk(accountData: any): number {
    // 基于账户余额和使用率计算风险
    const balance = accountData.balance || 0;
    const usedMargin = accountData.usedMargin || 0;

    if (balance <= 0) return 1;

    const utilizationRate = usedMargin / balance;
    return Math.min(1, utilizationRate);
  }

  private calculateFrequencyRisk(orders: any[]): number {
    // 基于交易频率计算风险
    const recentOrders = orders.filter(order => {
      const orderTime = new Date(order.createdAt);
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      return orderTime > oneDayAgo;
    });

    const maxDailyOrders = 50; // 最大日交易次数
    return Math.min(1, recentOrders.length / maxDailyOrders);
  }

  private calculateLossRisk(accountData: any): number {
    // 基于历史损失计算风险
    const totalPnl = accountData.totalPnl || 0;
    const initialBalance = accountData.initialBalance || accountData.balance || 1;

    if (totalPnl >= 0) return 0;

    const lossRate = Math.abs(totalPnl) / initialBalance;
    return Math.min(1, lossRate * 2); // 损失50%时风险为1
  }
}
