/**
 * 订单执行器 - 负责执行交易订单（真实执行逻辑）
 */

import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { TradingPosition } from '../entities/trading-position';
import { TradingAccount } from '../entities/trading-account';
import { IRealOrderExecutionEngine } from './real-order-execution-engine';
import { OrderLifecycleManager, OrderStatus, OrderType, OrderSide } from '../../../../shared/infrastructure/order/order-lifecycle-manager';

export interface OrderRequest {
  accountId: string;
  symbolId: string;
  orderType: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
  side: 'BUY' | 'SELL';
  quantity: number;
  price?: number;
  stopPrice?: number;
  positionId?: string;
  metadata?: any;
}

export interface OrderResult {
  success: boolean;
  orderId?: string;
  executedPrice?: number;
  executedQuantity?: number;
  commission?: number;
  error?: string;
  timestamp: Date;
}

export interface PositionOpenRequest {
  account: TradingAccount;
  symbolId: string;
  signalId: string;
  side: 'LONG' | 'SHORT';
  entryPrice: number;
  quantity: number;
  marginUsed: number;
  stopLoss: number;
  takeProfit: number;
  atrValue?: number;
  metadata?: any;
}

export interface PositionOpenResult {
  success: boolean;
  position?: TradingPosition;
  orders: OrderResult[];
  error?: string;
}

@injectable()
export class OrderExecutor {
  constructor(
    private readonly logger: Logger,
    private readonly realExecutionEngine: IRealOrderExecutionEngine,
    private readonly orderLifecycleManager: OrderLifecycleManager
  ) {}

  /**
   * 开启新仓位
   */
  async openPosition(request: PositionOpenRequest): Promise<PositionOpenResult> {
    try {
      this.logger.info('开始开启新仓位', {
        accountId: request.account.id,
        side: request.side,
        quantity: request.quantity,
        entryPrice: request.entryPrice
      });

      // 1. 验证账户状态
      if (!request.account.canOpenNewPosition()) {
        return {
          success: false,
          orders: [],
          error: '账户风险限制，无法开新仓位'
        };
      }

      // 2. 创建仓位实体
      const position = TradingPosition.create({
        accountId: request.account.id,
        symbolId: request.symbolId,
        signalId: request.signalId,
        side: request.side,
        entryPrice: request.entryPrice,
        quantity: request.quantity,
        leverage: request.account.riskSettings.leverage,
        marginUsed: request.marginUsed,
        stopLoss: request.stopLoss,
        takeProfit: request.takeProfit,
        atrValue: request.atrValue,
        metadata: request.metadata
      });

      // 3. 执行市价开仓订单
      const entryOrder = await this.executeOrder({
        accountId: request.account.id,
        symbolId: request.symbolId,
        orderType: 'MARKET',
        side: request.side === 'LONG' ? 'BUY' : 'SELL',
        quantity: request.quantity,
        price: request.entryPrice,
        positionId: position.id,
        metadata: {
          orderPurpose: 'ENTRY',
          signalId: request.signalId
        }
      });

      if (!entryOrder.success) {
        return {
          success: false,
          orders: [entryOrder],
          error: `开仓订单执行失败: ${entryOrder.error}`
        };
      }

      // 4. 设置止损订单
      const stopLossOrder = await this.executeOrder({
        accountId: request.account.id,
        symbolId: request.symbolId,
        orderType: 'STOP',
        side: request.side === 'LONG' ? 'SELL' : 'BUY',
        quantity: request.quantity,
        stopPrice: request.stopLoss,
        positionId: position.id,
        metadata: {
          orderPurpose: 'STOP_LOSS'
        }
      });

      // 5. 设置止盈订单
      const takeProfitOrder = await this.executeOrder({
        accountId: request.account.id,
        symbolId: request.symbolId,
        orderType: 'LIMIT',
        side: request.side === 'LONG' ? 'SELL' : 'BUY',
        quantity: request.quantity,
        price: request.takeProfit,
        positionId: position.id,
        metadata: {
          orderPurpose: 'TAKE_PROFIT'
        }
      });

      // 6. 更新仓位价格（使用实际执行价格）
      if (entryOrder.executedPrice) {
        position.updateCurrentPrice(entryOrder.executedPrice);
      }

      this.logger.info('新仓位开启成功', {
        positionId: position.id,
        entryPrice: entryOrder.executedPrice,
        stopLoss: request.stopLoss,
        takeProfit: request.takeProfit
      });

      return {
        success: true,
        position,
        orders: [entryOrder, stopLossOrder, takeProfitOrder]
      };

    } catch (error) {
      this.logger.error('开启仓位失败', {
        error: error instanceof Error ? error.message : String(error),
        accountId: request.account.id
      });

      return {
        success: false,
        orders: [],
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 平仓
   */
  async closePosition(
    position: TradingPosition, 
    closePrice: number, 
    reason: string
  ): Promise<OrderResult> {
    try {
      this.logger.info('开始平仓', {
        positionId: position.id,
        side: position.side,
        quantity: position.quantity,
        closePrice,
        reason
      });

      const closeOrder = await this.executeOrder({
        accountId: position.accountId,
        symbolId: position.symbolId,
        orderType: 'MARKET',
        side: position.side === 'LONG' ? 'SELL' : 'BUY',
        quantity: position.quantity,
        price: closePrice,
        positionId: position.id,
        metadata: {
          orderPurpose: 'CLOSE',
          closeReason: reason
        }
      });

      if (closeOrder.success) {
        // 更新仓位状态
        position.close(closeOrder.executedPrice || closePrice);
        
        this.logger.info('平仓成功', {
          positionId: position.id,
          executedPrice: closeOrder.executedPrice,
          realizedPnl: position.realizedPnl
        });
      }

      return closeOrder;

    } catch (error) {
      this.logger.error('平仓失败', {
        error: error instanceof Error ? error.message : String(error),
        positionId: position.id
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date()
      };
    }
  }

  /**
   * 执行单个订单（真实执行逻辑）
   */
  private async executeOrder(request: OrderRequest): Promise<OrderResult> {
    let systemOrderId: string | null = null;

    try {
      // 🔥 创建订单生命周期记录
      systemOrderId = await this.orderLifecycleManager.createOrder({
        accountId: request.accountId,
        symbol: request.symbolId,
        type: request.orderType as OrderType,
        side: request.side as OrderSide,
        quantity: request.quantity,
        price: request.price,
        stopPrice: request.stopPrice,
        metadata: request.metadata
      });

      // 🔥 更新订单状态为等待执行
      await this.orderLifecycleManager.updateOrderStatus(
        systemOrderId,
        OrderStatus.PENDING,
        undefined,
        '订单准备执行'
      );

      // 转换为真实执行引擎的请求格式
      const executionRequest = {
        accountId: request.accountId,
        symbolId: request.symbolId,
        orderType: request.orderType,
        side: request.side,
        quantity: request.quantity,
        price: request.price,
        stopPrice: request.stopPrice,
        clientOrderId: systemOrderId // 使用系统订单ID作为客户端订单ID
      };

      // 🔥 更新订单状态为已提交
      await this.orderLifecycleManager.updateOrderStatus(
        systemOrderId,
        OrderStatus.SUBMITTED,
        undefined,
        '订单已提交到交易所'
      );

      // 使用真实执行引擎
      const executionResult = await this.realExecutionEngine.executeOrder(executionRequest);

      if (!executionResult.success) {
        // 🔥 更新订单状态为失败
        await this.orderLifecycleManager.updateOrderStatus(
          systemOrderId,
          OrderStatus.FAILED,
          {
            executedAt: new Date()
          },
          executionResult.error || '订单执行失败'
        );

        return {
          success: false,
          orderId: systemOrderId,
          error: executionResult.error || '订单执行失败',
          timestamp: new Date()
        };
      }

      // 🔥 更新订单状态为已成交
      await this.orderLifecycleManager.updateOrderStatus(
        systemOrderId,
        OrderStatus.FILLED,
        {
          filledQuantity: executionResult.executedQuantity || request.quantity,
          averagePrice: executionResult.executedPrice || request.price || 0,
          commission: executionResult.commission || 0,
          exchangeOrderId: executionResult.orderId,
          executedAt: new Date()
        },
        '订单执行成功'
      );

      this.logger.info('订单执行成功', {
        systemOrderId,
        exchangeOrderId: executionResult.orderId,
        type: request.orderType,
        side: request.side,
        quantity: request.quantity,
        requestedPrice: request.price,
        executedPrice: executionResult.executedPrice,
        commission: executionResult.commission
      });

      return {
        success: true,
        orderId: systemOrderId, // 返回系统订单ID
        executedPrice: executionResult.executedPrice || 0,
        executedQuantity: executionResult.executedQuantity || request.quantity,
        commission: executionResult.commission || 0,
        timestamp: new Date()
      };

    } catch (error) {
      // 🔥 如果有系统订单ID，更新订单状态为失败
      if (systemOrderId) {
        try {
          await this.orderLifecycleManager.updateOrderStatus(
            systemOrderId,
            OrderStatus.FAILED,
            {
              executedAt: new Date()
            },
            error instanceof Error ? error.message : '订单执行异常'
          );
        } catch (updateError) {
          this.logger.error('更新订单状态失败', {
            systemOrderId,
            updateError: updateError instanceof Error ? updateError.message : String(updateError)
          });
        }
      }

      this.logger.error('订单执行失败', {
        systemOrderId,
        error: error instanceof Error ? error.message : String(error),
        orderRequest: request
      });

      return {
        success: false,
        orderId: systemOrderId,
        error: error instanceof Error ? error.message : '订单执行异常',
        timestamp: new Date()
      };
    }
  }

  /**
   * 批量取消订单
   */
  async cancelOrders(orderIds: string[]): Promise<{ [orderId: string]: boolean }> {
    const results: { [orderId: string]: boolean } = {};

    for (const orderId of orderIds) {
      try {
        // 使用真实执行引擎取消订单
        const cancelSuccess = await this.realExecutionEngine.cancelOrder(orderId);
        results[orderId] = cancelSuccess;

        this.logger.debug('订单取消', { orderId, success: results[orderId] });
      } catch (error) {
        results[orderId] = false;
        this.logger.error('取消订单失败', { orderId, error });
      }
    }

    return results;
  }

  /**
   * 修改订单
   */
  async modifyOrder(
    orderId: string, 
    newPrice?: number, 
    newQuantity?: number
  ): Promise<OrderResult> {
    try {
      // 模拟订单修改
      await new Promise(resolve => setTimeout(resolve, 100));

      this.logger.info('订单修改成功', {
        orderId,
        newPrice,
        newQuantity
      });

      return {
        success: true,
        orderId,
        timestamp: new Date()
      };

    } catch (error) {
      this.logger.error('修改订单失败', {
        error: error instanceof Error ? error.message : String(error),
        orderId
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : '修改订单失败',
        timestamp: new Date()
      };
    }
  }
}
