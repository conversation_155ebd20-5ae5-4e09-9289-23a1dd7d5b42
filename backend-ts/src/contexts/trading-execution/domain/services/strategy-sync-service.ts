/**
 * 策略同步服务
 * 实现模拟环境验证通过后同步到真实环境的完整机制
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { PrismaClient } from '@prisma/client';

/**
 * 策略同步配置
 */
export interface StrategySyncConfig {
  // 同步触发条件
  minSuccessRate: number;          // 最小成功率阈值
  minTradeCount: number;           // 最小交易次数
  minProfitRate: number;           // 最小盈利率
  maxDrawdown: number;             // 最大回撤限制
  
  // 风险调整参数
  riskReductionFactor: number;     // 风险降低因子（如0.5表示真实交易风险降低50%）
  positionSizeReduction: number;   // 仓位大小调整因子
  leverageReduction: number;       // 杠杆调整因子
  
  // 同步控制
  enableAutoSync: boolean;         // 是否启用自动同步
  syncCooldown: number;           // 同步冷却时间（毫秒）
  maxSyncAttempts: number;        // 最大同步尝试次数
}

/**
 * 策略验证结果
 */
export interface StrategyValidationResult {
  isValid: boolean;
  successRate: number;
  tradeCount: number;
  profitRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  validationPeriod: {
    start: Date;
    end: Date;
    durationDays: number;
  };
  failureReasons: string[];
  recommendations: string[];
}

/**
 * 策略同步结果
 */
export interface StrategySyncResult {
  success: boolean;
  syncId: string;
  sourceAccountId: string;
  targetAccountId: string;
  syncedParameters: any;
  adjustedParameters: any;
  syncTimestamp: Date;
  validationResult: StrategyValidationResult;
  errors: string[];
  warnings: string[];
}

/**
 * 策略同步状态
 */
export enum StrategySyncStatus {
  PENDING = 'PENDING',
  VALIDATING = 'VALIDATING',
  APPROVED = 'APPROVED',
  SYNCING = 'SYNCING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

/**
 * 策略同步记录
 */
export interface StrategySyncRecord {
  id: string;
  sourceAccountId: string;
  targetAccountId: string;
  status: StrategySyncStatus;
  config: StrategySyncConfig;
  validationResult?: StrategyValidationResult;
  syncResult?: StrategySyncResult;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

/**
 * 策略同步服务接口
 */
export interface IStrategySyncService {
  // 验证策略是否可以同步
  validateStrategy(accountId: string, config: StrategySyncConfig): Promise<StrategyValidationResult>;
  
  // 执行策略同步
  syncStrategy(sourceAccountId: string, targetAccountId: string, config: StrategySyncConfig): Promise<StrategySyncResult>;
  
  // 获取同步状态
  getSyncStatus(syncId: string): Promise<StrategySyncRecord>;
  
  // 获取同步历史
  getSyncHistory(accountId: string): Promise<StrategySyncRecord[]>;
  
  // 取消同步
  cancelSync(syncId: string): Promise<boolean>;
  
  // 自动同步检查
  checkAutoSync(): Promise<void>;
}

/**
 * 策略同步服务实现
 */
@injectable()
export class StrategySyncService implements IStrategySyncService {
  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Database) private readonly prisma: PrismaClient
  ) {}

  /**
   * 验证策略是否可以同步
   */
  async validateStrategy(accountId: string, config: StrategySyncConfig): Promise<StrategyValidationResult> {
    try {
      this.logger.info('开始验证策略同步条件', { accountId });

      // 获取账户的交易历史
      const trades = await this.getAccountTrades(accountId);
      
      if (trades.length === 0) {
        return {
          isValid: false,
          successRate: 0,
          tradeCount: 0,
          profitRate: 0,
          maxDrawdown: 0,
          sharpeRatio: 0,
          validationPeriod: {
            start: new Date(),
            end: new Date(),
            durationDays: 0
          },
          failureReasons: ['没有足够的交易历史数据'],
          recommendations: ['建议先在模拟环境中进行更多交易以积累数据']
        };
      }

      // 计算性能指标
      const metrics = await this.calculatePerformanceMetrics(trades);
      
      // 验证各项指标
      const failureReasons: string[] = [];
      const recommendations: string[] = [];

      if (metrics.successRate < config.minSuccessRate) {
        failureReasons.push(`成功率${(metrics.successRate * 100).toFixed(1)}%低于要求的${(config.minSuccessRate * 100).toFixed(1)}%`);
        recommendations.push('建议优化交易策略以提高成功率');
      }

      if (metrics.tradeCount < config.minTradeCount) {
        failureReasons.push(`交易次数${metrics.tradeCount}低于要求的${config.minTradeCount}`);
        recommendations.push('建议积累更多交易数据');
      }

      if (metrics.profitRate < config.minProfitRate) {
        failureReasons.push(`盈利率${(metrics.profitRate * 100).toFixed(2)}%低于要求的${(config.minProfitRate * 100).toFixed(2)}%`);
        recommendations.push('建议优化策略以提高盈利能力');
      }

      if (metrics.maxDrawdown > config.maxDrawdown) {
        failureReasons.push(`最大回撤${(metrics.maxDrawdown * 100).toFixed(2)}%超过限制的${(config.maxDrawdown * 100).toFixed(2)}%`);
        recommendations.push('建议加强风险控制以降低回撤');
      }

      const isValid = failureReasons.length === 0;

      const result: StrategyValidationResult = {
        isValid,
        successRate: metrics.successRate,
        tradeCount: metrics.tradeCount,
        profitRate: metrics.profitRate,
        maxDrawdown: metrics.maxDrawdown,
        sharpeRatio: metrics.sharpeRatio,
        validationPeriod: metrics.period,
        failureReasons,
        recommendations
      };

      this.logger.info('策略验证完成', { 
        accountId, 
        isValid, 
        successRate: metrics.successRate,
        tradeCount: metrics.tradeCount 
      });

      return result;

    } catch (error) {
      this.logger.error('策略验证失败', { accountId, error });
      throw error;
    }
  }

  /**
   * 执行策略同步
   */
  async syncStrategy(
    sourceAccountId: string, 
    targetAccountId: string, 
    config: StrategySyncConfig
  ): Promise<StrategySyncResult> {
    const syncId = this.generateSyncId();
    
    try {
      this.logger.info('开始执行策略同步', { 
        syncId, 
        sourceAccountId, 
        targetAccountId 
      });

      // 1. 验证源策略
      const validationResult = await this.validateStrategy(sourceAccountId, config);
      
      if (!validationResult.isValid) {
        return {
          success: false,
          syncId,
          sourceAccountId,
          targetAccountId,
          syncedParameters: {},
          adjustedParameters: {},
          syncTimestamp: new Date(),
          validationResult,
          errors: ['策略验证未通过', ...validationResult.failureReasons],
          warnings: []
        };
      }

      // 2. 获取源账户的策略参数
      const sourceParameters = await this.getAccountParameters(sourceAccountId);
      
      // 3. 调整参数以适应真实交易
      const adjustedParameters = this.adjustParametersForLiveTrading(sourceParameters, config);
      
      // 4. 应用到目标账户
      await this.applyParametersToAccount(targetAccountId, adjustedParameters);
      
      // 5. 记录同步结果
      const syncResult: StrategySyncResult = {
        success: true,
        syncId,
        sourceAccountId,
        targetAccountId,
        syncedParameters: sourceParameters,
        adjustedParameters,
        syncTimestamp: new Date(),
        validationResult,
        errors: [],
        warnings: this.generateSyncWarnings(sourceParameters, adjustedParameters)
      };

      // 6. 保存同步记录
      await this.saveSyncRecord(syncResult, config);

      this.logger.info('策略同步完成', { syncId, success: true });
      
      return syncResult;

    } catch (error) {
      this.logger.error('策略同步失败', { syncId, error });
      
      return {
        success: false,
        syncId,
        sourceAccountId,
        targetAccountId,
        syncedParameters: {},
        adjustedParameters: {},
        syncTimestamp: new Date(),
        validationResult: await this.validateStrategy(sourceAccountId, config),
        errors: [error instanceof Error ? error.message : '未知错误'],
        warnings: []
      };
    }
  }

  /**
   * 获取同步状态
   */
  async getSyncStatus(syncId: string): Promise<StrategySyncRecord> {
    try {
      const record = await this.prisma.strategySyncRecords.findUnique({
        where: { id: syncId }
      });

      if (!record) {
        throw new Error(`同步记录不存在: ${syncId}`);
      }

      return this.mapToStrategySyncRecord(record);

    } catch (error) {
      this.logger.error('获取同步状态失败', { syncId, error });
      throw error;
    }
  }

  /**
   * 获取同步历史
   */
  async getSyncHistory(accountId: string): Promise<StrategySyncRecord[]> {
    try {
      const records = await this.prisma.strategySyncRecords.findMany({
        where: {
          OR: [
            { sourceAccountId: accountId },
            { targetAccountId: accountId }
          ]
        },
        orderBy: { createdAt: 'desc' },
        take: 50
      });

      return records.map(record => this.mapToStrategySyncRecord(record));

    } catch (error) {
      this.logger.error('获取同步历史失败', { accountId, error });
      throw error;
    }
  }

  /**
   * 取消同步
   */
  async cancelSync(syncId: string): Promise<boolean> {
    try {
      await this.prisma.strategySyncRecords.update({
        where: { id: syncId },
        data: { 
          status: StrategySyncStatus.CANCELLED,
          updatedAt: new Date()
        }
      });

      this.logger.info('同步已取消', { syncId });
      return true;

    } catch (error) {
      this.logger.error('取消同步失败', { syncId, error });
      return false;
    }
  }

  /**
   * 自动同步检查
   */
  async checkAutoSync(): Promise<void> {
    try {
      this.logger.debug('开始自动同步检查');

      // 获取启用自动同步的账户配置
      const accounts = await this.getAutoSyncEnabledAccounts();
      
      for (const account of accounts) {
        await this.processAutoSync(account);
      }

      this.logger.debug('自动同步检查完成');

    } catch (error) {
      this.logger.error('自动同步检查失败', { error });
    }
  }

  // 私有辅助方法
  private generateSyncId(): string {
    // 🔥 使用真实的UUID生成器替换随机数
    const { v4: uuidv4 } = require('uuid');
    return `sync_${Date.now()}_${uuidv4().replace(/-/g, '').substring(0, 9)}`;
  }

  private async getAccountTrades(accountId: string): Promise<any[]> {
    try {
      // 获取账户的交易订单历史
      const trades = await this.prisma.tradingOrders.findMany({
        where: {
          accountId,
          status: 'FILLED'
        },
        include: {
          TradingPositions: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 100 // 最近100笔交易
      });

      return trades.map(trade => ({
        id: trade.id,
        side: trade.side,
        quantity: parseFloat(trade.quantity.toString()),
        price: parseFloat(trade.averagePrice?.toString() || '0'),
        commission: parseFloat(trade.commission.toString()),
        pnl: (trade as any).TradingPositions ? parseFloat((trade as any).TradingPositions.unrealizedPnl.toString()) : 0,
        createdAt: trade.createdAt,
        isProfit: (trade as any).TradingPositions ? parseFloat((trade as any).TradingPositions.unrealizedPnl.toString()) > 0 : false
      }));
    } catch (error) {
      this.logger.error('获取账户交易历史失败', { accountId, error });
      return [];
    }
  }

  private async calculatePerformanceMetrics(trades: any[]): Promise<any> {
    if (trades.length === 0) {
      return {
        successRate: 0,
        tradeCount: 0,
        profitRate: 0,
        maxDrawdown: 0,
        sharpeRatio: 0,
        period: {
          start: new Date(),
          end: new Date(),
          durationDays: 0
        }
      };
    }

    // 计算成功率
    const profitableTrades = trades.filter(trade => trade.isProfit);
    const successRate = profitableTrades.length / trades.length;

    // 计算总盈亏
    const totalPnl = trades.reduce((sum, trade) => sum + trade.pnl, 0);
    const totalVolume = trades.reduce((sum, trade) => sum + (trade.quantity * trade.price), 0);
    const profitRate = totalVolume > 0 ? totalPnl / totalVolume : 0;

    // 计算最大回撤
    let maxDrawdown = 0;
    let peak = 0;
    let runningPnl = 0;

    for (const trade of trades.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())) {
      runningPnl += trade.pnl;
      if (runningPnl > peak) {
        peak = runningPnl;
      }
      const drawdown = (peak - runningPnl) / Math.max(peak, 1);
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }

    // 计算夏普比率（简化版）
    const returns = trades.map(trade => trade.pnl / Math.max(trade.quantity * trade.price, 1));
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const returnStd = Math.sqrt(
      returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length
    );
    const sharpeRatio = returnStd > 0 ? avgReturn / returnStd : 0;

    // 计算时间周期
    const sortedTrades = trades.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
    const start = sortedTrades[0].createdAt;
    const end = sortedTrades[sortedTrades.length - 1].createdAt;
    const durationDays = Math.ceil((end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000));

    return {
      successRate,
      tradeCount: trades.length,
      profitRate,
      maxDrawdown,
      sharpeRatio,
      period: {
        start,
        end,
        durationDays
      }
    };
  }

  private async getAccountParameters(accountId: string): Promise<any> {
    try {
      const account = await this.prisma.tradingAccounts.findUnique({
        where: { id: accountId }
      });

      if (!account) {
        throw new Error(`账户不存在: ${accountId}`);
      }

      const riskSettings = account.riskSettings as any;

      return {
        leverage: riskSettings?.leverage || 1,
        riskPerTrade: riskSettings?.riskPerTrade || 0.01,
        maxPositions: riskSettings?.maxPositions || 1,
        fixedInvestment: riskSettings?.fixedInvestment || 10,
        takeProfitRatio: riskSettings?.takeProfitRatio || 2.0,
        stopLossRatio: riskSettings?.stopLossRatio || 1.0,
        trailingStopRatio: riskSettings?.trailingStopRatio || 0.8,
        maxDrawdownLimit: riskSettings?.maxDrawdownLimit || 0.15,
        dailyLossLimit: riskSettings?.dailyLossLimit || 0.05,
        minConfidenceScore: riskSettings?.minConfidenceScore || 0.75,
        atrPeriod: riskSettings?.atrPeriod || 14,
        atrMultiplier: riskSettings?.atrMultiplier || 1.5,
        pyramidTriggerRatio: riskSettings?.pyramidTriggerRatio || 0.5,
        pyramidSizeRatio: riskSettings?.pyramidSizeRatio || 0.5,
        maxPyramidLevels: riskSettings?.maxPyramidLevels || 3
      };
    } catch (error) {
      this.logger.error('获取账户参数失败', { accountId, error });
      throw error;
    }
  }

  private adjustParametersForLiveTrading(sourceParameters: any, config: StrategySyncConfig): any {
    // 实现参数调整逻辑
    return {
      ...sourceParameters,
      riskPerTrade: sourceParameters.riskPerTrade * config.riskReductionFactor,
      positionSize: sourceParameters.positionSize * config.positionSizeReduction,
      leverage: Math.min(sourceParameters.leverage * config.leverageReduction, 10)
    };
  }

  private async applyParametersToAccount(accountId: string, parameters: any): Promise<void> {
    try {
      const account = await this.prisma.tradingAccounts.findUnique({
        where: { id: accountId }
      });

      if (!account) {
        throw new Error(`目标账户不存在: ${accountId}`);
      }

      const currentRiskSettings = account.riskSettings as any;
      const updatedRiskSettings = {
        ...currentRiskSettings,
        ...parameters,
        // 记录同步信息
        lastSyncAt: new Date(),
        syncSource: 'strategySync',
        syncParameters: parameters
      };

      await this.prisma.tradingAccounts.update({
        where: { id: accountId },
        data: {
          riskSettings: updatedRiskSettings,
          updatedAt: new Date()
        }
      });

      this.logger.info('参数已应用到账户', { accountId, parameters });
    } catch (error) {
      this.logger.error('应用参数到账户失败', { accountId, parameters, error });
      throw error;
    }
  }

  private generateSyncWarnings(sourceParameters: any, adjustedParameters: any): string[] {
    const warnings: string[] = [];
    
    if (adjustedParameters.riskPerTrade < sourceParameters.riskPerTrade) {
      warnings.push('风险参数已降低以适应真实交易');
    }
    
    if (adjustedParameters.leverage < sourceParameters.leverage) {
      warnings.push('杠杆已降低以控制风险');
    }
    
    return warnings;
  }

  private async saveSyncRecord(syncResult: StrategySyncResult, config: StrategySyncConfig): Promise<void> {
    try {
      await this.prisma.strategySyncRecords.create({
        data: {
          id: syncResult.syncId,
          sourceAccountId: syncResult.sourceAccountId,
          targetAccountId: syncResult.targetAccountId,
          status: syncResult.success ? StrategySyncStatus.COMPLETED : StrategySyncStatus.FAILED,
          syncConfig: config as any,
          validationResult: syncResult.validationResult as any,
          syncResult: syncResult as any,
          sourceParameters: syncResult.syncedParameters,
          adjustedParameters: syncResult.adjustedParameters,
          syncTimestamp: syncResult.syncTimestamp,
          completedAt: syncResult.success ? syncResult.syncTimestamp : null,
          updatedAt: new Date()
        }
      });

      this.logger.info('同步记录已保存', { syncId: syncResult.syncId });
    } catch (error) {
      this.logger.error('保存同步记录失败', { syncId: syncResult.syncId, error });
      throw error;
    }
  }

  private mapToStrategySyncRecord(record: any): StrategySyncRecord {
    return {
      id: record.id,
      sourceAccountId: record.sourceAccountId,
      targetAccountId: record.targetAccountId,
      status: record.status as StrategySyncStatus,
      config: record.syncConfig,
      validationResult: record.validationResult,
      syncResult: record.syncResult,
      createdAt: record.createdAt,
      updatedAt: record.updatedAt,
      completedAt: record.completedAt
    };
  }

  private async getAutoSyncEnabledAccounts(): Promise<any[]> {
    try {
      const accounts = await this.prisma.tradingAccounts.findMany({
        where: {
          accountType: 'SIMULATION',
          isActive: true
        }
      });

      return accounts.filter(account => {
        const riskSettings = account.riskSettings as any;
        return riskSettings?.autoSync?.enabled === true;
      });
    } catch (error) {
      this.logger.error('获取启用自动同步的账户失败', { error });
      return [];
    }
  }

  private async processAutoSync(account: any): Promise<void> {
    try {
      const riskSettings = account.riskSettings as any;
      const autoSyncConfig = riskSettings?.autoSync;

      if (!autoSyncConfig?.enabled) {
        return;
      }

      // 检查冷却时间
      const lastSyncTime = autoSyncConfig.lastSyncAt ? new Date(autoSyncConfig.lastSyncAt) : null;
      const cooldownMs = autoSyncConfig.cooldown || 24 * 60 * 60 * 1000; // 默认24小时

      if (lastSyncTime && (Date.now() - lastSyncTime.getTime()) < cooldownMs) {
        return; // 还在冷却期内
      }

      // 查找对应的真实交易账户
      const targetAccount = await this.prisma.tradingAccounts.findFirst({
        where: {
          accountType: 'LIVE',
          executionEngine: 'binance',
          isActive: true
        }
      });

      if (!targetAccount) {
        this.logger.warn('未找到对应的真实交易账户', { sourceAccountId: account.id });
        return;
      }

      // 执行自动同步
      const syncConfig: StrategySyncConfig = {
        ...autoSyncConfig.config,
        enableAutoSync: true
      };

      const syncResult = await this.syncStrategy(account.id, targetAccount.id, syncConfig);

      // 更新最后同步时间
      if (syncResult.success) {
        const updatedRiskSettings = {
          ...riskSettings,
          autoSync: {
            ...autoSyncConfig,
            lastSyncAt: new Date(),
            lastSyncResult: syncResult
          }
        };

        await this.prisma.tradingAccounts.update({
          where: { id: account.id },
          data: { riskSettings: updatedRiskSettings }
        });

        this.logger.info('自动同步完成', {
          sourceAccountId: account.id,
          targetAccountId: targetAccount.id,
          syncId: syncResult.syncId
        });
      }
    } catch (error) {
      this.logger.error('处理自动同步失败', { accountId: account.id, error });
    }
  }
}
