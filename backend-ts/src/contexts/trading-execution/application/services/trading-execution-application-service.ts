/**
 * 交易执行应用服务 - 统一的交易执行业务接口
 *
 * 重构后继承BaseApplicationService，消除请求编排模式重复
 */

import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { PrismaClient } from '@prisma/client';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { CredentialManager } from '../../infrastructure/services/credential-manager';
import { ApiCredentials } from '../../domain/types/dual-track.types';
// import { getEnvironmentManager } from '../../../../shared/infrastructure/config/environment/environment-manager'; // 导出不存在
import { TradingStrategyEngine, TradingDecision } from '../../domain/services/trading-strategy-engine';
import { PositionManager } from '../../domain/services/position-manager';
import { OrderExecutor, PositionOpenResult } from '../../domain/services/order-executor';
import { RiskMonitor, RiskStatus } from '../../domain/services/risk-monitor';
import { ExecutionEngineRouter } from '../../domain/services/execution-engine-router';
import {
  PositionOpenResult as EnginePositionOpenResult,
  AccountType,
  ValidationResult,
  AccountBalance
} from '../../domain/interfaces/execution-engine.interface';
import { PositionInfo } from '../../../../shared/infrastructure/types/unified-interfaces';
import { TradingAccount } from '../../domain/entities/trading-account';
import { TradingPosition } from '../../domain/entities/trading-position';
import { RiskEnforcementEngine, TradeExecutionCheckResult } from '../../../../shared/infrastructure/risk/risk-enforcement-engine';
import { EnhancedTradingExecutionMonitor } from '../../../../shared/infrastructure/monitoring/enhanced-trading-execution-monitor';

// 导入基础应用服务
import {
  BaseApplicationService,
  // RequestContext, // 导出不存在
  // ValidationResult as BaseValidationResult, // 导出不存在
  // BusinessRuleResult // 导出不存在
} from '../../../../shared/application/base-application-service';
import {
  BaseRequest,
  BaseResponse,
  IApplicationService,
  HealthCheckResult
} from '../../../../shared/application/application-service-interfaces';
import { IEventBus } from '../../../../shared/domain/events/domain-event';

// 导入统一DTO映射器 - 解决问题19：实体到DTO映射逻辑重复
import {
  UnifiedDtoMapperRegistry,
  TradingPositionDto
} from '../../../../shared/application/dto-mappers';

export interface TradingExecutionRequest extends BaseRequest {
  accountId: string;
  forceExecution?: boolean;
  skipRiskCheck?: boolean;
}

export interface TradingExecutionResponse extends BaseResponse {
  data: {
    accountId: string;
    decision: TradingDecision;
    positionResult?: PositionOpenResult;
    riskStatus: RiskStatus;
  };
}

export interface PositionManagementResult {
  accountId: string;
  processedPositions: number;
  closedPositions: number;
  updatedPositions: number;
  riskActions: number;
  message: string;
}

@injectable()
export class TradingExecutionApplicationService extends BaseApplicationService implements IApplicationService {
  readonly serviceName = 'TradingExecutionApplicationService';

  constructor(
    @inject(TYPES.Logger) logger: Logger,
    @inject(TYPES.EventBus) eventBus: IEventBus,
    @inject(TYPES.Shared.Database) private readonly prisma: PrismaClient,
    @inject(TYPES.TradingExecution.TradingStrategyEngine)
    private readonly strategyEngine: TradingStrategyEngine,
    @inject(TYPES.TradingExecution.PositionManager)
    private readonly positionManager: PositionManager,
    @inject(TYPES.TradingExecution.OrderExecutor)
    private readonly orderExecutor: OrderExecutor,
    @inject(TYPES.TradingExecution.RiskMonitor)
    private readonly riskMonitor: RiskMonitor,
    @inject(TYPES.TradingExecution.ExecutionEngineRouter)
    private readonly executionEngineRouter: ExecutionEngineRouter,
    @inject(TYPES.TradingExecution.CredentialManager)
    private readonly credentialManager: CredentialManager,
    // 注入统一DTO映射器 - 解决问题19：实体到DTO映射逻辑重复
    @inject(TYPES.Shared.UnifiedDtoMapperRegistry)
    private readonly dtoMapperRegistry: UnifiedDtoMapperRegistry,
    // 注入风险强制执行引擎
    @inject(TYPES.Shared.RiskEnforcementEngine)
    private readonly riskEnforcementEngine: RiskEnforcementEngine,
    // 注入交易执行监控引擎
    @inject(TYPES.Shared.EnhancedTradingExecutionMonitor)
    private readonly executionMonitor: EnhancedTradingExecutionMonitor
  ) {
    super(logger, eventBus);
  }

  /**
   * 执行单个交易订单
   */
  async executeOrder(request: {
    accountId: string;
    symbolId: string;
    orderType: 'MARKET' | 'LIMIT' | 'STOP_LOSS' | 'TAKE_PROFIT';
    side: 'BUY' | 'SELL';
    quantity: number;
    price?: number;
    stopPrice?: number;
    timeInForce?: 'GTC' | 'IOC' | 'FOK';
    leverage?: number;
  }): Promise<{ success: boolean; orderId?: string; error?: string; message?: string }> {
    try {
      this.logger.info('执行单个交易订单', {
        accountId: request.accountId,
        symbol: request.symbolId,
        side: request.side,
        type: request.orderType,
        quantity: request.quantity
      });

      // 获取交易账户
      const account = await this.getTradingAccount(request.accountId);
      if (!account) {
        return {
          success: false,
          error: '交易账户不存在'
        };
      }

      // 🔥 风险强制执行检查
      const riskCheckResult = await this.performRiskEnforcementCheck(request, account);
      if (!riskCheckResult.allowed) {
        return {
          success: false,
          error: `交易被风险管理系统阻止: ${riskCheckResult.blockedReasons.join(', ')}`,
          message: `风险违规: ${riskCheckResult.violations.map(v => v.violationType).join(', ')}`
        };
      }

      // 应用风险管理修改（如果有）
      const modifiedRequest = this.applyRiskModifications(request, riskCheckResult);

      // 获取执行引擎
      const engine = this.executionEngineRouter.getEngine(account);
      if (!engine || !engine.isConnected()) {
        return {
          success: false,
          error: '执行引擎未连接'
        };
      }

      // 转换订单类型
      let orderType: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
      switch (request.orderType) {
        case 'STOP_LOSS':
          orderType = 'STOP';
          break;
        case 'TAKE_PROFIT':
          orderType = 'LIMIT';
          break;
        default:
          orderType = request.orderType as 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
      }

      // 执行订单（使用风险管理修改后的请求）
      const orderResult = await engine.executeOrder({
        accountId: modifiedRequest.accountId,
        symbolId: modifiedRequest.symbolId,
        side: modifiedRequest.side,
        orderType,
        quantity: modifiedRequest.quantity,
        price: modifiedRequest.price,
        stopPrice: modifiedRequest.stopPrice,
        timeInForce: modifiedRequest.timeInForce || 'GTC'
      });

      if (orderResult.success) {
        this.logger.info('订单执行成功', {
          accountId: request.accountId,
          orderId: orderResult.orderId
        });

        return {
          success: true,
          orderId: orderResult.orderId,
          message: '订单执行成功'
        };
      } else {
        this.logger.error('订单执行失败', {
          accountId: request.accountId,
          error: orderResult.error
        });

        return {
          success: false,
          error: orderResult.error || '订单执行失败'
        };
      }

    } catch (error) {
      this.logger.error('执行订单时发生错误', {
        error: error instanceof Error ? error.message : String(error),
        accountId: request.accountId
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 执行交易策略 - 使用统一请求编排模式
   */
  async executeTradingStrategy(request: TradingExecutionRequest): Promise<TradingExecutionResponse> {
    const result = await this.processRequest<TradingExecutionRequest, TradingExecutionResponse>(
      request,
      '交易策略执行',
      {
        skipValidation: false,
        skipBusinessRules: false,
        skipAdditionalData: false,
        enablePerformanceTracking: true
      }
    );

    if (!result.success) {
      throw new Error(result.error || '交易策略执行失败');
    }

    return result.data!;
  }

  /**
   * 兼容性方法 - 返回旧格式的结果
   */
  async executeTradingStrategyLegacy(request: TradingExecutionRequest): Promise<any> {
    const response = await this.executeTradingStrategy(request);

    return {
      success: response.success,
      accountId: response.data.accountId,
      decision: response.data.decision,
      positionResult: response.data.positionResult,
      riskStatus: response.data.riskStatus,
      message: response.message || '交易策略执行完成',
      timestamp: response.timestamp
    };
  }

  /**
   * 获取所有交易账户
   */
  async getAllTradingAccounts(): Promise<any[]> {
    return this.getTradingAccounts();
  }

  /**
   * 获取所有交易账户
   */
  async getTradingAccounts(): Promise<any[]> {
    try {
      const accounts = await this.prisma.tradingAccounts.findMany({
        orderBy: { createdAt: 'desc' },
        include: {
          _count: {
            select: {
              TradingPositions: true,
              TradingOrders: true
            }
          }
        }
      });

      return accounts.map(account => ({
        id: account.id,
        name: account.name,
        initialCapital: account.initialCapital.toNumber(),
        currentBalance: account.currentBalance.toNumber(),
        availableBalance: account.availableBalance.toNumber(),
        totalPnl: account.totalPnl.toNumber(),
        maxDrawdown: account.maxDrawdown.toNumber(),
        dailyPnl: account.dailyPnl.toNumber(),
        isActive: account.isActive,
        riskSettings: account.riskSettings,
        accountType: account.accountType,
        executionEngine: account.executionEngine,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt,
        positionCount: (account as any)._count?.TradingPositions || 0,
        orderCount: (account as any)._count?.TradingOrders || 0
      }));
    } catch (error) {
      this.logger.error('获取交易账户列表失败', { error });
      throw error;
    }
  }

  /**
   * 获取交易账户详情
   */
  async getTradingAccountDetails(accountId: string): Promise<any | null> {
    try {
      const account = await this.prisma.tradingAccounts.findUnique({
        where: { id: accountId },
        include: {
          TradingPositions: {
            where: { status: 'OPEN' },
            include: { symbols: true }
          },
          tradingOrders: {
            orderBy: { createdAt: 'desc' },
            take: 10,
            include: { symbols: true }
          },
          _count: {
            select: {
              TradingPositions: true,
              TradingOrders: true
            }
          }
        }
      });

      if (!account) return null;

      return {
        id: account.id,
        name: account.name,
        initialCapital: account.initialCapital.toNumber(),
        currentBalance: account.currentBalance.toNumber(),
        availableBalance: account.availableBalance.toNumber(),
        totalPnl: account.totalPnl.toNumber(),
        maxDrawdown: account.maxDrawdown.toNumber(),
        dailyPnl: account.dailyPnl.toNumber(),
        isActive: account.isActive,
        riskSettings: account.riskSettings,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt,
        positions: ((account as any).tradingPositions || []).map((pos: any) => ({
          id: pos.id,
          symbolId: pos.symbolId,
          side: pos.side,
          entryPrice: pos.entryPrice.toNumber(),
          quantity: pos.quantity.toNumber(),
          currentPrice: pos.currentPrice?.toNumber(),
          unrealizedPnl: pos.unrealizedPnl.toNumber(),
          status: pos.status,
          openedAt: pos.openedAt,
          symbol: pos.symbols
        })),
        orders: ((account as any).tradingOrders || []).map((order: any) => ({
          id: order.id,
          orderType: order.orderType,
          side: order.side,
          quantity: order.quantity.toNumber(),
          price: order.price.toNumber(),
          status: order.status,
          createdAt: order.createdAt,
          symbol: order.symbols
        })),
        positionCount: (account as any)._count?.tradingPositions || 0,
        orderCount: (account as any)._count?.tradingOrders || 0
      };
    } catch (error) {
      this.logger.error('获取交易账户详情失败', { error, accountId });
      throw error;
    }
  }

  /**
   * 创建交易账户
   */
  async createTradingAccount(accountData: {
    name: string;
    type: string;
    initialCapital: number;
    description: string;
    userId: string;
  }): Promise<any> {
    try {
      const { name, type, initialCapital, description, userId } = accountData;

      const account = await this.prisma.tradingAccounts.create({
        data: {
          name,
          accountType: type,
          executionEngine: type === 'SIMULATION' ? 'simulation' : 'binance',
          initialCapital,
          currentBalance: initialCapital,
          availableBalance: initialCapital,
          totalPnl: 0,
          maxDrawdown: 0,
          dailyPnl: 0,
          isActive: true,
          userId,
          riskSettings: {
            maxPositionSize: initialCapital * 0.1, // 10% of capital
            maxDailyLoss: initialCapital * 0.05,   // 5% daily loss limit
            maxLeverage: type === 'SIMULATION' ? 10 : 3
          }
        }
      });

      return {
        id: account.id,
        name: account.name,
        type: account.accountType,
        initialCapital: account.initialCapital.toNumber(),
        currentBalance: account.currentBalance.toNumber(),
        availableBalance: account.availableBalance.toNumber(),
        totalPnl: account.totalPnl.toNumber(),
        maxDrawdown: account.maxDrawdown.toNumber(),
        dailyPnl: account.dailyPnl.toNumber(),
        isActive: account.isActive,
        riskSettings: account.riskSettings,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt
      };
    } catch (error) {
      this.logger.error('创建交易账户失败', { error, accountData });
      throw error;
    }
  }

  /**
   * 获取账户的活跃仓位（公共方法）
   * 返回格式化的持仓数据，符合前端期望的数据结构
   */
  async getActivePositions(accountId: string): Promise<any[]> {
    try {
      // 首先获取账户信息
      const account = await this.prisma.tradingAccounts.findUnique({
        where: { id: accountId }
      });

      if (!account) {
        throw new Error(`账户不存在: ${accountId}`);
      }

      // 如果是真实交易账户，尝试从币安获取实时持仓数据
      if (account.executionEngine === 'binance' && (account.accountType === 'LIVE' || account.accountType === 'LIVE_BINANCE')) {
        try {
          await this.syncPositionsFromBinance(accountId);
          this.logger.debug('币安持仓数据同步成功', { accountId });
        } catch (syncError) {
          this.logger.warn('币安持仓数据同步失败，使用数据库数据', {
            accountId,
            error: syncError instanceof Error ? syncError.message : String(syncError)
          });
        }
      }

      // 从数据库获取持仓数据
      const positions = await this.prisma.tradingPositions.findMany({
        where: {
          accountId,
          status: 'OPEN'
        },
        include: {
          symbols: true,
          TradingOrders: {
            orderBy: { createdAt: 'desc' },
            take: 5
          }
        },
        orderBy: { openedAt: 'desc' }
      });

      return this.dtoMapperRegistry.mapTradingPositionArray(positions);
    } catch (error) {
      this.logger.error('获取活跃仓位失败', { error, accountId });
      throw error;
    }
  }

  // 原formatPositionForAPI方法已移除 - 使用统一DTO映射器替代

  /**
   * 从币安同步持仓数据到数据库
   */
  private async syncPositionsFromBinance(accountId: string): Promise<void> {
    try {
      // 获取账户信息
      const account = await this.prisma.tradingAccounts.findUnique({
        where: { id: accountId }
      });

      if (!account || account.executionEngine !== 'binance') {
        return;
      }

      // 转换为TradingAccount实体
      const tradingAccount = TradingAccount.fromPersistence({
        id: account.id,
        name: account.name,
        initialCapital: account.initialCapital.toNumber(),
        currentBalance: account.currentBalance.toNumber(),
        availableBalance: account.availableBalance.toNumber(),
        totalPnl: account.totalPnl.toNumber(),
        maxDrawdown: account.maxDrawdown.toNumber(),
        dailyPnl: account.dailyPnl.toNumber(),
        isActive: account.isActive,
        riskSettings: account.riskSettings as any,
        accountType: account.accountType as any,
        executionEngine: account.executionEngine,
        apiCredentials: account.apiCredentials as any,
        syncSettings: account.syncSettings as any,
        parentAccountId: account.parentAccountId || undefined,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt
      });

      // 通过执行引擎路由器获取币安持仓数据
      const engine = this.executionEngineRouter.getEngine(tradingAccount);
      const binancePositions = await engine.getPositions(accountId);

      // 清理现有的持仓数据
      await this.prisma.tradingPositions.deleteMany({
        where: { accountId }
      });

      // 同步活跃持仓到数据库
      for (const binancePos of binancePositions) {
        if (binancePos.size === 0) continue; // 跳过空仓位

        // 查找或创建交易对
        let symbol = await this.prisma.symbols.findFirst({
          where: { symbol: binancePos.symbol }
        });

        if (!symbol) {
          // 创建新的交易对
          symbol = await this.prisma.symbols.create({
            data: {
              symbol: binancePos.symbol,
              baseAsset: binancePos.symbol.replace('USDT', ''),
              quoteAsset: 'USDT',
              isActive: true,
              exchange: 'binance',
              tradingRules: {
                tickSize: 0.01,
                stepSize: 0.001,
                minNotional: 5.0
              }
            }
          });
        }

        // 创建持仓记录
        await this.prisma.tradingPositions.create({
          data: {
            accountId: accountId,
            symbolId: symbol.id,
            side: binancePos.side, // 直接使用币安引擎已经正确计算的方向
            entryPrice: binancePos.entryPrice,
            quantity: binancePos.size, // 币安引擎已经转换为绝对值
            leverage: (binancePos as any).leverage || 1,
            marginUsed: (binancePos as any).margin || 0,
            currentPrice: (binancePos as any).markPrice || binancePos.currentPrice,
            unrealizedPnl: binancePos.unrealizedPnl,
            realizedPnl: 0,
            status: 'OPEN',
            pyramidLevel: 1,
            atrValue: Math.abs(((binancePos as any).markPrice || binancePos.currentPrice || binancePos.entryPrice) - binancePos.entryPrice),
            openedAt: new Date(),
            executionSource: 'BINANCE_LIVE'
          }
        });
      }

      this.logger.info('币安持仓数据同步完成', {
        accountId,
        syncedPositions: binancePositions.filter(p => p.size !== 0).length
      });

    } catch (error) {
      this.logger.error('同步币安持仓数据失败', { accountId, error });
      throw error;
    }
  }

  /**
   * 获取订单历史（控制器接口）
   */
  async getOrderHistory(params: {
    accountId: string;
    limit?: number;
    offset?: number;
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const result = await this.getTradingOrders(params.accountId, {
        limit: params.limit,
        offset: params.offset
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error('获取订单历史失败', {
        error: error instanceof Error ? error.message : String(error),
        accountId: params.accountId
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : '获取订单历史失败'
      };
    }
  }

  /**
   * 获取交易订单历史
   */
  async getTradingOrders(accountId: string, params?: { limit?: number; offset?: number }): Promise<any> {
    try {
      const { limit = 50, offset = 0 } = params || {};

      const orders = await this.prisma.tradingOrders.findMany({
        where: { accountId },
        include: {
          symbols: true,
          TradingPositions: true
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      });

      const totalCount = await this.prisma.tradingOrders.count({
        where: { accountId }
      });

      return {
        orders: orders.map(order => ({
          id: order.id,
          positionId: order.positionId,
          accountId: order.accountId,
          symbolId: order.symbolId,
          orderType: order.orderType,
          side: order.side,
          quantity: order.quantity.toNumber(),
          price: order.price.toNumber(),
          stopPrice: order.stopPrice?.toNumber(),
          status: order.status,
          exchangeOrderId: order.exchangeOrderId,
          filledQuantity: order.filledQuantity.toNumber(),
          averagePrice: order.averagePrice?.toNumber(),
          commission: order.commission.toNumber(),
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
          filledAt: order.filledAt,
          symbol: order.symbolId,
          position: (order as any).tradingPositions
        })),
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + orders.length < totalCount
        }
      };
    } catch (error) {
      this.logger.error('获取交易订单历史失败', { error, accountId });
      throw error;
    }
  }

  /**
   * 获取交易统计
   */
  async getTradingStatistics(accountId: string, params?: { days?: number }): Promise<any> {
    try {
      const { days = 30 } = params || {};

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const statistics = await this.prisma.tradingStatistics.findMany({
        where: {
          accountId,
          date: {
            gte: startDate
          }
        },
        orderBy: { date: 'desc' }
      });

      // 计算汇总统计
      const summary = statistics.reduce((acc, stat) => ({
        totalTrades: acc.totalTrades + stat.totalTrades,
        winningTrades: acc.winningTrades + stat.winningTrades,
        losingTrades: acc.losingTrades + stat.losingTrades,
        totalPnl: acc.totalPnl + stat.totalPnl.toNumber(),
        grossProfit: acc.grossProfit + stat.grossProfit.toNumber(),
        grossLoss: acc.grossLoss + stat.grossLoss.toNumber(),
        maxDrawdown: Math.max(acc.maxDrawdown, stat.maxDrawdown.toNumber())
      }), {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        totalPnl: 0,
        grossProfit: 0,
        grossLoss: 0,
        maxDrawdown: 0
      });

      // 计算胜率和盈亏比
      const winRate = summary.totalTrades > 0 ? summary.winningTrades / summary.totalTrades : 0;
      const profitFactor = summary.grossLoss !== 0 ? summary.grossProfit / Math.abs(summary.grossLoss) : 0;

      return {
        daily: statistics.map(stat => ({
          date: stat.date.toISOString(),
          totalTrades: stat.totalTrades,
          winningTrades: stat.winningTrades,
          losingTrades: stat.losingTrades,
          totalPnl: stat.totalPnl.toNumber(),
          grossProfit: stat.grossProfit.toNumber(),
          grossLoss: stat.grossLoss.toNumber(),
          maxDrawdown: stat.maxDrawdown.toNumber(),
          winRate: stat.winningTrades > 0 ? stat.winningTrades / stat.totalTrades : 0
        })),
        summary: {
          ...summary,
          winRate,
          profitFactor,
          averageWin: summary.winningTrades > 0 ? summary.grossProfit / summary.winningTrades : 0,
          averageLoss: summary.losingTrades > 0 ? summary.grossLoss / summary.losingTrades : 0
        }
      };
    } catch (error) {
      this.logger.error('获取交易统计失败', { error, accountId });
      throw error;
    }
  }

  /**
   * 获取风险事件日志
   */
  async getRiskEvents(params?: { limit?: number; severity?: string }): Promise<any[]> {
    try {
      const { limit = 50, severity } = params || {};

      const whereClause: any = {};
      if (severity) {
        whereClause.severity = severity;
      }

      const events = await this.prisma.riskEvents.findMany({
        where: whereClause,
        include: {
          TradingAccounts: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit
      });

      return events.map(event => ({
        id: event.id,
        accountId: event.accountId,
        eventType: event.eventType,
        severity: event.severity,
        message: event.description || '',
        data: {
          currentValue: event.currentValue,
          thresholdValue: event.thresholdValue,
          actionTaken: event.actionTaken,
          resolved: event.resolved
        },
        createdAt: event.createdAt,
        account: (event as any).tradingAccounts
      }));
    } catch (error) {
      this.logger.error('获取风险事件日志失败', { error });
      throw error;
    }
  }

  /**
   * 管理现有仓位
   */
  async managePositions(accountId: string): Promise<PositionManagementResult> {
    try {
      this.logger.info('开始管理仓位', { accountId });

      // 1. 获取账户和仓位
      const account = await this.getTradingAccount(accountId);
      if (!account) {
        return {
          accountId,
          processedPositions: 0,
          closedPositions: 0,
          updatedPositions: 0,
          riskActions: 0,
          message: '账户不存在'
        };
      }

      const positions = await this.getActivePositionsInternal(accountId);
      if (positions.length === 0) {
        return {
          accountId,
          processedPositions: 0,
          closedPositions: 0,
          updatedPositions: 0,
          riskActions: 0,
          message: '无活跃仓位'
        };
      }

      // 2. 获取当前价格
      const currentPrices = await this.getCurrentPrices(positions);

      // 3. 管理每个仓位
      let closedPositions = 0;
      let updatedPositions = 0;
      let riskActions = 0;

      for (const position of positions) {
        const currentPrice = currentPrices.get(position.symbolId);
        if (!currentPrice) {
          this.logger.warn('缺少价格数据，跳过仓位管理', { positionId: position.id });
          continue;
        }

        const result = await this.positionManager.managePosition(position, currentPrice, account);
        
        // 执行仓位管理行动
        for (const action of result.actions) {
          switch (action.type) {
            case 'CLOSE_POSITION':
              const closeResult = await this.executionEngineRouter.closePosition(
                account,
                {
                  positionId: position.id,
                  accountId: account.id,
                  reason: action.data.reason,
                  closePrice: currentPrice
                }
              );
              if (closeResult.success) {
                closedPositions++;
                await this.updatePositionInDatabase(position);
                await this.updateAccountBalance(account, position.realizedPnl);
              }
              break;
            case 'PYRAMID_ADD':
              // 实现金字塔加仓逻辑
              riskActions++;
              break;
            case 'STOP_LOSS_UPDATE':
              position.updateStopLoss(action.data.newStopLoss);
              updatedPositions++;
              await this.updatePositionInDatabase(position);
              break;
            case 'PARTIAL_CLOSE':
              // 实现部分平仓逻辑
              riskActions++;
              break;
          }
        }

        if (result.actions.length === 0) {
          // 仅更新价格和PnL
          await this.updatePositionInDatabase(position);
          updatedPositions++;
        }
      }

      this.logger.info('仓位管理完成', {
        accountId,
        processedPositions: positions.length,
        closedPositions,
        updatedPositions,
        riskActions
      });

      return {
        accountId,
        processedPositions: positions.length,
        closedPositions,
        updatedPositions,
        riskActions,
        message: `处理${positions.length}个仓位，平仓${closedPositions}个，更新${updatedPositions}个`
      };

    } catch (error) {
      this.logger.error('仓位管理失败', {
        error: error instanceof Error ? error.message : String(error),
        accountId
      });

      return {
        accountId,
        processedPositions: 0,
        closedPositions: 0,
        updatedPositions: 0,
        riskActions: 0,
        message: `管理失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 获取交易账户
   */
  private async getTradingAccount(accountId: string): Promise<TradingAccount | null> {
    try {
      const accountData = await this.prisma.tradingAccounts.findUnique({
        where: { id: accountId }
      });

      if (!accountData) return null;

      return TradingAccount.fromPersistence({
        id: accountData.id,
        name: accountData.name,
        initialCapital: accountData.initialCapital.toNumber(),
        currentBalance: accountData.currentBalance.toNumber(),
        availableBalance: accountData.availableBalance.toNumber(),
        totalPnl: accountData.totalPnl.toNumber(),
        maxDrawdown: accountData.maxDrawdown.toNumber(),
        dailyPnl: accountData.dailyPnl.toNumber(),
        isActive: accountData.isActive,
        riskSettings: accountData.riskSettings as any,
        accountType: accountData.accountType as any,
        executionEngine: accountData.executionEngine,
        apiCredentials: accountData.apiCredentials as any,
        syncSettings: accountData.syncSettings as any,
        parentAccountId: accountData.parentAccountId || undefined,
        createdAt: accountData.createdAt,
        updatedAt: accountData.updatedAt
      });
    } catch (error) {
      this.logger.error('获取交易账户失败', { error, accountId });
      return null;
    }
  }

  /**
   * 获取活跃仓位（内部使用）
   */
  private async getActivePositionsInternal(accountId: string): Promise<TradingPosition[]> {
    try {
      const positionsData = await this.prisma.tradingPositions.findMany({
        where: { 
          accountId, 
          status: 'OPEN' 
        },
        include: { symbols: true }
      });

      return positionsData.map(pos => TradingPosition.fromPersistence({
        id: pos.id,
        accountId: pos.accountId,
        symbolId: pos.symbolId,
        signalId: pos.signalId ?? undefined,
        side: pos.side as 'LONG' | 'SHORT',
        entryPrice: pos.entryPrice.toNumber(),
        quantity: pos.quantity.toNumber(),
        leverage: pos.leverage,
        marginUsed: pos.marginUsed.toNumber(),
        stopLoss: pos.stopLoss?.toNumber(),
        takeProfit: pos.takeProfit?.toNumber(),
        currentPrice: pos.currentPrice?.toNumber(),
        unrealizedPnl: pos.unrealizedPnl.toNumber(),
        realizedPnl: pos.realizedPnl.toNumber(),
        status: pos.status as 'OPEN' | 'CLOSED' | 'LIQUIDATED',
        pyramidLevel: pos.pyramidLevel,
        atrValue: pos.atrValue?.toNumber(),
        openedAt: pos.openedAt,
        closedAt: pos.closedAt ?? undefined,
        metadata: pos.metadata
      }));
    } catch (error) {
      this.logger.error('获取活跃仓位失败', { error, accountId });
      return [];
    }
  }

  /**
   * 获取当前价格
   */
  private async getCurrentPrices(positions: TradingPosition[]): Promise<Map<string, number>> {
    const priceMap = new Map<string, number>();
    
    try {
      const symbolIds = [...new Set(positions.map(p => p.symbolId))];
      
      for (const symbolId of symbolIds) {
        const priceData = await this.prisma.priceData.findFirst({
          where: { symbolId },
          orderBy: { timestamp: 'desc' }
        });
        
        if (priceData) {
          priceMap.set(symbolId, priceData.price.toNumber());
        }
      }
    } catch (error) {
      this.logger.error('获取当前价格失败', { error });
    }
    
    return priceMap;
  }

  /**
   * 执行新仓位
   */
  private async executeNewPosition(
    account: TradingAccount, 
    decision: TradingDecision
  ): Promise<PositionOpenResult> {
    try {
      // 获取BTC符号ID
      const btcSymbol = await this.prisma.symbols.findUnique({
        where: { symbol: 'BTC/USDT' }
      });

      if (!btcSymbol) {
        return {
          success: false,
          orders: [],
          error: 'BTC符号不存在'
        };
      }

      // 获取当前价格
      const priceData = await this.prisma.priceData.findFirst({
        where: { symbolId: btcSymbol.id },
        orderBy: { timestamp: 'desc' }
      });

      if (!priceData) {
        return {
          success: false,
          orders: [],
          error: '无法获取当前价格'
        };
      }

      const currentPrice = priceData.price.toNumber();

      return await this.executionEngineRouter.openPosition(account, {
        account,
        symbolId: btcSymbol.id,
        signalId: `signal_${Date.now()}`,
        side: decision.positionSize && decision.positionSize > 0 ? 'LONG' : 'SHORT',
        entryPrice: currentPrice,
        quantity: Math.abs(decision.positionSize || 0.001),
        marginUsed: (account.riskSettings as any).fixedInvestment || 10,
        stopLoss: decision.stopLoss || currentPrice * 0.98,
        takeProfit: decision.takeProfit || currentPrice * 1.04,
        atrValue: priceData.atr?.toNumber(),
        metadata: {
          confidence: decision.confidence,
          reason: decision.reason
        }
      });

    } catch (error) {
      this.logger.error('执行新仓位失败', { error });
      return {
        success: false,
        orders: [],
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 执行风险行动
   */
  private async executeRiskActions(riskStatus: RiskStatus, positions: TradingPosition[]): Promise<void> {
    for (const action of riskStatus.actions) {
      try {
        switch (action.type) {
          case 'EMERGENCY_STOP':
            this.logger.warn('执行紧急停止', { reason: action.reason });
            // 实现紧急停止逻辑
            break;
          case 'SUSPEND_TRADING':
            this.logger.warn('暂停交易', { reason: action.reason });
            // 实现暂停交易逻辑
            break;
          case 'CLOSE_POSITION':
            if (action.target) {
              const position = positions.find(p => p.id === action.target);
              if (position) {
                // 实现强制平仓逻辑
                this.logger.warn('强制平仓', { positionId: action.target, reason: action.reason });
              }
            }
            break;
        }
      } catch (error) {
        this.logger.error('执行风险行动失败', { action, error });
      }
    }
  }

  /**
   * 更新数据库中的仓位
   */
  private async updatePositionInDatabase(position: TradingPosition): Promise<void> {
    try {
      await this.prisma.tradingPositions.update({
        where: { id: position.id },
        data: position.toPersistence()
      });
    } catch (error) {
      this.logger.error('更新仓位失败', { positionId: position.id, error });
    }
  }

  /**
   * 更新账户余额
   */
  private async updateAccountBalance(account: TradingAccount, pnl: number): Promise<void> {
    try {
      account.updatePnl(pnl);
      
      await this.prisma.tradingAccounts.update({
        where: { id: account.id },
        data: {
          currentBalance: account.currentBalance,
          totalPnl: account.totalPnl,
          dailyPnl: account.dailyPnl
        }
      });
    } catch (error) {
      this.logger.error('更新账户余额失败', { accountId: account.id, error });
    }
  }

  /**
   * 生成执行消息
   */
  private generateExecutionMessage(
    decision: TradingDecision,
    positionResult?: PositionOpenResult
  ): string {
    if (decision.action === 'SKIP') {
      return `跳过交易: ${decision.reason}`;
    }

    if (decision.action === 'EXECUTE') {
      if (positionResult?.success) {
        return `交易执行成功: ${decision.reason}`;
      } else {
        return `交易执行失败: ${positionResult?.error || '未知错误'}`;
      }
    }

    return `等待条件: ${decision.reason}`;
  }

  // ==================== 双轨制相关方法 ====================

  /**
   * 获取账户余额（通过执行引擎路由）
   */
  async getAccountBalance(accountId: string): Promise<AccountBalance> {
    try {
      const account = await this.getAccountById(accountId);
      if (!account) {
        throw new Error(`账户不存在: ${accountId}`);
      }

      return await this.executionEngineRouter.getAccountBalance(account);
    } catch (error) {
      this.logger.error('获取账户余额失败', {
        accountId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 验证订单（通过执行引擎路由）
   */
  async validateOrder(accountId: string, orderRequest: any): Promise<ValidationResult> {
    try {
      const account = await this.getAccountById(accountId);
      if (!account) {
        throw new Error(`账户不存在: ${accountId}`);
      }

      return await this.executionEngineRouter.validateOrder(account, orderRequest);
    } catch (error) {
      this.logger.error('验证订单失败', {
        accountId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 检查风险限制（通过执行引擎路由）
   */
  async checkRiskLimits(accountId: string) {
    try {
      const account = await this.getAccountById(accountId);
      if (!account) {
        throw new Error(`账户不存在: ${accountId}`);
      }

      return await this.executionEngineRouter.checkRiskLimits(account);
    } catch (error) {
      this.logger.error('检查风险限制失败', {
        accountId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取双轨制性能对比数据
   */
  async getDualTrackPerformanceComparison(accountId?: string) {
    try {
      let whereClause = '';
      if (accountId) {
        whereClause = `WHERE ta.id = '${accountId}'`;
      }

      const performanceData = await this.prisma.$queryRaw`
        SELECT
          ta."accountType",
          tp."executionSource",
          COUNT(tp.id) as "totalTrades",
          AVG(tp."realizedPnl") as "avgPnl",
          COUNT(CASE WHEN tp."realizedPnl" > 0 THEN 1 END)::FLOAT / COUNT(tp.id) as "winRate",
          SUM(tp."quantity" * tp."entryPrice") as "totalVolume",
          AVG(
            COALESCE(
              (SELECT SUM(to2."commission")
               FROM "TradingOrders" to2
               WHERE to2."positionId" = tp.id),
              0
            )
          ) as "avgCommission",
          ta."maxDrawdown",
          CASE
            WHEN STDDEV(tp."realizedPnl") > 0
            THEN AVG(tp."realizedPnl") / STDDEV(tp."realizedPnl")
            ELSE 0
          END as "sharpeRatio",
          NOW() as "lastUpdated"
        FROM "TradingPositions" tp
        JOIN "TradingAccounts" ta ON tp."accountId" = ta.id
        WHERE tp.status = 'CLOSED' ${whereClause}
        GROUP BY ta."accountType", tp."executionSource", ta."maxDrawdown"
        ORDER BY ta."accountType", tp."executionSource";
      `;

      return performanceData;
    } catch (error) {
      this.logger.error('获取双轨制性能对比数据失败', {
        accountId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取执行引擎状态
   */
  async getExecutionEngineStatus() {
    try {
      const engines = this.executionEngineRouter.getRegisteredEngines();

      const engineStatus = engines.map(engine => ({
        engineType: engine.engineType,
        name: engine.name,
        isLive: engine.isLive,
        isConnected: engine.isConnected()
      }));

      return {
        totalEngines: engines.length,
        connectedEngines: engines.filter(e => e.isConnected()).length,
        engines: engineStatus
      };
    } catch (error) {
      this.logger.error('获取执行引擎状态失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 切换账户执行引擎
   */
  async switchAccountEngine(accountId: string, newEngineType: string) {
    try {
      this.logger.info('开始切换账户执行引擎', {
        accountId,
        newEngineType
      });

      // 更新数据库中的执行引擎类型
      await this.prisma.tradingAccounts.update({
        where: { id: accountId },
        data: {
          executionEngine: newEngineType,
          updatedAt: new Date()
        }
      });

      this.logger.info('账户执行引擎切换成功', {
        accountId,
        newEngineType
      });

      return { success: true, message: '执行引擎切换成功' };
    } catch (error) {
      this.logger.error('切换账户执行引擎失败', {
        accountId,
        newEngineType,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取账户实体的辅助方法
   */
  private async getAccountById(accountId: string): Promise<TradingAccount | null> {
    try {
      const accountData = await this.prisma.tradingAccounts.findUnique({
        where: { id: accountId }
      });

      if (!accountData) {
        return null;
      }

      return TradingAccount.fromPersistence({
        id: accountData.id,
        name: accountData.name,
        initialCapital: accountData.initialCapital.toNumber(),
        currentBalance: accountData.currentBalance.toNumber(),
        availableBalance: accountData.availableBalance.toNumber(),
        totalPnl: accountData.totalPnl.toNumber(),
        maxDrawdown: accountData.maxDrawdown.toNumber(),
        dailyPnl: accountData.dailyPnl.toNumber(),
        isActive: accountData.isActive,
        riskSettings: accountData.riskSettings as any,
        accountType: (accountData as any).accountType || 'SIMULATION',
        executionEngine: (accountData as any).executionEngine || 'simulation',
        apiCredentials: (accountData as any).apiCredentials,
        syncSettings: (accountData as any).syncSettings,
        parentAccountId: (accountData as any).parentAccountId,
        createdAt: accountData.createdAt,
        updatedAt: accountData.updatedAt
      });
    } catch (error) {
      this.logger.error('获取账户实体失败', {
        accountId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 配置交易所API凭证
   */
  async configureCredentials(config: {
    exchange: string;
    apiKey: string;
    secretKey: string;
    testnet: boolean;
    permissions: string[];
  }): Promise<{ success: boolean; message: string; maskedCredentials?: any }> {
    try {
      this.logger.info('配置交易所API凭证', {
        exchange: config.exchange,
        testnet: config.testnet,
        permissions: config.permissions
      });

      // 创建API凭证对象
      const credentials: ApiCredentials = {
        apiKey: config.apiKey,
        secretKey: config.secretKey,
        testnet: config.testnet,
        permissions: config.permissions,
        encryptedAt: new Date()
      };

      // 验证凭证格式
      const isValid = await this.credentialManager.validateCredentials(credentials);
      if (!isValid) {
        return {
          success: false,
          message: 'API凭证格式无效'
        };
      }

      // 加密并存储凭证
      const encryptedCredentials = await this.credentialManager.encryptCredentials(credentials);

      // 这里可以将加密的凭证存储到数据库或配置文件中
      // 目前先存储到环境变量或内存中

      // 返回掩码后的凭证信息
      const maskedCredentials = this.credentialManager.maskCredentials(credentials);

      this.logger.info('API凭证配置成功', {
        exchange: config.exchange,
        testnet: config.testnet
      });

      return {
        success: true,
        message: 'API凭证配置成功',
        maskedCredentials
      };

    } catch (error) {
      this.logger.error('配置API凭证失败', {
        error: error instanceof Error ? error.message : String(error),
        exchange: config.exchange
      });

      return {
        success: false,
        message: `配置API凭证失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 获取已配置的API凭证信息
   */
  async getCredentials(): Promise<{ success: boolean; data?: any; message?: string }> {
    try {
      this.logger.info('获取API凭证信息');

      // 从真实的配置存储中获取凭证信息
      const credentialsInfo = await this.getRealCredentialsInfo();

      return {
        success: true,
        data: credentialsInfo
      };

    } catch (error) {
      this.logger.error('获取API凭证信息失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        message: `获取API凭证信息失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 从真实配置存储获取凭证信息
   */
  private async getRealCredentialsInfo(): Promise<any> {
    try {
      // 查询数据库中的凭证配置
      const storedCredentials = await this.prisma.apiKeys.findMany({
        where: {
          isActive: true
        },
        select: {
          id: true,
          name: true,
          isActive: true,
          scopes: true,
          lastUsedAt: true,
          createdAt: true,
          updatedAt: true
        }
      });

      const credentialsInfo: Record<string, any> = {};

      for (const cred of storedCredentials) {
        credentialsInfo[cred.name.toLowerCase()] = {
          configured: true,
          testnet: false, // 默认值
          permissions: cred.scopes || [],
          lastValidated: cred.lastUsedAt?.toISOString(),
          status: cred.isActive ? 'active' : 'inactive',
          createdAt: cred.createdAt.toISOString(),
          updatedAt: cred.updatedAt.toISOString()
        };
      }

      // 如果没有配置任何凭证，返回空配置
      if (Object.keys(credentialsInfo).length === 0) {
        this.logger.warn('未找到任何已配置的API凭证');
        return {
          message: '未配置任何交易所API凭证',
          configured: false
        };
      }

      this.logger.info('成功获取API凭证信息', {
        exchangeCount: Object.keys(credentialsInfo).length,
        exchanges: Object.keys(credentialsInfo)
      });

      return credentialsInfo;

    } catch (error) {
      this.logger.error('从数据库获取凭证信息失败', { error });

      // 如果数据库查询失败，尝试从环境变量获取基础配置
      return await this.getFallbackCredentialsInfo();
    }
  }

  /**
   * 从环境变量获取备用凭证信息
   */
  private async getFallbackCredentialsInfo(): Promise<any> {
    try {
      this.logger.info('尝试从环境变量获取凭证配置');

      // const envManager = getEnvironmentManager(); // 函数不存在
      // const apiKeysConfig = envManager.getExtendedApiKeysConfig();
      const apiKeysConfig = {}; // 默认空配置

      const credentialsInfo: Record<string, any> = {};

      // 检查Binance配置
      if ((apiKeysConfig as any).binanceApiKey && (apiKeysConfig as any).binanceSecretKey) {
        credentialsInfo.binance = {
          configured: true,
          testnet: (apiKeysConfig as any).binanceTestnet || false,
          permissions: ['SPOT', 'FUTURES'], // 默认权限
          lastValidated: null,
          status: 'UNKNOWN', // 需要验证
          source: 'environment'
        };
      }

      // 检查其他交易所配置
      // 可以根据需要添加更多交易所

      if (Object.keys(credentialsInfo).length === 0) {
        return {
          message: '未在环境变量中找到API凭证配置',
          configured: false,
          suggestion: '请配置API凭证或设置环境变量'
        };
      }

      this.logger.info('从环境变量获取到凭证配置', {
        exchanges: Object.keys(credentialsInfo)
      });

      return credentialsInfo;

    } catch (error) {
      this.logger.error('从环境变量获取凭证配置失败', { error });

      return {
        message: '无法获取API凭证配置',
        configured: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 同步币安交易记录
   */
  async syncBinanceTradingHistory(accountId: string): Promise<void> {
    try {
      this.logger.info('开始同步币安交易记录', { accountId });

      // 获取账户信息
      const account = await this.prisma.tradingAccounts.findUnique({
        where: { id: accountId }
      });

      if (!account) {
        throw new Error(`账户不存在: ${accountId}`);
      }

      if (account.accountType !== 'REAL' && account.accountType !== 'LIVE' && account.accountType !== 'LIVE_BINANCE') {
        this.logger.warn('跳过非真实账户的币安交易记录同步', { accountId, accountType: account.accountType });
        return;
      }

      // 获取币安执行引擎
      const engine = this.executionEngineRouter.getEngineByType(AccountType.LIVE_BINANCE);
      if (!engine) {
        throw new Error('币安执行引擎未初始化');
      }

      // 获取所有支持的交易符号
      const symbols = await this.prisma.symbols.findMany({
        where: {
          symbol: {
            in: ['BTCUSDT'] // 项目专注于BTC交易
          }
        }
      });

      if (symbols.length === 0) {
        throw new Error('未找到支持的交易符号');
      }

      // 检查引擎是否已连接
      if (!engine.isConnected()) {
        this.logger.warn('币安执行引擎未连接');
        throw new Error('币安执行引擎未连接');
      }

      this.logger.info('开始从币安API获取真实交易记录', {
        accountId,
        supportedSymbols: symbols.map(s => s.symbol)
      });

      let totalSyncedOrders = 0;

      // 为每个符号同步交易记录
      for (const symbol of symbols) {
        try {
          this.logger.info(`开始同步 ${symbol.symbol} 的交易记录`);

          // 通过引擎获取订单历史和交易历史
          const binanceOrders = await (engine as any).getOrderHistory(symbol.symbol, 100);
          this.logger.info(`获取 ${symbol.symbol} 订单历史成功`, { count: binanceOrders.length });

          const binanceTrades = await (engine as any).getTradeHistory(symbol.symbol, 100);
          this.logger.info(`获取 ${symbol.symbol} 交易历史成功`, { count: binanceTrades.length });

          let syncedOrdersCount = 0;

          // 同步订单数据
          for (const binanceOrder of binanceOrders) {
            // 检查订单是否已存在
            const existingOrder = await this.prisma.tradingOrders.findFirst({
              where: {
                accountId,
                exchangeOrderId: binanceOrder.orderId.toString(),
                executionSource: 'BINANCE_LIVE'
              }
            });

            if (existingOrder) {
              continue; // 跳过已存在的订单
            }

            // 映射订单状态
            const statusMapping: Record<string, string> = {
              'NEW': 'PENDING',
              'PARTIALLY_FILLED': 'PARTIALLY_FILLED',
              'FILLED': 'FILLED',
              'CANCELED': 'CANCELLED',
              'REJECTED': 'REJECTED',
              'EXPIRED': 'EXPIRED'
            };

            const status = statusMapping[binanceOrder.status] || binanceOrder.status;

            // 计算手续费（从交易记录中获取实际手续费）
            const relatedTrade = binanceTrades.find(trade => trade.orderId === binanceOrder.orderId);
            const commission = relatedTrade ? parseFloat(relatedTrade.commission) : 0;

            // 创建订单记录
            await this.prisma.tradingOrders.create({
              data: {
                accountId,
                symbolId: symbol.id,
                orderType: binanceOrder.type,
                side: binanceOrder.side,
                quantity: parseFloat(binanceOrder.origQty),
            price: parseFloat(binanceOrder.price || '0'),
            stopPrice: binanceOrder.stopPrice ? parseFloat(binanceOrder.stopPrice) : null,
            status,
            exchangeOrderId: binanceOrder.orderId.toString(),
            filledQuantity: parseFloat(binanceOrder.executedQty),
            averagePrice: binanceOrder.avgPrice ? parseFloat(binanceOrder.avgPrice) : null,
            commission,
            createdAt: new Date(binanceOrder.time),
            updatedAt: new Date(binanceOrder.updateTime),
            filledAt: status === 'FILLED' ? new Date(binanceOrder.updateTime) : null,
            executionSource: 'BINANCE_LIVE'
          }
        });

            syncedOrdersCount++;
          }

          totalSyncedOrders += syncedOrdersCount;

          this.logger.info(`${symbol.symbol} 交易记录同步完成`, {
            accountId,
            symbol: symbol.symbol,
            syncedOrders: syncedOrdersCount,
            totalBinanceOrders: binanceOrders.length,
            totalBinanceTrades: binanceTrades.length
          });

        } catch (symbolError) {
          this.logger.error(`同步 ${symbol.symbol} 交易记录失败`, {
            accountId,
            symbol: symbol.symbol,
            error: symbolError instanceof Error ? symbolError.message : String(symbolError)
          });
          // 继续处理其他符号，不中断整个同步过程
        }
      }

      this.logger.info('所有币种交易记录同步完成', {
        accountId,
        totalSyncedOrders,
        processedSymbols: symbols.length
      });

    } catch (error) {
      this.logger.error('同步币安交易记录失败', { accountId, error });
      throw error;
    }
  }

  // ========== BaseApplicationService 抽象方法实现 ==========

  /**
   * 验证请求输入 - 实现基类抽象方法
   */
  protected async validateRequest<T>(request: T, context: any): Promise<any> {
    const errors: string[] = [];
    const req = request as any;

    try {
      if (!req.accountId || typeof req.accountId !== 'string') {
        errors.push('账户ID无效或缺失');
      }

      // 检查账户是否存在
      const account = await this.getTradingAccount(req.accountId);
      if (!account) {
        errors.push('交易账户不存在');
      }
    } catch (error) {
      errors.push(`验证过程中发生错误: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 将请求参数转换为领域值对象 - 实现基类抽象方法
   */
  protected async convertToDomainObjects<T>(request: T, context: any): Promise<any> {
    const req = request as any;

    // 获取交易账户
    const account = await this.getTradingAccount(req.accountId);

    return {
      accountId: req.accountId,
      account,
      forceExecution: req.forceExecution ?? false,
      skipRiskCheck: req.skipRiskCheck ?? false
    };
  }

  /**
   * 执行核心业务逻辑 - 实现基类抽象方法
   */
  protected async executeCoreBusinessLogic(domainObjects: any, context: any): Promise<any> {
    const { account, accountId, skipRiskCheck } = domainObjects;

    // 获取活跃仓位
    const positions = await this.getActivePositionsInternal(accountId);

    // 风险检查
    const riskStatus = await this.riskMonitor.monitorAccountRisk(account, positions);

    if (!skipRiskCheck && (riskStatus.level === 'EMERGENCY' || riskStatus.level === 'CRITICAL')) {
      return {
        success: false,
        accountId,
        decision: { action: 'SKIP', reason: `风险等级过高: ${riskStatus.level}` },
        riskStatus,
        skipExecution: true,
        message: `风险等级${riskStatus.level}，暂停交易`
      };
    }

    // 处理风险行动
    await this.executeRiskActions(riskStatus, positions);

    // 生成交易决策
    const decision = await this.strategyEngine.processLatestSignals(account);

    return {
      account,
      accountId,
      positions,
      riskStatus,
      decision,
      skipExecution: false
    };
  }

  /**
   * 应用业务规则后处理 - 实现基类抽象方法
   */
  protected async applyBusinessRules<T>(
    coreResult: any,
    originalRequest: T,
    context: any
  ): Promise<any> {
    const warnings: string[] = [];
    const recommendations: string[] = [];

    // 如果因风险跳过执行，直接返回
    if (coreResult.skipExecution) {
      return {
        data: coreResult,
        warnings: [`风险等级${coreResult.riskStatus.level}，暂停交易`],
        recommendations: coreResult.riskStatus.recommendations || []
      };
    }

    const req = originalRequest as any;
    const { account, decision, riskStatus } = coreResult;

    // 执行交易决策
    let positionResult: PositionOpenResult | undefined;
    if (decision.action === 'EXECUTE' && (req.forceExecution || riskStatus.level !== 'CRITICAL')) {
      try {
        positionResult = await this.executeNewPosition(account, decision);

        if (!positionResult.success) {
          warnings.push('交易执行失败');
          recommendations.push('检查账户余额和市场条件');
        }
      } catch (error) {
        warnings.push(`交易执行异常: ${error instanceof Error ? error.message : String(error)}`);
        recommendations.push('检查交易引擎状态和网络连接');
      }
    } else if (decision.action === 'SKIP') {
      recommendations.push('当前市场条件不适合交易，建议等待更好的机会');
    }

    // 风险等级检查
    if (riskStatus.level === 'HIGH') {
      warnings.push('账户风险等级较高');
      recommendations.push('考虑减少仓位或暂停交易');
    }

    const success = decision.action === 'SKIP' || (positionResult?.success ?? false);

    return {
      data: {
        ...coreResult,
        positionResult,
        success
      },
      warnings,
      recommendations
    };
  }

  /**
   * 获取可选的附加数据 - 实现基类抽象方法
   */
  protected async enrichWithAdditionalData<T>(
    processedResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<any> {
    // 交易执行通常不需要额外的附加数据
    return processedResult;
  }

  /**
   * 格式化响应DTO - 实现基类抽象方法
   */
  protected async formatResponse<T>(
    enrichedResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<any> {
    const { accountId, decision, positionResult, riskStatus, success } = enrichedResult;

    return {
      success: success ?? true,
      message: success ? '交易策略执行完成' : '交易策略执行失败',
      timestamp: new Date(),
      requestId: context.requestId,
      data: {
        accountId,
        decision,
        positionResult,
        riskStatus
      }
    };
  }

  // 健康检查已移至统一健康检查聚合器 - 避免重复实现
  // 使用 TradingExecutionHealthProvider 替代

  /**
   * 总结请求内容 - 重写基类方法
   */
  protected summarizeRequest<T>(request: T): any {
    const req = request as any;
    return {
      accountId: req.accountId,
      forceExecution: req.forceExecution,
      skipRiskCheck: req.skipRiskCheck
    };
  }

  /**
   * 执行风险强制执行检查
   */
  private async performRiskEnforcementCheck(
    request: any,
    account: TradingAccount
  ): Promise<TradeExecutionCheckResult> {
    try {
      // 获取当前风险评估
      const positions = await this.getActivePositionsInternal(account.id);
      const currentRiskAssessment = await this.riskMonitor.monitorAccountRisk(account, positions);

      // 构建订单请求对象
      const orderRequest = {
        symbol: request.symbolId,
        side: request.side,
        quantity: request.quantity,
        price: request.price,
        orderType: request.orderType,
        leverage: request.leverage
      };

      // 执行风险强制执行检查
      const checkResult = await this.riskEnforcementEngine.checkTradeExecution(
        account.id,
        orderRequest,
        currentRiskAssessment
      );

      // 如果有违规，执行强制行动
      if (checkResult.violations.length > 0) {
        for (const violation of checkResult.violations) {
          await this.riskEnforcementEngine.executeEnforcementAction(
            account.id,
            violation.action,
            violation,
            { orderRequest, account }
          );
        }
      }

      return checkResult;

    } catch (error) {
      this.logger.error('风险强制执行检查失败', {
        accountId: account.id,
        error: error instanceof Error ? error.message : String(error)
      });

      // 安全起见，拒绝交易
      return {
        allowed: false,
        blockedReasons: ['风险检查系统异常'],
        warnings: [],
        modifications: {},
        violations: [],
        riskScore: 100,
        enforcementActions: []
      };
    }
  }

  /**
   * 应用风险管理修改
   */
  private applyRiskModifications(
    originalRequest: any,
    riskCheckResult: TradeExecutionCheckResult
  ): any {
    const modifiedRequest = { ...originalRequest };

    // 应用订单大小限制
    if (riskCheckResult.modifications.maxOrderSize !== undefined) {
      modifiedRequest.quantity = Math.min(
        modifiedRequest.quantity,
        riskCheckResult.modifications.maxOrderSize
      );

      this.logger.info('应用订单大小限制', {
        originalQuantity: originalRequest.quantity,
        limitedQuantity: modifiedRequest.quantity,
        accountId: originalRequest.accountId
      });
    }

    // 记录需要审批的情况
    if (riskCheckResult.modifications.requiredApproval) {
      this.logger.warn('订单需要风险管理审批', {
        accountId: originalRequest.accountId,
        symbol: originalRequest.symbolId,
        quantity: originalRequest.quantity
      });
      // 这里可以添加审批流程的逻辑
    }

    // 记录警告信息
    if (riskCheckResult.warnings.length > 0) {
      this.logger.warn('风险管理警告', {
        accountId: originalRequest.accountId,
        warnings: riskCheckResult.warnings
      });
    }

    return modifiedRequest;
  }

  /**
   * 总结响应内容 - 重写基类方法
   */
  protected summarizeResponse<T>(response: T): any {
    const resp = response as any;
    return {
      success: resp.success,
      accountId: resp.data?.accountId,
      decision: resp.data?.decision?.action,
      positionSuccess: resp.data?.positionResult?.success,
      riskLevel: resp.data?.riskStatus?.level
    };
  }
}
