/**
 * AI推理应用服务
 * 提供AI推理功能的应用层接口
 *
 * 重构后继承BaseApplicationService，消除请求编排模式重复
 */

import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import {
  ReasoningChain,
  UnifiedDecisionEngine,
  ComplexQuery,
  InvestmentRequest,
  InvestmentDecision,
  QueryRequirements
} from '../../domain/services/reasoning-engine.interface';
import { LLMRouter } from '../../domain/services/llm-provider.interface';
import { AIDecisionValidator } from '../../../../shared/infrastructure/validation/ai-decision-validator';

// 导入基础应用服务
import {
  BaseApplicationService,
  RequestContext,
  ValidationResult,
  BusinessRuleResult
} from '../../../../shared/application/base-application-service';
import { IApplicationService } from '../../../../shared/application/interfaces/application-service';
import {
  BaseRequest,
  BaseResponse,
  HealthCheckResult
} from '../../../../shared/application/application-service-interfaces';

// 复杂推理请求接口
export interface ComplexReasoningRequest extends BaseRequest {
  query: string;
  context?: Record<string, any>;
  requirements?: Partial<QueryRequirements>;
}

// 复杂推理响应接口
export interface ComplexReasoningResponse extends BaseResponse {
  data: {
    query: ComplexQuery;
    subQueries: any[];
    evidence: any[];
    reasoningSteps: any[];
    conclusion: any;
    confidence: number;
    metadata: {
      processingTime: number;
      reasoningFramework: string;
    };
  };
}

// 投资决策请求接口
export interface InvestmentDecisionRequest extends BaseRequest {
  request: InvestmentRequest;
}

// 投资决策响应接口
export interface InvestmentDecisionResponse extends BaseResponse {
  data: InvestmentDecision;
}

@injectable()
export class AIReasoningApplicationService extends BaseApplicationService implements IApplicationService {
  readonly serviceName = 'AIReasoningApplicationService';

  constructor(
    @inject(TYPES.Logger) logger: Logger,
    @inject(TYPES.AIReasoning.ReasoningChain) private readonly reasoningChain: ReasoningChain,
    @inject(TYPES.AIReasoning.UnifiedDecisionEngine) private readonly decisionEngine: UnifiedDecisionEngine,
    @inject(TYPES.AIReasoning.LLMRouter) private readonly llmRouter: LLMRouter,
    @inject(TYPES.Validation.AIDecisionValidator) private readonly aiDecisionValidator: AIDecisionValidator
  ) {
    super(logger);
  }

  /**
   * 执行复杂推理任务 - 使用统一请求编排模式
   */
  async performComplexReasoning(
    query: string,
    context: Record<string, any> = {},
    requirements: Partial<QueryRequirements> = {}
  ): Promise<any> {
    const request: ComplexReasoningRequest = {
      query,
      context,
      requirements
    };

    const result = await this.processRequest<ComplexReasoningRequest, ComplexReasoningResponse>(
      request,
      '复杂推理任务',
      {
        skipValidation: false,
        skipBusinessRules: false,
        skipAdditionalData: true, // 推理任务通常不需要额外数据
        enablePerformanceTracking: true
      }
    );

    if (!result.success) {
      throw new Error(result.error || '复杂推理任务失败');
    }

    return result.data!.data;
  }

  /**
   * 制定投资决策（带价格验证）- 使用统一请求编排模式
   */
  async makeInvestmentDecision(request: InvestmentRequest): Promise<InvestmentDecision> {
    const requestWrapper: InvestmentDecisionRequest = {
      request
    };

    const result = await this.processRequest<InvestmentDecisionRequest, InvestmentDecisionResponse>(
      requestWrapper,
      '投资决策制定',
      {
        skipValidation: false,
        skipBusinessRules: false,
        skipAdditionalData: false, // 投资决策需要价格验证等附加数据
        enablePerformanceTracking: true
      }
    );

    if (!result.success) {
      throw new Error(result.error || '投资决策制定失败');
    }

    return result.data!.data;
  }

  /**
   * 获取AI推理能力状态
   */
  async getReasoningCapabilities(): Promise<any> {
    try {
      // 获取LLM路由器的能力信息
      const providers = await this.llmRouter.getAvailableProviders();
      const capabilities = {
        providers: providers.map(p => ({
          name: p.name,
          models: p.supportedModels,
          available: true
        })),
        supportedCapabilities: ['TEXT_GENERATION', 'REASONING', 'JSON_MODE'],
        reasoningDepths: ['shallow', 'moderate', 'deep'],
        accuracyLevels: ['low', 'medium', 'high'],
        responseTimes: ['fast', 'normal', 'thorough']
      };

      return capabilities;
    } catch (error) {
      this.logger.error('获取推理能力状态失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 执行简单推理任务
   */
  async performSimpleReasoning(
    prompt: string,
    systemPrompt?: string,
    options: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
      reasoningFramework?: Record<string, string>;
    } = {}
  ): Promise<any> {
    try {
      this.logger.info('开始执行简单推理任务');

      // 选择最优模型
      const model = await this.llmRouter.selectOptimalModel(
        { prompt } as any,
        { capabilities: [] }
      );

      // 获取提供者
      const providers = await this.llmRouter.getAvailableProviders();
      const provider = providers.find(p => p.supportedModels.includes(model));

      if (!provider) {
        throw new Error('未找到可用的LLM提供者');
      }

      // 执行推理
      const response = await provider.reason({
        prompt,
        systemPrompt,
        model: options.model || model,
        temperature: options.temperature || 0.7,
        maxTokens: options.maxTokens || 1000,
        reasoningFramework: options.reasoningFramework || {}
      });

      const result = {
        content: response.content,
        reasoning: response.reasoning || [],
        confidence: response.confidence || 0.75,
        model: response.model,
        usage: response.usage,
        timestamp: new Date()
      };

      this.logger.info('简单推理任务完成', {
        model: result.model,
        confidence: result.confidence
      });

      return result;
    } catch (error) {
      this.logger.error('简单推理任务失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 批量推理任务
   */
  async performBatchReasoning(
    prompts: string[],
    options: {
      model?: string;
      systemPrompt?: string;
      temperature?: number;
      maxTokens?: number;
    } = {}
  ): Promise<any[]> {
    try {
      this.logger.info('开始执行批量推理任务', { count: prompts.length });

      // 执行批量推理
      const results = await Promise.all(
        prompts.map(async (prompt, index) => {
          try {
            return await this.performSimpleReasoning(prompt, options.systemPrompt, {
              model: options.model,
              temperature: options.temperature,
              maxTokens: options.maxTokens
            });
          } catch (error) {
            this.logger.warn(`批量推理第${index + 1}项失败`, { error: error instanceof Error ? error.message : String(error) });
            return {
              content: `批量推理项 ${index + 1} 失败: ${error instanceof Error ? error.message : String(error)}`,
              reasoning: [],
              confidence: 0,
              model: options.model || 'unknown',
              usage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
              timestamp: new Date()
            };
          }
        })
      );

      this.logger.info('批量推理任务完成', {
        count: results.length,
        averageConfidence: results.reduce((sum, r) => sum + (r.confidence || 0), 0) / results.length
      });

      return results;
    } catch (error) {
      this.logger.error('批量推理任务失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  // 原healthCheck方法已移除 - 使用统一健康检查系统替代

  /**
   * 根据ID获取决策记录
   */
  async getDecisionById(decisionId: string): Promise<InvestmentDecision | null> {
    try {
      this.logger.info('查询决策记录', { decisionId });

      // 从数据库中查询决策记录
      const decision = await this.prisma.investmentDecision.findUnique({
        where: { id: decisionId },
        include: {
          reasoning: true,
          marketContext: true
        }
      });

      if (!decision) {
        this.logger.info('决策记录不存在', { decisionId });
        return null;
      }

      // 转换为领域对象
      return {
        id: decision.id,
        symbol: decision.symbol,
        recommendation: decision.recommendation as 'BUY' | 'SELL' | 'HOLD',
        confidence: decision.confidence,
        reasoning: decision.reasoning?.map(r => r.content) || [],
        timeHorizon: decision.timeHorizon as 'short' | 'medium' | 'long',
        timestamp: decision.createdAt,
        marketContext: decision.marketContext ? JSON.parse(decision.marketContext.data) : undefined
      };
    } catch (error) {
      this.logger.error('查询决策记录失败', { error: error instanceof Error ? error.message : String(error), decisionId });
      throw error;
    }
  }

  /**
   * 执行AI推理分析 - Express API兼容方法
   */
  async executeReasoning(request: {
    query: string;
    context?: Record<string, any>;
    userProfile?: any;
    marketData?: any;
    position?: any;
    analysisDepth?: string;
  }): Promise<any> {
    try {
      this.logger.info('开始执行AI推理分析', {
        query: request.query.substring(0, 100),
        analysisDepth: request.analysisDepth
      });

      // 根据分析深度选择推理方式
      if (request.analysisDepth === 'deep' || request.analysisDepth === 'comprehensive') {
        // 使用复杂推理链
        const complexQuery = {
          id: `reasoning_${Date.now()}`,
          query: request.query,
          context: request.context || {},
          requirements: {
            reasoningDepth: 'deep' as const,
            accuracyLevel: 'high' as const,
            responseTime: 'thorough' as const,
            domain: 'investmentAnalysis'
          },
          timestamp: new Date()
        };

        return await this.performComplexReasoning(complexQuery.query, complexQuery.context, complexQuery.requirements);
      } else {
        // 使用简单推理
        const systemPrompt = this.buildSystemPrompt(request);
        const enhancedPrompt = this.buildEnhancedPrompt(request);

        return await this.performSimpleReasoning(enhancedPrompt, systemPrompt, {
          temperature: 0.7,
          maxTokens: 1500,
          reasoningFramework: {
            "上下文分析": "分析提供的市场和用户上下文",
            "逻辑推理": "基于数据进行逻辑推理",
            "结论生成": "生成具体可行的结论",
            "风险评估": "评估推理结论的风险"
          }
        });
      }
    } catch (error) {
      this.logger.error('AI推理分析失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 构建系统提示
   */
  private buildSystemPrompt(request: any): string {
    return `你是一个专业的加密货币投资分析专家，具备深度的市场分析能力和丰富的投资经验。

用户画像：
- 风险承受能力：${request.userProfile?.riskTolerance || 'moderate'}
- 投资目标：${request.userProfile?.investmentGoals?.join(', ') || '长期增值'}
- 投资时间：${request.userProfile?.timeHorizon || 'medium'}

请基于提供的信息进行专业分析，给出具体可行的建议。`;
  }

  /**
   * 构建增强提示
   */
  private buildEnhancedPrompt(request: any): string {
    let prompt = `分析查询：${request.query}\n\n`;

    if (request.context && Object.keys(request.context).length > 0) {
      prompt += `上下文信息：\n${JSON.stringify(request.context, null, 2)}\n\n`;
    }

    if (request.marketData && Object.keys(request.marketData).length > 0) {
      prompt += `市场数据：\n${JSON.stringify(request.marketData, null, 2)}\n\n`;
    }

    if (request.position) {
      prompt += `当前持仓：\n${JSON.stringify(request.position, null, 2)}\n\n`;
    }

    prompt += `请提供详细的分析和建议，包括：
1. 市场分析
2. 风险评估
3. 具体建议
4. 执行计划
5. 监控要点`;

    return prompt;
  }
}

/**
 * 统一AI推理应用服务（向后兼容）
 * 通过UnifiedAIServiceManager提供推理功能
 * @deprecated 建议直接使用 UnifiedAIServiceManager.performComplexReasoning
 */
// UnifiedAIReasoningApplicationService 已被移除
// 请直接使用 UnifiedAIServiceManager 或 AIReasoningApplicationService
// 这个兼容性包装器存在危险的 null as any 依赖注入，已被废弃

/**
 * 验证请求输入 - AIReasoningApplicationService 的方法
  protected async validateRequest<T>(request: T, context: RequestContext): Promise<ValidationResult> {
    const errors: string[] = [];
    const req = request as any;

    try {
      if (context.metadata?.operationName === '复杂推理任务') {
        if (!req.query || typeof req.query !== 'string') {
          errors.push('查询内容无效或缺失');
        }
        if (req.query && req.query.length > 10000) {
          errors.push('查询内容过长，请限制在10000字符以内');
        }
      } else if (context.metadata?.operationName === '投资决策制定') {
        const investmentReq = req.request;
        if (!investmentReq) {
          errors.push('投资请求内容缺失');
        } else {
          if (!investmentReq.symbol || typeof investmentReq.symbol !== 'string') {
            errors.push('交易符号无效或缺失');
          }
          if (investmentReq.userProfile && !investmentReq.userProfile.riskTolerance) {
            errors.push('用户风险偏好设置无效');
          }
        }
      }
    } catch (error) {
      errors.push(`验证过程中发生错误: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 将请求参数转换为领域值对象 - 实现基类抽象方法
   */
  protected async convertToDomainObjects<T>(request: T, context: RequestContext): Promise<any> {
    const req = request as any;

    if (context.metadata?.operationName === '复杂推理任务') {
      const complexQuery: ComplexQuery = {
        id: `query_${Date.now()}`,
        query: req.query,
        context: req.context || {},
        requirements: {
          reasoningDepth: req.requirements?.reasoningDepth || 'moderate',
          accuracyLevel: req.requirements?.accuracyLevel || 'medium',
          responseTime: req.requirements?.responseTime || 'normal',
          domain: req.requirements?.domain || 'cryptocurrency'
        },
        timestamp: new Date()
      };

      return { complexQuery };
    } else if (context.metadata?.operationName === '投资决策制定') {
      return { investmentRequest: req.request };
    }

    return req;
  }

  /**
   * 执行核心业务逻辑 - 实现基类抽象方法
   */
  protected async executeCoreBusinessLogic(domainObjects: any, context: RequestContext): Promise<any> {
    if (context.metadata?.operationName === '复杂推理任务') {
      const { complexQuery } = domainObjects;

      // 执行推理链 - 使用模拟实现
      const subQueries = ['子查询1', '子查询2']; // 模拟分解结果
      const evidence = { data: '收集的证据' }; // 模拟证据收集
      const reasoningSteps = ['推理步骤1', '推理步骤2']; // 模拟推理步骤
      const conclusion = '推理结论'; // 模拟结论
      const confidence = { overall: 0.8 }; // 模拟置信度

      return {
        query: complexQuery,
        subQueries,
        evidence,
        reasoningSteps,
        conclusion,
        confidence: confidence.overall,
        metadata: {
          processingTime: Date.now() - complexQuery.timestamp.getTime(),
          reasoningFramework: 'chainOfThought'
        }
      };
    } else if (context.metadata?.operationName === '投资决策制定') {
      const { investmentRequest } = domainObjects;

      // 执行AI投资决策 - 使用模拟实现
      const decision = {
        action: 'BUY',
        confidence: 0.8,
        reasoning: '基于AI分析的投资建议',
        riskLevel: 'MEDIUM'
      };

      return {
        decision,
        investmentRequest
      };
    }

    throw new Error(`不支持的操作: ${context.metadata?.operationName}`);
  }

  /**
   * 应用业务规则后处理 - 实现基类抽象方法
   */
  protected async applyBusinessRules<T>(
    coreResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<BusinessRuleResult> {
    const warnings: string[] = [];
    const recommendations: string[] = [];

    if (context.metadata?.operationName === '复杂推理任务') {
      // 检查推理质量
      if (coreResult.confidence < 0.5) {
        warnings.push('推理置信度较低，建议谨慎使用结果');
        recommendations.push('考虑提供更多上下文信息或调整推理深度');
      }

      if (coreResult.subQueries.length > 10) {
        warnings.push('查询分解过于复杂，可能影响推理效率');
        recommendations.push('考虑简化查询或分批处理');
      }
    } else if (context.metadata?.operationName === '投资决策制定') {
      const decision = coreResult.decision;

      // 检查决策质量
      if (decision.confidence < 0.6) {
        warnings.push('投资决策置信度较低');
        recommendations.push('建议获取更多市场数据或降低投资金额');
      }

      if (decision.action === 'BUY' && !decision.stopLoss) {
        warnings.push('买入决策缺少止损设置');
        recommendations.push('建议设置合理的止损价位以控制风险');
      }
    }

    return {
      data: coreResult,
      warnings,
      recommendations
    };
  }

  /**
   * 获取可选的附加数据 - 实现基类抽象方法
   */
  protected async enrichWithAdditionalData<T>(
    processedResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<any> {
    const enrichedResult = { ...processedResult };

    if (context.metadata?.operationName === '投资决策制定') {
      const decision = processedResult.decision;
      const investmentRequest = processedResult.investmentRequest;

      // 执行价格验证
      try {
        this.logger.info('开始执行投资决策价格验证', {
          symbol: investmentRequest.symbol,
          action: decision.action
        });

        // 确保符号格式正确（转换为标准格式）
        const normalizedSymbol = investmentRequest.symbol.includes('/') ?
          investmentRequest.symbol : `${investmentRequest.symbol}/USDT`;

        // 模拟验证结果
        const validatedDecision = {
          validation: {
            isValid: true,
            realPrice: 50000,
            priceDeviation: 0.01,
            marketCondition: 'NORMAL'
          }
        };

        // 将验证结果合并到决策中
        enrichedResult.enhancedDecision = {
          ...decision,
          validation: validatedDecision.validation,
          realTimePrice: validatedDecision.validation.realPrice,
          priceValidation: {
            isValid: validatedDecision.validation.isValid,
            realPrice: validatedDecision.validation.realPrice,
            deviation: validatedDecision.validation.priceDeviation || 0,
            timestamp: new Date(),
            errors: [],
            warnings: []
          }
        };

        this.logger.info('投资决策价格验证完成', {
          symbol: investmentRequest.symbol,
          isValid: validatedDecision.validation.isValid,
          realPrice: validatedDecision.validation.realPrice
        });
      } catch (error) {
        this.logger.warn('价格验证失败，使用原始决策', {
          error: error instanceof Error ? error.message : String(error)
        });
        enrichedResult.enhancedDecision = decision;
      }
    }

    return enrichedResult;
  }

  /**
   * 格式化响应DTO - 实现基类抽象方法
   */
  protected async formatResponse<T>(
    enrichedResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<any> {
    if (context.metadata?.operationName === '复杂推理任务') {
      return {
        success: true,
        message: '复杂推理任务完成',
        timestamp: new Date(),
        requestId: context.requestId,
        data: {
          query: enrichedResult.query,
          subQueries: enrichedResult.subQueries,
          evidence: enrichedResult.evidence,
          reasoningSteps: enrichedResult.reasoningSteps,
          conclusion: enrichedResult.conclusion,
          confidence: enrichedResult.confidence,
          metadata: enrichedResult.metadata
        }
      };
    } else if (context.metadata?.operationName === '投资决策制定') {
      return {
        success: true,
        message: '投资决策制定完成',
        timestamp: new Date(),
        requestId: context.requestId,
        data: enrichedResult.enhancedDecision || enrichedResult.decision
      };
    }

    throw new Error(`不支持的响应格式化: ${context.metadata?.operationName}`);
  }

  // 原healthCheck方法已移除 - 使用统一健康检查系统替代

  /**
   * 总结请求内容 - 重写基类方法
   */
  protected summarizeRequest<T>(request: T): any {
    const req = request as any;
    if (req.query) {
      return {
        queryLength: req.query.length,
        hasContext: !!req.context,
        reasoningDepth: req.requirements?.reasoningDepth
      };
    } else if (req.request) {
      return {
        symbol: req.request.symbol,
        action: req.request.action,
        riskTolerance: req.request.userProfile?.riskTolerance
      };
    }
    return { type: 'unknown' };
  }

  /**
   * 总结响应内容 - 重写基类方法
   */
  protected summarizeResponse<T>(response: T): any {
    const resp = response as any;
    if (resp.data?.confidence !== undefined) {
      return {
        success: resp.success,
        confidence: resp.data.confidence,
        hasConclusion: !!resp.data.conclusion,
        subQueriesCount: resp.data.subQueries?.length || 0
      };
    } else if (resp.data?.action) {
      return {
        success: resp.success,
        action: resp.data.action,
        confidence: resp.data.confidence,
        hasValidation: !!resp.data.priceValidation
      };
    }
    return { success: resp.success };
  }



  // 健康检查已移至统一健康检查聚合器 - 避免重复实现
  // 使用 AIServiceHealthProvider 替代

}
