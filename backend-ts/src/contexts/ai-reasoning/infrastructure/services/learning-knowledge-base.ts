/**
 * 学习知识库实现 - 短周期学习机制 2.0
 */

import { injectable, inject } from 'inversify';
import { PrismaClient } from '@prisma/client';
import { Logger } from 'winston';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import {
  ILearningKnowledgeBase,
  MarketInsight,
  TypePerformance,
  CrossPattern,
  KnowledgeItem,
  HistoricalPerformance,
  LearningPattern,
  KnowledgeType,
  PerformanceMetric,
  MarketContext,
  KnowledgeStatistics,
  QualityAssessment
} from '../../domain/services/learning-knowledge-base.interface';

@injectable()
export class LearningKnowledgeBase implements ILearningKnowledgeBase {
  private readonly knowledgeCache: Map<string, KnowledgeItem> = new Map();
  // private cacheExpiry: Date = new Date(0);
  // private readonly CACHE_TTL = 10 * 60 * 1000; // 10分钟缓存（暂时不使用）

  constructor(
    @inject(TYPES.Database) private readonly prisma: PrismaClient,
    @inject(TYPES.Logger) private readonly logger: Logger
  ) {
    this.logger.info('学习知识库初始化完成');
  }

  /**
   * 更新市场条件知识
   */
  async updateMarketConditionKnowledge(
    condition: string,
    metrics: PerformanceMetric[]
  ): Promise<void> {
    try {
      this.logger.info('更新市场条件知识', { condition, metricsCount: metrics.length });

      if (metrics.length === 0) {
        this.logger.warn('没有性能指标数据，跳过知识更新', { condition });
        return;
      }

      // 计算统计数据
      const avgAccuracy = metrics.reduce((sum, m) => sum + m.accuracy, 0) / metrics.length;
      const avgConfidence = metrics.reduce((sum, m) => sum + m.confidence, 0) / metrics.length;

      // 识别最佳策略
      const bestStrategies = this.identifyBestStrategies(metrics);
      const riskFactors = this.identifyRiskFactors(metrics);
      const optimalParameters = this.calculateOptimalParameters(metrics);

      // 构建知识数据
      const knowledgeData = {
        averageAccuracy: avgAccuracy,
        averageConfidence: avgConfidence,
        bestStrategies: bestStrategies,
        riskFactors: riskFactors,
        optimalParameters: optimalParameters,
        performanceDistribution: this.calculatePerformanceDistribution(metrics),
        temporalPatterns: this.analyzeTemporalPatterns(metrics)
      };

      // 计算置信度分数
      const confidenceScore = this.calculateKnowledgeConfidence(metrics, avgAccuracy);

      // 保存或更新知识
      await this.prisma.learningKnowledgeBase.upsert({
        where: {
          knowledgeType_knowledgeKey: {
            knowledgeType: KnowledgeType.MARKET_CONDITION,
            knowledgeKey: condition
          }
        },
        update: {
          knowledgeData,
          confidenceScore,
          sampleCount: metrics.length,
          lastUpdated: new Date()
        },
        create: {
          knowledgeType: KnowledgeType.MARKET_CONDITION,
          knowledgeKey: condition,
          knowledgeData,
          confidenceScore,
          sampleCount: metrics.length,
          lastUpdated: new Date()
        }
      });

      // 清除缓存
      this.invalidateCache();

      this.logger.info('市场条件知识更新完成', {
        condition,
        avgAccuracy: avgAccuracy.toFixed(3),
        confidenceScore: confidenceScore.toFixed(3),
        sampleCount: metrics.length
      });

    } catch (error) {
      this.logger.error('更新市场条件知识失败', { condition, error });
      throw error;
    }
  }

  /**
   * 获取市场条件洞察
   */
  async getMarketConditionInsights(condition: string): Promise<MarketInsight[]> {
    try {
      this.logger.debug('获取市场条件洞察', { condition });

      const knowledge = await this.prisma.learningKnowledgeBase.findUnique({
        where: {
          knowledgeType_knowledgeKey: {
            knowledgeType: KnowledgeType.MARKET_CONDITION,
            knowledgeKey: condition
          }
        }
      });

      if (!knowledge) {
        this.logger.warn('未找到市场条件知识', { condition });
        return [];
      }

      const data = knowledge.knowledgeData as any;
      const insight: MarketInsight = {
        condition,
        averageAccuracy: data.averageAccuracy || 0,
        bestStrategies: data.bestStrategies || [],
        riskFactors: data.riskFactors || [],
        optimalParameters: data.optimalParameters || {},
        sampleCount: knowledge.sampleCount,
        confidenceScore: Number(knowledge.confidenceScore),
        description: this.generateInsightDescription(condition, data)
      };

      return [insight];

    } catch (error) {
      this.logger.error('获取市场条件洞察失败', { condition, error });
      return [];
    }
  }

  /**
   * 获取所有市场条件
   */
  async getAllMarketConditions(): Promise<string[]> {
    try {
      const conditions = await this.prisma.learningKnowledgeBase.findMany({
        where: { knowledgeType: KnowledgeType.MARKET_CONDITION },
        select: { knowledgeKey: true }
      });

      return conditions.map(c => c.knowledgeKey);
    } catch (error) {
      this.logger.error('获取所有市场条件失败', { error });
      return [];
    }
  }

  /**
   * 更新预测类型知识
   */
  async updatePredictionTypeKnowledge(
    type: string,
    performance: TypePerformance
  ): Promise<void> {
    try {
      this.logger.info('更新预测类型知识', { type });

      const knowledgeData = {
        averageAccuracy: performance.averageAccuracy,
        bestMarketConditions: performance.bestMarketConditions,
        worstMarketConditions: performance.worstMarketConditions,
        optimalParameters: performance.optimalParameters,
        trend: performance.trend,
        performanceAnalysis: this.analyzeTypePerformance(performance)
      };

      const confidenceScore = Math.min(performance.sampleCount / 100, 1.0);

      await this.prisma.learningKnowledgeBase.upsert({
        where: {
          knowledgeType_knowledgeKey: {
            knowledgeType: KnowledgeType.PREDICTION_TYPE,
            knowledgeKey: type
          }
        },
        update: {
          knowledgeData,
          confidenceScore,
          sampleCount: performance.sampleCount,
          lastUpdated: new Date()
        },
        create: {
          knowledgeType: KnowledgeType.PREDICTION_TYPE,
          knowledgeKey: type,
          knowledgeData,
          confidenceScore,
          sampleCount: performance.sampleCount,
          lastUpdated: new Date()
        }
      });

      this.invalidateCache();
      this.logger.info('预测类型知识更新完成', { type });

    } catch (error) {
      this.logger.error('更新预测类型知识失败', { type, error });
      throw error;
    }
  }

  /**
   * 获取预测类型性能
   */
  async getPredictionTypePerformance(type: string): Promise<TypePerformance | null> {
    try {
      const knowledge = await this.prisma.learningKnowledgeBase.findUnique({
        where: {
          knowledgeType_knowledgeKey: {
            knowledgeType: KnowledgeType.PREDICTION_TYPE,
            knowledgeKey: type
          }
        }
      });

      if (!knowledge) {
        return null;
      }

      const data = knowledge.knowledgeData as any;
      return {
        predictionType: type,
        averageAccuracy: data.averageAccuracy || 0,
        bestMarketConditions: data.bestMarketConditions || [],
        worstMarketConditions: data.worstMarketConditions || [],
        optimalParameters: data.optimalParameters || {},
        sampleCount: knowledge.sampleCount,
        trend: data.trend || 'stable'
      };

    } catch (error) {
      this.logger.error('获取预测类型性能失败', { type, error });
      return null;
    }
  }

  /**
   * 分析交叉条件模式
   */
  async analyzeCrossConditionPatterns(): Promise<CrossPattern[]> {
    try {
      this.logger.info('开始分析交叉条件模式');

      // 获取所有市场条件知识
      const marketKnowledge = await this.prisma.learningKnowledgeBase.findMany({
        where: { knowledgeType: KnowledgeType.MARKET_CONDITION }
      });

      if (marketKnowledge.length < 2) {
        this.logger.warn('市场条件数据不足，无法进行交叉分析');
        return [];
      }

      const patterns: CrossPattern[] = [];

      // 分析条件间的相关性
      for (let i = 0; i < marketKnowledge.length; i++) {
        for (let j = i + 1; j < marketKnowledge.length; j++) {
          const condition1 = marketKnowledge[i];
          const condition2 = marketKnowledge[j];

          const correlation = this.calculateConditionCorrelation(
            condition1.knowledgeData as any,
            condition2.knowledgeData as any
          );

          if (Math.abs(correlation) > 0.5) { // 相关性阈值
            const pattern: CrossPattern = {
              patternId: `${condition1.knowledgeKey}_${condition2.knowledgeKey}`,
              conditions: [condition1.knowledgeKey, condition2.knowledgeKey],
              correlations: {
                accuracy: correlation,
                strategies: this.calculateStrategyCorrelation(condition1, condition2)
              },
              predictiveValue: Math.abs(correlation),
              description: this.generatePatternDescription(condition1, condition2, correlation),
              examples: this.generatePatternExamples(condition1, condition2)
            };

            patterns.push(pattern);
          }
        }
      }

      // 保存交叉模式知识
      for (const pattern of patterns) {
        await this.prisma.learningKnowledgeBase.upsert({
          where: {
            knowledgeType_knowledgeKey: {
              knowledgeType: KnowledgeType.CROSS_PATTERN,
              knowledgeKey: pattern.patternId
            }
          },
          update: {
            knowledgeData: JSON.parse(JSON.stringify(pattern)),
            confidenceScore: pattern.predictiveValue,
            sampleCount: 1,
            lastUpdated: new Date()
          },
          create: {
            knowledgeType: KnowledgeType.CROSS_PATTERN,
            knowledgeKey: pattern.patternId,
            knowledgeData: JSON.parse(JSON.stringify(pattern)),
            confidenceScore: pattern.predictiveValue,
            sampleCount: 1,
            lastUpdated: new Date()
          }
        });
      }

      this.logger.info('交叉条件模式分析完成', { patternCount: patterns.length });
      return patterns;

    } catch (error) {
      this.logger.error('分析交叉条件模式失败', { error });
      return [];
    }
  }

  /**
   * 查找相似条件
   */
  async findSimilarConditions(
    currentCondition: string,
    threshold: number = 0.7
  ): Promise<string[]> {
    try {
      const currentKnowledge = await this.prisma.learningKnowledgeBase.findUnique({
        where: {
          knowledgeType_knowledgeKey: {
            knowledgeType: KnowledgeType.MARKET_CONDITION,
            knowledgeKey: currentCondition
          }
        }
      });

      if (!currentKnowledge) {
        return [];
      }

      const allConditions = await this.prisma.learningKnowledgeBase.findMany({
        where: {
          knowledgeType: KnowledgeType.MARKET_CONDITION,
          knowledgeKey: { not: currentCondition }
        }
      });

      const similarConditions: string[] = [];
      const currentData = currentKnowledge.knowledgeData as any;

      for (const condition of allConditions) {
        const similarity = this.calculateConditionSimilarity(
          currentData,
          condition.knowledgeData as any
        );

        if (similarity >= threshold) {
          similarConditions.push(condition.knowledgeKey);
        }
      }

      return similarConditions.sort((a, b) => {
        // 按相似度排序（这里简化处理）
        return a.localeCompare(b);
      });

    } catch (error) {
      this.logger.error('查找相似条件失败', { currentCondition, error });
      return [];
    }
  }

  /**
   * 获取历史表现
   */
  async getHistoricalPerformance(
    symbol: string,
    condition: string,
    days: number = 30
  ): Promise<HistoricalPerformance | null> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // 从短周期预测记录中查询历史表现
      const predictions = await this.prisma.shortCyclePredictions.findMany({
        where: {
          Symbols: { symbol },
          predictionTimestamp: { gte: startDate },
          isVerified: true
        },
        include: { Symbols: true }
      });

      if (predictions.length === 0) {
        return null;
      }

      // 过滤符合条件的预测
      const relevantPredictions = predictions.filter(p => {
        const marketContext = p.marketContext as any;
        return this.matchesCondition(marketContext, condition);
      });

      if (relevantPredictions.length === 0) {
        return null;
      }

      // 计算统计数据
      const accuracies = relevantPredictions
        .map(p => Number(p.accuracyScore))
        .filter(a => !isNaN(a));

      const avgAccuracy = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;

      // 识别最佳策略和风险因子
      const bestStrategies = this.identifyBestStrategiesFromPredictions(relevantPredictions);
      const riskFactors = this.identifyRiskFactorsFromPredictions(relevantPredictions);

      return {
        symbol,
        condition,
        accuracy: avgAccuracy,
        bestStrategies,
        riskFactors,
        sampleCount: relevantPredictions.length,
        timeRange: {
          start: startDate,
          end: new Date()
        }
      };

    } catch (error) {
      this.logger.error('获取历史表现失败', { symbol, condition, error });
      return null;
    }
  }

  /**
   * 识别学习模式
   */
  async identifyLearningPatterns(
    timeRange: { start: Date; end: Date }
  ): Promise<LearningPattern[]> {
    try {
      this.logger.info('识别学习模式', { timeRange });

      // 获取时间范围内的所有知识更新
      const knowledgeUpdates = await this.prisma.learningKnowledgeBase.findMany({
        where: {
          lastUpdated: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        },
        orderBy: { lastUpdated: 'asc' }
      });

      const patterns: LearningPattern[] = [];

      // 分析准确率改进模式
      const accuracyPattern = this.analyzeAccuracyPattern(knowledgeUpdates);
      if (accuracyPattern) {
        patterns.push(accuracyPattern);
      }

      // 分析参数优化模式
      const parameterPattern = await this.analyzeParameterOptimizationPattern(timeRange);
      if (parameterPattern) {
        patterns.push(parameterPattern);
      }

      // 分析市场适应模式
      const adaptationPattern = this.analyzeMarketAdaptationPattern(knowledgeUpdates);
      if (adaptationPattern) {
        patterns.push(adaptationPattern);
      }

      this.logger.info('学习模式识别完成', { patternCount: patterns.length });
      return patterns;

    } catch (error) {
      this.logger.error('识别学习模式失败', { timeRange, error });
      return [];
    }
  }

  /**
   * 获取知识库统计
   */
  async getKnowledgeStatistics(): Promise<KnowledgeStatistics> {
    try {
      const totalItems = await this.prisma.learningKnowledgeBase.count();

      const knowledgeByType = await this.prisma.learningKnowledgeBase.groupBy({
        by: ['knowledgeType'],
        _count: { id: true }
      });

      const avgConfidence = await this.prisma.learningKnowledgeBase.aggregate({
        _avg: { confidenceScore: true }
      });

      const totalSamples = await this.prisma.learningKnowledgeBase.aggregate({
        _sum: { sampleCount: true }
      });

      const lastUpdated = await this.prisma.learningKnowledgeBase.aggregate({
        _max: { lastUpdated: true }
      });

      // 获取表现最好的条件
      const topConditions = await this.prisma.learningKnowledgeBase.findMany({
        where: { knowledgeType: KnowledgeType.MARKET_CONDITION },
        orderBy: { confidenceScore: 'desc' },
        take: 5,
        select: { knowledgeKey: true }
      });

      // 统计低置信度项目
      const lowConfidenceCount = await this.prisma.learningKnowledgeBase.count({
        where: { confidenceScore: { lt: 0.5 } }
      });

      const typeMap: Record<KnowledgeType, number> = {} as any;
      knowledgeByType.forEach(item => {
        typeMap[item.knowledgeType as KnowledgeType] = item._count.id;
      });

      return {
        totalKnowledgeItems: totalItems,
        knowledgeByType: typeMap,
        averageConfidenceScore: Number(avgConfidence._avg.confidenceScore) || 0,
        totalSampleCount: totalSamples._sum.sampleCount || 0,
        lastUpdated: lastUpdated._max.lastUpdated || new Date(),
        topPerformingConditions: topConditions.map(c => c.knowledgeKey),
        lowConfidenceItems: lowConfidenceCount
      };

    } catch (error) {
      this.logger.error('获取知识库统计失败', { error });
      throw error;
    }
  }

  /**
   * 评估知识质量
   */
  async assessKnowledgeQuality(knowledgeKey: string): Promise<QualityAssessment> {
    try {
      const knowledge = await this.prisma.learningKnowledgeBase.findFirst({
        where: { knowledgeKey }
      });

      if (!knowledge) {
        throw new Error(`知识项不存在: ${knowledgeKey}`);
      }

      const now = new Date();
      const daysSinceUpdate = Math.floor(
        (now.getTime() - knowledge.lastUpdated.getTime()) / (1000 * 60 * 60 * 24)
      );

      // 计算质量分数
      let qualityScore = Number(knowledge.confidenceScore);

      // 样本充足性评估
      const sampleSufficiency = knowledge.sampleCount >= 50;
      if (!sampleSufficiency) {
        qualityScore *= 0.8;
      }

      // 数据新鲜度评估
      let dataFreshness: 'fresh' | 'stale' | 'outdated' = 'fresh';
      if (daysSinceUpdate > 7) {
        dataFreshness = 'stale';
        qualityScore *= 0.9;
      }
      if (daysSinceUpdate > 30) {
        dataFreshness = 'outdated';
        qualityScore *= 0.7;
      }

      // 置信度等级
      let confidenceLevel: 'high' | 'medium' | 'low' = 'high';
      if (qualityScore < 0.7) confidenceLevel = 'medium';
      if (qualityScore < 0.5) confidenceLevel = 'low';

      // 生成建议
      const recommendations: string[] = [];
      if (!sampleSufficiency) {
        recommendations.push('增加样本数量以提高知识可靠性');
      }
      if (dataFreshness !== 'fresh') {
        recommendations.push('更新知识数据以保持时效性');
      }
      if (qualityScore < 0.6) {
        recommendations.push('重新评估知识的准确性和相关性');
      }

      return {
        knowledgeKey,
        qualityScore,
        confidenceLevel,
        sampleSufficiency,
        dataFreshness,
        recommendations
      };

    } catch (error) {
      this.logger.error('评估知识质量失败', { knowledgeKey, error });
      throw error;
    }
  }

  /**
   * 清理过期知识
   */
  async cleanupOutdatedKnowledge(maxAge: number): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - maxAge);

      const result = await this.prisma.learningKnowledgeBase.deleteMany({
        where: {
          lastUpdated: { lt: cutoffDate },
          confidenceScore: { lt: 0.3 } // 只删除低置信度的过期知识
        }
      });

      this.invalidateCache();
      this.logger.info('清理过期知识完成', { deletedCount: result.count, maxAge });

      return result.count;
    } catch (error) {
      this.logger.error('清理过期知识失败', { maxAge, error });
      throw error;
    }
  }

  /**
   * 压缩知识库
   */
  async compressKnowledgeBase(): Promise<void> {
    try {
      this.logger.info('开始压缩知识库');

      // 合并相似的知识项
      await this.mergeSimilarKnowledge();

      // 清理重复数据
      await this.removeDuplicateKnowledge();

      // 优化知识表示
      await this.optimizeKnowledgeRepresentation();

      this.invalidateCache();
      this.logger.info('知识库压缩完成');

    } catch (error) {
      this.logger.error('压缩知识库失败', { error });
      throw error;
    }
  }

  /**
   * 搜索知识
   */
  async searchKnowledge(
    query: string,
    knowledgeType?: KnowledgeType
  ): Promise<KnowledgeItem[]> {
    try {
      const whereClause: any = {
        OR: [
          { knowledgeKey: { contains: query, mode: 'insensitive' } },
          // 这里可以添加更复杂的搜索逻辑
        ]
      };

      if (knowledgeType) {
        whereClause.knowledgeType = knowledgeType;
      }

      const results = await this.prisma.learningKnowledgeBase.findMany({
        where: whereClause,
        orderBy: { confidenceScore: 'desc' },
        take: 20
      });

      return results.map(r => ({
        id: r.id,
        knowledgeType: r.knowledgeType as KnowledgeType,
        knowledgeKey: r.knowledgeKey,
        knowledgeData: r.knowledgeData,
        confidenceScore: Number(r.confidenceScore),
        sampleCount: r.sampleCount,
        lastUpdated: r.lastUpdated
      }));

    } catch (error) {
      this.logger.error('搜索知识失败', { query, knowledgeType, error });
      return [];
    }
  }

  /**
   * 推荐相关知识
   */
  async recommendRelevantKnowledge(
    context: MarketContext
  ): Promise<KnowledgeItem[]> {
    try {
      // 基于市场上下文推荐相关知识
      const relevantKnowledge: KnowledgeItem[] = [];

      // 1. 获取当前市场条件的知识
      const conditionKnowledge = await this.searchKnowledge(
        context.condition,
        KnowledgeType.MARKET_CONDITION
      );
      relevantKnowledge.push(...conditionKnowledge);

      // 2. 获取相似条件的知识
      const similarConditions = await this.findSimilarConditions(context.condition);
      for (const condition of similarConditions.slice(0, 3)) {
        const similarKnowledge = await this.searchKnowledge(
          condition,
          KnowledgeType.MARKET_CONDITION
        );
        relevantKnowledge.push(...similarKnowledge);
      }

      // 3. 获取交叉模式知识
      const crossPatterns = await this.prisma.learningKnowledgeBase.findMany({
        where: {
          knowledgeType: KnowledgeType.CROSS_PATTERN,
          knowledgeKey: { contains: context.condition }
        },
        take: 5
      });

      relevantKnowledge.push(...crossPatterns.map(p => ({
        id: p.id,
        knowledgeType: p.knowledgeType as KnowledgeType,
        knowledgeKey: p.knowledgeKey,
        knowledgeData: p.knowledgeData,
        confidenceScore: Number(p.confidenceScore),
        sampleCount: p.sampleCount,
        lastUpdated: p.lastUpdated
      })));

      // 去重并按置信度排序
      const uniqueKnowledge = this.deduplicateKnowledge(relevantKnowledge);
      return uniqueKnowledge
        .sort((a, b) => b.confidenceScore - a.confidenceScore)
        .slice(0, 10);

    } catch (error) {
      this.logger.error('推荐相关知识失败', { context, error });
      return [];
    }
  }

  // 私有辅助方法

  private identifyBestStrategies(metrics: PerformanceMetric[]): string[] {
    // 根据性能指标识别最佳策略
    const strategies = new Map<string, number[]>();

    metrics.forEach(metric => {
      const strategy = metric.additionalData?.strategy || 'default';
      if (!strategies.has(strategy)) {
        strategies.set(strategy, []);
      }
      strategies.get(strategy)!.push(metric.accuracy);
    });

    const strategyPerformance = Array.from(strategies.entries())
      .map(([strategy, accuracies]) => ({
        strategy,
        avgAccuracy: accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length,
        count: accuracies.length
      }))
      .filter(s => s.count >= 3) // 至少3个样本
      .sort((a, b) => b.avgAccuracy - a.avgAccuracy);

    return strategyPerformance.slice(0, 3).map(s => s.strategy);
  }

  private identifyRiskFactors(metrics: PerformanceMetric[]): string[] {
    const riskFactors: string[] = [];

    // 识别低准确率的情况
    const lowAccuracyMetrics = metrics.filter(m => m.accuracy < 0.5);
    if (lowAccuracyMetrics.length > metrics.length * 0.2) {
      riskFactors.push('高错误率风险');
    }

    // 识别高波动性
    const accuracies = metrics.map(m => m.accuracy);
    const avgAccuracy = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - avgAccuracy, 2), 0) / accuracies.length;
    const stdDev = Math.sqrt(variance);

    if (stdDev > 0.2) {
      riskFactors.push('预测稳定性风险');
    }

    // 识别置信度校准问题
    const confidenceAccuracyDiff = metrics.map(m => Math.abs(m.confidence - m.accuracy));
    const avgDiff = confidenceAccuracyDiff.reduce((sum, diff) => sum + diff, 0) / confidenceAccuracyDiff.length;

    if (avgDiff > 0.15) {
      riskFactors.push('置信度校准风险');
    }

    return riskFactors;
  }

  private calculateOptimalParameters(metrics: PerformanceMetric[]): Record<string, number> {
    // 基于性能指标计算最优参数
    const parameters: Record<string, number> = {};

    // 计算最优置信度阈值
    const sortedByAccuracy = [...metrics].sort((a, b) => b.accuracy - a.accuracy);
    const topPerformers = sortedByAccuracy.slice(0, Math.ceil(metrics.length * 0.3));

    if (topPerformers.length > 0) {
      parameters.confidenceThreshold = topPerformers.reduce((sum, m) => sum + m.confidence, 0) / topPerformers.length;
    }

    // 计算其他参数...
    parameters.minSampleSize = Math.max(10, Math.ceil(metrics.length * 0.1));

    return parameters;
  }

  private calculatePerformanceDistribution(metrics: PerformanceMetric[]): any {
    const accuracies = metrics.map(m => m.accuracy);
    const bins = [0, 0.2, 0.4, 0.6, 0.8, 1.0];
    const distribution: Record<string, number> = {};

    for (let i = 0; i < bins.length - 1; i++) {
      const binKey = `${bins[i]}-${bins[i + 1]}`;
      distribution[binKey] = accuracies.filter(acc => acc >= bins[i] && acc < bins[i + 1]).length;
    }

    return distribution;
  }

  private analyzeTemporalPatterns(metrics: PerformanceMetric[]): any {
    // 分析时间模式
    const sortedMetrics = [...metrics].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    if (sortedMetrics.length < 10) {
      return { trend: 'insufficientData' };
    }

    const firstHalf = sortedMetrics.slice(0, Math.floor(sortedMetrics.length / 2));
    const secondHalf = sortedMetrics.slice(Math.floor(sortedMetrics.length / 2));

    const firstHalfAvg = firstHalf.reduce((sum, m) => sum + m.accuracy, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, m) => sum + m.accuracy, 0) / secondHalf.length;

    const improvement = secondHalfAvg - firstHalfAvg;

    return {
      trend: improvement > 0.05 ? 'improving' : improvement < -0.05 ? 'declining' : 'stable',
      improvementRate: improvement,
      firstHalfAccuracy: firstHalfAvg,
      secondHalfAccuracy: secondHalfAvg
    };
  }

  private calculateKnowledgeConfidence(metrics: PerformanceMetric[], avgAccuracy: number): number {
    // 基于样本数量和准确率计算知识置信度
    const sampleWeight = Math.min(metrics.length / 100, 1.0); // 样本数量权重
    const accuracyWeight = Math.max(avgAccuracy, 0.1); // 准确率权重
    const consistencyWeight = this.calculateConsistency(metrics); // 一致性权重

    return (sampleWeight * 0.3 + accuracyWeight * 0.5 + consistencyWeight * 0.2);
  }

  private calculateConsistency(metrics: PerformanceMetric[]): number {
    if (metrics.length < 2) return 0.5;

    const accuracies = metrics.map(m => m.accuracy);
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    const stdDev = Math.sqrt(variance);

    // 标准差越小，一致性越高
    return Math.max(0, 1 - stdDev * 2);
  }

  private generateInsightDescription(condition: string, data: any): string {
    const accuracy = (data.averageAccuracy * 100).toFixed(1);
    const sampleCount = data.sampleCount || 0;

    return `在${condition}市场条件下，基于${sampleCount}个样本的分析显示平均准确率为${accuracy}%`;
  }

  private analyzeTypePerformance(performance: TypePerformance): any {
    return {
      performanceRating: performance.averageAccuracy > 0.7 ? 'excellent' :
                         performance.averageAccuracy > 0.6 ? 'good' :
                         performance.averageAccuracy > 0.5 ? 'fair' : 'poor',
      improvementPotential: performance.trend === 'improving' ? 'high' :
                           performance.trend === 'stable' ? 'medium' : 'low',
      reliabilityScore: Math.min(performance.sampleCount / 50, 1.0)
    };
  }

  private calculateConditionCorrelation(data1: any, data2: any): number {
    // 简化的相关性计算
    const acc1 = data1.averageAccuracy || 0;
    const acc2 = data2.averageAccuracy || 0;

    // 这里可以实现更复杂的相关性计算
    return Math.abs(acc1 - acc2) < 0.1 ? 0.8 : 0.3;
  }

  private calculateStrategyCorrelation(condition1: any, condition2: any): number {
    const strategies1 = condition1.knowledgeData?.bestStrategies || [];
    const strategies2 = condition2.knowledgeData?.bestStrategies || [];

    const commonStrategies = strategies1.filter((s: string) => strategies2.includes(s));
    const totalStrategies = new Set([...strategies1, ...strategies2]).size;

    return totalStrategies > 0 ? commonStrategies.length / totalStrategies : 0;
  }

  private generatePatternDescription(condition1: any, condition2: any, correlation: number): string {
    const type = correlation > 0 ? '正相关' : '负相关';
    return `${condition1.knowledgeKey}与${condition2.knowledgeKey}之间存在${type}模式，相关系数为${correlation.toFixed(2)}`;
  }

  private generatePatternExamples(condition1: any, condition2: any): string[] {
    return [
      `当${condition1.knowledgeKey}时，${condition2.knowledgeKey}的表现类似`,
      `两种条件下的最佳策略有重叠`
    ];
  }

  private calculateConditionSimilarity(data1: any, data2: any): number {
    // 计算条件相似度
    const accSimilarity = 1 - Math.abs((data1.averageAccuracy || 0) - (data2.averageAccuracy || 0));
    const strategySimilarity = this.calculateStrategySimilarity(
      data1.bestStrategies || [],
      data2.bestStrategies || []
    );

    return (accSimilarity * 0.6 + strategySimilarity * 0.4);
  }

  private calculateStrategySimilarity(strategies1: string[], strategies2: string[]): number {
    if (strategies1.length === 0 && strategies2.length === 0) return 1;
    if (strategies1.length === 0 || strategies2.length === 0) return 0;

    const intersection = strategies1.filter(s => strategies2.includes(s));
    const union = new Set([...strategies1, ...strategies2]);

    return intersection.length / union.size;
  }

  private matchesCondition(marketContext: any, condition: string): boolean {
    // 简化的条件匹配逻辑
    return marketContext?.condition === condition ||
           marketContext?.trend === condition ||
           marketContext?.phase === condition;
  }

  private identifyBestStrategiesFromPredictions(_predictions: any[]): string[] {
    // 从预测记录中识别最佳策略
    return ['trendFollowing', 'meanReversion', 'momentumTrading'];
  }

  private identifyRiskFactorsFromPredictions(_predictions: any[]): string[] {
    // 从预测记录中识别风险因子
    return ['highVolatility', 'lowVolume', 'newsImpact'];
  }

  private analyzeAccuracyPattern(knowledgeUpdates: any[]): LearningPattern | null {
    if (knowledgeUpdates.length < 5) return null;

    const accuracies = knowledgeUpdates
      .map(k => k.knowledgeData?.averageAccuracy)
      .filter(acc => acc !== undefined);

    if (accuracies.length < 3) return null;

    const trend = accuracies[accuracies.length - 1] > accuracies[0] ? 'improving' : 'declining';

    return {
      patternType: 'accuracyImprovement',
      frequency: accuracies.length,
      accuracy: accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length,
      conditions: ['learningActive'],
      parameters: { improvementRate: (accuracies[accuracies.length - 1] - accuracies[0]) / accuracies.length },
      description: `准确率呈现${trend === 'improving' ? '上升' : '下降'}趋势`
    };
  }

  private async analyzeParameterOptimizationPattern(timeRange: { start: Date; end: Date }): Promise<LearningPattern | null> {
    try {
      const optimizations = await this.prisma.parameterAdjustmentHistory.findMany({
        where: {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      });

      if (optimizations.length < 3) return null;

      const successfulOptimizations = optimizations.filter(opt =>
        opt.actualImprovement && Number(opt.actualImprovement) > 0
      );

      return {
        patternType: 'parameterOptimization',
        frequency: optimizations.length,
        accuracy: successfulOptimizations.length / optimizations.length,
        conditions: ['parameterAdjustmentActive'],
        parameters: {
          successRate: successfulOptimizations.length / optimizations.length,
          avgImprovement: successfulOptimizations.reduce((sum, opt) =>
            sum + Number(opt.actualImprovement || 0), 0) / successfulOptimizations.length
        },
        description: `参数优化成功率为${(successfulOptimizations.length / optimizations.length * 100).toFixed(1)}%`
      };

    } catch (error) {
      this.logger.error('分析参数优化模式失败', { error });
      return null;
    }
  }

  private analyzeMarketAdaptationPattern(knowledgeUpdates: any[]): LearningPattern | null {
    const marketConditionUpdates = knowledgeUpdates.filter(k =>
      k.knowledgeType === KnowledgeType.MARKET_CONDITION
    );

    if (marketConditionUpdates.length < 3) return null;

    const adaptationSpeed = marketConditionUpdates.length /
      ((knowledgeUpdates[knowledgeUpdates.length - 1]?.lastUpdated?.getTime() -
        knowledgeUpdates[0]?.lastUpdated?.getTime()) / (1000 * 60 * 60 * 24));

    return {
      patternType: 'marketAdaptation',
      frequency: marketConditionUpdates.length,
      accuracy: 0.8, // 假设的适应准确率
      conditions: ['marketVolatility'],
      parameters: { adaptationSpeed: adaptationSpeed },
      description: `市场适应速度为每天${adaptationSpeed.toFixed(2)}次更新`
    };
  }

  private async mergeSimilarKnowledge(): Promise<void> {
    // 合并相似知识的实现
    this.logger.debug('合并相似知识');
  }

  private async removeDuplicateKnowledge(): Promise<void> {
    // 移除重复知识的实现
    this.logger.debug('移除重复知识');
  }

  private async optimizeKnowledgeRepresentation(): Promise<void> {
    // 优化知识表示的实现
    this.logger.debug('优化知识表示');
  }

  private deduplicateKnowledge(knowledge: KnowledgeItem[]): KnowledgeItem[] {
    const seen = new Set<string>();
    return knowledge.filter(item => {
      const key = `${item.knowledgeType}_${item.knowledgeKey}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  private invalidateCache(): void {
    this.knowledgeCache.clear();
    // this.cacheExpiry = new Date(0);
  }
}