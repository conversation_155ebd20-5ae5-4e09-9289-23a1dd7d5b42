/**
 * 持续学习引擎实现
 * 基于投资决策结果的反馈，持续改进AI系统的决策质量
 */

import { injectable, inject } from 'inversify';
import { LazyServiceIdentifer } from 'inversify';
import { Logger } from 'winston';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import {
  ContinuousLearningEngine,
  InvestmentDecision,
  InvestmentOutcome,
  LearningInsights,
  PerformanceData,
  StrategyOptimization,
  LearningProgress
} from '../../domain/services/reasoning-engine.interface';
import { LLMRouter, LLMRequest, ModelCapability } from '../../domain/services/llm-provider.interface';
import { FinancialKnowledgeGraph } from '../../domain/services/knowledge-graph.interface';

@injectable()
export class AIContinuousLearningEngine implements ContinuousLearningEngine {
  constructor(
    @inject(TYPES.Logger) private readonly logger: Logger,
    @inject(TYPES.Database) private readonly prisma: PrismaClient,
    @inject(new LazyServiceIdentifer(() => TYPES.AIReasoning.LLMRouter))
    private readonly llmRouter: LLMRouter,
    @inject(new LazyServiceIdentifer(() => TYPES.AIReasoning.FinancialKnowledgeGraph))
    private readonly knowledgeGraph: FinancialKnowledgeGraph
  ) {}

  /**
   * 从决策结果中学习
   */
  async learnFromOutcome(decision: InvestmentDecision, outcome: InvestmentOutcome): Promise<LearningInsights> {
    try {
      this.logger.info('开始从投资结果中学习', {
        decisionId: outcome.decisionId,
        actualReturn: outcome.actualReturn,
        riskRealized: outcome.riskRealized
      });

      // 1. 记录决策结果
      await this.recordDecisionOutcome(decision, outcome);

      // 2. AI分析决策质量
      const insights = await this.analyzeDecisionQuality(decision, outcome);

      // 3. 更新知识库
      await this.updateKnowledgeBase(insights);

      // 4. 优化推理参数
      await this.optimizeReasoningParameters(insights);

      this.logger.info('学习过程完成', {
        accuracyImprovements: insights.accuracyImprovements.length,
        riskRefinements: insights.riskAssessmentRefinements.length,
        strategyOptimizations: insights.strategyOptimizations.length
      });

      return insights;
    } catch (error) {
      this.logger.error('学习过程失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 更新知识库
   */
  async updateKnowledgeBase(insights: LearningInsights): Promise<void> {
    try {
      this.logger.info('更新知识库', { insights });

      // 更新准确性改进知识
      for (const improvement of insights.accuracyImprovements) {
        await this.knowledgeGraph.updateKnowledge({
          entity: 'accuracyImprovement',
          type: 'update',
          timestamp: new Date(),
          data: {
            description: improvement,
            type: 'improvement',
            category: 'accuracy',
            timestamp: new Date()
          },
          domain: 'decisionLearning',
          confidence: 0.8,
          source: 'continuousLearning'
        });
      }

      // 更新风险评估改进知识
      for (const refinement of insights.riskAssessmentRefinements) {
        await this.knowledgeGraph.updateKnowledge({
          entity: 'riskRefinement',
          type: 'update',
          timestamp: new Date(),
          data: {
            description: refinement,
            type: 'refinement',
            category: 'riskAssessment',
            timestamp: new Date()
          },
          domain: 'riskManagement',
          confidence: 0.85,
          source: 'continuousLearning'
        });
      }

      // 更新策略优化知识
      for (const optimization of insights.strategyOptimizations) {
        await this.knowledgeGraph.updateKnowledge({
          entity: 'strategyOptimization',
          type: 'update',
          timestamp: new Date(),
          data: {
            description: optimization,
            type: 'optimization',
            category: 'strategy',
            timestamp: new Date()
          },
          domain: 'tradingStrategy',
          confidence: 0.9,
          source: 'continuousLearning'
        });
      }

      this.logger.info('知识库更新完成');
    } catch (error) {
      this.logger.error('知识库更新失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 优化推理策略
   */
  async optimizeReasoningStrategy(performanceData: PerformanceData): Promise<StrategyOptimization> {
    try {
      this.logger.info('开始优化推理策略', { performanceData });

      const modelName = await this.llmRouter.selectOptimalModel({
        prompt: '',
        systemPrompt: '',
        reasoningFramework: {}
      }, {
        capabilities: [ModelCapability.REASONING]
      });

      // 获取对应的提供者
      const provider = await this.getProviderByModel(modelName);

      const request: LLMRequest = {
        prompt: `作为AI系统优化专家，分析以下性能数据并提供优化建议：

性能数据:
- 准确性趋势: ${performanceData.accuracyTrends.join(', ')}
- 响应时间: ${performanceData.responseTimes.join(', ')}ms
- 用户满意度: ${performanceData.userSatisfactionScores.join(', ')}
- 错误率: ${performanceData.errorRates.join(', ')}
- 改进领域: ${performanceData.improvementAreas.join(', ')}

请提供系统优化方案：
1. 需要优化的参数和具体数值
2. 预期的性能改进幅度
3. 详细的实施计划
4. 验证优化效果的标准

要求：
- 基于数据驱动的分析
- 提供可量化的改进目标
- 考虑系统稳定性和可靠性
- 制定渐进式优化策略

请按以下JSON格式返回：
{
  "optimizedParameters": {
    "confidenceThreshold": 0.75,
    "riskWeight": 0.3,
    "trendSensitivity": 0.6
  },
  "expectedImprovement": 0.15,
  "implementationPlan": ["步骤1", "步骤2"],
  "validationCriteria": ["标准1", "标准2"]
}`,
        systemPrompt: `你是一个专业的AI系统优化专家，专注于金融决策系统的性能改进。`,
        reasoningFramework: {
          "数据分析": "分析性能趋势和瓶颈",
          "参数优化": "确定最优参数配置",
          "风险评估": "评估优化的潜在风险",
          "实施规划": "制定详细的实施计划"
        }
      };

      const response = await provider.reason(request);
      const optimization = typeof response.content === 'string'
        ? JSON.parse(response.content)
        : response.content;

      // 记录优化策略
      await this.recordOptimizationStrategy(optimization);

      this.logger.info('推理策略优化完成', {
        expectedImprovement: optimization.expectedImprovement
      });

      return optimization;
    } catch (error) {
      this.logger.error('推理策略优化失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 获取学习进度
   */
  async getLearningProgress(): Promise<LearningProgress> {
    try {
      this.logger.info('获取学习进度');

      // 查询决策历史统计
      const totalDecisions = await this.prisma.decisionOutcomes.count();
      
      // 查询最近的准确性趋势
      const recentOutcomes = await this.prisma.decisionOutcomes.findMany({
        orderBy: { createdAt: 'desc' },
        take: 20,
        select: {
          actualReturn: true,
          expectedReturn: true,
          createdAt: true
        }
      });

      // 计算准确性趋势
      const accuracyTrend = recentOutcomes.map(outcome => {
        const accuracy = Math.abs(Number(outcome.actualReturn) - Number(outcome.expectedReturn)) < 0.1 ? 1 : 0;
        return accuracy;
      }).reverse();

      // 查询知识库大小
      const knowledgeBaseSize = await this.prisma.knowledgeEntities.count({
        where: { isActive: true }
      });

      // 计算改进率
      const improvementRate = this.calculateImprovementRate(accuracyTrend);

      // 查询最近的优化记录
      const recentOptimizations = await this.prisma.optimizationRecords.findMany({
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: { description: true }
      });

      const progress: LearningProgress = {
        totalDecisions: totalDecisions,
        accuracyTrend: accuracyTrend,
        knowledgeBaseSize: knowledgeBaseSize,
        improvementRate: improvementRate,
        recentOptimizations: recentOptimizations.map(r => r.description)
      };

      this.logger.info('学习进度获取完成', { progress });
      return progress;
    } catch (error) {
      this.logger.error('获取学习进度失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 记录决策结果
   */
  private async recordDecisionOutcome(decision: InvestmentDecision, outcome: InvestmentOutcome): Promise<void> {
    try {
      await this.prisma.decisionOutcomes.upsert({
        where: {
          decisionId: outcome.decisionId
        },
        update: {
          actualReturn: outcome.actualReturn,
          timeToTarget: outcome.timeToTarget,
          riskRealized: outcome.riskRealized,
          userSatisfaction: outcome.userSatisfaction,
          marketConditions: JSON.stringify(outcome.marketConditions)
        },
        create: {
          id: uuidv4(),
          decisionId: outcome.decisionId,
          Symbols: { connect: { id: 'default-symbol-id' } }, // 暂时使用备用值，需要从请求中获取
          action: decision.action,
          expectedReturn: decision.expectedReturn?.target ?? 0,
          actualReturn: outcome.actualReturn,
          timeToTarget: outcome.timeToTarget,
          riskRealized: outcome.riskRealized,
          userSatisfaction: outcome.userSatisfaction,
          marketConditions: JSON.stringify(outcome.marketConditions),
          confidence: decision.confidence,
          reasoning: JSON.stringify(decision.reasoning),
          createdAt: new Date()
        }
      });

      this.logger.info('决策结果记录完成', { decisionId: outcome.decisionId });
    } catch (error) {
      this.logger.error('记录决策结果失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * AI分析决策质量
   */
  private async analyzeDecisionQuality(decision: InvestmentDecision, outcome: InvestmentOutcome): Promise<LearningInsights> {
    try {
      const modelName = await this.llmRouter.selectOptimalModel({
        prompt: '',
        systemPrompt: '',
        reasoningFramework: {}
      }, {
        capabilities: [ModelCapability.REASONING]
      });

      // 获取对应的提供者
      const provider = await this.getProviderByModel(modelName);

      const request: LLMRequest = {
        prompt: `作为投资决策分析专家，深度分析以下投资决策的结果，提取学习要点：

原始决策:
- 行动: ${decision.action}
- 预期收益: ${decision.expectedReturn?.target ?? 'N/A'}%
- 风险控制: ${JSON.stringify(decision.riskControls)}
- 推理逻辑: ${decision.reasoning.join('; ')}
- AI置信度: ${decision.confidence}

实际结果:
- 实际收益: ${outcome.actualReturn}%
- 实现时间: ${outcome.timeToTarget}天
- 风险实现: ${outcome.riskRealized}%
- 用户满意度: ${outcome.userSatisfaction}/10
- 市场条件: ${JSON.stringify(outcome.marketConditions)}

请深度分析并提供学习洞察：

1. 准确性改进建议（accuracyImprovements）
   - 分析预测与实际的偏差
   - 识别预测模型的不足
   - 提供具体的改进方向

2. 风险评估优化（riskAssessmentRefinements）
   - 评估风险预测的准确性
   - 识别风险控制的有效性
   - 提供风险模型的改进建议

3. 策略优化建议（strategyOptimizations）
   - 分析执行策略的效果
   - 识别策略的优缺点
   - 提供策略改进方案

4. 偏见纠正（biasCorrections）
   - 识别决策中的认知偏见
   - 分析系统性错误
   - 提供偏见消除方法

5. 知识缺口（knowledgeGaps）
   - 识别知识盲区
   - 分析信息不足的领域
   - 提供知识补充建议

请按以下JSON格式返回：
{
  "accuracyImprovements": ["改进建议1", "改进建议2"],
  "riskAssessmentRefinements": ["风险优化1", "风险优化2"],
  "strategyOptimizations": ["策略优化1", "策略优化2"],
  "biasCorrections": ["偏见纠正1", "偏见纠正2"],
  "knowledgeGaps": ["知识缺口1", "知识缺口2"]
}`,
        systemPrompt: `你是一个专业的投资决策分析专家，专注于从投资结果中提取学习洞察。`,
        reasoningFramework: {
          "结果对比": "对比预期与实际结果的差异",
          "原因分析": "深入分析差异产生的根本原因",
          "模式识别": "识别决策中的系统性问题",
          "改进建议": "提供具体可行的改进方案"
        }
      };

      const response = await provider.reason(request);
      const insights = typeof response.content === 'string'
        ? JSON.parse(response.content)
        : response.content;

      this.logger.info('决策质量分析完成', { insights });
      return insights;
    } catch (error) {
      this.logger.error('决策质量分析失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 优化推理参数
   */
  private async optimizeReasoningParameters(insights: LearningInsights): Promise<void> {
    try {
      // 基于学习洞察调整系统参数
      const optimizations = [];

      // 如果准确性需要改进，调整置信度阈值
      if (insights.accuracyImprovements.length > 0) {
        optimizations.push({
          parameter: 'confidenceThreshold',
          adjustment: -0.05, // 降低置信度阈值，增加谨慎性
          reason: '提高预测准确性'
        });
      }

      // 如果风险评估需要改进，调整风险权重
      if (insights.riskAssessmentRefinements.length > 0) {
        optimizations.push({
          parameter: 'riskWeight',
          adjustment: 0.1, // 增加风险权重
          reason: '改善风险评估'
        });
      }

      // 记录参数优化
      for (const opt of optimizations) {
        await this.prisma.parameterOptimizations.create({
          data: {
            id: uuidv4(),
            parameter: opt.parameter,
            adjustment: opt.adjustment,
            reason: opt.reason,
            createdAt: new Date()
          }
        });
      }

      this.logger.info('推理参数优化完成', { optimizations });
    } catch (error) {
      this.logger.error('推理参数优化失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 记录优化策略
   */
  private async recordOptimizationStrategy(optimization: StrategyOptimization): Promise<void> {
    try {
      await this.prisma.optimizationRecords.create({
        data: {
          id: uuidv4(),
          description: `系统优化: 预期改进${(optimization.expectedImprovement * 100).toFixed(1)}%`,
          optimizedParameters: JSON.stringify(optimization.optimizedParameters),
          expectedImprovement: optimization.expectedImprovement,
          implementationPlan: JSON.stringify(optimization.implementationPlan),
          validationCriteria: JSON.stringify(optimization.validationCriteria),
          createdAt: new Date()
        }
      });

      this.logger.info('优化策略记录完成');
    } catch (error) {
      this.logger.error('优化策略记录失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 计算改进率
   */
  private calculateImprovementRate(accuracyTrend: number[]): number {
    if (accuracyTrend.length < 2) return 0;

    const recent = accuracyTrend.slice(-10); // 最近10次
    const earlier = accuracyTrend.slice(0, 10); // 较早10次

    if (earlier.length === 0) return 0;

    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, val) => sum + val, 0) / earlier.length;

    return recentAvg - earlierAvg;
  }

  /**
   * 根据模型名称获取对应的提供者
   */
  private async getProviderByModel(modelName: string): Promise<any> {
    try {
      // 使用LLM路由器获取可用提供者
      const availableProviders = await this.llmRouter.getAvailableProviders();

      if (availableProviders.length === 0) {
        throw new Error('没有可用的LLM提供者');
      }

      // 查找支持指定模型的提供者
      for (const provider of availableProviders) {
        if (provider.supportedModels.includes(modelName)) {
          this.logger.info('找到支持模型的提供者', {
            model: modelName,
            provider: provider.name
          });
          return provider;
        }
      }

      // 如果没有找到支持指定模型的提供者，使用第一个可用提供者
      const defaultProvider = availableProviders[0];
      this.logger.warn('未找到支持指定模型的提供者，使用默认提供者', {
        requestedModel: modelName,
        defaultProvider: defaultProvider.name,
        availableModels: defaultProvider.supportedModels
      });

      return defaultProvider;
    } catch (error) {
      this.logger.error('获取LLM提供者失败', {
        model: modelName,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`无法获取LLM提供者: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
