/**
 * 用户配置服务
 * 提供用户画像和配置管理的统一接口
 */

import { PrismaClient } from '@prisma/client';

// 在user-config上下文中定义自己的UserProfile类型，避免跨Context依赖
export interface UserProfile {
  id: string;
  userId: string;
  preferences: Record<string, any>;
  riskTolerance: 'LOW' | 'MEDIUM' | 'HIGH';
  tradingExperience: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  createdAt: Date;
  updatedAt: Date;
}

// 简单的日志接口
interface ILogger {
  debug(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  error(message: string, meta?: any): void;
}

export interface IUserConfigService {
  getUserProfile(userId: string): Promise<UserProfile | null>;
  createUserProfile(profile: Omit<UserProfile, 'createdAt' | 'updatedAt'>): Promise<UserProfile>;
  updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile>;
  deleteUserProfile(userId: string): Promise<void>;
  getUserPreferences(userId: string): Promise<Record<string, any>>;
  updateUserPreferences(userId: string, preferences: Record<string, any>): Promise<void>;
}

export class UserConfigService implements IUserConfigService {
  private readonly logger: ILogger;
  private readonly prisma: PrismaClient;

  constructor(prisma: PrismaClient, logger?: ILogger) {
    this.prisma = prisma;
    this.logger = logger || {
      debug: (message: string, meta?: any) => console.log(`[DEBUG] ${message}`, meta),
      info: (message: string, meta?: any) => console.log(`[INFO] ${message}`, meta),
      error: (message: string, meta?: any) => console.error(`[ERROR] ${message}`, meta)
    };
  }

  /**
   * 获取用户画像
   */
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      this.logger.debug('获取用户画像', { userId });

      // 从数据库获取用户画像
      const userProfileRecord = await this.prisma.userProfiles.findUnique({
        where: { userId }
      });

      if (userProfileRecord) {
        // 转换为接口格式返回
        return {
          id: userProfileRecord.id,
          userId: userProfileRecord.userId,
          preferences: {
            riskTolerance: userProfileRecord.riskTolerance,
            targetReturn: 0.15, // 默认值，字段不存在于schema中
            maxAcceptableDrawdown: 0.20, // 默认值，字段不存在于schema中
            investmentHorizon: userProfileRecord.investmentHorizon,
            preferredAssets: userProfileRecord.preferredAssets,
            tradingStyle: userProfileRecord.tradingStyle,
            maxPositionSize: userProfileRecord.maxPositionSize,
            enableStopLoss: true, // 默认值，字段不存在于schema中
            enableTakeProfit: true, // 默认值，字段不存在于schema中
            customThresholds: {} // 默认值，字段不存在于schema中
          },
          riskTolerance: userProfileRecord.riskTolerance as 'LOW' | 'MEDIUM' | 'HIGH',
          tradingExperience: 'INTERMEDIATE', // 默认值
          createdAt: userProfileRecord.createdAt,
          updatedAt: userProfileRecord.updatedAt
        };
      }

      // 如果数据库中没有用户画像，创建默认画像并保存
      this.logger.info('用户画像不存在，创建默认画像', { userId });

      const defaultProfile = await this.prisma.userProfiles.create({
        data: {
          userId,
          riskTolerance: 'BALANCED',
          // maxAcceptableDrawdown: 0.20, // 字段不存在于schema中
          investmentHorizon: 'MEDIUM',
          preferredAssets: ['BTC', 'ETH'],
          tradingStyle: 'MIXED',
          maxPositionSize: 0.10,
          // enableStopLoss: true, // 字段不存在于schema中
          // enableTakeProfit: true, // 字段不存在于schema中
          // customThresholds: { // 字段不存在于schema中
          //   trendStrengthThreshold: 0.6,
          //   riskScoreThreshold: 0.7,
          //   confidenceThreshold: 0.5
          // }
        }
      });

      // 转换为接口格式返回
      return {
        id: defaultProfile.id,
        userId: defaultProfile.userId,
        preferences: {
          riskTolerance: defaultProfile.riskTolerance,
          targetReturn: 0.15, // 默认值，字段不存在于schema中
          maxAcceptableDrawdown: 0.20, // 默认值，字段不存在于schema中
          investmentHorizon: defaultProfile.investmentHorizon,
          preferredAssets: defaultProfile.preferredAssets,
          tradingStyle: defaultProfile.tradingStyle,
          maxPositionSize: defaultProfile.maxPositionSize,
          enableStopLoss: true, // 默认值，字段不存在于schema中
          enableTakeProfit: true, // 默认值，字段不存在于schema中
          customThresholds: {} // 默认值，字段不存在于schema中
        },
        riskTolerance: defaultProfile.riskTolerance as 'LOW' | 'MEDIUM' | 'HIGH',
        tradingExperience: 'INTERMEDIATE', // 默认值
        createdAt: defaultProfile.createdAt,
        updatedAt: defaultProfile.updatedAt
      };
    } catch (error) {
      this.logger.error('获取用户画像失败', { userId, error });
      throw error;
    }
  }

  /**
   * 创建用户画像
   */
  async createUserProfile(profileData: any): Promise<UserProfile> {
    try {
      this.logger.info('创建用户画像', { userId: profileData.userId });

      // 保存到数据库
      const savedProfile = await this.prisma.userProfiles.create({
        data: {
          userId: profileData.userId,
          riskTolerance: profileData.riskTolerance || 'BALANCED',
          // targetReturn: profileData.targetReturn || 0.15, // 字段不存在于schema中
          // maxAcceptableDrawdown: profileData.maxAcceptableDrawdown || 0.20, // 字段不存在于schema中
          investmentHorizon: profileData.investmentHorizon || 'MEDIUM',
          preferredAssets: profileData.preferredAssets || ['BTC', 'ETH'],
          tradingStyle: profileData.tradingStyle || 'MIXED',
          maxPositionSize: profileData.maxPositionSize || 0.10,
          // enableStopLoss: profileData.enableStopLoss ?? true, // 字段不存在于schema中
          // enableTakeProfit: profileData.enableTakeProfit ?? true, // 字段不存在于schema中
          // customThresholds: profileData.customThresholds || {} // 字段不存在于schema中
        }
      });

      this.logger.info('用户画像创建成功', { userId: profileData.userId });
      
      // 转换为接口格式返回
      return {
        id: savedProfile.id,
        userId: savedProfile.userId,
        preferences: {
          riskTolerance: savedProfile.riskTolerance,
          targetReturn: 0.15, // 默认值，字段不存在于schema中
          maxAcceptableDrawdown: 0.20, // 默认值，字段不存在于schema中
          investmentHorizon: savedProfile.investmentHorizon,
          preferredAssets: savedProfile.preferredAssets,
          tradingStyle: savedProfile.tradingStyle,
          maxPositionSize: savedProfile.maxPositionSize,
          enableStopLoss: true, // 默认值，字段不存在于schema中
          enableTakeProfit: true, // 默认值，字段不存在于schema中
          customThresholds: {} // 默认值，字段不存在于schema中
        },
        riskTolerance: savedProfile.riskTolerance as 'LOW' | 'MEDIUM' | 'HIGH',
        tradingExperience: 'INTERMEDIATE', // 默认值
        createdAt: savedProfile.createdAt,
        updatedAt: savedProfile.updatedAt
      };
    } catch (error) {
      this.logger.error('创建用户画像失败', { userId: profileData.userId, error });
      throw error;
    }
  }

  /**
   * 更新用户画像
   */
  async updateUserProfile(userId: string, updates: any): Promise<UserProfile> {
    try {
      this.logger.info('更新用户画像', { userId, updates });

      // 检查用户画像是否存在
      const existingProfile = await this.prisma.userProfiles.findUnique({
        where: { userId }
      });
      
      if (!existingProfile) {
        throw new Error(`用户画像不存在: ${userId}`);
      }

      // 更新数据库中的用户画像
      const updatedProfile = await this.prisma.userProfiles.update({
        where: { userId },
        data: {
          riskTolerance: updates.riskTolerance || existingProfile.riskTolerance,
          // targetReturn: updates.targetReturn ?? existingProfile.targetReturn, // 字段不存在于schema中
          // maxAcceptableDrawdown: updates.maxAcceptableDrawdown ?? existingProfile.maxAcceptableDrawdown, // 字段不存在于schema中
          investmentHorizon: updates.investmentHorizon || existingProfile.investmentHorizon,
          preferredAssets: updates.preferredAssets || existingProfile.preferredAssets,
          tradingStyle: updates.tradingStyle || existingProfile.tradingStyle,
          maxPositionSize: updates.maxPositionSize ?? existingProfile.maxPositionSize,
          // enableStopLoss: updates.enableStopLoss ?? existingProfile.enableStopLoss, // 字段不存在于schema中
          // enableTakeProfit: updates.enableTakeProfit ?? existingProfile.enableTakeProfit, // 字段不存在于schema中
          // customThresholds: updates.customThresholds || existingProfile.customThresholds, // 字段不存在于schema中
          // version: { increment: 1 } // 字段不存在于schema中
        }
      });

      this.logger.info('用户画像更新成功', { userId });
      
      // 转换为接口格式返回
      return {
        id: updatedProfile.id,
        userId: updatedProfile.userId,
        preferences: {
          riskTolerance: updatedProfile.riskTolerance,
          targetReturn: 0.15, // 默认值，字段不存在于schema中
          maxAcceptableDrawdown: 0.20, // 默认值，字段不存在于schema中
          investmentHorizon: updatedProfile.investmentHorizon,
          preferredAssets: updatedProfile.preferredAssets,
          tradingStyle: updatedProfile.tradingStyle,
          maxPositionSize: updatedProfile.maxPositionSize,
          enableStopLoss: true, // 默认值，字段不存在于schema中
          enableTakeProfit: true, // 默认值，字段不存在于schema中
          customThresholds: {} // 默认值，字段不存在于schema中
        },
        riskTolerance: updatedProfile.riskTolerance as 'LOW' | 'MEDIUM' | 'HIGH',
        tradingExperience: 'INTERMEDIATE', // 默认值
        createdAt: updatedProfile.createdAt,
        updatedAt: updatedProfile.updatedAt
      };
    } catch (error) {
      this.logger.error('更新用户画像失败', { userId, error });
      throw error;
    }
  }

  /**
   * 删除用户画像
   */
  async deleteUserProfile(userId: string): Promise<void> {
    try {
      this.logger.info('删除用户画像', { userId });

      // 检查用户画像是否存在
      const existingProfile = await this.prisma.userProfiles.findUnique({
        where: { userId }
      });
      
      if (!existingProfile) {
        throw new Error(`用户画像不存在: ${userId}`);
      }

      // 从数据库删除用户画像
      await this.prisma.userProfiles.delete({
        where: { userId }
      });

      // 记录删除历史
      await this.prisma.userConfigHistory.create({
        data: {
          userId,
          // configType: 'PROFILE', // 字段不存在于schema中
          oldValue: JSON.stringify(existingProfile),
          newValue: null,
          // changeReason: '用户画像删除', // 字段不存在于schema中
          changedBy: userId
        }
      });

      this.logger.info('用户画像删除成功', { userId });
    } catch (error) {
      this.logger.error('删除用户画像失败', { userId, error });
      throw error;
    }
  }

  /**
   * 获取用户偏好设置
   */
  async getUserPreferences(userId: string): Promise<Record<string, any>> {
    try {
      this.logger.debug('获取用户偏好设置', { userId });

      // 从数据库获取用户偏好设置
      const userPreferencesRecord = await this.prisma.userPreferences.findUnique({
        where: { userId } as any
      });

      if (userPreferencesRecord) {
        // 转换为接口格式返回
        return {
          id: userPreferencesRecord.id,
          userId: userPreferencesRecord.userId,
          notifications: {}, // 默认值，字段不存在于schema中
          display: {}, // 默认值，字段不存在于schema中
          trading: {}, // 默认值，字段不存在于schema中
          customSettings: {}, // 默认值，字段不存在于schema中
          createdAt: userPreferencesRecord.createdAt,
          updatedAt: userPreferencesRecord.updatedAt
        };
      }

      // 如果数据库中没有用户偏好设置，创建默认设置并保存
      this.logger.info('用户偏好设置不存在，创建默认设置', { userId });

      const defaultPreferences = await this.prisma.userPreferences.create({
        data: {
          userId,
          // notificationSettings: { // 字段不存在于schema中
          //   email: true,
          //   push: true,
          //   sms: false,
          //   tradingSignals: true,
          //   riskAlerts: true,
          //   marketUpdates: false,
          //   systemNotifications: true
          // },
          // displaySettings: { // 字段不存在于schema中
          //   theme: 'dark',
          //   language: 'zh-CN',
          //   timezone: 'Asia/Shanghai',
          //   currency: 'USD',
          //   dateFormat: 'YYYY-MM-DD',
          //   numberFormat: 'en-US',
          //   chartType: 'candlestick',
          //   showAdvancedFeatures: false
          // },
          // tradingSettings: { // 字段不存在于schema中
          //   autoExecute: false,
          //   confirmBeforeOrder: true,
          //   defaultOrderType: 'LIMIT',
          //   slippageTolerance: 0.005,
          //   gasPrice: 'standard',
          //   enablePaperTrading: false,
          //   showPnlInRealTime: true
          // },
          // customSettings: {} // 字段不存在于schema中
        }
      });
      
      return {
        id: defaultPreferences.id,
        userId: defaultPreferences.userId,
        notifications: {}, // 默认值，字段不存在于schema中
        display: {}, // 默认值，字段不存在于schema中
        trading: {}, // 默认值，字段不存在于schema中
        customSettings: {}, // 默认值，字段不存在于schema中
        createdAt: defaultPreferences.createdAt,
        updatedAt: defaultPreferences.updatedAt
      };
    } catch (error) {
      this.logger.error('获取用户偏好设置失败', { userId, error });
      throw error;
    }
  }

  /**
   * 更新用户偏好设置
   */
  async updateUserPreferences(userId: string, preferences: Record<string, any>): Promise<void> {
    try {
      this.logger.info('更新用户偏好设置', { userId, preferences });

      // 获取现有偏好设置用于历史记录
      const existingPreferences = await this.prisma.userPreferences.findUnique({
        where: { userId }
      });

      // 更新或创建用户偏好设置
      const updatedPreferences = await this.prisma.userPreferences.upsert({
        where: { userId },
        update: {
          // notificationSettings: preferences.notificationSettings || existingPreferences?.notificationSettings, // 字段不存在于schema中
          // displaySettings: preferences.displaySettings || existingPreferences?.displaySettings, // 字段不存在于schema中
          // tradingSettings: preferences.tradingSettings || existingPreferences?.tradingSettings, // 字段不存在于schema中
          customSettings: preferences.customSettings || existingPreferences?.customSettings || {}
        },
        create: {
          userId,
          // notificationSettings: preferences.notificationSettings || { // 字段不存在于schema中
          //   email: true,
          //   push: true,
          //   sms: false,
          //   tradingSignals: true,
          //   riskAlerts: true,
          //   marketUpdates: false,
          //   systemNotifications: true
          // },
          displaySettings: preferences.displaySettings || {
            theme: 'dark',
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
            currency: 'USD',
            dateFormat: 'YYYY-MM-DD',
            numberFormat: 'en-US',
            chartType: 'candlestick',
            showAdvancedFeatures: false
          },
          tradingSettings: preferences.tradingSettings || {
            autoExecute: false,
            confirmBeforeOrder: true,
            defaultOrderType: 'LIMIT',
            slippageTolerance: 0.005,
            gasPrice: 'standard',
            enablePaperTrading: false,
            showPnlInRealTime: true
          },
          customSettings: preferences.customSettings || {}
        }
      });

      // 记录变更历史
      await this.prisma.userConfigHistory.create({
        data: {
          userId,
          configType: 'PREFERENCES',
          oldValue: existingPreferences,
          newValue: updatedPreferences,
          changeReason: '用户偏好设置更新',
          changedBy: userId
        }
      });

      this.logger.info('用户偏好设置更新成功', { userId });
    } catch (error) {
      this.logger.error('更新用户偏好设置失败', { userId, error });
      throw error;
    }
  }

  /**
   * 获取用户风险配置
   */
  async getUserRiskConfig(userId: string): Promise<{
    riskTolerance: string;
    maxDrawdown: number;
    positionSizing: number;
    stopLossEnabled: boolean;
    takeProfitEnabled: boolean;
  }> {
    const profile = await this.getUserProfile(userId);
    if (!profile) {
      throw new Error(`用户画像不存在: ${userId}`);
    }

    return {
      riskTolerance: profile.preferences.riskTolerance,
      maxDrawdown: profile.preferences.maxAcceptableDrawdown,
      positionSizing: profile.preferences.maxPositionSize,
      stopLossEnabled: profile.preferences.enableStopLoss,
      takeProfitEnabled: profile.preferences.enableTakeProfit
    };
  }

  /**
   * 获取用户交易偏好
   */
  async getUserTradingPreferences(userId: string): Promise<{
    tradingStyle: string;
    preferredAssets: string[];
    investmentHorizon: string;
    customThresholds?: any;
  }> {
    const profile = await this.getUserProfile(userId);
    if (!profile) {
      throw new Error(`用户画像不存在: ${userId}`);
    }

    return {
      tradingStyle: profile.preferences.tradingStyle,
      preferredAssets: profile.preferences.preferredAssets,
      investmentHorizon: profile.preferences.investmentHorizon,
      customThresholds: profile.preferences.customThresholds
    };
  }

  /**
   * 验证用户配置的完整性
   */
  async validateUserConfig(userId: string): Promise<{
    isValid: boolean;
    missingFields: string[];
    warnings: string[];
  }> {
    try {
      const profile = await this.getUserProfile(userId);
      const missingFields: string[] = [];
      const warnings: string[] = [];

      if (!profile) {
        return {
          isValid: false,
          missingFields: ['userProfile'],
          warnings: ['用户画像不存在，将使用默认配置']
        };
      }

      // 检查必要字段
      if (!profile.preferences.riskTolerance) missingFields.push('riskTolerance');
      if (profile.preferences.targetReturn === undefined) missingFields.push('targetReturn');
      if (profile.preferences.maxAcceptableDrawdown === undefined) missingFields.push('maxAcceptableDrawdown');

      // 检查合理性
      if (profile.preferences.targetReturn > 1) warnings.push('目标收益率过高，建议调整');
      if (profile.preferences.maxAcceptableDrawdown > 0.5) warnings.push('最大回撤设置过高，风险较大');

      return {
        isValid: missingFields.length === 0,
        missingFields,
        warnings
      };
    } catch (error) {
      this.logger.error('验证用户配置失败', { userId, error });
      throw error;
    }
  }

  /**
   * 根据日期获取用户配置
   * 🔥 修复虚假实现：从数据库获取真实的用户配置统计数据
   */
  async getUserConfigsByDate(date: Date): Promise<any[]> {
    try {
      // 获取指定日期的用户配置统计
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      // 🔥 从数据库获取真实的用户配置变更统计
      const configChanges = await this.prisma.userConfigHistory.findMany({
        where: {
          createdAt: {
            gte: startOfDay,
            lte: endOfDay
          }
        },
        select: {
          configType: true,
          createdAt: true,
          userId: true
        }
      });

      // 统计真实数据
      const totalChanges = configChanges.length;
      const uniqueUsers = new Set(configChanges.map(c => c.userId)).size;
      const configTypes = configChanges.reduce((acc, change) => {
        acc[change.configType] = (acc[change.configType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return [{
        date: date.toISOString().split('T')[0],
        totalConfigChanges: totalChanges,
        uniqueUsersModified: uniqueUsers,
        configTypeBreakdown: configTypes,
        averageChangesPerUser: uniqueUsers > 0 ? totalChanges / uniqueUsers : 0
      }];
    } catch (error) {
      this.logger.error('获取日期配置失败', { date, error });
      return [];
    }
  }

  /**
   * 根据提供者获取用户配置
   * 🔥 修复虚假实现：从数据库获取真实的提供者相关配置数据
   */
  async getUserConfigsByProvider(provider: string): Promise<any[]> {
    try {
      // 🔥 从数据库获取真实的提供者相关配置统计
      const userProfiles = await this.prisma.userProfiles.findMany({
        where: {
          preferences: {
            path: ['preferredDataProviders'],
            array_contains: provider
          }
        },
        select: {
          userId: true,
          preferences: true,
          createdAt: true,
          updatedAt: true
        }
      });

      // 获取相关的配置变更历史
      const configHistory = await this.prisma.userConfigHistory.findMany({
        where: {
          userId: {
            in: userProfiles.map(p => p.userId)
          },
          configType: 'PREFERENCES'
        },
        select: {
          userId: true,
          createdAt: true,
          changeReason: true
        }
      });

      // 统计真实数据
      const totalUsers = userProfiles.length;
      const recentChanges = configHistory.filter(
        h => h.createdAt > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 最近30天
      ).length;
      
      const avgConfigAge = userProfiles.length > 0 ? 
        userProfiles.reduce((sum, p) => sum + (Date.now() - p.updatedAt.getTime()), 0) / userProfiles.length / (24 * 60 * 60 * 1000) : 0;

      return [{
        provider,
        totalUsersUsingProvider: totalUsers,
        recentConfigChanges: recentChanges,
        averageConfigAgeInDays: Math.round(avgConfigAge * 100) / 100,
        lastUpdated: new Date().toISOString()
      }];
    } catch (error) {
      this.logger.error('获取提供者配置失败', { provider, error });
      return [];
    }
  }


}
