/**
 * Prisma用户偏好设置仓储实现 - 组合模式
 * 符合仓储模式合规性规范
 */

import { injectable, inject } from 'inversify';
import { PrismaClient } from '@prisma/client';
import { UserPreferences } from '../../domain/entities/user-preferences';
import { IUserPreferencesRepository } from '../../domain/repositories/user-preferences-repository.interface';
import { UniqueEntityId } from '../../../../shared/domain/entities/base-entity';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { IRepositoryBaseService } from '../../../../shared/domain/repositories/repository-base.interface';
import { DataMappingService } from '../../../../shared/infrastructure/database/unified-data-mapper';

@injectable()
export class PrismaUserPreferencesRepository implements IUserPreferencesRepository {
  constructor(
    @inject(TYPES.Database) private readonly prisma: PrismaClient,
    @inject(TYPES.Shared.RepositoryBaseService) private readonly baseService: IRepositoryBaseService<UserPreferences>,
    @inject(TYPES.Shared.DataMappingService) private readonly dataMapper: DataMappingService
  ) {}

  async findByUserId(userId: string): Promise<UserPreferences | null> {
    return this.baseService.executeWithMonitoring('findByUserId', async () => {
      const record = await this.prisma.userPreferences.findUnique({
        where: { userId }
      });

      return record ? this.toDomain(record) : null;
    });
  }

  async save(preferences: UserPreferences): Promise<void> {
    return this.baseService.executeWithMonitoring('save', async () => {
      const data = this.toPersistence(preferences);

      await this.prisma.userPreferences.upsert({
        where: { userId: preferences.userId },
        update: data,
        create: data
      });
    });
  }

  async delete(userId: string): Promise<void> {
    return this.baseService.executeWithMonitoring('delete', async () => {
      await this.prisma.userPreferences.delete({
        where: { userId }
      });
    });
  }

  async exists(userId: string): Promise<boolean> {
    return this.baseService.executeWithMonitoring('exists', async () => {
      const count = await this.prisma.userPreferences.count({
        where: { userId }
      });
      return count > 0;
    });
  }

  async findByUserIds(userIds: string[]): Promise<UserPreferences[]> {
    return this.baseService.executeWithMonitoring('findByUserIds', async () => {
      const records = await this.prisma.userPreferences.findMany({
        where: { userId: { in: userIds } }
      });

      return records.map(record => this.toDomain(record));
    });
  }

  async findUsersWithNotificationEnabled(notificationType: string): Promise<string[]> {
    return this.baseService.executeWithMonitoring('findUsersWithNotificationEnabled', async () => {
      // 这里需要根据具体的数据库结构来实现
      // 假设notificationSettings是JSON字段
      const records = await this.prisma.userPreferences.findMany({
        where: {
          notificationSettings: {
            path: [notificationType],
            equals: true
          }
        },
        select: { userId: true }
      });

      return records.map(record => record.userId);
    });
  }

  async getStatistics(): Promise<{
    totalUsers: number;
    themeDistribution: Record<string, number>;
    languageDistribution: Record<string, number>;
    notificationStats: Record<string, number>;
  }> {
    return this.baseService.executeWithMonitoring('getStatistics', async () => {
      const totalUsers = await this.prisma.userPreferences.count();

      // 由于Prisma对JSON字段的聚合查询支持有限，这里使用原始查询
      // 注意：PostgreSQL使用双引号包围表名，并且JSON操作符不同
      const themeStats = await this.prisma.$queryRaw`
        SELECT
          ("displaySettings"->>'theme') as theme,
          COUNT(*) as count
        FROM "UserPreferences"
        WHERE "displaySettings" IS NOT NULL
        GROUP BY ("displaySettings"->>'theme')
      ` as Array<{ theme: string; count: bigint }>;

      const languageStats = await this.prisma.$queryRaw`
        SELECT
          ("displaySettings"->>'language') as language,
          COUNT(*) as count
        FROM "UserPreferences"
        WHERE "displaySettings" IS NOT NULL
        GROUP BY ("displaySettings"->>'language')
      ` as Array<{ language: string; count: bigint }>;

      const notificationStats = await this.prisma.$queryRaw`
        SELECT
          'email' as type,
          SUM(CASE WHEN ("notificationSettings"->>'email')::boolean = true THEN 1 ELSE 0 END) as count
        FROM "UserPreferences"
        WHERE "notificationSettings" IS NOT NULL
        UNION ALL
        SELECT
          'push' as type,
          SUM(CASE WHEN ("notificationSettings"->>'push')::boolean = true THEN 1 ELSE 0 END) as count
        FROM "UserPreferences"
        WHERE "notificationSettings" IS NOT NULL
        UNION ALL
        SELECT
          'tradingSignals' as type,
          SUM(CASE WHEN ("notificationSettings"->>'tradingSignals')::boolean = true THEN 1 ELSE 0 END) as count
        FROM "UserPreferences"
        WHERE "notificationSettings" IS NOT NULL
      ` as Array<{ type: string; count: bigint }>;

      return {
        totalUsers,
        themeDistribution: themeStats.reduce((acc, item) => {
          acc[item.theme] = Number(item.count);
          return acc;
        }, {} as Record<string, number>),
        languageDistribution: languageStats.reduce((acc, item) => {
          acc[item.language] = Number(item.count);
          return acc;
        }, {} as Record<string, number>),
        notificationStats: notificationStats.reduce((acc, item) => {
          acc[item.type] = Number(item.count);
          return acc;
        }, {} as Record<string, number>)
      };
    });
  }

  private toDomain(record: any): UserPreferences {
    return UserPreferences.create({
      userId: record.userId,
      notificationSettings: record.notificationSettings || {
        email: true,
        push: true,
        sms: false,
        tradingSignals: true,
        riskAlerts: true,
        marketUpdates: false,
        systemNotifications: true
      },
      displaySettings: record.displaySettings || {
        theme: 'dark',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        currency: 'USD',
        dateFormat: 'YYYY-MM-DD',
        numberFormat: 'en-US',
        chartType: 'candlestick',
        showAdvancedFeatures: false
      },
      tradingSettings: record.tradingSettings || {
        autoExecute: false,
        confirmBeforeOrder: true,
        defaultOrderType: 'LIMIT',
        slippageTolerance: 0.005,
        gasPrice: 'standard',
        enablePaperTrading: false,
        showPnlInRealTime: true
      },
      customSettings: record.customSettings || {}
    }, new UniqueEntityId(record.id));
  }

  private toPersistence(preferences: UserPreferences): any {
    return {
      userId: preferences.userId,
      notificationSettings: preferences.notificationSettings,
      displaySettings: preferences.displaySettings,
      tradingSettings: preferences.tradingSettings,
      customSettings: preferences.customSettings,
      createdAt: preferences.createdAt,
      updatedAt: preferences.updatedAt
    };
  }
}
