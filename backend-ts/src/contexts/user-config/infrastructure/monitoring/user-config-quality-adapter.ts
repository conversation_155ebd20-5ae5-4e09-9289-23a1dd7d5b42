/**
 * User-Config系统数据质量适配器
 * 将user-config系统集成到统一数据质量监控中
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { SecureIdGenerator } from '../../../../shared/infrastructure/utils/secure-id-generator';

export interface UserConfigQualityMetrics {
  userProfilesQuality: {
    totalProfiles: number;
    validProfiles: number;
    completenessScore: number;
    consistencyScore: number;
    issues: string[];
  };
  userPreferencesQuality: {
    totalPreferences: number;
    validPreferences: number;
    completenessScore: number;
    consistencyScore: number;
    issues: string[];
  };
  overallQuality: number;
  recommendations: string[];
}

export interface UserConfigDataEvent {
  id: string;
  source: 'user-config';
  type: 'userProfile' | 'userPreferences' | 'riskTolerance';
  timestamp: Date;
  data: {
    userId: string;
    operation: 'CREATE' | 'UPDATE' | 'DELETE';
    payload: any;
  };
  metadata: {
    version: number;
    checksum?: string;
    validationStatus: 'VALID' | 'INVALID' | 'WARNING';
  };
}

@injectable()
export class UserConfigQualityAdapter {
  constructor(
    @inject(TYPES.Logger)
    private readonly logger: IBasicLogger,
    @inject(TYPES.UserConfig.UserProfileApplicationService)
    private readonly userProfileService: any, // 使用any避免直接依赖Application层类型
    @inject(TYPES.UserConfig.UserPreferencesApplicationService)
    private readonly userPreferencesService: any // 使用any避免直接依赖Application层类型
  ) {}

  /**
   * 生成用户配置数据事件
   */
  createDataEvent(
    type: 'userProfile' | 'userPreferences' | 'riskTolerance',
    userId: string,
    operation: 'CREATE' | 'UPDATE' | 'DELETE',
    payload: any
  ): UserConfigDataEvent {
    return {
      id: this.generateEventId(),
      source: 'user-config',
      type,
      timestamp: new Date(),
      data: {
        userId,
        operation,
        payload
      },
      metadata: {
        version: 1,
        checksum: this.calculateChecksum(payload),
        validationStatus: this.validatePayload(type, payload)
      }
    };
  }

  /**
   * 评估用户配置数据质量
   */
  async assessUserConfigQuality(): Promise<UserConfigQualityMetrics> {
    this.logger.info('开始评估用户配置数据质量');

    try {
      // 评估用户画像质量
      const userProfilesQuality = await this.assessUserProfilesQuality();
      
      // 评估用户偏好质量
      const userPreferencesQuality = await this.assessUserPreferencesQuality();

      // 计算总体质量分数
      const overallQuality = (
        userProfilesQuality.completenessScore * 0.4 +
        userProfilesQuality.consistencyScore * 0.3 +
        userPreferencesQuality.completenessScore * 0.2 +
        userPreferencesQuality.consistencyScore * 0.1
      );

      // 生成建议
      const recommendations = this.generateQualityRecommendations(
        userProfilesQuality,
        userPreferencesQuality,
        overallQuality
      );

      const metrics: UserConfigQualityMetrics = {
        userProfilesQuality,
        userPreferencesQuality,
        overallQuality,
        recommendations
      };

      this.logger.info('用户配置数据质量评估完成', {
        overallQuality,
        profilesValid: userProfilesQuality.validProfiles,
        preferencesValid: userPreferencesQuality.validPreferences
      });

      return metrics;

    } catch (error) {
      this.logger.error('用户配置数据质量评估失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 评估用户画像质量
   */
  private async assessUserProfilesQuality(): Promise<{
    totalProfiles: number;
    validProfiles: number;
    completenessScore: number;
    consistencyScore: number;
    issues: string[];
  }> {
    // 获取统计信息
    const statistics = await this.userProfileService.getStatistics();
    const totalProfiles = statistics.totalUsers;
    
    let validProfiles = 0;
    let completenessScore = 0;
    let consistencyScore = 0;
    const issues: string[] = [];

    // 这里应该实现具体的质量检查逻辑
    // 由于没有批量获取用户画像的方法，我们基于统计信息进行评估
    
    // 基于风险容忍度分布评估一致性
    const riskDistribution = statistics.riskToleranceDistribution;
    const totalRiskEntries = Object.values(riskDistribution).reduce((sum, count) => sum + (count as number), 0);
    
    if (totalRiskEntries === totalProfiles) {
      consistencyScore = 0.95; // 高一致性
      validProfiles = totalProfiles;
    } else {
      consistencyScore = Number(totalRiskEntries) / Number(totalProfiles);
      validProfiles = totalRiskEntries;
      issues.push(`${totalProfiles - totalRiskEntries} 个用户画像缺少风险容忍度设置`);
    }

    // 评估完整性（基于分布的均匀性）
    const distributionValues = Object.values(riskDistribution);
    const avgCount = distributionValues.reduce((sum, count) => sum + (count as number), 0) / distributionValues.length;
    const variance = distributionValues.reduce((sum, count) => sum + Math.pow((count as number) - avgCount, 2), 0) / distributionValues.length;
    completenessScore = Math.max(0, 1 - (variance / (avgCount * avgCount))); // 标准化方差

    if (completenessScore < 0.8) {
      issues.push('用户画像数据分布不均匀，可能存在数据质量问题');
    }

    return {
      totalProfiles,
      validProfiles,
      completenessScore,
      consistencyScore,
      issues
    };
  }

  /**
   * 评估用户偏好质量
   */
  private async assessUserPreferencesQuality(): Promise<{
    totalPreferences: number;
    validPreferences: number;
    completenessScore: number;
    consistencyScore: number;
    issues: string[];
  }> {
    // 获取统计信息
    const statistics = await this.userPreferencesService.getStatistics();
    const totalPreferences = statistics.totalUsers;
    
    let validPreferences = totalPreferences; // 假设大部分是有效的
    let completenessScore = 0.9; // 基础分数
    let consistencyScore = 0.9; // 基础分数
    const issues: string[] = [];

    // 基于主题分布评估
    const themeDistribution = statistics.themeDistribution;
    const totalThemeEntries = Object.values(themeDistribution).reduce((sum, count) => sum + count, 0);
    
    if (totalThemeEntries < totalPreferences) {
      const missingThemes = totalPreferences - totalThemeEntries;
      issues.push(`${missingThemes} 个用户偏好缺少主题设置`);
      completenessScore *= (totalThemeEntries / totalPreferences);
    }

    // 基于语言分布评估
    const languageDistribution = statistics.languageDistribution;
    const totalLanguageEntries = Object.values(languageDistribution).reduce((sum, count) => sum + count, 0);
    
    if (totalLanguageEntries < totalPreferences) {
      const missingLanguages = totalPreferences - totalLanguageEntries;
      issues.push(`${missingLanguages} 个用户偏好缺少语言设置`);
      completenessScore *= (totalLanguageEntries / totalPreferences);
    }

    // 基于通知统计评估
    const notificationStats = statistics.notificationStats;
    const avgNotificationEnabled = Object.values(notificationStats).reduce((sum, count) => sum + count, 0) / Object.keys(notificationStats).length;
    
    if (avgNotificationEnabled < totalPreferences * 0.5) {
      issues.push('通知设置启用率较低，可能影响用户体验');
      consistencyScore *= 0.9;
    }

    return {
      totalPreferences,
      validPreferences,
      completenessScore,
      consistencyScore,
      issues
    };
  }

  /**
   * 生成质量建议
   */
  private generateQualityRecommendations(
    profilesQuality: any,
    preferencesQuality: any,
    overallQuality: number
  ): string[] {
    const recommendations: string[] = [];

    if (overallQuality < 0.8) {
      recommendations.push('用户配置数据质量需要改进，建议进行数据清理和验证');
    }

    if (profilesQuality.completenessScore < 0.9) {
      recommendations.push('用户画像数据完整性不足，建议引导用户完善个人信息');
    }

    if (profilesQuality.consistencyScore < 0.9) {
      recommendations.push('用户画像数据一致性问题，建议检查数据验证规则');
    }

    if (preferencesQuality.completenessScore < 0.9) {
      recommendations.push('用户偏好设置不完整，建议优化用户引导流程');
    }

    if (profilesQuality.issues.length > 0 || preferencesQuality.issues.length > 0) {
      recommendations.push('发现数据质量问题，建议定期进行数据质量检查');
    }

    if (recommendations.length === 0) {
      recommendations.push('用户配置数据质量良好，继续保持');
    }

    return recommendations;
  }

  /**
   * 验证载荷数据
   */
  private validatePayload(type: string, payload: any): 'VALID' | 'INVALID' | 'WARNING' {
    if (!payload) {
      return 'INVALID';
    }

    switch (type) {
      case 'userProfile':
        return this.validateUserProfile(payload);
      case 'userPreferences':
        return this.validateUserPreferences(payload);
      case 'riskTolerance':
        return this.validateRiskTolerance(payload);
      default:
        return 'WARNING';
    }
  }

  /**
   * 验证用户画像
   */
  private validateUserProfile(payload: any): 'VALID' | 'INVALID' | 'WARNING' {
    const required = ['userId', 'riskTolerance', 'targetReturn', 'maxAcceptableDrawdown'];
    const missing = required.filter(field => !payload[field]);
    
    if (missing.length > 0) {
      return 'INVALID';
    }

    if (payload.targetReturn < 0 || payload.targetReturn > 1) {
      return 'WARNING';
    }

    if (payload.maxAcceptableDrawdown < 0 || payload.maxAcceptableDrawdown > 1) {
      return 'WARNING';
    }

    return 'VALID';
  }

  /**
   * 验证用户偏好
   */
  private validateUserPreferences(payload: any): 'VALID' | 'INVALID' | 'WARNING' {
    if (!payload.userId) {
      return 'INVALID';
    }

    if (!payload.notificationSettings || !payload.displaySettings || !payload.tradingSettings) {
      return 'WARNING';
    }

    return 'VALID';
  }

  /**
   * 验证风险容忍度
   */
  private validateRiskTolerance(payload: any): 'VALID' | 'INVALID' | 'WARNING' {
    const validValues = ['CONSERVATIVE', 'BALANCED', 'AGGRESSIVE'];
    
    if (!payload.riskTolerance || !validValues.includes(payload.riskTolerance)) {
      return 'INVALID';
    }

    return 'VALID';
  }

  /**
   * 计算校验和
   */
  private calculateChecksum(payload: any): string {
    const str = JSON.stringify(payload);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return SecureIdGenerator.generateUserConfigId();
  }
}
