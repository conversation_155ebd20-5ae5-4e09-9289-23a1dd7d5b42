/**
 * 均值回归策略实现
 * 基于价格偏离均值的程度，在超买超卖时进行反向交易
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import {
  ITradingStrategy,
  TrendAnalysisInput,
  RiskManagementInput,
  StrategyExecutionContext,
  StrategyExecutionResult
} from '../../domain/interfaces/trading-strategy.interface';
import { UserProfileEntity, UserProfile } from '../../domain/entities/user-profile';
import { TradingSignalEntity } from '../../domain/entities/trading-signal';
import { IUnifiedTechnicalIndicatorCalculator } from '../../../../shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';
import { UNIFIED_TECHNICAL_INDICATOR_TYPES } from '../../../../shared/infrastructure/technical-indicators/types';

/**
 * 均值回归策略配置
 */
interface MeanReversionConfig {
  oversoldThreshold: number; // 超卖阈值
  overboughtThreshold: number; // 超买阈值
  minDeviationFromMean: number; // 最小偏离均值程度
  maxTrendStrength: number; // 最大趋势强度（避免在强趋势中逆势）
  minConfidenceLevel: number; // 最小信心度阈值
  maxRiskScore: number; // 最大风险评分阈值
  stopLossPercentage: number; // 止损百分比
  takeProfitRatio: number; // 止盈比例
  maxPositionHoldTime: number; // 最大持仓时间（小时）
  meanReversionPeriod: number; // 均值回归周期
}

@injectable()
export class MeanReversionStrategy implements ITradingStrategy {
  public readonly name = 'Mean Reversion Strategy';
  public readonly version = '1.0.0';
  public readonly description = '基于均值回归理论的交易策略，在价格极端偏离时进行反向交易';
  public readonly suitableMarketConditions = [
    'TREND_SIDEWAYS',
    'TREND_WEAK_BULL',
    'TREND_WEAK_BEAR',
    'VOLATILITY_LOW',
    'VOLATILITY_MEDIUM',
    'LIQUIDITY_HIGH',
    'LIQUIDITY_MEDIUM',
    'RANGE_BOUND_MARKET'
  ];
  public readonly riskLevel = 'MEDIUM';

  private readonly defaultConfig: MeanReversionConfig = {
    oversoldThreshold: 0.3, // RSI < 30
    overboughtThreshold: 0.7, // RSI > 70
    minDeviationFromMean: 0.02, // 2%偏离
    maxTrendStrength: 0.6, // 避免在强趋势中交易
    minConfidenceLevel: 0.6,
    maxRiskScore: 0.7,
    stopLossPercentage: 0.025, // 2.5%
    takeProfitRatio: 2.0, // 2:1 风险收益比
    maxPositionHoldTime: 12, // 12小时
    meanReversionPeriod: 20 // 20周期均值
  };

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(UNIFIED_TECHNICAL_INDICATOR_TYPES.UnifiedTechnicalIndicatorCalculator)
    private readonly technicalCalculator: IUnifiedTechnicalIndicatorCalculator
  ) {}

  /**
   * 执行策略
   */
  async execute(
    trendAnalysis: TrendAnalysisInput,
    riskManagement: RiskManagementInput,
    userProfile: UserProfile,
    context: StrategyExecutionContext
  ): Promise<StrategyExecutionResult> {
    this.logger.info('执行均值回归策略', {
      symbol: context.symbol,
      userId: userProfile.userId,
      currentPrice: context.currentPrice,
      trendDirection: trendAnalysis.trendDirection,
      trendScore: trendAnalysis.trendScore,
      riskScore: riskManagement.riskScore
    });

    try {
      // 获取策略配置
      const config = this.getConfiguration(userProfile);

      // 验证输入数据
      const validationResult = this.validateInputs(trendAnalysis, riskManagement, userProfile);
      if (!validationResult.isValid) {
        return this.createFailureResult(validationResult.errors.join('; '), context);
      }

      // 分析均值回归信号
      const reversionSignal = await this.analyzeMeanReversionSignal(context, trendAnalysis, config);
      if (!reversionSignal.isValid) {
        return this.createNoSignalResult(reversionSignal.reason, context);
      }

      // 评估风险
      const riskAssessment = this.assessRisk(riskManagement, trendAnalysis, config);
      if (!riskAssessment.acceptable) {
        return this.createNoSignalResult(riskAssessment.reason, context);
      }

      // 生成交易信号
      const signal = this.generateTradingSignal(
        context,
        userProfile as any,
        trendAnalysis,
        riskManagement,
        reversionSignal,
        config
      );

      // 计算预期表现
      const expectedPerformance = this.calculateExpectedPerformance(
        trendAnalysis,
        riskManagement,
        userProfile
      );

      return {
        signal,
        confidence: this.calculateOverallConfidence(reversionSignal, riskAssessment),
        reasoning: this.generateReasoning(reversionSignal, riskAssessment, config),
        riskAssessment: {
          level: riskManagement.riskScore > 0.7 ? 'HIGH' : riskManagement.riskScore > 0.4 ? 'MEDIUM' : 'LOW',
          factors: [`风险评分: ${riskManagement.riskScore}`, `趋势强度: ${trendAnalysis.trendScore}`],
          mitigationActions: ['设置止损', '控制仓位']
        },
        executionRecommendations: {
          positionSize: 0.1,
          entryPrice: context.currentPrice,
          timeHorizon: 'SHORT'
        },
        metadata: {
          strategyName: this.name,
          strategyVersion: this.version,
          processingTime: Date.now() - Date.now(),
          dataQuality: 85
          // config and reversionSignal removed as not part of metadata interface
          // reversionSignal,
          // riskAssessment
        }
      };
    } catch (error) {
      this.logger.error('均值回归策略执行失败', { error: error.message, context });
      return this.createFailureResult(`策略执行异常: ${error.message}`, context);
    }
  }

  /**
   * 检查策略是否适用
   */
  isApplicable(
    context: StrategyExecutionContext,
    userProfile: UserProfile
  ): boolean {
    // 检查用户交易风格
    if (userProfile.tradingStyle !== 'SWING_TRADING' && userProfile.tradingStyle !== 'MIXED') {
      return false;
    }

    // 检查市场条件
    const { trend, volatility, liquidity } = context.marketConditions;
    
    // 均值回归策略不适合强趋势市场
    if (trend === 'STRONG_BULL' || trend === 'STRONG_BEAR') {
      return false;
    }

    // 需要足够的流动性
    if (liquidity === 'LOW') {
      return false;
    }

    // 极高波动性可能不适合
    if (volatility === 'HIGH') {
      return false;
    }

    return true;
  }

  /**
   * 获取策略配置
   */
  getConfiguration(userProfile?: UserProfile): MeanReversionConfig {
    const config = { ...this.defaultConfig };

    if (userProfile) {
      // 根据用户风险偏好调整配置
      switch (userProfile.riskTolerance) {
        case 'CONSERVATIVE':
          config.oversoldThreshold = 0.25; // 更严格的超卖条件
          config.overboughtThreshold = 0.75; // 更严格的超买条件
          config.minDeviationFromMean = 0.025; // 更大的偏离要求
          config.maxTrendStrength = 0.5; // 更严格的趋势限制
          config.minConfidenceLevel = 0.7;
          config.maxRiskScore = 0.6;
          config.stopLossPercentage = 0.02; // 2%
          config.takeProfitRatio = 2.5; // 更保守的风险收益比
          break;
        case 'AGGRESSIVE':
          config.oversoldThreshold = 0.35; // 较宽松的超卖条件
          config.overboughtThreshold = 0.65; // 较宽松的超买条件
          config.minDeviationFromMean = 0.015; // 较小的偏离要求
          config.maxTrendStrength = 0.7; // 较宽松的趋势限制
          config.minConfidenceLevel = 0.5;
          config.maxRiskScore = 0.8;
          config.stopLossPercentage = 0.03; // 3%
          config.takeProfitRatio = 1.5; // 更激进的风险收益比
          break;
        default: // BALANCED
          // 使用默认配置
          break;
      }

      // 根据投资期限调整
      if (userProfile.investmentHorizon === 'SHORT') {
        config.maxPositionHoldTime = 6; // 6小时
        config.meanReversionPeriod = 10; // 更短的均值周期
      } else if (userProfile.investmentHorizon === 'LONG') {
        config.maxPositionHoldTime = 24; // 24小时
        config.meanReversionPeriod = 30; // 更长的均值周期
      }
    }

    return config;
  }

  /**
   * 验证输入数据
   */
  validateInputs(
    trendAnalysis: TrendAnalysisInput,
    riskManagement: RiskManagementInput,
    userProfile: UserProfile
  ): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证趋势分析
    if (trendAnalysis.trendScore < 0 || trendAnalysis.trendScore > 1) {
      errors.push('趋势强度必须在0-1之间');
    }
    if (trendAnalysis.confidence < 0 || trendAnalysis.confidence > 1) {
      errors.push('趋势分析信心度必须在0-1之间');
    }

    // 验证风险管理
    if (riskManagement.riskScore < 0 || riskManagement.riskScore > 1) {
      errors.push('风险评分必须在0-1之间');
    }

    // 验证用户画像
    if (!userProfile.id) {
      errors.push('用户画像数据无效');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 计算策略的预期表现
   */
  calculateExpectedPerformance(
    trendAnalysis: TrendAnalysisInput,
    riskManagement: RiskManagementInput,
    userProfile: UserProfile
  ): {
    expectedReturn: number;
    expectedRisk: number;
    winRate: number;
    averageHoldingPeriod: number;
  } {
    const config = this.getConfiguration(userProfile);

    // 均值回归策略的胜率通常较高但收益较小
    const baseWinProbability = 0.65; // 基础胜率65%
    // 移除对context的引用，使用趋势分析数据
    const lowVolatilityBonus = 0.05; // 基础低波动性加成
    const sidewaysBonus = trendAnalysis.trendDirection === 'NEUTRAL' ? 0.05 : 0;
    const trendPenalty = trendAnalysis.trendScore > 0.5 ? trendAnalysis.trendScore * 0.1 : 0;
    const riskPenalty = riskManagement.riskScore * 0.05;
    
    const winProbability = Math.min(0.8, Math.max(0.5, 
      baseWinProbability + lowVolatilityBonus + sidewaysBonus - trendPenalty - riskPenalty
    ));

    // 计算预期收益和风险
    const stopLossRisk = config.stopLossPercentage;
    const takeProfitReturn = stopLossRisk * config.takeProfitRatio;
    
    const expectedReturn = (winProbability * takeProfitReturn) - ((1 - winProbability) * stopLossRisk);
    const expectedRisk = stopLossRisk;
    const riskRewardRatio = takeProfitReturn / stopLossRisk;

    return {
      expectedReturn: Math.round(expectedReturn * 10000) / 100,
      expectedRisk: Math.round(expectedRisk * 10000) / 100,
      winRate: Math.round(winProbability * 10000) / 100,
      averageHoldingPeriod: config.maxPositionHoldTime
    };
  }

  /**
   * 分析均值回归信号
   */
  private async analyzeMeanReversionSignal(
    context: StrategyExecutionContext,
    trendAnalysis: TrendAnalysisInput,
    config: MeanReversionConfig
  ): Promise<{
    isValid: boolean;
    reason: string;
    direction: 'BUY' | 'SELL' | null;
    strength: number;
    deviationFromMean: number;
  }> {
    const currentPrice = context.currentPrice;
    
    // 计算移动平均线和偏离程度
    const movingAverage = await this.calculateMovingAverage(context.symbol, config.meanReversionPeriod);
    const deviationFromMean = Math.abs(currentPrice - movingAverage) / movingAverage;
    const pricePosition = (currentPrice - movingAverage) / movingAverage;
    
    // 计算RSI值
    const rsi = await this.calculateRSI(context.symbol, 14);

    // 检查趋势强度限制
    if (trendAnalysis.trendScore > config.maxTrendStrength) {
      return {
        isValid: false,
        reason: `趋势强度${trendAnalysis.trendScore}超过阈值${config.maxTrendStrength}，不适合均值回归`,
        direction: null,
        strength: 0,
        deviationFromMean
      };
    }

    // 检查偏离程度
    if (deviationFromMean < config.minDeviationFromMean) {
      return {
        isValid: false,
        reason: `价格偏离均值程度${(deviationFromMean * 100).toFixed(2)}%低于阈值${(config.minDeviationFromMean * 100).toFixed(2)}%`,
        direction: null,
        strength: 0,
        deviationFromMean
      };
    }

    // 判断超买超卖状态
    let direction: 'BUY' | 'SELL' | null = null;
    let signalReason = '';
    
    if (rsi <= config.oversoldThreshold && pricePosition < 0) {
      direction = 'BUY';
      signalReason = `RSI ${(rsi * 100).toFixed(1)} 显示超卖，价格低于均值${(Math.abs(pricePosition) * 100).toFixed(2)}%`;
    } else if (rsi >= config.overboughtThreshold && pricePosition > 0) {
      direction = 'SELL';
      signalReason = `RSI ${(rsi * 100).toFixed(1)} 显示超买，价格高于均值${(pricePosition * 100).toFixed(2)}%`;
    }

    if (!direction) {
      return {
        isValid: false,
        reason: `未达到超买超卖条件，RSI: ${(rsi * 100).toFixed(1)}`,
        direction: null,
        strength: 0,
        deviationFromMean
      };
    }

    // 计算信号强度
    const extremeLevel = direction === 'BUY' 
      ? (config.oversoldThreshold - rsi) / config.oversoldThreshold
      : (rsi - config.overboughtThreshold) / (1 - config.overboughtThreshold);
    
    const strength = Math.min(1, deviationFromMean * 10 + extremeLevel);

    return {
      isValid: true,
      reason: signalReason,
      direction,
      strength,
      deviationFromMean
    };
  }

  /**
   * 评估风险
   */
  private assessRisk(
    riskManagement: RiskManagementInput,
    trendAnalysis: TrendAnalysisInput,
    config: MeanReversionConfig
  ): { acceptable: boolean; reason: string; score: number } {
    // 基础风险检查
    if (riskManagement.riskScore > config.maxRiskScore) {
      return {
        acceptable: false,
        reason: `风险评分${riskManagement.riskScore}超过阈值${config.maxRiskScore}`,
        score: riskManagement.riskScore
      };
    }

    // 趋势风险检查
    if (trendAnalysis.trendScore > config.maxTrendStrength) {
      return {
        acceptable: false,
        reason: `趋势强度${trendAnalysis.trendScore}过高，逆势交易风险大`,
        score: riskManagement.riskScore
      };
    }

    // 信心度检查
    if (trendAnalysis.confidence < config.minConfidenceLevel) {
      return {
        acceptable: false,
        reason: `分析信心度${trendAnalysis.confidence}低于阈值${config.minConfidenceLevel}`,
        score: riskManagement.riskScore
      };
    }

    return {
      acceptable: true,
      reason: '风险水平可接受',
      score: riskManagement.riskScore
    };
  }

  /**
   * 生成交易信号
   */
  private generateTradingSignal(
    context: StrategyExecutionContext,
    userProfile: UserProfileEntity,
    trendAnalysis: TrendAnalysisInput,
    riskManagement: RiskManagementInput,
    reversionSignal: any,
    config: MeanReversionConfig
  ): TradingSignalEntity {
    const currentPrice = context.currentPrice;
    const action = reversionSignal.direction;
    
    // 计算止损和止盈价格
    const stopLossDistance = currentPrice * config.stopLossPercentage;
    const takeProfitDistance = stopLossDistance * config.takeProfitRatio;
    
    let stopLoss: number;
    let takeProfit: number;
    
    if (action === 'BUY') {
      stopLoss = currentPrice - stopLossDistance;
      takeProfit = currentPrice + takeProfitDistance;
    } else {
      stopLoss = currentPrice + stopLossDistance;
      takeProfit = currentPrice - takeProfitDistance;
    }

    const signalData = {
      id: `mean-reversion-${Date.now()}-${this.generateSecureId()}`,
      userId: userProfile.userId,
      symbol: context.symbol,
      timestamp: new Date(),
      action,
      strength: reversionSignal.strength,
      confidence: this.calculateOverallConfidence(reversionSignal, { acceptable: true, score: riskManagement.riskScore }),
      currentPrice,
      targetPrice: action === 'BUY' ? takeProfit : stopLoss,
      stopLoss,
      takeProfit,
      recommendedPositionSize: 0.1, // 默认10%仓位
      maxPositionSize: 0.2, // 最大20%仓位
      riskAmount: currentPrice * 0.1 * (config.stopLossPercentage || 0.02),
      riskLevel: riskManagement.riskScore > 0.7 ? 'HIGH' : riskManagement.riskScore > 0.4 ? 'MEDIUM' : 'LOW' as 'HIGH' | 'MEDIUM' | 'LOW' | 'VERY_HIGH',
      expectedReturn: config.takeProfitRatio * (config.stopLossPercentage || 0.02),
      maxDrawdown: config.stopLossPercentage || 0.02,
      timeHorizon: 'SHORT' as 'SHORT' | 'MEDIUM' | 'LONG',
      timeframe: trendAnalysis.timeframe || '1h',
      validUntil: new Date(Date.now() + config.maxPositionHoldTime * 60 * 60 * 1000),
      strategyName: this.name,
      strategyVersion: this.version,
      trendAnalysisId: undefined,
      riskAssessmentId: undefined,
      userProfileVersion: '1.0',
      reasoning: this.generateReasoning(reversionSignal, riskManagement, config),
      keyFactors: [
        `均值偏离度: ${(reversionSignal.deviationFromMean * 100).toFixed(2)}%`,
        `信号强度: ${(reversionSignal.strength * 100).toFixed(1)}%`,
        `风险评分: ${(riskManagement.riskScore * 100).toFixed(1)}%`
      ],
      riskWarnings: riskManagement.riskScore > 0.7 ? ['高风险交易，请谨慎操作'] : [],
      status: 'PENDING' as 'PENDING' | 'EXECUTED' | 'CANCELLED' | 'EXPIRED',
      executedAt: undefined,
      executionPrice: undefined,
      processingTime: Date.now() - Date.now(), // 将在实际实现中计算
      dataQuality: 85, // 默认数据质量评分
      riskMetrics: {
        riskScore: riskManagement.riskScore,
        volatility: 0.15, // 默认波动率
        maxDrawdown: config.stopLossPercentage || 0.02,
        sharpeRatio: 1.5 // 默认夏普比率
      },
      metadata: {
        generatedAt: new Date(),
        expiresAt: new Date(Date.now() + config.maxPositionHoldTime * 60 * 60 * 1000),
        priority: 'NORMAL'
      },
      strategy: {
        name: this.name,
        version: this.version,
        reasoning: this.generateReasoning(reversionSignal, riskManagement, config)
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return new TradingSignalEntity(signalData);
  }

  /**
   * 计算综合信心度
   */
  private calculateOverallConfidence(
    reversionSignal: any,
    riskAssessment: { acceptable: boolean; score: number }
  ): number {
    let confidence = reversionSignal.strength;
    
    // 偏离程度加成
    confidence += reversionSignal.deviationFromMean * 2;
    
    // 风险调整
    if (riskAssessment.acceptable) {
      confidence += (1 - riskAssessment.score) * 0.1;
    } else {
      confidence -= 0.2;
    }
    
    return Math.min(1, Math.max(0, confidence));
  }

  /**
   * 生成推理说明
   */
  private generateReasoning(
    reversionSignal: any,
    riskAssessment: any,
    config: MeanReversionConfig
  ): string[] {
    const reasoning = [];
    
    reasoning.push(`均值回归分析: ${reversionSignal.reason}`);
    reasoning.push(`风险评估: ${riskAssessment.reason}`);
    reasoning.push(`信号强度: ${(reversionSignal.strength * 100).toFixed(1)}%`);
    reasoning.push(`偏离均值: ${(reversionSignal.deviationFromMean * 100).toFixed(2)}%`);
    reasoning.push(`止损设置: ${(config.stopLossPercentage * 100).toFixed(1)}%`);
    reasoning.push(`风险收益比: 1:${config.takeProfitRatio}`);
    
    return reasoning;
  }

  /**
   * 获取历史价格数据 - 使用真实市场数据
   * 🔥 零容忍虚假数据 - 完全移除Math.random()模拟实现
   */
  private async getHistoricalPriceData(symbol: string, period: number = 50): Promise<number[]> {
    try {
      this.logger.info('获取真实历史价格数据', { symbol, period });

      // 策略1: 使用RealDataIntegrationService获取真实数据
      const realDataService = await this.getRealDataIntegrationService();
      if (realDataService) {
        const integratedData = await realDataService.getBTCIntegratedData();
        if (integratedData?.technical?.historicalPrices && integratedData.technical.historicalPrices.length > 0) {
          const prices = integratedData.technical.historicalPrices.slice(-period);
          this.logger.info('成功从RealDataIntegrationService获取历史价格', {
            symbol,
            dataPoints: prices.length,
            source: 'RealDataIntegrationService'
          });
          return prices;
        }
      }

      // 策略2: 使用RealMarketDataProvider获取K线数据
      const marketDataProvider = await this.getRealMarketDataProvider();
      if (marketDataProvider) {
        const klineData = await marketDataProvider.getKlineData({
          symbol,
          timeframe: '1h',
          limit: period
        });
        
        if (klineData && klineData.length > 0) {
          const prices = klineData.map(kline => parseFloat(kline.close));
          this.logger.info('成功从RealMarketDataProvider获取历史价格', {
            symbol,
            dataPoints: prices.length,
            source: 'RealMarketDataProvider'
          });
          return prices;
        }
      }

      // 策略3: 直接调用Binance API获取真实数据
      const binanceData = await this.fetchFromBinanceAPI(symbol, period);
      if (binanceData && binanceData.length > 0) {
        this.logger.info('成功从Binance API获取历史价格', {
          symbol,
          dataPoints: binanceData.length,
          source: 'BinanceAPI'
        });
        return binanceData;
      }

      // 🚨 如果所有真实数据源都失败，抛出错误而不是返回虚假数据
      throw new Error(`无法获取${symbol}的真实历史价格数据，所有数据源均失败`);
      
    } catch (error) {
      this.logger.error('获取历史价格数据失败', {
        symbol,
        period,
        error: error instanceof Error ? error.message : String(error)
      });
      
      // 🔥 零容忍虚假数据：失败时抛出错误，不返回模拟数据
      throw new Error(`历史价格数据获取失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取RealDataIntegrationService实例
   */
  private async getRealDataIntegrationService(): Promise<any> {
    try {
      const { getContainer } = await import('../../../../shared/infrastructure/di/container');
      const { TYPES } = await import('../../../../shared/infrastructure/di/types');
      const container = getContainer();
      return container.get(TYPES.MarketData.RealDataIntegrationService);
    } catch (error) {
      this.logger.warn('无法获取RealDataIntegrationService', { error });
      return null;
    }
  }

  /**
   * 获取RealMarketDataProvider实例
   */
  private async getRealMarketDataProvider(): Promise<any> {
    try {
      const { getContainer } = await import('../../../../shared/infrastructure/di/container');
      const { TYPES } = await import('../../../../shared/infrastructure/di/types');
      const container = getContainer();
      // return container.get(TYPES.TrendAnalysis.RealMarketDataProvider); // 服务不存在
      return null; // 暂时返回null
    } catch (error) {
      this.logger.warn('无法获取RealMarketDataProvider', { error });
      return null;
    }
  }

  /**
   * 直接从Binance API获取真实K线数据
   */
  private async fetchFromBinanceAPI(symbol: string, period: number): Promise<number[]> {
    try {
      const binanceSymbol = symbol === 'BTC' ? 'BTCUSDT' : `${symbol}USDT`;
      const response = await fetch(`https://api.binance.com/api/v3/klines?symbol=${binanceSymbol}&interval=1h&limit=${period}`);
      
      if (!response.ok) {
        throw new Error(`Binance API请求失败: ${response.status} ${response.statusText}`);
      }
      
      const klineData = await response.json() as any[];
      
      if (!Array.isArray(klineData) || klineData.length === 0) {
        throw new Error('Binance API返回空数据');
      }
      
      // 提取收盘价格 (索引4是收盘价)
      const prices = klineData.map(kline => parseFloat(kline[4]));
      
      // 验证价格数据有效性
      const validPrices = prices.filter(price => price > 0 && !isNaN(price));
      if (validPrices.length === 0) {
        throw new Error('Binance API返回的价格数据无效');
      }
      
      return validPrices;
    } catch (error) {
      this.logger.error('从Binance API获取数据失败', {
        symbol,
        period,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 计算移动平均线 (使用统一技术指标计算器)
   */
  private async calculateMovingAverage(symbol: string, period: number = 20): Promise<number> {
    try {
      const historicalPrices = await this.getHistoricalPriceData(symbol, period + 10);
      return this.technicalCalculator.calculateSMA(historicalPrices, period);
    } catch (error) {
      this.logger.error('计算移动平均线失败', { symbol, period, error: error.message });
      // 🔥 零容忍虚假数据：失败时抛出错误，不返回模拟数据
      throw new Error(`无法计算${symbol}的移动平均线: ${error.message}`);
    }
  }

  /**
   * 计算相对强弱指数 (使用统一技术指标计算器)
   */
  private async calculateRSI(symbol: string, period: number = 14): Promise<number> {
    try {
      const historicalPrices = await this.getHistoricalPriceData(symbol, period + 10);
      const rsiResult = this.technicalCalculator.calculateRSI(historicalPrices, period);
      return rsiResult.value;
    } catch (error) {
      this.logger.error('计算RSI失败', { symbol, period, error: error.message });
      // 🔥 零容忍虚假数据：失败时抛出错误，不返回模拟数据
      throw new Error(`无法计算${symbol}的RSI指标: ${error.message}`);
    }
  }

  /**
   * 创建失败结果
   */
  private createFailureResult(reason: string, context: StrategyExecutionContext): StrategyExecutionResult {
    return {
      signal: null,
      confidence: 0,
      reasoning: [reason],
      riskAssessment: {
        level: 'HIGH',
        factors: ['策略执行失败'],
        mitigationActions: ['检查数据源', '重试执行']
      },
      executionRecommendations: {
        positionSize: 0,
        entryPrice: context.currentPrice,
        timeHorizon: 'SHORT'
      },
      metadata: {
        strategyName: this.name,
        strategyVersion: this.version,
        processingTime: 0,
        dataQuality: 0
      }
    };
  }

  /**
   * 创建无信号结果
   */
  private createNoSignalResult(reason: string, context: StrategyExecutionContext): StrategyExecutionResult {
    return {
      signal: null,
      confidence: 0,
      reasoning: [reason],
      riskAssessment: {
        level: 'MEDIUM',
        factors: ['无明确交易信号'],
        mitigationActions: ['等待更好的入场时机']
      },
      executionRecommendations: {
        positionSize: 0,
        entryPrice: context.currentPrice,
        timeHorizon: 'MEDIUM'
      },
      metadata: {
        strategyName: this.name,
        strategyVersion: this.version,
        processingTime: 0,
        dataQuality: 0.8
      }
    };
  }

  /**
   * 生成安全的唯一ID (替代Math.random())
   */
  private generateSecureId(): string {
    try {
      // 使用crypto模块生成安全的随机字符串
      const crypto = require('crypto');
      return crypto.randomBytes(6).toString('hex');
    } catch (error) {
      // 降级方案：使用时间戳和进程ID
      return `${process.pid}-${Date.now().toString(36)}`;
    }
  }
}