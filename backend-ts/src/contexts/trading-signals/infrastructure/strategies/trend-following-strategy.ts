/**
 * 趋势跟随策略实现
 * 基于趋势分析结果，在趋势明确时进入交易
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import {
  ITradingStrategy,
  TrendAnalysisInput,
  RiskManagementInput,
  StrategyExecutionContext,
  StrategyExecutionResult
} from '../../domain/interfaces/trading-strategy.interface';
import { UserProfileEntity } from '../../domain/entities/user-profile';
import { TradingSignalEntity, TradingSignalData } from '../../domain/entities/trading-signal';
import { UserProfile } from '../../domain/entities/user-profile';
import { SecureIdGenerator } from '../../../../shared/infrastructure/utils/secure-id-generator';

/**
 * 趋势跟随策略配置
 */
interface TrendFollowingConfig {
  minTrendStrength: number; // 最小趋势强度阈值
  minConfidenceLevel: number; // 最小信心度阈值
  maxRiskScore: number; // 最大风险评分阈值
  trendConfirmationPeriod: number; // 趋势确认周期
  stopLossPercentage: number; // 止损百分比
  takeProfitRatio: number; // 止盈比例（相对于止损）
  maxPositionHoldTime: number; // 最大持仓时间（小时）
  minDataQualityThreshold: number; // 最小数据质量阈值，低于此值返回NO_SIGNAL
}

@injectable()
export class TrendFollowingStrategy implements ITradingStrategy {
  public readonly name = 'Trend Following Strategy';
  public readonly version = '1.0.0';
  public readonly description = '基于趋势分析的跟随策略，在明确趋势中进入交易';
  public readonly suitableMarketConditions = [
    'TREND_STRONG_BULL',
    'TREND_STRONG_BEAR',
    'TREND_BULL',
    'TREND_BEAR',
    'VOLATILITY_MEDIUM',
    'VOLATILITY_HIGH',
    'LIQUIDITY_HIGH',
    'LIQUIDITY_MEDIUM'
  ];
  public readonly riskLevel = 'MEDIUM';

  private readonly defaultConfig: TrendFollowingConfig = {
    minTrendStrength: 0.6,
    minConfidenceLevel: 0.65,
    maxRiskScore: 0.7,
    trendConfirmationPeriod: 4,
    stopLossPercentage: 0.02, // 2%
    takeProfitRatio: 2.5, // 2.5:1 风险收益比
    maxPositionHoldTime: 24, // 24小时
    minDataQualityThreshold: 70 // 最小数据质量阈值，低于此值返回NO_SIGNAL
  };

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger
  ) {}

  /**
   * 执行策略
   */
  async execute(
    trendAnalysis: TrendAnalysisInput,
    riskManagement: RiskManagementInput,
    userProfile: UserProfile,
    context: StrategyExecutionContext
  ): Promise<StrategyExecutionResult> {
    this.logger.info('执行趋势跟随策略', {
      symbol: context.symbol,
      userId: userProfile.userId,
      trendDirection: trendAnalysis.trendDirection,
      trendStrength: trendAnalysis.trendScore,
      confidence: trendAnalysis.confidence
    });

    try {
      // 获取策略配置
      const config = this.getConfiguration(userProfile);

      // 验证输入数据
      const validationResult = this.validateInputs(trendAnalysis, riskManagement, userProfile);
      if (!validationResult.isValid) {
        return this.createFailureResult(validationResult.errors.join('; '), context);
      }

      // 分析趋势信号
      const trendSignal = this.analyzeTrendSignal(trendAnalysis, config);
      if (!trendSignal.isValid) {
        return this.createNoSignalResult(trendSignal.reason, context);
      }

      // 评估风险
      const riskAssessment = this.assessRisk(riskManagement, config);
      if (!riskAssessment.acceptable) {
        return this.createNoSignalResult(riskAssessment.reason, context);
      }

      // 生成交易信号
      const signal = this.generateTradingSignal(
        context,
        userProfile,
        trendAnalysis,
        riskManagement,
        config
      );

      // 计算预期表现
      const expectedPerformance = this.calculateExpectedPerformance(
        trendAnalysis,
        riskManagement,
        userProfile
      );

      return {
        signal,
        confidence: this.calculateOverallConfidence(trendAnalysis, riskAssessment),
        reasoning: this.generateReasoning(trendSignal, riskAssessment, config),
        riskAssessment: {
          level: riskAssessment.acceptable ? 'LOW' : 'HIGH',
          factors: [riskAssessment.reason],
          mitigationActions: riskAssessment.acceptable ? [] : ['降低仓位大小', '设置更严格的止损']
        },
        executionRecommendations: {
          positionSize: signal.recommendedPositionSize,
          entryPrice: signal.currentPrice,
          stopLoss: signal.stopLoss,
          takeProfit: signal.takeProfit,
          timeHorizon: signal.timeHorizon
        },
        metadata: {
          strategyName: this.name,
          strategyVersion: this.version,
          processingTime: Date.now() - context.timestamp.getTime(),
          dataQuality: 85
        }
      };
    } catch (error) {
      this.logger.error('趋势跟随策略执行失败', { error: error.message, context });
      return this.createFailureResult(`策略执行异常: ${error.message}`, context);
    }
  }

  /**
   * 检查策略是否适用
   */
  isApplicable(
    context: StrategyExecutionContext,
    userProfile: UserProfile
  ): boolean {
    // 检查用户交易风格是否适合趋势跟随策略
    // UserProfile.tradingStyle 是 UserConfigTradingStyle 类型
    const suitableStyles = ['POSITION_TRADING', 'MIXED'];
    if (!suitableStyles.includes(userProfile.tradingStyle)) {
      return false;
    }

    // 检查市场条件
    const { trend, volatility, liquidity } = context.marketConditions;
    
    // 趋势跟随策略需要明确的趋势
    if (trend === 'NEUTRAL') {
      return false;
    }

    // 需要足够的流动性
    if (liquidity === 'LOW') {
      return false;
    }

    // 极高波动性可能不适合
    if (volatility === 'HIGH') {
      return false;
    }

    return true;
  }

  /**
   * 获取策略配置
   */
  getConfiguration(userProfile: UserProfile): TrendFollowingConfig {
    const config = { ...this.defaultConfig };

    if (userProfile) {
      // 根据用户风险偏好调整配置
      switch (userProfile.riskTolerance) {
        case 'CONSERVATIVE':
          config.minTrendStrength = 0.7;
          config.minConfidenceLevel = 0.75;
          config.maxRiskScore = 0.5;
          config.stopLossPercentage = 0.015; // 1.5%
          config.takeProfitRatio = 3.0; // 更保守的风险收益比
          break;
        case 'AGGRESSIVE':
          config.minTrendStrength = 0.5;
          config.minConfidenceLevel = 0.55;
          config.maxRiskScore = 0.8;
          config.stopLossPercentage = 0.025; // 2.5%
          config.takeProfitRatio = 2.0; // 更激进的风险收益比
          break;
        default: // BALANCED
          // 使用默认配置
          break;
      }

      // 根据投资期限调整
      if (userProfile.investmentHorizon === 'SHORT') {
        config.maxPositionHoldTime = 8; // 8小时
        config.trendConfirmationPeriod = 2;
      } else if (userProfile.investmentHorizon === 'LONG') {
        config.maxPositionHoldTime = 72; // 72小时
        config.trendConfirmationPeriod = 6;
      }
    }

    return config;
  }

  /**
   * 验证输入数据
   */
  validateInputs(
    trendAnalysis: TrendAnalysisInput,
    riskManagement: RiskManagementInput,
    userProfile: UserProfile
  ): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证趋势分析数据的基本完整性

    // 验证趋势分析
    if (!trendAnalysis.trendDirection) {
      errors.push('趋势方向不能为空');
    }
    if (trendAnalysis.trendScore < 0 || trendAnalysis.trendScore > 1) {
      errors.push('趋势强度必须在0-1之间');
    }
    if (trendAnalysis.confidence < 0 || trendAnalysis.confidence > 1) {
      errors.push('趋势分析信心度必须在0-1之间');
    }

    // 验证风险管理
    if (riskManagement.riskScore < 0 || riskManagement.riskScore > 1) {
      errors.push('风险评分必须在0-1之间');
    }

    // 验证用户画像
    if (!userProfile.userId) {
      errors.push('用户画像数据无效');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 计算预期表现
   */
  calculateExpectedPerformance(
    trendAnalysis: TrendAnalysisInput,
    riskManagement: RiskManagementInput,
    userProfile: UserProfile
  ): {
    expectedReturn: number;
    expectedRisk: number;
    winRate: number;
    averageHoldingPeriod: number;
  } {
    const config = this.getConfiguration(userProfile);

    // 基于趋势强度和信心度计算胜率
    const baseWinProbability = 0.55; // 基础胜率55%
    const trendBonus = trendAnalysis.trendScore * 0.15; // 趋势强度加成
    const confidenceBonus = trendAnalysis.confidence * 0.1; // 信心度加成
    const riskPenalty = riskManagement.riskScore * 0.05; // 风险惩罚
    
    const winRate = Math.min(0.8, Math.max(0.4, 
      baseWinProbability + trendBonus + confidenceBonus - riskPenalty
    ));

    // 计算预期收益和风险
    const stopLossRisk = config.stopLossPercentage;
    const takeProfitReturn = stopLossRisk * config.takeProfitRatio;
    
    const expectedReturn = (winRate * takeProfitReturn) - ((1 - winRate) * stopLossRisk);
    const expectedRisk = stopLossRisk;

    return {
      expectedReturn: Math.round(expectedReturn * 10000) / 100, // 转换为百分比
      expectedRisk: Math.round(expectedRisk * 10000) / 100,
      winRate: Math.round(winRate * 10000) / 100,
      averageHoldingPeriod: config.maxPositionHoldTime
    };
  }

  /**
   * 分析趋势信号
   */
  private analyzeTrendSignal(
    trendAnalysis: TrendAnalysisInput,
    config: TrendFollowingConfig
  ): { isValid: boolean; reason: string; strength: number } {
    // 检查趋势强度
    if (trendAnalysis.trendScore < config.minTrendStrength) {
      return {
        isValid: false,
        reason: `趋势强度${trendAnalysis.trendScore}低于阈值${config.minTrendStrength}`,
        strength: 0
      };
    }

    // 检查信心度
    if (trendAnalysis.confidence < config.minConfidenceLevel) {
      return {
        isValid: false,
        reason: `信心度${trendAnalysis.confidence}低于阈值${config.minConfidenceLevel}`,
        strength: 0
      };
    }

    // 检查趋势方向
    if (trendAnalysis.trendDirection === 'NEUTRAL') {
      return {
        isValid: false,
        reason: '横盘趋势不适合趋势跟随策略',
        strength: 0
      };
    }

    // 计算信号强度
    const strength = (trendAnalysis.trendScore + trendAnalysis.confidence / 100) / 2;

    return {
      isValid: true,
      reason: '趋势信号有效',
      strength
    };
  }

  /**
   * 评估风险
   */
  private assessRisk(
    riskManagement: RiskManagementInput,
    config: TrendFollowingConfig
  ): { acceptable: boolean; reason: string; score: number } {
    if (riskManagement.riskScore > config.maxRiskScore) {
      return {
        acceptable: false,
        reason: `风险评分${riskManagement.riskScore}超过阈值${config.maxRiskScore}`,
        score: riskManagement.riskScore
      };
    }

    return {
      acceptable: true,
      reason: '风险水平可接受',
      score: riskManagement.riskScore
    };
  }

  /**
   * 生成交易信号
   */
  private generateTradingSignal(
    context: StrategyExecutionContext,
    userProfile: UserProfile,
    trendAnalysis: TrendAnalysisInput,
    riskManagement: RiskManagementInput,
    config: TrendFollowingConfig
  ): TradingSignalEntity {
    const currentPrice = context.currentPrice;
    
    // 如果趋势方向无效或数据质量不足，返回NO_SIGNAL
    if (!trendAnalysis.trendDirection || (trendAnalysis as any).dataQuality < config.minDataQualityThreshold) {
      return this.generateNoSignalResponse(context, userProfile);
    }
    
    const action = trendAnalysis.trendDirection === 'BULLISH' ? 'BUY' : 'SELL';
    
    // 计算止损和止盈价格
    const stopLossDistance = currentPrice * config.stopLossPercentage;
    const takeProfitDistance = stopLossDistance * config.takeProfitRatio;
    
    let stopLoss: number;
    let takeProfit: number;
    
    if (action === 'BUY') {
      stopLoss = currentPrice - stopLossDistance;
      takeProfit = currentPrice + takeProfitDistance;
    } else {
      stopLoss = currentPrice + stopLossDistance;
      takeProfit = currentPrice - takeProfitDistance;
    }

    // 计算信号强度和信心度
    const signalStrength = (trendAnalysis.trendScore + (1 - riskManagement.riskScore)) / 2;
    const confidence = this.calculateOverallConfidence(trendAnalysis, { acceptable: true, score: riskManagement.riskScore });

    const signalData: TradingSignalData = {
      id: SecureIdGenerator.generateTrendFollowId(),
      userId: userProfile.userId,
      symbol: context.symbol,
      timestamp: new Date(),
      action,
      strength: Math.round(signalStrength * 10), // 转换为1-10范围
      confidence: Math.round(confidence * 100), // 转换为0-100范围
      currentPrice,
      stopLoss,
      takeProfit,
      recommendedPositionSize: Math.min(userProfile.maxPositionSize, 1 - riskManagement.riskScore),
      maxPositionSize: userProfile.maxPositionSize,
      riskLevel: riskManagement.riskLevel,
      expectedReturn: config.takeProfitRatio,
      maxDrawdown: riskManagement.maxDrawdown,
      timeHorizon: 'MEDIUM',
      timeframe: trendAnalysis.timeframe,
      strategyName: this.name,
      strategyVersion: this.version,
      userProfileVersion: '1.0',
      reasoning: this.generateReasoning(
        { isValid: true, reason: '趋势信号有效', strength: signalStrength },
        { acceptable: true, reason: '风险可接受', score: riskManagement.riskScore },
        config
      ),
      keyFactors: [
        `趋势方向: ${trendAnalysis.trendDirection}`,
        `信号强度: ${(signalStrength * 100).toFixed(1)}%`,
        `风险评分: ${(riskManagement.riskScore * 100).toFixed(1)}%`
      ],
      riskWarnings: riskManagement.riskWarnings,
      status: 'PENDING',
      processingTime: 0,
      dataQuality: 85,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return new TradingSignalEntity(signalData);
  }
  
  /**
   * 生成NO_SIGNAL响应
   * 当无法生成有效的交易信号时使用
   */
  private generateNoSignalResponse(
    context: StrategyExecutionContext,
    userProfile: UserProfile
  ): TradingSignalEntity {
    const signalData: TradingSignalData = {
      id: SecureIdGenerator.generateTrendFollowId(),
      userId: userProfile.userId,
      symbol: context.symbol,
      timestamp: new Date(),
      action: 'NO_SIGNAL',
      strength: 0,
      confidence: 0,
      currentPrice: context.currentPrice,
      recommendedPositionSize: 0,
      maxPositionSize: userProfile.maxPositionSize,
      riskLevel: 'LOW',
      expectedReturn: 0,
      maxDrawdown: 0,
      timeHorizon: 'SHORT',
      strategyName: this.name,
      strategyVersion: this.version,
      userProfileVersion: '1.0',
      reasoning: [
        '无法生成有效的交易信号',
        '数据质量不足或趋势方向不明确',
        '建议等待更明确的市场信号'
      ],
      keyFactors: [
        '信号类型: 无信号',
        '数据质量不足',
        '市场趋势不明确'
      ],
      riskWarnings: [
        '当前市场状况不明确，不建议进行交易'
      ],
      status: 'PENDING',
      processingTime: 0,
      dataQuality: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return new TradingSignalEntity(signalData);
  }

  /**
   * 计算综合信心度
   */
  private calculateOverallConfidence(
    trendAnalysis: TrendAnalysisInput,
    riskAssessment: { acceptable: boolean; score: number }
  ): number {
    let confidence = trendAnalysis.confidence;
    
    // 趋势强度加成
    confidence += trendAnalysis.trendScore * 0.1;
    
    // 风险调整
    if (riskAssessment.acceptable) {
      confidence += (1 - riskAssessment.score) * 0.05;
    } else {
      confidence -= 0.2;
    }
    
    return Math.min(1, Math.max(0, confidence));
  }

  /**
   * 生成推理说明
   */
  private generateReasoning(
    trendSignal: { isValid: boolean; reason: string; strength: number },
    riskAssessment: { acceptable: boolean; reason: string; score: number },
    config: TrendFollowingConfig
  ): string[] {
    const reasoning = [];
    
    reasoning.push(`趋势分析: ${trendSignal.reason}`);
    reasoning.push(`风险评估: ${riskAssessment.reason}`);
    reasoning.push(`信号强度: ${(trendSignal.strength * 100).toFixed(1)}%`);
    reasoning.push(`止损设置: ${(config.stopLossPercentage * 100).toFixed(1)}%`);
    reasoning.push(`风险收益比: 1:${config.takeProfitRatio}`);
    
    return reasoning;
  }

  /**
   * 创建失败结果
   */
  private createFailureResult(reason: string, context: StrategyExecutionContext): StrategyExecutionResult {
    return {
      signal: null,
      confidence: 0,
      reasoning: [reason],
      riskAssessment: {
        level: 'HIGH',
        factors: [reason],
        mitigationActions: ['检查输入数据', '重新分析市场条件']
      },
      executionRecommendations: {
        positionSize: 0,
        entryPrice: 0,
        timeHorizon: 'SHORT'
      },
      metadata: {
        strategyName: this.name,
        strategyVersion: this.version,
        processingTime: 0,
        dataQuality: 0
      }
    };
  }

  /**
   * 创建无信号结果
   */
  private createNoSignalResult(reason: string, context: StrategyExecutionContext): StrategyExecutionResult {
    // 生成NO_SIGNAL类型的信号实体
    const noSignalEntity = this.generateNoSignalResponse(context, {
      userId: 'system', // 将在实际执行时被替换
      maxPositionSize: 0,
      riskTolerance: 'CONSERVATIVE',
      tradingStyle: 'MIXED',
      investmentHorizon: 'SHORT'
    });
    
    return {
      signal: noSignalEntity, // 返回NO_SIGNAL类型的信号实体，而不是null
      confidence: 0,
      reasoning: [reason],
      riskAssessment: {
        level: 'LOW',
        factors: ['无交易信号'],
        mitigationActions: ['等待更明确的市场方向']
      },
      executionRecommendations: {
        positionSize: 0,
        entryPrice: context.currentPrice,
        timeHorizon: 'SHORT'
      },
      metadata: {
        strategyName: this.name,
        strategyVersion: '1.0',
        processingTime: 0,
        dataQuality: 0
      }
    };
  }
}