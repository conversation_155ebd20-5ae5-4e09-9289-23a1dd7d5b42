/**
 * 信号生成应用服务
 * 系统的核心编排层，负责协调trend-analysis、risk-management和user-config三大输入
 * 根据用户画像选择合适的交易策略，生成个性化的交易信号
 */

import { injectable, inject, optional, LazyServiceIdentifer } from 'inversify';
import { Logger } from 'winston';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { TradingSignalEntity, TradingSignalBuilder } from '../../domain/entities/trading-signal';
import { UserProfile, UserProfileEntity } from '../../domain/entities/user-profile';
import {
  ITradingStrategy,
  IStrategySelector,
  IPositionSizeCalculator,
  TrendAnalysisInput,
  RiskManagementInput,
  StrategyExecutionContext,
  StrategyExecutionResult
} from '../../domain/interfaces/trading-strategy.interface';
import { SignalGenerationResponse } from '../../domain/interfaces/signal-generation.interface';
import { v4 as uuidv4 } from 'uuid';

// 信号生成结果接口
export interface SignalGenerationResult {
  success: boolean;
  signal: SignalGenerationResponse;
  symbol: string;
  executionContext: {
    marketConditions: string;
    selectedStrategy: string;
    riskAssessment: string;
  };
}

// 服务工厂 - 解决循环依赖
import { LazyServiceResolver, ServiceFactoryManager, LazyServiceIdentifier } from '../../../../shared/infrastructure/di/base/service-factory';
import { getContainer } from '../../../../shared/infrastructure/di/container';

export interface SignalGenerationRequest {
  userId: string;
  symbol: string;
  timeframe?: string;
  strategy?: string; // 指定策略名称，可选
  forceGeneration?: boolean; // 强制生成信号，忽略缓存
}

export interface SignalGenerationResponse {
  signal: TradingSignalEntity | null;
  metadata: {
    processingTime: number;
    strategyUsed: string;
    dataQuality: {
      trendAnalysis: number;
      riskManagement: number;
      userProfile: number;
      overall: number;
    };
    warnings: string[];
    recommendations: string[];
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

@injectable()
export class SignalGenerationApplicationService {
  // 使用服务工厂解决循环依赖问题
  private serviceFactoryManager: ServiceFactoryManager;
  private lazyServiceResolver: LazyServiceResolver;

  // 延迟加载的服务
  private trendAnalysisService: LazyServiceIdentifier<any>;
  private riskManagementService: LazyServiceIdentifier<any>;
  private userConfigService: LazyServiceIdentifier<any>;
  private availableStrategies: ITradingStrategy[] = [];

  constructor(
    @inject(TYPES.Logger) private readonly logger: Logger,
    @inject(TYPES.TradingSignals.StrategySelector) private readonly strategySelector: IStrategySelector,
    @inject(TYPES.TradingSignals.PositionSizeCalculator) private readonly positionSizeCalculator: IPositionSizeCalculator
  ) {
    // 初始化服务工厂
    const containerManager = getContainer();
    this.serviceFactoryManager = new ServiceFactoryManager(containerManager.getContainer());
    this.lazyServiceResolver = LazyServiceResolver.getInstance(containerManager.getContainer());

    // 创建延迟服务标识符
    this.trendAnalysisService = this.lazyServiceResolver.createLazyService(TYPES.TrendAnalysis.TrendAnalysisApplicationService);
    this.riskManagementService = this.lazyServiceResolver.createLazyService(TYPES.RiskManagement.RiskAssessmentApplicationService);
    this.userConfigService = this.lazyServiceResolver.createLazyService(TYPES.UserConfig.UserConfigService);

    this.availableStrategies = [];
    this.logger.info('信号生成应用服务初始化完成 - 使用服务工厂解决循环依赖');
  }

  /**
   * 生成交易信号的主要方法
   */
  async generateSignal(request: SignalGenerationRequest): Promise<SignalGenerationResponse>;
  async generateSignal(userId: string, symbol: string, userProfile: any, options?: any): Promise<SignalGenerationResponse>;
  async generateSignal(requestOrUserId: SignalGenerationRequest | string, symbol?: string, userProfile?: any, options?: any): Promise<SignalGenerationResponse> {
    // 处理重载情况
    let request: SignalGenerationRequest;
    
    if (typeof requestOrUserId === 'string') {
      request = {
        userId: requestOrUserId,
        symbol: symbol!,
        timeframe: options?.timeframe || '1h',
        strategy: options?.strategyType
      };
    } else {
      request = requestOrUserId;
    }
    const startTime = Date.now();
    const warnings: string[] = [];
    const recommendations: string[] = [];

    try {
      this.logger.info('开始生成交易信号', {
        userId: request.userId,
        symbol: request.symbol,
        timeframe: request.timeframe || '1h'
      });

      // 第一步：并行获取三大输入数据
      const [trendAnalysis, riskManagement, userProfile] = await Promise.all([
        this.getTrendAnalysis(request.symbol, request.timeframe),
        this.getRiskManagement(request.symbol),
        this.getUserProfile(request.userId)
      ]);

      // 第二步：验证输入数据质量
      const dataQuality = this.assessDataQuality(trendAnalysis, riskManagement, userProfile);
      if (dataQuality.overall < 50) {
        warnings.push('数据质量较低，可能影响信号准确性');
      }

      // 第三步：构建执行上下文
      const context = await this.buildExecutionContext(request.symbol);

      // 第四步：选择合适的交易策略
      const selectedStrategy = request.strategy 
        ? this.findStrategyByName(request.strategy)
        : this.strategySelector.selectStrategy(context, userProfile as any, this.availableStrategies);

      if (!selectedStrategy) {
        this.logger.warn('未找到合适的交易策略', {
          userId: request.userId,
          symbol: request.symbol,
          requestedStrategy: request.strategy
        });
        
        return {
          signal: null,
          metadata: {
            processingTime: Date.now() - startTime,
            dataQuality: dataQuality.overall,
            modelVersion: '1.0.0'
          }
        };
      }

      // 第五步：验证策略输入数据
      const validation = selectedStrategy.validateInputs(trendAnalysis, riskManagement, userProfile as any);
      if (!validation.isValid) {
        warnings.push(...validation.errors);
        recommendations.push(...validation.warnings);
        
        if (validation.errors.length > 0) {
          this.logger.error('策略输入数据验证失败', {
            strategy: selectedStrategy.name,
            errors: validation.errors
          });
          
          return {
            signal: null,
            metadata: {
              processingTime: Date.now() - startTime,
              dataQuality: dataQuality.overall,
              modelVersion: '1.0.0'
            }
          };
        }
      }

      // 第六步：执行策略生成信号
      const strategyResult = await selectedStrategy.execute(
        trendAnalysis,
        riskManagement,
        userProfile as any,
        context
      );

      // 第七步：后处理和优化
      const finalSignal = await this.postProcessSignal(
        strategyResult,
        userProfile,
        riskManagement,
        context
      );

      const processingTime = Date.now() - startTime;

      this.logger.info('交易信号生成完成', {
        userId: request.userId,
        symbol: request.symbol,
        strategy: selectedStrategy.name,
        signal: finalSignal?.action || 'NONE',
        confidence: finalSignal?.confidence || 0,
        processingTime
      });

      return {
        signal: finalSignal,
        metadata: {
          processingTime,
          dataQuality: dataQuality.overall,
          modelVersion: '1.0.0'
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      this.logger.error('交易信号生成失败', {
        userId: request.userId,
        symbol: request.symbol,
        error: error instanceof Error ? error.message : String(error),
        processingTime
      });

      return {
        signal: null,
        metadata: {
          processingTime,
          dataQuality: 0,
          modelVersion: '1.0.0'
        },
        error: {
          code: 'SIGNAL_GENERATION_FAILED',
          message: error instanceof Error ? error.message : String(error),
          details: { processingTime }
        }
      };
    }
  }

  /**
   * 批量生成信号 - 基于用户ID、符号列表和用户画像
   */
  async generateBatchSignals(
    userId: string,
    symbols: string[],
    userProfile: UserProfileEntity,
    options?: {
      timeframe?: string;
      strategyType?: string;
      requestId?: string;
    }
  ): Promise<SignalGenerationResult[]> {
    // 将参数转换为 SignalGenerationRequest 数组
    const requests = symbols.map(symbol => ({
      userId,
      symbol,
      timeframe: options?.timeframe || '1h',
      strategyType: options?.strategyType as any,
      userProfile: userProfile as any
    }));
    
    // 调用基础方法生成信号
    const responses = await this.generateBatchSignalsBase(requests);
    
    // 转换为 SignalGenerationResult 格式
    return responses.map(response => ({
      success: !!response.signalType && response.signalType !== 'HOLD',
      signal: response,
      symbol: response.symbol,
      executionContext: {
        marketConditions: 'Based on current market analysis',
        selectedStrategy: response.executionContext?.strategyUsed || 'default',
        riskAssessment: response.executionContext?.riskFactors?.join(', ') || 'standard'
      }
    }));
  }

  /**
   * 批量生成信号 - 基于请求数组
   */
  async generateBatchSignalsBase(requests: SignalGenerationRequest[]): Promise<SignalGenerationResponse[]> {
    this.logger.info('开始批量生成交易信号', { count: requests.length });

    const results = await Promise.allSettled(
      requests.map(request => this.generateSignal(request))
    );

    const responses: SignalGenerationResponse[] = [];
    let successCount = 0;
    let failureCount = 0;

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        responses.push(result.value);
        if (result.value.signalType && result.value.signalType !== 'HOLD') {
          successCount++;
        } else {
          failureCount++;
        }
      } else {
        failureCount++;
        this.logger.error('批量信号生成失败', {
          index,
          request: requests[index],
          error: result.reason
        });
        
        // 创建一个符合 SignalGenerationResponse 接口的错误响应对象
        responses.push({
          signalId: `error-${Date.now()}-${index}`,
          symbol: requests[index].symbol,
          signalType: 'HOLD',
          strength: 0,
          confidence: 0,
          reasoning: [`批量处理失败: ${result.reason}`],
          riskLevel: 'HIGH',
          timeframe: requests[index].timeframe || '1h',
          timestamp: new Date(),
          metadata: {
            processingTime: 0,
            dataQuality: 0,
            modelVersion: '1.0'
          }
        });
      }
    });

    this.logger.info('批量交易信号生成完成', {
      total: requests.length,
      success: successCount,
      failure: failureCount
    });

    return responses;
  }

  /**
   * 获取趋势分析数据
   */
  private async getTrendAnalysis(symbol: string, timeframe?: string): Promise<TrendAnalysisInput> {
    try {
      // 使用延迟服务标识符获取服务
      const trendService = await this.trendAnalysisService();

      if (!trendService) {
        throw new Error('趋势分析服务不可用');
      }
      const analysis = await trendService.analyzeTrend({
        symbol,
        timeframe: timeframe || '1h'
      });

      return {
        id: analysis.id || uuidv4(),
        symbol,
        timestamp: new Date(analysis.timestamp || Date.now()),
        trendScore: analysis.trendScore || 0.5,
        trendDirection: analysis.trendDirection || 'NEUTRAL',
        confidence: analysis.confidence || 50,
        timeframe: timeframe || '1h',
        technicalIndicators: analysis.technicalIndicators || {
          rsi: 50,
          macd: { signal: 0, histogram: 0, macd: 0 },
          bollinger: { upper: 0, middle: 0, lower: 0 },
          adx: 25,
          sma20: 0,
          sma50: 0,
          ema12: 0,
          ema26: 0
        },
        supportLevels: analysis.supportLevels || [],
        resistanceLevels: analysis.resistanceLevels || [],
        keyPatterns: analysis.keyPatterns || [],
        marketPhase: analysis.marketPhase || 'ACCUMULATION',
        volatility: analysis.volatility || 0.02,
        volume: analysis.volume || {
          current: 0,
          average: 0,
          trend: 'STABLE'
        }
      };
    } catch (error) {
      this.logger.error('获取趋势分析数据失败', { symbol, error });
      throw new Error(`趋势分析服务不可用: ${error}`);
    }
  }

  /**
   * 获取风险管理数据
   */
  private async getRiskManagement(symbol: string): Promise<RiskManagementInput> {
    try {
      // 使用延迟服务标识符获取服务
      const riskService = await this.riskManagementService();

      if (!riskService) {
        throw new Error('风险管理服务不可用');
      }
      const risk = await riskService.assessRisk({ symbol });

      return {
        id: risk.id || uuidv4(),
        symbol,
        timestamp: new Date(risk.timestamp || Date.now()),
        riskScore: risk.riskScore || 0.5,
        riskLevel: risk.riskLevel || 'MEDIUM',
        maxDrawdown: risk.maxDrawdown || 0.1,
        volatilityRisk: risk.volatilityRisk || 0.3,
        liquidityRisk: risk.liquidityRisk || 0.2,
        correlationRisk: risk.correlationRisk || 0.1,
        marketRisk: risk.marketRisk || 0.4,
        positionSizeLimit: risk.positionSizeLimit || 0.1,
        stopLossRecommendation: risk.stopLossRecommendation || 0,
        riskWarnings: risk.riskWarnings || [],
        complianceStatus: risk.complianceStatus || 'COMPLIANT',
        riskMetrics: risk.riskMetrics || {
          var: 0.05,
          cvar: 0.08,
          sharpeRatio: 1.0,
          sortinoRatio: 1.2,
          maxDrawdownPeriod: 30
        }
      };
    } catch (error) {
      this.logger.error('获取风险管理数据失败', { symbol, error });
      throw new Error(`风险管理服务不可用: ${error}`);
    }
  }

  /**
   * 获取用户画像
   */
  private async getUserProfile(userId: string): Promise<UserProfileEntity> {
    try {
      // 使用延迟服务标识符获取服务
      const userService = await this.userConfigService();

      if (!userService) {
        throw new Error('用户配置服务不可用');
      }
      const profile = await userService.getUserProfile(userId);
      return new UserProfileEntity(profile);
    } catch (error) {
      this.logger.error('获取用户画像失败', { userId, error });
      throw new Error(`用户配置服务不可用: ${error}`);
    }
  }

  /**
   * 构建策略执行上下文
   */
  private async buildExecutionContext(symbol: string): Promise<StrategyExecutionContext> {
    // 这里可以集成市场数据服务获取实时价格和市场条件
    return {
      symbol,
      currentPrice: 50000, // 临时值，应从市场数据服务获取
      timestamp: new Date(),
      marketConditions: {
        volatility: 'MEDIUM',
        liquidity: 'HIGH',
        trend: 'NEUTRAL'
      }
    };
  }

  /**
   * 评估数据质量
   */
  private assessDataQuality(
    trendAnalysis: TrendAnalysisInput,
    riskManagement: RiskManagementInput,
    userProfile: UserProfileEntity
  ): { trendAnalysis: number; riskManagement: number; userProfile: number; overall: number } {
    const trendQuality = this.assessTrendAnalysisQuality(trendAnalysis);
    const riskQuality = this.assessRiskManagementQuality(riskManagement);
    const profileQuality = this.assessUserProfileQuality(userProfile);
    
    const overall = (trendQuality + riskQuality + profileQuality) / 3;

    return {
      trendAnalysis: trendQuality,
      riskManagement: riskQuality,
      userProfile: profileQuality,
      overall
    };
  }

  private assessTrendAnalysisQuality(analysis: TrendAnalysisInput): number {
    let score = 100;
    
    if (!analysis.technicalIndicators || Object.keys(analysis.technicalIndicators).length === 0) {
      score -= 30;
    }
    if (analysis.confidence < 50) {
      score -= 20;
    }
    if (!analysis.supportLevels || analysis.supportLevels.length === 0) {
      score -= 10;
    }
    if (!analysis.resistanceLevels || analysis.resistanceLevels.length === 0) {
      score -= 10;
    }
    
    return Math.max(0, score);
  }

  private assessRiskManagementQuality(risk: RiskManagementInput): number {
    let score = 100;
    
    if (!risk.riskMetrics) {
      score -= 40;
    }
    if (risk.complianceStatus === 'VIOLATION') {
      score -= 30;
    }
    if (risk.riskWarnings && risk.riskWarnings.length > 3) {
      score -= 20;
    }
    
    return Math.max(0, score);
  }

  private assessUserProfileQuality(profile: UserProfileEntity): number {
    return profile.validate() ? 100 : 50;
  }

  /**
   * 根据名称查找策略
   */
  private findStrategyByName(strategyName: string): ITradingStrategy | null {
    return this.availableStrategies.find(strategy => strategy.name === strategyName) || null;
  }

  /**
   * 信号后处理和优化
   */
  private async postProcessSignal(
    strategyResult: StrategyExecutionResult,
    userProfile: UserProfileEntity,
    riskManagement: RiskManagementInput,
    context: StrategyExecutionContext
  ): Promise<TradingSignalEntity | null> {
    if (!strategyResult.signal) {
      return null;
    }

    // 重新计算仓位大小
    const positionSizing = this.positionSizeCalculator.calculatePositionSize(
      strategyResult.signal,
      userProfile as any,
      riskManagement,
      context.accountInfo
    );

    // 更新信号的仓位大小
    const updatedSignalData = {
      ...strategyResult.signal.toPlainObject(),
      recommendedPositionSize: positionSizing.recommendedSize,
      maxPositionSize: positionSizing.maxSize,
      reasoning: [...(strategyResult.signal.toPlainObject().reasoning || []), ...positionSizing.reasoning]
    };

    return new TradingSignalEntity(updatedSignalData);
  }
}