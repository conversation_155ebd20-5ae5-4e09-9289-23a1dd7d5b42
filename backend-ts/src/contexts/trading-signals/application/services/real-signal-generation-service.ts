/**
 * 真实信号生成服务
 * 使用真实数据源替换虚假的信号生成逻辑
 */

import { Logger } from 'winston';
import RealDataIntegrationService, { IntegratedMarketData } from '../../../market-data/application/services/real-data-integration-service';
import { injectable, inject, LazyServiceIdentifer } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types';
import { IDynamicWeightingService } from '../../../../shared/infrastructure/analysis/interfaces/IDynamicWeightingService';
import { IPatternRecognitionService } from '../../../../shared/infrastructure/analysis/interfaces/IPatternRecognitionService';
import { IMultiTimeframeService } from '../../../../shared/infrastructure/analysis/interfaces/IMultiTimeframeService';
import { IUnifiedTechnicalIndicatorCalculator } from '../../../../shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';
// 导入LazyServiceResolver用于解决循环依赖
import { LazyServiceResolver } from '../../../../shared/infrastructure/di/base/service-factory';
import { getContainer } from '../../../../shared/infrastructure/di/container';

export interface RealTradingSignal {
  id: string;
  symbol: string;
  signalType: 'BUY' | 'SELL' | 'HOLD' | 'NO_SIGNAL';
  strength: number; // 1-10
  confidence: number; // 0-1

  // 策略执行相关数据
  strategyUsed: string;
  entryPrice?: number;
  stopLoss?: number;
  takeProfit?: number;
  positionSize?: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';

  // 执行上下文
  executionContext: {
    marketConditions: string;
    riskAssessment: string;
    userProfile: any;
  };
  
  // 策略分析结果
  strategyAnalysis: {
    recommendation: string;
    confidence: number;
    reasoning: string[];
    keyFactors: string[];
  };

  // 数据质量（简化）
  dataQuality: {
    overall: number;
    sources: string[];
    freshness: number;
  };
  
  timestamp: Date;
  expiresAt: Date;
}

@injectable()
export class RealSignalGenerationService {
  private readonly logger: Logger;
  private readonly dataIntegrationService: RealDataIntegrationService;
  private readonly dynamicWeightingService: IDynamicWeightingService;
  private readonly patternRecognitionService: IPatternRecognitionService;
  private readonly multiTimeframeService: IMultiTimeframeService;
  private readonly technicalIndicatorCalculator: IUnifiedTechnicalIndicatorCalculator;
  private readonly lazyServiceResolver: LazyServiceResolver;
  private productionSignalService: any; // 使用any类型避免直接引用，防止循环依赖

  constructor(
    @inject(TYPES.Logger) logger: Logger,
    @inject(new LazyServiceIdentifer(() => TYPES.MarketData.RealDataIntegrationService)) dataIntegrationService: RealDataIntegrationService,
    @inject(new LazyServiceIdentifer(() => TYPES.Shared.DynamicWeightingService)) dynamicWeightingService: IDynamicWeightingService,
    @inject(new LazyServiceIdentifer(() => TYPES.Shared.PatternRecognitionService)) patternRecognitionService: IPatternRecognitionService,
    @inject(new LazyServiceIdentifer(() => TYPES.Shared.MultiTimeframeService)) multiTimeframeService: IMultiTimeframeService,
    @inject(new LazyServiceIdentifer(() => TYPES.Shared.UnifiedTechnicalIndicatorCalculator)) technicalIndicatorCalculator: IUnifiedTechnicalIndicatorCalculator
  ) {
    this.logger = logger;
    this.dataIntegrationService = dataIntegrationService;
    this.dynamicWeightingService = dynamicWeightingService;
    this.patternRecognitionService = patternRecognitionService;
    this.multiTimeframeService = multiTimeframeService;
    
    // 初始化LazyServiceResolver
    const containerManager = getContainer();
    this.lazyServiceResolver = LazyServiceResolver.getInstance(containerManager.getContainer());
    this.technicalIndicatorCalculator = technicalIndicatorCalculator;
  }
  
  /**
   * 初始化生产信号服务
   * 延迟加载ProductionSignalService以解决循环依赖
   */
  private async initializeProductionSignalService(): Promise<void> {
    if (!this.productionSignalService) {
      try {
        // 使用LazyServiceResolver延迟加载ProductionSignalService
        // const productionServiceIdentifier = this.lazyServiceResolver.createLazyServiceIdentifier(
        //   TYPES.TradingAnalysis.ProductionSignalService
        // );
        // this.productionSignalService = await productionServiceIdentifier();
        // 暂时使用简单的实例化
        this.productionSignalService = null;
        this.logger.info('成功初始化ProductionSignalService');
      } catch (error) {
        this.logger.error('初始化ProductionSignalService失败', { error: error.message });
        // 不抛出错误，允许在没有ProductionSignalService的情况下继续运行
      }
    }
  }

  /**
   * 生成BTC的真实交易信号
   */
  async generateBTCSignal(): Promise<RealTradingSignal> {
    this.logger.info('开始生成BTC真实交易信号');

    try {
      // 获取集成的真实数据
      const integratedData = await this.dataIntegrationService.getBTCIntegratedData();

      // 生成真实信号
      const signal = await this.createRealSignal(integratedData);
      
      this.logger.info('BTC真实交易信号生成完成', {
        signalType: signal.signalType,
        strength: signal.strength,
        confidence: signal.confidence,
        dataQuality: signal.dataQuality.overall
      });

      return signal;
    } catch (error) {
      this.logger.error('生成BTC真实交易信号失败', { error });
      throw new Error(`生成真实交易信号失败: ${error}`);
    }
  }

  /**
   * 创建策略执行信号
   */
  private async createRealSignal(data: IntegratedMarketData): Promise<RealTradingSignal> {
    // 尝试初始化ProductionSignalService，但不阻止信号生成流程
    await this.initializeProductionSignalService();
    
    const signalId = `strategy-signal-${Date.now()}`;
    const timestamp = new Date();
    const expiresAt = new Date(timestamp.getTime() + 15 * 60 * 1000); // 15分钟后过期

    // 根据市场数据选择策略
    const strategy = this.selectStrategy(data);

    // 执行策略分析
    const strategyAnalysis = await this.executeStrategyAnalysis(data, strategy);

    // 计算仓位大小和风险参数
    const riskParams = this.calculateRiskParameters(data, strategy);

    // 确定信号类型和强度
    const { signalType, strength } = this.determineSignalTypeAndStrength(data.overallAssessment);

    return {
      id: signalId,
      symbol: data.symbol,
      signalType,
      strength,
      confidence: data.overallAssessment.confidence / 100,
      strategyUsed: strategy,
      entryPrice: (data as any).currentPrice || 0,
      stopLoss: riskParams.stopLoss,
      takeProfit: riskParams.takeProfit,
      positionSize: riskParams.positionSize,
      riskLevel: riskParams.riskLevel,
      executionContext: {
        marketConditions: (data.overallAssessment as any).marketCondition || 'UNKNOWN',
        riskAssessment: data.overallAssessment.riskLevel || 'MEDIUM',
        userProfile: null // 将从user-config获取
      },
      strategyAnalysis: {
        recommendation: strategyAnalysis.recommendation,
        confidence: strategyAnalysis.confidence,
        reasoning: strategyAnalysis.reasoning,
        keyFactors: strategyAnalysis.keyFactors
      },
      dataQuality: {
        overall: data.dataQuality.overallQuality,
        sources: data.dataQuality.missingSources || [],
        freshness: data.dataQuality.dataFreshness
      },
      timestamp,
      expiresAt
    };
  }

  /**
   * 选择交易策略
   */
  private selectStrategy(data: IntegratedMarketData): string {
    // 根据市场条件选择策略
    const marketCondition = (data.overallAssessment as any)?.marketCondition || 'UNKNOWN';

    switch (marketCondition) {
      case 'TRENDING':
        return 'TREND_FOLLOWING';
      case 'RANGING':
        return 'MEAN_REVERSION';
      case 'VOLATILE':
        return 'BREAKOUT';
      default:
        return 'BALANCED';
    }
  }

  /**
   * 执行策略分析
   */
  private async executeStrategyAnalysis(data: IntegratedMarketData, strategy: string): Promise<{
    recommendation: string;
    confidence: number;
    reasoning: string[];
    keyFactors: string[];
  }> {
    const reasoning: string[] = [];
    const keyFactors: string[] = [];

    // 基于策略类型进行分析
    switch (strategy) {
      case 'TREND_FOLLOWING':
        reasoning.push('趋势跟随策略：基于技术指标确认趋势方向');
        keyFactors.push('技术指标', '趋势强度', '成交量');
        break;
      case 'MEAN_REVERSION':
        reasoning.push('均值回归策略：寻找价格偏离均值的机会');
        keyFactors.push('价格偏离度', '支撑阻力位', '波动率');
        break;
      default:
        reasoning.push('平衡策略：综合考虑多种因素');
        keyFactors.push('技术面', '基本面', '市场情绪');
    }

    return {
      recommendation: data.overallAssessment?.recommendation || 'HOLD',
      confidence: data.overallAssessment?.confidence || 0.5,
      reasoning,
      keyFactors
    };
  }

  /**
   * 计算风险参数
   */
  private calculateRiskParameters(data: IntegratedMarketData, strategy: string): {
    stopLoss?: number;
    takeProfit?: number;
    positionSize?: number;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  } {
    const currentPrice = (data as any).currentPrice || 0;
    const riskLevel = data.overallAssessment?.riskLevel === 'HIGH' ? 'HIGH' :
                     data.overallAssessment?.riskLevel === 'LOW' ? 'LOW' : 'MEDIUM';

    // 根据策略和风险级别计算参数
    let stopLossPercent = 0.02; // 默认2%
    let takeProfitPercent = 0.04; // 默认4%

    switch (strategy) {
      case 'TREND_FOLLOWING':
        stopLossPercent = 0.03;
        takeProfitPercent = 0.06;
        break;
      case 'MEAN_REVERSION':
        stopLossPercent = 0.015;
        takeProfitPercent = 0.03;
        break;
    }

    return {
      stopLoss: currentPrice * (1 - stopLossPercent),
      takeProfit: currentPrice * (1 + takeProfitPercent),
      positionSize: 0.1, // 默认10%仓位
      riskLevel
    };
  }

  /**
   * 处理技术信号 - 保留用于向后兼容
   */


  /**
   * 确定信号类型和强度
   */
  private determineSignalTypeAndStrength(overallAssessment: any): {
    signalType: 'BUY' | 'SELL' | 'HOLD';
    strength: number;
  } {
    const recommendation = overallAssessment?.recommendation || 'HOLD';
    const confidence = overallAssessment?.confidence || 0.5;

    let signalType: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
    let strength = Math.round(confidence * 10);

    if (recommendation.includes('BUY')) {
      signalType = 'BUY';
    } else if (recommendation.includes('SELL')) {
      signalType = 'SELL';
    }

    return { signalType, strength };
  }







}

export default RealSignalGenerationService;
