/**
 * 仓位大小计算器服务
 * 根据风险管理规则和用户配置计算合适的仓位大小
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import {
  IPositionSizeCalculator,
  RiskManagementInput,
  StrategyExecutionContext
} from '../interfaces/trading-strategy.interface';
import { UserProfileEntity } from '../entities/user-profile';
import { TradingSignalEntity } from '../entities/trading-signal';

/**
 * 仓位计算参数
 */
interface PositionCalculationParams {
  accountBalance: number;
  riskPerTrade: number; // 每笔交易风险百分比
  stopLossDistance: number; // 止损距离（价格点数）
  entryPrice: number;
  leverage?: number;
  maxPositionSize?: number; // 最大仓位限制
  minPositionSize?: number; // 最小仓位限制
}

/**
 * 仓位计算结果
 */
interface PositionCalculationResult {
  positionSize: number;
  positionValue: number;
  riskAmount: number;
  riskPercentage: number;
  leverageUsed: number;
  marginRequired: number;
  calculationMethod: string;
  warnings: string[];
  metadata: {
    accountBalance: number;
    entryPrice: number;
    stopLossPrice: number;
    stopLossDistance: number;
    maxRiskAmount: number;
    appliedLimits: string[];
  };
}

@injectable()
export class PositionSizeCalculator implements IPositionSizeCalculator {
  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger
  ) {}

  /**
   * 计算仓位大小
   */
  calculatePositionSize(
    context: StrategyExecutionContext,
    userProfile: UserProfileEntity,
    riskManagement: RiskManagementInput,
    signal: TradingSignalEntity
  ): number {
    const result = this.calculatePositionSizeDetailed(
      context,
      userProfile,
      riskManagement,
      signal
    );
    
    return result.positionSize;
  }

  /**
   * 详细计算仓位大小
   */
  calculatePositionSizeDetailed(
    context: StrategyExecutionContext,
    userProfile: UserProfileEntity,
    riskManagement: RiskManagementInput,
    signal: TradingSignalEntity
  ): PositionCalculationResult {
    this.logger.info('开始计算仓位大小', {
      symbol: context.symbol,
      userId: userProfile.userId,
      signalType: signal.action,
      entryPrice: signal.entryPrice,
      stopLoss: signal.stopLoss
    });

    // 构建计算参数
    const params = this.buildCalculationParams(
      context,
      userProfile,
      riskManagement,
      signal
    );

    // 执行计算
    const result = this.performPositionCalculation(params);

    this.logger.info('仓位大小计算完成', {
      positionSize: result.positionSize,
      positionValue: result.positionValue,
      riskAmount: result.riskAmount,
      riskPercentage: result.riskPercentage,
      method: result.calculationMethod,
      warnings: result.warnings
    });

    return result;
  }

  /**
   * 构建计算参数
   */
  private buildCalculationParams(
    context: StrategyExecutionContext,
    userProfile: UserProfileEntity,
    riskManagement: RiskManagementInput,
    signal: TradingSignalEntity
  ): PositionCalculationParams {
    // 获取账户余额
    const accountBalance = (context as any).accountBalance || 10000; // 默认值

    // 计算每笔交易风险百分比
    const baseRiskPerTrade = this.calculateBaseRiskPerTrade(userProfile);
    const adjustedRiskPerTrade = this.adjustRiskForSignalConfidence(
      baseRiskPerTrade,
      signal.confidence,
      riskManagement
    );

    // 计算止损距离
    const stopLossDistance = Math.abs(signal.entryPrice - (signal.stopLoss || signal.entryPrice * 0.95));

    // 获取杠杆
    const leverage = this.determineLeverage(userProfile, context);

    // 获取仓位限制
    const { maxPositionSize, minPositionSize } = this.getPositionLimits(
      accountBalance,
      userProfile,
      riskManagement
    );

    return {
      accountBalance,
      riskPerTrade: adjustedRiskPerTrade,
      stopLossDistance,
      entryPrice: signal.entryPrice,
      leverage,
      maxPositionSize,
      minPositionSize
    };
  }

  /**
   * 执行仓位计算
   */
  private performPositionCalculation(
    params: PositionCalculationParams
  ): PositionCalculationResult {
    const warnings: string[] = [];
    const appliedLimits: string[] = [];

    // 计算最大风险金额
    const maxRiskAmount = params.accountBalance * (params.riskPerTrade / 100);

    // 基于风险的仓位计算（Kelly公式变种）
    let positionSize = this.calculateRiskBasedPosition(
      maxRiskAmount,
      params.stopLossDistance,
      params.entryPrice
    );

    // 应用杠杆调整
    if (params.leverage && params.leverage > 1) {
      positionSize = positionSize * params.leverage;
    }

    // 应用仓位限制
    const originalPositionSize = positionSize;
    
    if (params.maxPositionSize && positionSize > params.maxPositionSize) {
      positionSize = params.maxPositionSize;
      appliedLimits.push('最大仓位限制');
      warnings.push(`仓位被限制在最大值: ${params.maxPositionSize}`);
    }

    if (params.minPositionSize && positionSize < params.minPositionSize) {
      positionSize = params.minPositionSize;
      appliedLimits.push('最小仓位限制');
      warnings.push(`仓位被调整到最小值: ${params.minPositionSize}`);
    }

    // 确保仓位不为负数或零
    if (positionSize <= 0) {
      positionSize = params.minPositionSize || 0.01;
      warnings.push('仓位计算结果无效，使用最小仓位');
    }

    // 计算相关指标
    const positionValue = positionSize * params.entryPrice;
    const actualRiskAmount = positionSize * params.stopLossDistance;
    const actualRiskPercentage = (actualRiskAmount / params.accountBalance) * 100;
    const leverageUsed = params.leverage || 1;
    const marginRequired = positionValue / leverageUsed;
    const stopLossPrice = params.entryPrice - params.stopLossDistance;

    // 风险检查
    if (actualRiskPercentage > params.riskPerTrade * 1.1) {
      warnings.push(`实际风险 ${actualRiskPercentage.toFixed(2)}% 超过目标风险 ${params.riskPerTrade}%`);
    }

    if (marginRequired > params.accountBalance * 0.8) {
      warnings.push('保证金需求过高，可能影响账户安全');
    }

    return {
      positionSize: Math.round(positionSize * 100) / 100, // 保留2位小数
      positionValue: Math.round(positionValue * 100) / 100,
      riskAmount: Math.round(actualRiskAmount * 100) / 100,
      riskPercentage: Math.round(actualRiskPercentage * 100) / 100,
      leverageUsed,
      marginRequired: Math.round(marginRequired * 100) / 100,
      calculationMethod: 'RISK_BASED_KELLY',
      warnings,
      metadata: {
        accountBalance: params.accountBalance,
        entryPrice: params.entryPrice,
        stopLossPrice,
        stopLossDistance: params.stopLossDistance,
        maxRiskAmount,
        appliedLimits
      }
    };
  }

  /**
   * 计算基础风险百分比
   */
  private calculateBaseRiskPerTrade(userProfile: UserProfileEntity): number {
    const riskToleranceMap = {
      'CONSERVATIVE': 1.0, // 1%
      'BALANCED': 2.0,     // 2%
      'AGGRESSIVE': 3.0    // 3%
    };

    let baseRisk = riskToleranceMap[userProfile.riskTolerance] || 2.0;

    // 根据投资期限调整
    if (userProfile.investmentHorizon === 'SHORT') {
      baseRisk *= 0.8; // 短期交易降低风险
    } else if (userProfile.investmentHorizon === 'LONG') {
      baseRisk *= 1.2; // 长期投资可以承受更高风险
    }

    // 根据交易风格调整
    if (userProfile.tradingStyle === 'MOMENTUM') {
      baseRisk *= 1.1; // 动量交易略微增加风险
    } else if (userProfile.tradingStyle === 'MEAN_REVERSION') {
      baseRisk *= 0.9; // 均值回归策略降低风险
    }

    return Math.min(5.0, Math.max(0.5, baseRisk)); // 限制在0.5%-5%之间
  }

  /**
   * 根据信号信心度调整风险
   */
  private adjustRiskForSignalConfidence(
    baseRisk: number,
    confidence: number,
    riskManagement: RiskManagementInput
  ): number {
    // 信心度调整系数
    let confidenceMultiplier = 1.0;

    if (confidence >= 0.8) {
      confidenceMultiplier = 1.2; // 高信心度增加仓位
    } else if (confidence >= 0.6) {
      confidenceMultiplier = 1.0; // 中等信心度保持不变
    } else if (confidence >= 0.4) {
      confidenceMultiplier = 0.8; // 低信心度减少仓位
    } else {
      confidenceMultiplier = 0.5; // 极低信心度大幅减少仓位
    }

    // 考虑风险管理输入
    if (riskManagement.riskScore > 0.7) {
      confidenceMultiplier *= 0.7; // 高风险环境降低仓位
    } else if (riskManagement.riskScore < 0.3) {
      confidenceMultiplier *= 1.1; // 低风险环境略微增加仓位
    }

    const adjustedRisk = baseRisk * confidenceMultiplier;
    return Math.min(5.0, Math.max(0.1, adjustedRisk));
  }

  /**
   * 确定杠杆倍数
   */
  private determineLeverage(
    userProfile: UserProfileEntity,
    context: StrategyExecutionContext
  ): number {
    // 基础杠杆根据风险偏好
    const baseLeverageMap = {
      'CONSERVATIVE': 1,
      'BALANCED': 2,
      'AGGRESSIVE': 3
    };

    let leverage = baseLeverageMap[userProfile.riskTolerance] || 1;

    // 根据市场条件调整
    const { volatility } = context.marketConditions;
    if (volatility === 'HIGH') {
      leverage = Math.max(1, leverage - 1); // 高波动性降低杠杆
    } else if (volatility === 'LOW') {
      leverage = Math.min(5, leverage + 1); // 低波动性可以增加杠杆
    }

    return leverage;
  }

  /**
   * 获取仓位限制
   */
  private getPositionLimits(
    accountBalance: number,
    userProfile: UserProfileEntity,
    riskManagement: RiskManagementInput
  ): { maxPositionSize: number; minPositionSize: number } {
    // 最大仓位限制（基于账户余额的百分比）
    const maxPositionPercentageMap = {
      'CONSERVATIVE': 0.1,  // 10%
      'BALANCED': 0.2,      // 20%
      'AGGRESSIVE': 0.3     // 30%
    };

    const maxPositionPercentage = maxPositionPercentageMap[userProfile.riskTolerance] || 0.2;
    const maxPositionValue = accountBalance * maxPositionPercentage;
    
    // 假设平均价格为100来计算最大仓位数量（实际应该用当前价格）
    const averagePrice = 100;
    const maxPositionSize = maxPositionValue / averagePrice;

    // 最小仓位限制
    const minPositionSize = 0.01;

    return {
      maxPositionSize,
      minPositionSize
    };
  }

  /**
   * 基于风险的仓位计算（Kelly公式变种）
   */
  private calculateRiskBasedPosition(
    maxRiskAmount: number,
    stopLossDistance: number,
    entryPrice: number
  ): number {
    if (stopLossDistance <= 0) {
      return 0;
    }

    // 基本公式：仓位大小 = 最大风险金额 / 每股风险
    const riskPerShare = stopLossDistance;
    const positionSize = maxRiskAmount / riskPerShare;

    return positionSize;
  }

  /**
   * 验证仓位计算结果
   */
  validatePositionSize(
    positionSize: number,
    context: StrategyExecutionContext,
    userProfile: UserProfileEntity
  ): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 基本验证
    if (positionSize <= 0) {
      errors.push('仓位大小必须大于0');
    }

    if (isNaN(positionSize) || !isFinite(positionSize)) {
      errors.push('仓位大小计算结果无效');
    }

    // 账户余额检查
    const accountBalance = (context as any).accountBalance || 10000;
    const positionValue = positionSize * 100; // 假设价格
    
    if (positionValue > accountBalance) {
      errors.push('仓位价值超过账户余额');
    }

    if (positionValue > accountBalance * 0.5) {
      warnings.push('仓位价值超过账户余额的50%，风险较高');
    }

    // 风险检查
    const riskAmount = positionSize * 5; // 假设5%的止损
    const riskPercentage = (riskAmount / accountBalance) * 100;
    
    if (riskPercentage > 10) {
      errors.push('单笔交易风险超过10%，过于危险');
    }

    if (riskPercentage > 5) {
      warnings.push('单笔交易风险超过5%，建议降低仓位');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 获取仓位计算建议
   */
  getPositionSizeRecommendations(
    context: StrategyExecutionContext,
    userProfile: UserProfileEntity,
    riskManagement: RiskManagementInput
  ): {
    conservative: number;
    balanced: number;
    aggressive: number;
    recommended: number;
    reasoning: string[];
  } {
    const baseParams = {
      accountBalance: (context as any).accountBalance || 10000,
      stopLossDistance: 5, // 假设5点止损
      entryPrice: 100, // 假设价格
      leverage: 1
    };

    // 计算不同风险级别的仓位
    const conservative = this.calculateRiskBasedPosition(
      baseParams.accountBalance * 0.01, // 1%风险
      baseParams.stopLossDistance,
      baseParams.entryPrice
    );

    const balanced = this.calculateRiskBasedPosition(
      baseParams.accountBalance * 0.02, // 2%风险
      baseParams.stopLossDistance,
      baseParams.entryPrice
    );

    const aggressive = this.calculateRiskBasedPosition(
      baseParams.accountBalance * 0.03, // 3%风险
      baseParams.stopLossDistance,
      baseParams.entryPrice
    );

    // 根据用户画像推荐
    const recommendedMap = {
      'CONSERVATIVE': conservative,
      'BALANCED': balanced,
      'AGGRESSIVE': aggressive
    };

    const recommended = recommendedMap[userProfile.riskTolerance] || balanced;

    const reasoning = [
      `基于您的${userProfile.riskTolerance}风险偏好`,
      `考虑当前市场${context.marketConditions.volatility}波动性`,
      `结合${userProfile.tradingStyle}交易风格`,
      `推荐仓位大小为${recommended.toFixed(2)}`
    ];

    return {
      conservative: Math.round(conservative * 100) / 100,
      balanced: Math.round(balanced * 100) / 100,
      aggressive: Math.round(aggressive * 100) / 100,
      recommended: Math.round(recommended * 100) / 100,
      reasoning
    };
  }
}