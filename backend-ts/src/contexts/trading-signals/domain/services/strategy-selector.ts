/**
 * 策略选择器服务
 * 根据市场条件和用户画像智能选择最适合的交易策略
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import {
  ITradingStrategy,
  IStrategySelector,
  StrategyExecutionContext
} from '../interfaces/trading-strategy.interface';
import { UserProfileEntity } from '../entities/user-profile';
import { UserProfile } from '../entities/user-profile';

/**
 * 策略评估结果
 */
interface StrategyEvaluation {
  strategy: ITradingStrategy;
  score: number;
  reasons: string[];
  suitabilityFactors: {
    marketConditionMatch: number; // 0-100
    riskLevelMatch: number; // 0-100
    tradingStyleMatch: number; // 0-100
    timeHorizonMatch: number; // 0-100
  };
}

@injectable()
export class StrategySelector implements IStrategySelector {
  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger
  ) {}

  /**
   * 选择最适合的策略
   */
  selectStrategy(
    context: StrategyExecutionContext,
    userProfile: UserProfileEntity,
    availableStrategies: ITradingStrategy[]
  ): ITradingStrategy | null {
    if (!availableStrategies || availableStrategies.length === 0) {
      this.logger.warn('没有可用的交易策略');
      return null;
    }

    this.logger.info('开始策略选择', {
      symbol: context.symbol,
      userId: userProfile.userId,
      availableStrategiesCount: availableStrategies.length,
      marketConditions: context.marketConditions,
      userRiskTolerance: userProfile.riskTolerance,
      userTradingStyle: userProfile.tradingStyle
    });

    // 评估所有可用策略
    const evaluations = availableStrategies
      .filter(strategy => strategy.isApplicable(context, userProfile as any))
      .map(strategy => this.evaluateStrategyDetailed(strategy, context, userProfile))
      .sort((a, b) => b.score - a.score);

    if (evaluations.length === 0) {
      this.logger.warn('没有适用的交易策略', {
        symbol: context.symbol,
        userId: userProfile.userId,
        marketConditions: context.marketConditions
      });
      return null;
    }

    const selectedStrategy = evaluations[0];
    
    this.logger.info('策略选择完成', {
      selectedStrategy: selectedStrategy.strategy.name,
      score: selectedStrategy.score,
      reasons: selectedStrategy.reasons,
      suitabilityFactors: selectedStrategy.suitabilityFactors
    });

    return selectedStrategy.strategy;
  }

  /**
   * 动态策略选择（扩展功能）
   * 基于市场条件、用户画像和风险评估动态选择最适合的交易策略
   */
  async selectOptimalStrategyDynamic(
    context: StrategyExecutionContext,
    userProfile: UserProfileEntity,
    availableStrategies: ITradingStrategy[],
    marketConditions?: {
      trend: 'STRONG_BULL' | 'BULL' | 'NEUTRAL' | 'BEAR' | 'STRONG_BEAR';
      volatility: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME';
      liquidity: 'LOW' | 'MEDIUM' | 'HIGH';
      volume: 'LOW' | 'MEDIUM' | 'HIGH';
    },
    trendData?: any,
    riskData?: any
  ): Promise<{
    primaryStrategy: ITradingStrategy | null;
    alternativeStrategies: ITradingStrategy[];
    confidence: number;
    reasoning: string[];
    suitabilityScore: number;
    expectedPerformance: {
      winRate: number;
      riskRewardRatio: number;
      maxDrawdown: number;
      averageReturn: number;
    };
    riskWarnings: string[];
    optimizationSuggestions: string[];
  }> {
    this.logger.info('开始动态策略选择', {
      symbol: context.symbol,
      userId: userProfile.userId,
      marketTrend: marketConditions?.trend,
      userRiskTolerance: userProfile.riskTolerance
    });

    // 使用现有的详细评估方法
    const evaluation = this.evaluateStrategyDetailed(context, userProfile, availableStrategies);

    const primaryStrategy = (evaluation as any).selectedStrategy;
    const alternativeStrategies = (evaluation as any).evaluations
      ?.slice(1, 4)
      ?.map((e: any) => e.strategy) || [];

    // 计算置信度
    const confidence = (evaluation as any).summary?.topScore / 100 || 0.5;

    // 生成推理
    const reasoning = primaryStrategy ?
      (evaluation as any).evaluations?.[0]?.reasons || ['策略评估完成'] :
      ['没有找到适合的策略'];

    // 计算适合度分数
    const suitabilityScore = (evaluation as any).summary?.topScore || 50;

    // 计算预期表现（基于策略类型的历史数据）
    const expectedPerformance = this.calculateExpectedPerformance(
      primaryStrategy,
      marketConditions,
      userProfile,
      trendData,
      riskData
    );

    // 生成风险警告
    const riskWarnings = this.generateRiskWarnings(primaryStrategy, riskData, userProfile);

    // 生成优化建议
    const optimizationSuggestions = this.generateOptimizationSuggestions(
      primaryStrategy,
      marketConditions,
      userProfile
    );

    this.logger.info('动态策略选择完成', {
      primaryStrategy: primaryStrategy?.name,
      confidence,
      suitabilityScore
    });

    return {
      primaryStrategy,
      alternativeStrategies,
      confidence,
      reasoning,
      suitabilityScore,
      expectedPerformance,
      riskWarnings,
      optimizationSuggestions
    };
  }

  /**
   * 评估策略适用性得分
   */
  evaluateStrategy(
    strategy: ITradingStrategy,
    context: StrategyExecutionContext,
    userProfile: UserProfile
  ): number {
    const evaluation = this.evaluateStrategyDetailed(strategy, context, userProfile);
    return evaluation.score;
  }

  /**
   * 详细评估策略
   */
  private evaluateStrategyDetailed(
    strategy: ITradingStrategy,
    context: StrategyExecutionContext,
    userProfile: UserProfile
  ): StrategyEvaluation {
    const reasons: string[] = [];
    
    // 1. 市场条件匹配度评估
    const marketConditionMatch = this.evaluateMarketConditionMatch(
      strategy,
      context,
      reasons
    );

    // 2. 风险等级匹配度评估
    const riskLevelMatch = this.evaluateRiskLevelMatch(
      strategy,
      userProfile,
      reasons
    );

    // 3. 交易风格匹配度评估
    const tradingStyleMatch = this.evaluateTradingStyleMatch(
      strategy,
      userProfile,
      reasons
    );

    // 4. 时间范围匹配度评估
    const timeHorizonMatch = this.evaluateTimeHorizonMatch(
      strategy,
      userProfile,
      context,
      reasons
    );

    // 计算加权总分
    const weights = {
      marketCondition: 0.3,
      riskLevel: 0.3,
      tradingStyle: 0.25,
      timeHorizon: 0.15
    };

    const totalScore = 
      marketConditionMatch * weights.marketCondition +
      riskLevelMatch * weights.riskLevel +
      tradingStyleMatch * weights.tradingStyle +
      timeHorizonMatch * weights.timeHorizon;

    return {
      strategy,
      score: Math.round(totalScore),
      reasons,
      suitabilityFactors: {
        marketConditionMatch,
        riskLevelMatch,
        tradingStyleMatch,
        timeHorizonMatch
      }
    };
  }

  /**
   * 评估市场条件匹配度
   */
  private evaluateMarketConditionMatch(
    strategy: ITradingStrategy,
    context: StrategyExecutionContext,
    reasons: string[]
  ): number {
    let score = 50; // 基础分

    const { volatility, liquidity, trend } = context.marketConditions;
    const suitableConditions = strategy.suitableMarketConditions;

    // 波动性匹配
    if (suitableConditions.includes(`VOLATILITY_${volatility}`)) {
      score += 20;
      reasons.push(`策略适合${volatility}波动性市场`);
    } else if (suitableConditions.includes('VOLATILITY_ANY')) {
      score += 10;
      reasons.push('策略适应各种波动性环境');
    }

    // 流动性匹配
    if (suitableConditions.includes(`LIQUIDITY_${liquidity}`)) {
      score += 15;
      reasons.push(`策略适合${liquidity}流动性市场`);
    } else if (suitableConditions.includes('LIQUIDITY_ANY')) {
      score += 8;
      reasons.push('策略适应各种流动性环境');
    }

    // 趋势匹配
    if (suitableConditions.includes(`TREND_${trend}`)) {
      score += 15;
      reasons.push(`策略适合${trend}趋势市场`);
    } else if (suitableConditions.includes('TREND_ANY')) {
      score += 8;
      reasons.push('策略适应各种趋势环境');
    }

    // 特殊市场条件加分
    if (volatility === 'HIGH' && suitableConditions.includes('HIGH_VOLATILITY_SPECIALIST')) {
      score += 10;
      reasons.push('策略专门针对高波动性市场优化');
    }

    if (trend === 'STRONG_BULL' && suitableConditions.includes('BULL_MARKET_SPECIALIST')) {
      score += 10;
      reasons.push('策略专门针对牛市优化');
    }

    if (trend === 'STRONG_BEAR' && suitableConditions.includes('BEAR_MARKET_SPECIALIST')) {
      score += 10;
      reasons.push('策略专门针对熊市优化');
    }

    return Math.min(100, Math.max(0, score));
  }

  /**
   * 评估风险等级匹配度
   */
  private evaluateRiskLevelMatch(
    strategy: ITradingStrategy,
    userProfile: UserProfile,
    reasons: string[]
  ): number {
    const strategyRisk = strategy.riskLevel;
    const userRisk = userProfile.riskTolerance;

    // 风险等级映射
    const riskLevelMap = {
      'LOW': 1,
      'MEDIUM': 2,
      'HIGH': 3
    };

    const userRiskMap = {
      'CONSERVATIVE': 1,
      'BALANCED': 2,
      'AGGRESSIVE': 3
    };

    const strategyRiskLevel = riskLevelMap[strategyRisk];
    const userRiskLevel = userRiskMap[userRisk];
    const riskDifference = Math.abs(strategyRiskLevel - userRiskLevel);

    let score = 100;

    switch (riskDifference) {
      case 0:
        reasons.push('策略风险等级与用户偏好完全匹配');
        break;
      case 1:
        score = 70;
        reasons.push('策略风险等级与用户偏好基本匹配');
        break;
      case 2:
        score = 30;
        reasons.push('策略风险等级与用户偏好存在较大差异');
        break;
      default:
        score = 0;
        reasons.push('策略风险等级与用户偏好严重不匹配');
    }

    // 保守用户的额外检查
    if (userRisk === 'CONSERVATIVE' && strategyRisk === 'HIGH') {
      score = Math.min(score, 20);
      reasons.push('高风险策略不适合保守型用户');
    }

    // 激进用户的额外检查
    if (userRisk === 'AGGRESSIVE' && strategyRisk === 'LOW') {
      score = Math.max(score, 60); // 激进用户可以接受低风险策略
      reasons.push('激进型用户可以使用低风险策略进行稳健投资');
    }

    return score;
  }

  /**
   * 评估交易风格匹配度
   */
  private evaluateTradingStyleMatch(
    strategy: ITradingStrategy,
    userProfile: UserProfile,
    reasons: string[]
  ): number {
    const userStyle = userProfile.tradingStyle;
    const strategyName = strategy.name.toLowerCase();

    let score = 50; // 基础分

    // 直接匹配
    if ((userStyle as any) === 'TREND_FOLLOWING' && strategyName.includes('trend')) {
      score = 100;
      reasons.push('策略与用户趋势跟随风格完美匹配');
    } else if ((userStyle as any) === 'MEAN_REVERSION' && strategyName.includes('reversion')) {
      score = 100;
      reasons.push('策略与用户均值回归风格完美匹配');
    } else if (userStyle === 'MOMENTUM' && strategyName.includes('momentum')) {
      score = 100;
      reasons.push('策略与用户动量交易风格完美匹配');
    } else if (userStyle === 'MIXED') {
      score = 80;
      reasons.push('用户偏好混合策略，当前策略适用');
    }

    // 部分匹配
    if (score === 50) {
      if (userStyle === 'TREND_FOLLOWING' && (strategyName.includes('momentum') || strategyName.includes('breakout'))) {
        score = 75;
        reasons.push('策略与趋势跟随风格部分匹配');
      } else if (userStyle === 'MEAN_REVERSION' && strategyName.includes('contrarian')) {
        score = 75;
        reasons.push('策略与均值回归风格部分匹配');
      }
    }

    return score;
  }

  /**
   * 评估时间范围匹配度
   */
  private evaluateTimeHorizonMatch(
    strategy: ITradingStrategy,
    userProfile: UserProfile,
    context: StrategyExecutionContext,
    reasons: string[]
  ): number {
    const userHorizon = userProfile.investmentHorizon;
    const strategyName = strategy.name.toLowerCase();

    let score = 70; // 基础分

    // 根据策略名称推断时间范围
    if (userHorizon === 'SHORT') {
      if (strategyName.includes('scalp') || strategyName.includes('intraday') || strategyName.includes('short')) {
        score = 100;
        reasons.push('策略适合短期交易');
      } else if (strategyName.includes('swing') || strategyName.includes('medium')) {
        score = 60;
        reasons.push('中期策略可用于短期交易，但效果可能不佳');
      } else if (strategyName.includes('long') || strategyName.includes('position')) {
        score = 30;
        reasons.push('长期策略不适合短期交易');
      }
    } else if (userHorizon === 'MEDIUM') {
      if (strategyName.includes('swing') || strategyName.includes('medium')) {
        score = 100;
        reasons.push('策略适合中期交易');
      } else if (strategyName.includes('short') || strategyName.includes('long')) {
        score = 70;
        reasons.push('策略可适应中期交易');
      }
    } else if (userHorizon === 'LONG') {
      if (strategyName.includes('long') || strategyName.includes('position') || strategyName.includes('trend')) {
        score = 100;
        reasons.push('策略适合长期投资');
      } else if (strategyName.includes('medium') || strategyName.includes('swing')) {
        score = 75;
        reasons.push('中期策略可用于长期投资组合');
      } else if (strategyName.includes('short') || strategyName.includes('scalp')) {
        score = 40;
        reasons.push('短期策略不适合长期投资');
      }
    }

    return score;
  }

  /**
   * 获取策略选择的详细报告
   */
  getSelectionReport(
    context: StrategyExecutionContext,
    userProfile: UserProfile,
    availableStrategies: ITradingStrategy[]
  ): {
    selectedStrategy: ITradingStrategy | null;
    evaluations: StrategyEvaluation[];
    summary: {
      totalStrategies: number;
      applicableStrategies: number;
      topScore: number;
      averageScore: number;
    };
  } {
    const evaluations = availableStrategies
      .filter(strategy => strategy.isApplicable(context, userProfile))
      .map(strategy => this.evaluateStrategyDetailed(strategy, context, userProfile))
      .sort((a, b) => b.score - a.score);

    const selectedStrategy = evaluations.length > 0 ? evaluations[0].strategy : null;
    const scores = evaluations.map(e => e.score);
    const topScore = scores.length > 0 ? Math.max(...scores) : 0;
    const averageScore = scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;

    return {
      selectedStrategy,
      evaluations,
      summary: {
        totalStrategies: availableStrategies.length,
        applicableStrategies: evaluations.length,
        topScore,
        averageScore: Math.round(averageScore)
      }
    };
  }

  /**
   * 计算预期表现
   */
  private calculateExpectedPerformance(
    strategy: ITradingStrategy | null,
    marketConditions?: any,
    userProfile?: UserProfile,
    trendData?: any,
    riskData?: any
  ): {
    winRate: number;
    riskRewardRatio: number;
    maxDrawdown: number;
    averageReturn: number;
  } {
    if (!strategy) {
      return {
        winRate: 0,
        riskRewardRatio: 1,
        maxDrawdown: 1,
        averageReturn: 0
      };
    }

    // 基于策略类型的基础表现指标
    const baseMetrics = this.getBasePerformanceMetrics(strategy.name);

    // 根据市场条件调整
    const marketAdjustment = this.getMarketAdjustment(marketConditions, trendData);

    // 根据风险水平调整
    const riskAdjustment = this.getRiskAdjustment(riskData);

    return {
      winRate: Math.max(0.3, Math.min(0.8, baseMetrics.winRate * marketAdjustment * riskAdjustment)),
      riskRewardRatio: Math.max(1.0, Math.min(4.0, baseMetrics.riskRewardRatio * marketAdjustment)),
      maxDrawdown: Math.max(0.05, Math.min(0.3, baseMetrics.maxDrawdown / riskAdjustment)),
      averageReturn: Math.max(0.01, Math.min(0.5, baseMetrics.averageReturn * marketAdjustment * riskAdjustment))
    };
  }

  /**
   * 获取基础表现指标
   */
  private getBasePerformanceMetrics(strategyName: string): {
    winRate: number;
    riskRewardRatio: number;
    maxDrawdown: number;
    averageReturn: number;
  } {
    const metricsMap: Record<string, any> = {
      'TrendFollowingStrategy': { winRate: 0.55, riskRewardRatio: 2.5, maxDrawdown: 0.15, averageReturn: 0.12 },
      'MeanReversionStrategy': { winRate: 0.65, riskRewardRatio: 1.8, maxDrawdown: 0.10, averageReturn: 0.08 },
      'MomentumStrategy': { winRate: 0.50, riskRewardRatio: 2.0, maxDrawdown: 0.18, averageReturn: 0.15 },
      'BreakoutStrategy': { winRate: 0.45, riskRewardRatio: 2.8, maxDrawdown: 0.20, averageReturn: 0.18 },
      'ScalpingStrategy': { winRate: 0.60, riskRewardRatio: 1.2, maxDrawdown: 0.08, averageReturn: 0.05 },
      'SwingStrategy': { winRate: 0.58, riskRewardRatio: 2.2, maxDrawdown: 0.12, averageReturn: 0.10 }
    };

    return metricsMap[strategyName] || { winRate: 0.5, riskRewardRatio: 2.0, maxDrawdown: 0.15, averageReturn: 0.10 };
  }

  /**
   * 获取市场调整因子
   */
  private getMarketAdjustment(marketConditions?: any, trendData?: any): number {
    let adjustment = 1.0;

    if (trendData?.trendStrength > 0.7) {
      adjustment *= 1.1;
    } else if (trendData?.trendStrength < 0.3) {
      adjustment *= 0.9;
    }

    if (marketConditions?.volatility === 'HIGH') {
      adjustment *= 0.9;
    } else if (marketConditions?.volatility === 'LOW') {
      adjustment *= 1.05;
    }

    return adjustment;
  }

  /**
   * 获取风险调整因子
   */
  private getRiskAdjustment(riskData?: any): number {
    if (!riskData) return 1.0;

    switch (riskData.riskLevel) {
      case 'LOW': return 1.1;
      case 'MEDIUM': return 1.0;
      case 'HIGH': return 0.8;
      case 'CRITICAL': return 0.6;
      default: return 1.0;
    }
  }

  /**
   * 生成风险警告
   */
  private generateRiskWarnings(
    strategy: ITradingStrategy | null,
    riskData?: any,
    userProfile?: UserProfile
  ): string[] {
    const warnings: string[] = [];

    if (riskData?.riskLevel === 'HIGH' || riskData?.riskLevel === 'CRITICAL') {
      warnings.push('⚠️ 当前市场风险较高，建议降低仓位或暂停交易');
    }

    if (!strategy) {
      warnings.push('⚠️ 未找到适合的策略，建议等待更好的市场条件');
    }

    if (userProfile?.riskTolerance === 'CONSERVATIVE' && strategy?.name.includes('Scalping')) {
      warnings.push('⚠️ 剥头皮策略风险较高，与您的保守风险偏好不匹配');
    }

    return warnings;
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationSuggestions(
    strategy: ITradingStrategy | null,
    marketConditions?: any,
    userProfile?: UserProfile
  ): string[] {
    const suggestions: string[] = [];

    if (marketConditions?.volatility === 'HIGH') {
      suggestions.push('💡 高波动性环境下建议缩小仓位，加强风险控制');
    }

    if (marketConditions?.liquidity === 'LOW') {
      suggestions.push('💡 低流动性环境下建议避免大额交易，分批执行');
    }

    if (!strategy) {
      suggestions.push('💡 建议等待更明确的市场信号');
    }

    suggestions.push('💡 建议结合多个时间框架确认信号');
    suggestions.push('💡 严格执行止损策略，控制单笔交易风险');

    return suggestions;
  }
}