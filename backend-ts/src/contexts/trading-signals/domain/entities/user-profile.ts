/**
 * 🔥 重要：使用统一的UserProfile接口，避免重复定义
 * 从User-Config系统导入统一的用户画像定义
 */

// 导入统一的用户画像接口和类型
import {
  UserProfile,
  RiskTolerance,
  InvestmentHorizon,
  TradingStyle as UserConfigTradingStyle
} from '../../../../contexts/user-config/domain/entities/user-profile';

// 重新导出以供外部使用
export {
  UserProfile,
  RiskTolerance,
  InvestmentHorizon,
  UserConfigTradingStyle
};

// Trading-Signals系统特定的交易风格类型
export type TradingSignalsTradingStyle = 'TREND_FOLLOWING' | 'MEAN_REVERSION' | 'MOMENTUM' | 'MIXED';

// 为了向后兼容，提供类型映射
export type UserProfileInterface = UserProfile;

/**
 * 用户画像实体包装器
 * 为Trading-Signals系统提供便捷的用户画像操作方法
 */
export class UserProfileEntity {
  constructor(private readonly profile: UserProfile) {}

  get userId(): string {
    return this.profile.userId;
  }

  get riskTolerance(): RiskTolerance {
    return this.profile.riskTolerance;
  }

  get targetReturn(): number {
    return this.profile.targetReturn;
  }

  get maxAcceptableDrawdown(): number {
    return this.profile.maxAcceptableDrawdown;
  }

  get investmentHorizon(): InvestmentHorizon {
    return this.profile.investmentHorizon;
  }

  get tradingStyle(): TradingSignalsTradingStyle {
    return this.adaptTradingStyle(this.profile.tradingStyle);
  }

  /**
   * 适配User-Config的交易风格到Trading-Signals的交易风格
   */
  private adaptTradingStyle(userConfigStyle: UserConfigTradingStyle): TradingSignalsTradingStyle {
    switch (userConfigStyle) {
      case 'SCALPING':
      case 'DAY_TRADING':
        return 'MOMENTUM'; // 短期交易映射到动量策略
      case 'SWING_TRADING':
        return 'MEAN_REVERSION'; // 波段交易映射到均值回归
      case 'POSITION_TRADING':
        return 'TREND_FOLLOWING'; // 持仓交易映射到趋势跟随
      case 'MIXED':
      default:
        return 'MIXED'; // 混合风格保持不变
    }
  }

  get preferredAssets(): string[] {
    return this.profile.preferredAssets;
  }

  get maxPositionSize(): number {
    return this.profile.maxPositionSize;
  }

  get enableStopLoss(): boolean {
    return this.profile.enableStopLoss;
  }

  get enableTakeProfit(): boolean {
    return this.profile.enableTakeProfit;
  }

  get customThresholds() {
    return this.profile.customThresholds;
  }

  /**
   * 根据风险偏好获取动态阈值
   */
  getTrendStrengthThreshold(): number {
    if (this.profile.customThresholds?.trendStrengthThreshold) {
      return this.profile.customThresholds.trendStrengthThreshold;
    }

    switch (this.profile.riskTolerance) {
      case 'CONSERVATIVE':
        return 0.85; // 保守型需要更强的趋势信号
      case 'BALANCED':
        return 0.7;
      case 'AGGRESSIVE':
        return 0.6; // 激进型可以接受较弱的趋势信号
      default:
        return 0.7;
    }
  }

  /**
   * 根据风险偏好获取风险评分阈值
   */
  getRiskScoreThreshold(): number {
    if (this.profile.customThresholds?.riskScoreThreshold) {
      return this.profile.customThresholds.riskScoreThreshold;
    }

    switch (this.profile.riskTolerance) {
      case 'CONSERVATIVE':
        return 0.3; // 保守型只接受低风险
      case 'BALANCED':
        return 0.5;
      case 'AGGRESSIVE':
        return 0.7; // 激进型可以接受较高风险
      default:
        return 0.5;
    }
  }

  /**
   * 根据风险偏好获取信心度阈值
   */
  getConfidenceThreshold(): number {
    if (this.profile.customThresholds?.confidenceThreshold) {
      return this.profile.customThresholds.confidenceThreshold;
    }

    switch (this.profile.riskTolerance) {
      case 'CONSERVATIVE':
        return 0.8; // 保守型需要高信心度
      case 'BALANCED':
        return 0.65;
      case 'AGGRESSIVE':
        return 0.5; // 激进型可以接受较低信心度
      default:
        return 0.65;
    }
  }

  /**
   * 计算基于风险偏好的仓位调整系数
   */
  getPositionSizeMultiplier(): number {
    switch (this.profile.riskTolerance) {
      case 'CONSERVATIVE':
        return 0.5; // 保守型减少仓位
      case 'BALANCED':
        return 0.75;
      case 'AGGRESSIVE':
        return 1.0; // 激进型使用最大仓位
      default:
        return 0.75;
    }
  }

  /**
   * 验证用户画像的有效性
   */
  validate(): boolean {
    return (
      this.profile.userId &&
      this.profile.targetReturn >= 0 && this.profile.targetReturn <= 2 &&
      this.profile.maxAcceptableDrawdown >= 0 && this.profile.maxAcceptableDrawdown <= 1 &&
      this.profile.maxPositionSize > 0 && this.profile.maxPositionSize <= 1
    );
  }

  toPlainObject(): UserProfile {
    return { ...this.profile } as UserProfile;
  }
}