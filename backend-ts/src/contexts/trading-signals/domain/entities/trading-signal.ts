/**
 * 交易信号实体
 * 包含完整的交易决策信息和执行参数
 */

export interface TradingSignalData {
  id: string;
  userId: string;
  symbol: string;
  timestamp: Date;
  
  // 信号核心信息
  action: 'BUY' | 'SELL' | 'HOLD' | 'NO_SIGNAL';
  strength: number; // 信号强度 1-10
  confidence: number; // 信心度 0-100%
  
  // 价格信息
  currentPrice: number;
  targetPrice?: number;
  stopLoss?: number;
  takeProfit?: number;
  
  // 仓位信息
  recommendedPositionSize: number; // 建议仓位大小 0-1
  maxPositionSize: number; // 最大仓位限制 0-1
  riskAmount?: number; // 风险金额
  
  // 风险信息
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
  expectedReturn: number; // 预期收益率
  maxDrawdown: number; // 最大回撤预期
  
  // 时间信息
  timeHorizon: 'SHORT' | 'MEDIUM' | 'LONG';
  timeframe?: string; // 时间周期
  validUntil?: Date; // 信号有效期
  
  // 策略信息
  strategyName: string;
  strategyVersion: string;
  
  // 输入数据来源
  trendAnalysisId?: string;
  riskAssessmentId?: string;
  userProfileVersion: string;
  
  // 分析详情
  reasoning: string[];
  keyFactors: string[];
  riskWarnings: string[];
  
  // 执行状态
  status: 'PENDING' | 'EXECUTED' | 'CANCELLED' | 'EXPIRED';
  executedAt?: Date;
  executionPrice?: number;
  
  // 元数据
  processingTime: number; // 生成耗时(ms)
  dataQuality: number; // 数据质量评分 0-100
  
  // 风险指标
  riskMetrics?: {
    riskScore: number;
    volatility: number;
    maxDrawdown: number;
    sharpeRatio: number;
  };
  
  // 元数据
  metadata?: {
    generatedAt: Date;
    expiresAt?: Date;
    priority: string;
  };
  
  // 策略详情
  strategy?: {
    name: string;
    version: string;
    reasoning: string[];
  };
  
  createdAt: Date;
  updatedAt: Date;
}

export class TradingSignalEntity {
  constructor(private readonly signalData: TradingSignalData) {}

  get id(): string {
    return this.signalData.id;
  }

  get userId(): string {
    return this.signalData.userId;
  }

  get symbol(): string {
    return this.signalData.symbol;
  }

  get action(): 'BUY' | 'SELL' | 'HOLD' | 'NO_SIGNAL' {
    return this.signalData.action;
  }

  get strength(): number {
    return this.signalData.strength;
  }

  get confidence(): number {
    return this.signalData.confidence;
  }

  get currentPrice(): number {
    return this.signalData.currentPrice;
  }

  get recommendedPositionSize(): number {
    return this.signalData.recommendedPositionSize;
  }

  get riskLevel(): 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH' {
    return this.signalData.riskLevel;
  }

  get strategyName(): string {
    return this.signalData.strategyName;
  }

  get status(): 'PENDING' | 'EXECUTED' | 'CANCELLED' | 'EXPIRED' {
    return this.signalData.status;
  }

  get timeHorizon(): 'SHORT' | 'MEDIUM' | 'LONG' {
    return this.signalData.timeHorizon;
  }

  get timeframe(): string | undefined {
    return this.signalData.timeframe;
  }

  get createdAt(): Date {
    return this.signalData.createdAt;
  }

  get updatedAt(): Date {
    return this.signalData.updatedAt;
  }

  get timestamp(): Date {
    return this.signalData.timestamp;
  }

  get entryPrice(): number | undefined {
    return this.signalData.currentPrice;
  }

  get targetPrice(): number | undefined {
    return this.signalData.targetPrice;
  }

  get stopLoss(): number | undefined {
    return this.signalData.stopLoss;
  }

  get takeProfit(): number | undefined {
    return this.signalData.takeProfit;
  }

  get positionSize(): number {
    return this.signalData.recommendedPositionSize;
  }

  get riskAmount(): number | undefined {
    return this.signalData.riskAmount;
  }

  get expectedReturn(): number {
    return this.signalData.expectedReturn;
  }

  get maxDrawdown(): number {
    return this.signalData.maxDrawdown;
  }

  get reasoning(): string[] {
    return this.signalData.reasoning;
  }

  get keyFactors(): string[] {
    return this.signalData.keyFactors;
  }

  get riskWarnings(): string[] {
    return this.signalData.riskWarnings;
  }

  get riskMetrics(): { riskScore: number; volatility: number; maxDrawdown: number; sharpeRatio: number } | undefined {
    return this.signalData.riskMetrics;
  }

  get metadata(): { generatedAt: Date; expiresAt?: Date; priority: string } | undefined {
    return this.signalData.metadata;
  }

  get strategy(): { name: string; version: string; reasoning: string[] } | undefined {
    return this.signalData.strategy;
  }

  /**
   * 检查信号是否仍然有效
   */
  isValid(): boolean {
    if (this.signalData.status !== 'PENDING') {
      return false;
    }

    if (this.signalData.validUntil && new Date() > this.signalData.validUntil) {
      return false;
    }

    return true;
  }

  /**
   * 检查信号是否为买入信号
   */
  isBuySignal(): boolean {
    return this.signalData.action === 'BUY';
  }

  /**
   * 检查信号是否为卖出信号
   */
  isSellSignal(): boolean {
    return this.signalData.action === 'SELL';
  }

  /**
   * 检查信号是否为持有信号
   */
  isHoldSignal(): boolean {
    return this.signalData.action === 'HOLD';
  }

  /**
   * 检查信号是否为无信号
   */
  isNoSignal(): boolean {
    return this.signalData.action === 'NO_SIGNAL';
  }

  /**
   * 获取风险调整后的仓位大小
   */
  getRiskAdjustedPositionSize(): number {
    const baseSize = this.signalData.recommendedPositionSize;
    const riskMultiplier = this.getRiskMultiplier();
    const adjustedSize = baseSize * riskMultiplier;
    
    return Math.min(adjustedSize, this.signalData.maxPositionSize);
  }

  /**
   * 根据风险等级获取风险调整系数
   */
  private getRiskMultiplier(): number {
    switch (this.signalData.riskLevel) {
      case 'LOW':
        return 1.0;
      case 'MEDIUM':
        return 0.8;
      case 'HIGH':
        return 0.6;
      case 'VERY_HIGH':
        return 0.4;
      default:
        return 0.8;
    }
  }

  /**
   * 计算预期风险收益比
   */
  getRiskReturnRatio(): number {
    if (this.signalData.maxDrawdown === 0) {
      return Infinity;
    }
    return this.signalData.expectedReturn / this.signalData.maxDrawdown;
  }

  /**
   * 标记信号为已执行
   */
  markAsExecuted(executionPrice: number): void {
    this.signalData.status = 'EXECUTED';
    this.signalData.executedAt = new Date();
    this.signalData.executionPrice = executionPrice;
    this.signalData.updatedAt = new Date();
  }

  /**
   * 标记信号为已取消
   */
  markAsCancelled(): void {
    this.signalData.status = 'CANCELLED';
    this.signalData.updatedAt = new Date();
  }

  /**
   * 标记信号为已过期
   */
  markAsExpired(): void {
    this.signalData.status = 'EXPIRED';
    this.signalData.updatedAt = new Date();
  }

  /**
   * 验证信号数据的完整性
   */
  validate(): boolean {
    return (
      !!this.signalData.id &&
      !!this.signalData.userId &&
      !!this.signalData.symbol &&
      !!this.signalData.action &&
      this.signalData.strength >= 1 && this.signalData.strength <= 10 &&
      this.signalData.confidence >= 0 && this.signalData.confidence <= 100 &&
      this.signalData.currentPrice > 0 &&
      this.signalData.recommendedPositionSize >= 0 && this.signalData.recommendedPositionSize <= 1 &&
      this.signalData.maxPositionSize >= 0 && this.signalData.maxPositionSize <= 1 &&
      !!this.signalData.strategyName &&
      !!this.signalData.userProfileVersion
    );
  }

  /**
   * 获取信号摘要信息
   */
  getSummary(): string {
    return `${this.signalData.action} ${this.signalData.symbol} at ${this.signalData.currentPrice} (Strength: ${this.signalData.strength}/10, Confidence: ${this.signalData.confidence}%)`;
  }

  toPlainObject(): TradingSignalData {
    return { ...this.signalData };
  }
}

/**
 * 交易信号构建器
 */
export class TradingSignalBuilder {
  private signalData: Partial<TradingSignalData> = {
    timestamp: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    status: 'PENDING'
  };

  setId(id: string): this {
    this.signalData.id = id;
    return this;
  }

  setUserId(userId: string): this {
    this.signalData.userId = userId;
    return this;
  }

  setSymbol(symbol: string): this {
    this.signalData.symbol = symbol;
    return this;
  }

  setAction(action: 'BUY' | 'SELL' | 'HOLD'): this {
    this.signalData.action = action;
    return this;
  }

  setStrength(strength: number): this {
    this.signalData.strength = Math.max(1, Math.min(10, strength));
    return this;
  }

  setConfidence(confidence: number): this {
    this.signalData.confidence = Math.max(0, Math.min(100, confidence));
    return this;
  }

  setCurrentPrice(price: number): this {
    this.signalData.currentPrice = price;
    return this;
  }

  setTargetPrice(price: number): this {
    this.signalData.targetPrice = price;
    return this;
  }

  setStopLoss(price: number): this {
    this.signalData.stopLoss = price;
    return this;
  }

  setTakeProfit(price: number): this {
    this.signalData.takeProfit = price;
    return this;
  }

  setPositionSize(size: number): this {
    this.signalData.recommendedPositionSize = Math.max(0, Math.min(1, size));
    return this;
  }

  setMaxPositionSize(size: number): this {
    this.signalData.maxPositionSize = Math.max(0, Math.min(1, size));
    return this;
  }

  setRiskAmount(amount: number): this {
    this.signalData.riskAmount = amount;
    return this;
  }

  setRiskLevel(level: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH'): this {
    this.signalData.riskLevel = level;
    return this;
  }

  setTimeHorizon(horizon: 'SHORT' | 'MEDIUM' | 'LONG'): this {
    this.signalData.timeHorizon = horizon;
    return this;
  }

  setTimeframe(timeframe: string): this {
    this.signalData.timeframe = timeframe;
    return this;
  }

  setStrategy(name: string, version: string): this {
    this.signalData.strategyName = name;
    this.signalData.strategyVersion = version;
    return this;
  }

  setUserProfileVersion(version: string): this {
    this.signalData.userProfileVersion = version;
    return this;
  }

  setReasoning(reasoning: string[]): this {
    this.signalData.reasoning = reasoning;
    return this;
  }

  setKeyFactors(factors: string[]): this {
    this.signalData.keyFactors = factors;
    return this;
  }

  setRiskWarnings(warnings: string[]): this {
    this.signalData.riskWarnings = warnings;
    return this;
  }

  setRiskMetrics(riskMetrics: { riskScore: number; volatility: number; maxDrawdown: number; sharpeRatio: number }): this {
    this.signalData.riskMetrics = riskMetrics;
    return this;
  }

  setMetadata(metadata: { generatedAt: Date; expiresAt?: Date; priority: string }): this {
    this.signalData.metadata = metadata;
    return this;
  }

  setStrategyDetails(strategy: { name: string; version: string; reasoning: string[] }): this {
    this.signalData.strategy = strategy;
    return this;
  }

  setProcessingTime(time: number): this {
    this.signalData.processingTime = time;
    return this;
  }

  setDataQuality(quality: number): this {
    this.signalData.dataQuality = Math.max(0, Math.min(100, quality));
    return this;
  }

  setValidUntil(date: Date): this {
    this.signalData.validUntil = date;
    return this;
  }

  build(): TradingSignalEntity {
    if (!this.signalData.id || !this.signalData.userId || !this.signalData.symbol || 
        !this.signalData.action || !this.signalData.strategyName || !this.signalData.userProfileVersion) {
      throw new Error('Missing required fields for TradingSignal');
    }

    const signal = new TradingSignalEntity(this.signalData as TradingSignalData);
    
    if (!signal.validate()) {
      throw new Error('Invalid TradingSignal data');
    }

    return signal;
  }
}