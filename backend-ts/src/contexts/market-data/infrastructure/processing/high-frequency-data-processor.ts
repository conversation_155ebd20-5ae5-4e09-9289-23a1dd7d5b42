/**
 * 高频数据处理器
 * 优化数据缓冲、批处理和背压处理机制
 */

import { injectable } from 'inversify';
import { EventEmitter } from 'events';
// import { MarketData } from '../websocket/real-websocket-manager';

interface MarketData {
  symbol: string;
  price: number;
  volume: number;
  timestamp: Date;
  exchange?: string;
}
import { ILogger, createLogger } from '../../../../shared/infrastructure/logging/logger-factory';

export interface ProcessedData {
  symbol: string;
  price: number;
  volume: number;
  timestamp: Date;
  exchange: string;
  processed: boolean;
  processingTime: number;
  batchId?: string;
}

export interface ProcessingMetrics {
  totalProcessed: number;
  averageProcessingTime: number;
  batchesProcessed: number;
  droppedMessages: number;
  backpressureEvents: number;
  bufferUtilization: number;
}

export interface ProcessingConfig {
  batchSize: number;
  batchInterval: number;
  maxBufferSize: number;
  backpressureThreshold: number;
  processingTimeout: number;
}

@injectable()
export class HighFrequencyDataProcessor extends EventEmitter {
  private readonly logger: ILogger;
  private dataBuffer: MarketData[] = [];
  private processingInterval: NodeJS.Timeout | null = null;
  private isProcessing = false;
  private isRunning = false;
  
  // 配置
  private config: ProcessingConfig = {
    batchSize: 100,
    batchInterval: 100, // 100ms
    maxBufferSize: 10000,
    backpressureThreshold: 8000,
    processingTimeout: 5000
  };
  
  // 指标
  private metrics: ProcessingMetrics = {
    totalProcessed: 0,
    averageProcessingTime: 0,
    batchesProcessed: 0,
    droppedMessages: 0,
    backpressureEvents: 0,
    bufferUtilization: 0
  };
  
  // 性能监控
  private processingTimes: number[] = [];
  private lastMetricsReset = Date.now();

  constructor(logger?: ILogger) {
    super();
    this.logger = logger || createLogger('HighFrequencyDataProcessor');
  }

  /**
   * 启动处理器
   */
  async startProcessing(config?: Partial<ProcessingConfig>): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('数据处理器已在运行');
      return;
    }

    // 更新配置
    if (config) {
      this.config = { ...this.config, ...config };
    }

    this.isRunning = true;
    this.resetMetrics();
    
    // 启动批处理定时器
    this.processingInterval = setInterval(async () => {
      await this.processBatch();
    }, this.config.batchInterval);
    
    this.logger.info('高频数据处理器已启动', { config: this.config });
  }

  /**
   * 停止处理器
   */
  async stopProcessing(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    
    // 处理剩余数据
    if (this.dataBuffer.length > 0) {
      await this.processBatch();
    }
    
    this.logger.info('高频数据处理器已停止', { 
      finalMetrics: this.getMetrics() 
    });
  }

  /**
   * 添加数据到处理队列
   */
  async addData(data: MarketData): Promise<boolean> {
    if (!this.isRunning) {
      this.logger.warn('处理器未运行，数据被丢弃');
      return false;
    }

    // 检查缓冲区大小
    if (this.dataBuffer.length >= this.config.maxBufferSize) {
      this.metrics.droppedMessages++;
      this.logger.warn('缓冲区已满，丢弃数据', { 
        bufferSize: this.dataBuffer.length,
        maxSize: this.config.maxBufferSize 
      });
      return false;
    }

    // 检查背压
    if (this.dataBuffer.length >= this.config.backpressureThreshold) {
      this.metrics.backpressureEvents++;
      this.emit('backpressure', {
        bufferSize: this.dataBuffer.length,
        threshold: this.config.backpressureThreshold
      });
      
      // 立即处理一批数据以减轻压力
      if (!this.isProcessing) {
        setImmediate(() => this.processBatch());
      }
    }

    this.dataBuffer.push(data);
    this.updateBufferUtilization();
    
    // 如果缓冲区达到批处理大小，立即处理
    if (this.dataBuffer.length >= this.config.batchSize && !this.isProcessing) {
      setImmediate(() => this.processBatch());
    }

    return true;
  }

  /**
   * 批处理数据
   */
  private async processBatch(): Promise<void> {
    if (this.isProcessing || this.dataBuffer.length === 0) {
      return;
    }

    this.isProcessing = true;
    const startTime = Date.now();
    const batchId = `batch_${Date.now()}_${require('uuid').v4().replace(/-/g, '').substring(0, 9)}`;
    
    try {
      // 提取批次数据
      const batchSize = Math.min(this.config.batchSize, this.dataBuffer.length);
      const batch = this.dataBuffer.splice(0, batchSize);
      
      if (batch.length === 0) {
        return;
      }

      this.logger.debug('开始处理批次', { 
        batchId, 
        batchSize: batch.length,
        remainingBuffer: this.dataBuffer.length 
      });

      // 处理批次数据
      const processedData = await this.processDataBatch(batch, batchId);
      
      const processingTime = Date.now() - startTime;
      this.updateProcessingMetrics(processingTime, batch.length);
      
      // 发送处理完成事件
      this.emit('batchProcessed', {
        batchId,
        processedData,
        processingTime,
        batchSize: batch.length
      });

      this.logger.debug('批次处理完成', { 
        batchId, 
        processingTime,
        processedCount: processedData.length 
      });

    } catch (error) {
      this.logger.error('批次处理失败', { 
        batchId,
        error: error instanceof Error ? error.message : String(error) 
      });
      
      this.emit('processingError', {
        batchId,
        error,
        timestamp: new Date()
      });
    } finally {
      this.isProcessing = false;
      this.updateBufferUtilization();
    }
  }

  /**
   * 处理数据批次
   */
  private async processDataBatch(batch: MarketData[], batchId: string): Promise<ProcessedData[]> {
    const processedData: ProcessedData[] = [];
    const startTime = Date.now();

    // 按交易所分组处理
    const exchangeGroups = this.groupByExchange(batch);
    
    for (const [exchange, data] of exchangeGroups) {
      const exchangeProcessed = await this.processExchangeData(data, exchange, batchId);
      processedData.push(...exchangeProcessed);
    }

    // 去重处理（基于symbol和timestamp）
    const deduplicatedData = this.deduplicateData(processedData);
    
    // 数据验证
    const validatedData = this.validateData(deduplicatedData);
    
    const processingTime = Date.now() - startTime;
    
    // 发送处理后的数据
    for (const data of validatedData) {
      this.emit('dataProcessed', data);
    }

    return validatedData;
  }

  /**
   * 按交易所分组
   */
  private groupByExchange(data: MarketData[]): Map<string, MarketData[]> {
    const groups = new Map<string, MarketData[]>();
    
    for (const item of data) {
      const exchange = item.exchange;
      if (!groups.has(exchange)) {
        groups.set(exchange, []);
      }
      groups.get(exchange)!.push(item);
    }
    
    return groups;
  }

  /**
   * 处理交易所数据
   */
  private async processExchangeData(
    data: MarketData[], 
    exchange: string, 
    batchId: string
  ): Promise<ProcessedData[]> {
    const processed: ProcessedData[] = [];
    const startTime = Date.now();
    
    for (const item of data) {
      const processedItem: ProcessedData = {
        ...item,
        exchange: item.exchange || 'unknown',
        processed: true,
        processingTime: Date.now() - startTime,
        batchId
      };
      
      // 数据标准化
      processedItem.price = this.normalizePrice(processedItem.price);
      processedItem.volume = this.normalizeVolume(processedItem.volume);
      
      processed.push(processedItem);
    }
    
    return processed;
  }

  /**
   * 去重数据
   */
  private deduplicateData(data: ProcessedData[]): ProcessedData[] {
    const seen = new Set<string>();
    const deduplicated: ProcessedData[] = [];
    
    for (const item of data) {
      const key = `${item.symbol}_${item.exchange}_${item.timestamp.getTime()}`;
      if (!seen.has(key)) {
        seen.add(key);
        deduplicated.push(item);
      }
    }
    
    return deduplicated;
  }

  /**
   * 验证数据
   */
  private validateData(data: ProcessedData[]): ProcessedData[] {
    return data.filter(item => {
      // 价格验证
      if (item.price <= 0 || !isFinite(item.price)) {
        this.logger.warn('无效价格数据', { 
          symbol: item.symbol, 
          price: item.price 
        });
        return false;
      }
      
      // 成交量验证
      if (item.volume < 0 || !isFinite(item.volume)) {
        this.logger.warn('无效成交量数据', { 
          symbol: item.symbol, 
          volume: item.volume 
        });
        return false;
      }
      
      // 时间戳验证
      const now = Date.now();
      const dataTime = item.timestamp.getTime();
      if (dataTime > now + 60000 || dataTime < now - 3600000) { // 未来1分钟或过去1小时
        this.logger.warn('无效时间戳', { 
          symbol: item.symbol, 
          timestamp: item.timestamp 
        });
        return false;
      }
      
      return true;
    });
  }

  /**
   * 标准化价格
   */
  private normalizePrice(price: number): number {
    return Math.round(price * 100) / 100; // 保留2位小数
  }

  /**
   * 标准化成交量
   */
  private normalizeVolume(volume: number): number {
    return Math.round(volume * 1000000) / 1000000; // 保留6位小数
  }

  /**
   * 更新处理指标
   */
  private updateProcessingMetrics(processingTime: number, batchSize: number): void {
    this.metrics.totalProcessed += batchSize;
    this.metrics.batchesProcessed++;
    
    this.processingTimes.push(processingTime);
    
    // 保持最近1000次处理时间
    if (this.processingTimes.length > 1000) {
      this.processingTimes = this.processingTimes.slice(-1000);
    }
    
    // 计算平均处理时间
    this.metrics.averageProcessingTime = 
      this.processingTimes.reduce((sum, time) => sum + time, 0) / this.processingTimes.length;
  }

  /**
   * 更新缓冲区利用率
   */
  private updateBufferUtilization(): void {
    this.metrics.bufferUtilization = 
      (this.dataBuffer.length / this.config.maxBufferSize) * 100;
  }

  /**
   * 重置指标
   */
  private resetMetrics(): void {
    this.metrics = {
      totalProcessed: 0,
      averageProcessingTime: 0,
      batchesProcessed: 0,
      droppedMessages: 0,
      backpressureEvents: 0,
      bufferUtilization: 0
    };
    this.processingTimes = [];
    this.lastMetricsReset = Date.now();
  }

  /**
   * 获取处理指标
   */
  getMetrics(): ProcessingMetrics & { 
    uptime: number;
    throughput: number;
    bufferSize: number;
  } {
    const uptime = Date.now() - this.lastMetricsReset;
    const throughput = this.metrics.totalProcessed / (uptime / 1000); // 每秒处理数
    
    return {
      ...this.metrics,
      uptime,
      throughput,
      bufferSize: this.dataBuffer.length
    };
  }

  /**
   * 获取配置
   */
  getConfig(): ProcessingConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ProcessingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.info('处理器配置已更新', { config: this.config });
  }

  /**
   * 获取缓冲区状态
   */
  getBufferStatus(): {
    size: number;
    maxSize: number;
    utilization: number;
    isBackpressure: boolean;
  } {
    return {
      size: this.dataBuffer.length,
      maxSize: this.config.maxBufferSize,
      utilization: this.metrics.bufferUtilization,
      isBackpressure: this.dataBuffer.length >= this.config.backpressureThreshold
    };
  }
}
