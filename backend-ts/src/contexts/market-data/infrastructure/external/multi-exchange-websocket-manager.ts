/**
 * 多源WebSocket协调器
 * 统一管理多个交易所的WebSocket连接，实现智能故障转移和连接健康监控
 * 重构后使用统一的WebSocket基础设施
 */

import { injectable } from 'inversify';
import { Logger } from 'winston';
import { createLogger, format, transports } from 'winston';
import {
  IExchangeWebSocketAdapter,
  IExchangeAdapterFactory
} from '../../domain/services/exchange-adapter';
import { TradingSymbol } from '../../domain/value-objects/trading-symbol';
import { Timeframe } from '../../domain/value-objects/timeframe';
import { IMultiExchangeWebSocketManager } from '../../domain/services/multi-exchange-websocket-manager.interface';
import {
  WebSocketConnectionManager,
  WebSocketSubscription,
  WebSocketConnectionState,
  WebSocketEventType,
  WebSocketLoadBalanceStrategy
} from '../websocket';
import { IWebSocketAdapter } from '../../../../shared/infrastructure/types/unified-interfaces';

/**
 * 连接状态
 */
enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  FAILED = 'failed'
}

/**
 * 交易所连接信息
 */
interface ExchangeConnection {
  name: string;
  adapter: IExchangeWebSocketAdapter;
  status: ConnectionStatus;
  priority: number;
  lastHeartbeat: number;
  errorCount: number;
  reconnectAttempts: number;
  subscriptions: Set<string>;
}

/**
 * 订阅信息
 */
interface SubscriptionInfo {
  id: string;
  type: 'ticker' | 'kline';
  symbols: TradingSymbol[];
  timeframe?: Timeframe;
  callback: (data: any) => void;
  activeExchanges: Set<string>;
  primaryExchange?: string;
}

/**
 * 健康检查配置
 */
interface HealthCheckConfig {
  interval: number;          // 检查间隔（毫秒）
  timeout: number;           // 超时时间（毫秒）
  maxErrors: number;         // 最大错误次数
  maxReconnectAttempts: number; // 最大重连次数
}

/**
 * 故障转移策略
 */
enum FailoverStrategy {
  PRIORITY_BASED = 'priorityBased',    // 基于优先级
  ROUND_ROBIN = 'roundRobin',          // 轮询
  QUALITY_BASED = 'qualityBased'       // 基于质量
}

@injectable()
export class MultiExchangeWebSocketManager implements IMultiExchangeWebSocketManager {
  private readonly logger: Logger;
  private readonly connectionManager: WebSocketConnectionManager;
  private readonly connections: Map<string, ExchangeConnection> = new Map();
  private readonly subscriptions: Map<string, SubscriptionInfo> = new Map();
  private readonly adapterFactory: IExchangeAdapterFactory;

  private healthCheckInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private restFallbackIntervals: Map<string, NodeJS.Timeout> = new Map();

  // 数据和错误回调函数
  private dataCallback: ((data: any) => void) | null = null;
  private errorCallback: ((error: Error) => void) | null = null;

  // 配置 - 优化健康检查参数
  private readonly healthCheckConfig: HealthCheckConfig = {
    interval: 60000,        // 60秒检查一次（减少频率）
    timeout: 45000,         // 45秒超时（更宽松）
    maxErrors: 10,          // 最大10次错误（增加容错）
    maxReconnectAttempts: 15 // 最大15次重连（增加重试）
  };

  private readonly failoverStrategy = FailoverStrategy.PRIORITY_BASED;

  // 交易所优先级配置（数字越小优先级越高）
  private readonly exchangePriorities: Record<string, number> = {
    'binance': 1,
    'okx': 2,
    'coinbase': 3,
    'kraken': 4,
    'huobi': 5
  };

  constructor(adapterFactory: IExchangeAdapterFactory) {
    this.logger = createLogger({
      level: 'info',
      format: format.combine(
        format.timestamp(),
        format.label({ label: 'MultiExchangeWebSocketManager' }),
        format.simple()
      ),
      transports: [new transports.Console()]
    });
    this.adapterFactory = adapterFactory;

    // 初始化连接管理器
    this.connectionManager = new WebSocketConnectionManager(
      this.logger as any,
      {
        maxConnections: 10,
        minConnections: 1,
        connectionTimeout: 10000,
        idleTimeout: 300000,
        healthCheckInterval: this.healthCheckConfig.interval
      },
      {
        strategy: WebSocketLoadBalanceStrategy.PRIORITY_BASED,
        priorities: this.exchangePriorities,
        healthCheckEnabled: true
      }
    );

    this.setupConnectionManagerEvents();
  }

  /**
   * 设置连接管理器事件
   */
  private setupConnectionManagerEvents(): void {
    this.connectionManager.on('connected', (data) => {
      this.logger.info('交易所适配器连接成功', { adapterId: data.connectionId });
      this.updateConnectionStatus(data.connectionId, ConnectionStatus.CONNECTED);
    });

    this.connectionManager.on('disconnected', (data) => {
      this.logger.warn('交易所适配器连接断开', { adapterId: data.connectionId });
      this.updateConnectionStatus(data.connectionId, ConnectionStatus.DISCONNECTED);
      this.handleConnectionFailure(data.connectionId).catch(error => {
        this.logger.error('处理连接失败时出错', { adapterId: data.connectionId, error });
      });
    });

    this.connectionManager.on('error', (data) => {
      this.logger.error('交易所适配器错误', {
        adapterId: data.connectionId,
        error: data.error?.message
      });
      this.updateConnectionStatus(data.connectionId, ConnectionStatus.FAILED);
      this.handleConnectionError(data.connectionId, data.error);
    });

    this.connectionManager.on('message', (data) => {
      if (this.dataCallback) {
        this.dataCallback(data.data);
      }
    });
  }

  /**
   * 更新连接状态
   */
  private updateConnectionStatus(adapterId: string, status: ConnectionStatus): void {
    for (const [exchangeName, connection] of this.connections) {
      if (connection.name === adapterId) {
        connection.status = status;
        connection.lastHeartbeat = Date.now();
        break;
      }
    }
  }



  /**
   * 处理连接错误
   */
  private handleConnectionError(adapterId: string, error?: Error): void {
    if (this.errorCallback && error) {
      this.errorCallback(error);
    }
  }

  /**
   * 启动管理器
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('WebSocket管理器已在运行');
      return;
    }

    this.isRunning = true;

    // 启动连接管理器
    await this.connectionManager.start();

    this.startHealthCheck();

    this.logger.info('多源WebSocket管理器已启动');
  }

  /**
   * 停止管理器
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    // 停止健康检查
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // 停止连接管理器
    await this.connectionManager.stop();

    this.connections.clear();
    this.subscriptions.clear();

    this.logger.info('多源WebSocket管理器已停止');
  }

  /**
   * 添加交易所连接
   */
  async addExchange(exchangeName: string): Promise<void> {
    if (this.connections.has(exchangeName)) {
      this.logger.warn('交易所连接已存在', { exchangeName });
      return;
    }

    try {
      const adapter = this.adapterFactory.createWebSocketAdapter(exchangeName);
      const priority = this.exchangePriorities[exchangeName.toLowerCase()] || 999;

      // 添加到连接管理器
      await this.connectionManager.addAdapter(adapter as any);
      
      const connection: ExchangeConnection = {
        name: exchangeName,
        adapter,
        status: ConnectionStatus.DISCONNECTED,
        priority,
        lastHeartbeat: Date.now(),
        errorCount: 0,
        reconnectAttempts: 0,
        subscriptions: new Set()
      };

      this.connections.set(exchangeName, connection);
      
      this.logger.info('添加交易所连接', { 
        exchangeName, 
        priority,
        totalConnections: this.connections.size 
      });

    } catch (error) {
      this.logger.error('添加交易所连接失败', {
        exchangeName,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 移除交易所连接
   */
  async removeExchange(exchangeName: string): Promise<void> {
    const connection = this.connections.get(exchangeName);
    if (!connection) {
      this.logger.warn('交易所连接不存在', { exchangeName });
      return;
    }

    await this.closeConnection(exchangeName);
    this.connections.delete(exchangeName);
    
    // 重新分配该交易所的订阅
    await this.redistributeSubscriptions(exchangeName);
    
    this.logger.info('移除交易所连接', { 
      exchangeName,
      remainingConnections: this.connections.size 
    });
  }

  /**
   * 订阅价格数据流
   */
  async subscribePriceStream(
    symbols: TradingSymbol[],
    callback: (data: any) => void,
    preferredExchanges?: string[]
  ): Promise<string> {
    const subscriptionId = `ticker_${Date.now()}_${require('uuid').v4().replace(/-/g, '').substring(0, 9)}`;
    
    const subscription: SubscriptionInfo = {
      id: subscriptionId,
      type: 'ticker',
      symbols,
      callback,
      activeExchanges: new Set()
    };

    // 选择最佳交易所
    const selectedExchanges = this.selectExchanges(preferredExchanges);
    if (selectedExchanges.length === 0) {
      throw new Error('没有可用的交易所连接');
    }

    // 在选定的交易所上订阅
    for (const exchangeName of selectedExchanges) {
      try {
        await this.subscribeOnExchange(exchangeName, subscription);
        subscription.activeExchanges.add(exchangeName);
        
        if (!subscription.primaryExchange) {
          subscription.primaryExchange = exchangeName;
        }
      } catch (error) {
        this.logger.error('在交易所订阅失败', {
          exchangeName,
          subscriptionId,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    if (subscription.activeExchanges.size === 0) {
      this.logger.warn('所有WebSocket订阅失败，启动REST降级模式', { subscriptionId });
      // 不抛出错误，而是启动REST降级模式
      this.startRestFallbackMode(subscriptionId, symbols, callback);
      // 创建一个虚拟订阅来维持系统运行
      subscription.activeExchanges.add('rest-fallback');
      subscription.primaryExchange = 'rest-fallback';
    }

    this.subscriptions.set(subscriptionId, subscription);
    
    this.logger.info('订阅价格数据流', {
      subscriptionId,
      symbols: symbols.map(s => s.symbol),
      activeExchanges: Array.from(subscription.activeExchanges),
      primaryExchange: subscription.primaryExchange
    });

    return subscriptionId;
  }

  /**
   * 订阅K线数据流
   */
  async subscribeKlineStream(
    symbol: TradingSymbol,
    timeframe: Timeframe,
    callback: (data: any) => void,
    preferredExchanges?: string[]
  ): Promise<string> {
    const subscriptionId = `kline_${symbol.symbol}_${timeframe.value}_${Date.now()}_${require('uuid').v4().replace(/-/g, '').substring(0, 9)}`;
    
    const subscription: SubscriptionInfo = {
      id: subscriptionId,
      type: 'kline',
      symbols: [symbol],
      timeframe,
      callback,
      activeExchanges: new Set()
    };

    // 选择最佳交易所
    const selectedExchanges = this.selectExchanges(preferredExchanges);
    if (selectedExchanges.length === 0) {
      throw new Error('没有可用的交易所连接');
    }

    // 在选定的交易所上订阅
    for (const exchangeName of selectedExchanges) {
      try {
        await this.subscribeKlineOnExchange(exchangeName, subscription);
        subscription.activeExchanges.add(exchangeName);
        
        if (!subscription.primaryExchange) {
          subscription.primaryExchange = exchangeName;
        }
      } catch (error) {
        this.logger.error('在交易所订阅K线失败', {
          exchangeName,
          subscriptionId,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    if (subscription.activeExchanges.size === 0) {
      throw new Error('所有交易所K线订阅都失败');
    }

    this.subscriptions.set(subscriptionId, subscription);
    
    this.logger.info('订阅K线数据流', {
      subscriptionId,
      symbol: symbol.symbol,
      timeframe: timeframe.value,
      activeExchanges: Array.from(subscription.activeExchanges),
      primaryExchange: subscription.primaryExchange
    });

    return subscriptionId;
  }

  /**
   * 取消订阅
   */
  async unsubscribe(subscriptionId: string): Promise<void> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) {
      this.logger.warn('订阅不存在', { subscriptionId });
      return;
    }

    // 在所有活跃交易所上取消订阅
    const unsubscribePromises = Array.from(subscription.activeExchanges).map(exchangeName =>
      this.unsubscribeFromExchange(exchangeName, subscriptionId)
    );

    await Promise.all(unsubscribePromises);
    this.subscriptions.delete(subscriptionId);

    this.logger.info('取消订阅', {
      subscriptionId,
      type: subscription.type,
      exchangeCount: subscription.activeExchanges.size
    });
  }

  /**
   * 取消所有订阅
   */
  async unsubscribeAll(): Promise<void> {
    const unsubscribePromises = Array.from(this.subscriptions.keys()).map(id =>
      this.unsubscribe(id)
    );

    await Promise.all(unsubscribePromises);
    this.logger.info('取消所有订阅');
  }



  /**
   * 获取订阅统计
   */
  getSubscriptionStats(): any {
    const stats = {
      totalSubscriptions: this.subscriptions.size,
      byType: { ticker: 0, kline: 0 },
      byExchange: {} as Record<string, number>
    };

    for (const subscription of this.subscriptions.values()) {
      stats.byType[subscription.type]++;

      for (const exchangeName of subscription.activeExchanges) {
        stats.byExchange[exchangeName] = (stats.byExchange[exchangeName] || 0) + 1;
      }
    }

    return stats;
  }

  /**
   * 选择交易所
   */
  private selectExchanges(preferredExchanges?: string[]): string[] {
    const availableConnections = Array.from(this.connections.values())
      .filter(conn => conn.status === ConnectionStatus.CONNECTED ||
                     conn.status === ConnectionStatus.DISCONNECTED)
      .sort((a, b) => a.priority - b.priority);

    if (preferredExchanges && preferredExchanges.length > 0) {
      // 优先使用指定的交易所
      const preferred = preferredExchanges.filter(name =>
        this.connections.has(name)
      );

      if (preferred.length > 0) {
        return preferred.slice(0, 2); // 最多选择2个
      }
    }

    // 根据策略选择交易所
    if (this.failoverStrategy === FailoverStrategy.PRIORITY_BASED) {
      return availableConnections.slice(0, 2).map(conn => conn.name);
    } else if (this.failoverStrategy === FailoverStrategy.QUALITY_BASED) {
      return availableConnections
        .filter(conn => conn.errorCount < this.healthCheckConfig.maxErrors)
        .slice(0, 2)
        .map(conn => conn.name);
    } else {
      return availableConnections.slice(0, 1).map(conn => conn.name);
    }
  }

  /**
   * 在指定交易所上订阅价格数据
   */
  private async subscribeOnExchange(
    exchangeName: string,
    subscription: SubscriptionInfo
  ): Promise<void> {
    const connection = this.connections.get(exchangeName);
    if (!connection) {
      throw new Error(`交易所连接不存在: ${exchangeName}`);
    }

    // 确保连接
    await this.ensureConnection(exchangeName);

    // 创建包装的回调函数
    const wrappedCallback = (data: any) => {
      // 添加数据源信息
      const enrichedData = {
        ...data,
        source: exchangeName,
        subscriptionId: subscription.id,
        timestamp: new Date(),
        isPrimary: subscription.primaryExchange === exchangeName
      };

      // 调用原始回调
      subscription.callback(enrichedData);

      // 调用全局数据回调
      this.triggerDataCallback(enrichedData);
    };

    // 订阅数据流
    await connection.adapter.subscribePriceStream(subscription.symbols, wrappedCallback);
    connection.subscriptions.add(subscription.id);
  }

  /**
   * 在指定交易所上订阅K线数据
   */
  private async subscribeKlineOnExchange(
    exchangeName: string,
    subscription: SubscriptionInfo
  ): Promise<void> {
    const connection = this.connections.get(exchangeName);
    if (!connection) {
      throw new Error(`交易所连接不存在: ${exchangeName}`);
    }

    if (!subscription.timeframe) {
      throw new Error('K线订阅缺少时间框架');
    }

    // 确保连接
    await this.ensureConnection(exchangeName);

    // 创建包装的回调函数
    const wrappedCallback = (data: any) => {
      // 添加数据源信息
      const enrichedData = {
        ...data,
        source: exchangeName,
        subscriptionId: subscription.id,
        timestamp: new Date(),
        isPrimary: subscription.primaryExchange === exchangeName
      };

      // 调用原始回调
      subscription.callback(enrichedData);

      // 调用全局数据回调
      this.triggerDataCallback(enrichedData);
    };

    // 订阅K线数据流
    await connection.adapter.subscribeKlineStream(
      subscription.symbols[0],
      subscription.timeframe,
      wrappedCallback
    );
    connection.subscriptions.add(subscription.id);
  }

  /**
   * 从交易所取消订阅
   */
  private async unsubscribeFromExchange(
    exchangeName: string,
    subscriptionId: string
  ): Promise<void> {
    const connection = this.connections.get(exchangeName);
    if (!connection) {
      this.logger.warn('交易所连接不存在', { exchangeName, subscriptionId });
      return;
    }

    try {
      // 获取WebSocket适配器并执行真实的取消订阅操作
      const wsAdapter = this.adapterFactory.createWebSocketAdapter(exchangeName);

      if (wsAdapter && typeof wsAdapter.unsubscribe === 'function') {
        // 使用适配器的取消订阅方法
        await wsAdapter.unsubscribe(subscriptionId);
        this.logger.info('WebSocket适配器取消订阅成功', { exchangeName, subscriptionId });
      } else {
        this.logger.warn('WebSocket适配器不支持取消订阅或适配器不存在', { exchangeName });
      }

      // 从本地连接记录中移除订阅
      connection.subscriptions.delete(subscriptionId);

      // 如果连接没有活跃订阅，考虑关闭连接以节省资源
      if (connection.subscriptions.size === 0 && connection.adapter) {
        this.logger.info('连接无活跃订阅，关闭连接', { exchangeName });
        if (typeof connection.adapter.close === 'function') {
          await connection.adapter.close();
        }
        connection.status = ConnectionStatus.DISCONNECTED;
      }

      this.logger.debug('从交易所取消订阅完成', {
        exchangeName,
        subscriptionId,
        remainingSubscriptions: connection.subscriptions.size
      });
    } catch (error) {
      this.logger.error('从交易所取消订阅失败', {
        exchangeName,
        subscriptionId,
        error: error instanceof Error ? error.message : String(error)
      });

      // 即使适配器取消订阅失败，也要清理本地记录
      connection.subscriptions.delete(subscriptionId);
    }
  }

  /**
   * 确保连接
   */
  private async ensureConnection(exchangeName: string): Promise<void> {
    const connection = this.connections.get(exchangeName);
    if (!connection) {
      throw new Error(`交易所连接不存在: ${exchangeName}`);
    }

    if (connection.adapter.isConnected()) {
      connection.status = ConnectionStatus.CONNECTED;
      connection.lastHeartbeat = Date.now();
      return;
    }

    if (connection.status === ConnectionStatus.CONNECTING) {
      // 等待连接完成
      await this.waitForConnection(exchangeName);
      return;
    }

    // 尝试连接
    await this.connectExchange(exchangeName);
  }

  /**
   * 连接交易所
   */
  private async connectExchange(exchangeName: string): Promise<void> {
    const connection = this.connections.get(exchangeName);
    if (!connection) {
      throw new Error(`交易所连接不存在: ${exchangeName}`);
    }

    connection.status = ConnectionStatus.CONNECTING;

    try {
      // 这里需要根据具体的适配器实现来建立连接
      // 由于当前的适配器没有显式的connect方法，我们假设在第一次订阅时会自动连接

      connection.status = ConnectionStatus.CONNECTED;
      connection.lastHeartbeat = Date.now();
      connection.errorCount = 0;
      connection.reconnectAttempts = 0;

      this.logger.info('交易所连接成功', { exchangeName });

    } catch (error) {
      connection.status = ConnectionStatus.FAILED;
      connection.errorCount++;

      this.logger.error('交易所连接失败', {
        exchangeName,
        error: error instanceof Error ? error.message : String(error),
        errorCount: connection.errorCount
      });

      throw error;
    }
  }

  /**
   * 等待连接建立
   */
  private async waitForConnection(exchangeName: string): Promise<void> {
    const connection = this.connections.get(exchangeName);
    if (!connection) {
      throw new Error(`交易所连接不存在: ${exchangeName}`);
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`等待${exchangeName}连接超时`));
      }, this.healthCheckConfig.timeout);

      const checkConnection = () => {
        if (connection.status === ConnectionStatus.CONNECTED) {
          clearTimeout(timeout);
          resolve();
        } else if (connection.status === ConnectionStatus.FAILED) {
          clearTimeout(timeout);
          reject(new Error(`${exchangeName}连接失败`));
        } else {
          setTimeout(checkConnection, 100);
        }
      };

      checkConnection();
    });
  }

  /**
   * 关闭连接
   */
  private async closeConnection(exchangeName: string): Promise<void> {
    const connection = this.connections.get(exchangeName);
    if (!connection) {
      return;
    }

    try {
      connection.status = ConnectionStatus.DISCONNECTED;
      await connection.adapter.close();
      connection.subscriptions.clear();

      this.logger.info('关闭交易所连接', { exchangeName });
    } catch (error) {
      this.logger.error('关闭交易所连接失败', {
        exchangeName,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        this.logger.error('健康检查执行失败', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }, this.healthCheckConfig.interval);

    this.logger.info('启动健康检查', {
      interval: this.healthCheckConfig.interval
    });
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    const now = Date.now();
    const unhealthyConnections: string[] = [];

    for (const [name, connection] of this.connections) {
      try {
        // 检查连接状态
        const isConnected = connection.adapter.isConnected();
        const timeSinceLastHeartbeat = now - connection.lastHeartbeat;

        if (!isConnected || timeSinceLastHeartbeat > this.healthCheckConfig.timeout) {
          connection.errorCount++;
          unhealthyConnections.push(name);

          this.logger.warn('检测到不健康的连接', {
            exchangeName: name,
            isConnected,
            timeSinceLastHeartbeat,
            errorCount: connection.errorCount
          });

          // 如果错误次数过多，标记为失败
          if (connection.errorCount >= this.healthCheckConfig.maxErrors) {
            connection.status = ConnectionStatus.FAILED;
            await this.handleConnectionFailure(name);
          }
        } else {
          // 连接正常，重置错误计数
          if (connection.errorCount > 0) {
            connection.errorCount = Math.max(0, connection.errorCount - 1);
          }
          connection.status = ConnectionStatus.CONNECTED;
          connection.lastHeartbeat = now;
        }
      } catch (error) {
        this.logger.error('健康检查失败', {
          exchangeName: name,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // 如果有不健康的连接，触发故障转移
    if (unhealthyConnections.length > 0) {
      await this.handleFailover(unhealthyConnections);
    }
  }

  /**
   * 处理连接失败
   */
  private async handleConnectionFailure(exchangeName: string): Promise<void> {
    const connection = this.connections.get(exchangeName);
    if (!connection) {
      return;
    }

    this.logger.error('交易所连接失败', {
      exchangeName,
      errorCount: connection.errorCount,
      reconnectAttempts: connection.reconnectAttempts
    });

    // 如果重连次数未达到上限，尝试重连
    if (connection.reconnectAttempts < this.healthCheckConfig.maxReconnectAttempts) {
      connection.reconnectAttempts++;
      connection.status = ConnectionStatus.RECONNECTING;

      // 使用非阻塞的异步重连，避免阻塞启动流程
      setImmediate(async () => {
        try {
          const delay = Math.min(1000 * Math.pow(2, connection.reconnectAttempts), 30000);
          await new Promise(resolve => setTimeout(resolve, delay));
          await this.connectExchange(exchangeName);
          await this.resubscribeExchange(exchangeName);
        } catch (error) {
          this.logger.error('重连失败', {
            exchangeName,
            attempt: connection.reconnectAttempts,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      });
    }
  }

  /**
   * 处理故障转移
   */
  private async handleFailover(failedExchanges: string[]): Promise<void> {
    this.logger.info('开始故障转移', { failedExchanges });

    for (const failedExchange of failedExchanges) {
      await this.redistributeSubscriptions(failedExchange);
    }
  }

  /**
   * 重新分配订阅
   */
  private async redistributeSubscriptions(failedExchange: string): Promise<void> {
    const affectedSubscriptions: SubscriptionInfo[] = [];

    // 找到受影响的订阅
    for (const subscription of this.subscriptions.values()) {
      if (subscription.activeExchanges.has(failedExchange)) {
        affectedSubscriptions.push(subscription);
      }
    }

    if (affectedSubscriptions.length === 0) {
      return;
    }

    this.logger.info('重新分配订阅', {
      failedExchange,
      affectedSubscriptions: affectedSubscriptions.length
    });

    // 为每个受影响的订阅寻找替代交易所
    for (const subscription of affectedSubscriptions) {
      subscription.activeExchanges.delete(failedExchange);

      // 如果这是主要交易所，需要选择新的主要交易所
      if (subscription.primaryExchange === failedExchange) {
        subscription.primaryExchange = subscription.activeExchanges.size > 0
          ? Array.from(subscription.activeExchanges)[0]
          : undefined;
      }

      // 如果没有活跃的交易所了，尝试添加新的
      if (subscription.activeExchanges.size === 0) {
        const availableExchanges = this.selectExchanges();
        const alternativeExchange = availableExchanges.find(name => name !== failedExchange);

        if (alternativeExchange) {
          try {
            if (subscription.type === 'ticker') {
              await this.subscribeOnExchange(alternativeExchange, subscription);
            } else {
              await this.subscribeKlineOnExchange(alternativeExchange, subscription);
            }

            subscription.activeExchanges.add(alternativeExchange);
            if (!subscription.primaryExchange) {
              subscription.primaryExchange = alternativeExchange;
            }

            this.logger.info('成功转移订阅到备用交易所', {
              subscriptionId: subscription.id,
              fromExchange: failedExchange,
              toExchange: alternativeExchange
            });
          } catch (error) {
            this.logger.error('转移订阅失败', {
              subscriptionId: subscription.id,
              alternativeExchange,
              error: error instanceof Error ? error.message : String(error)
            });
          }
        }
      }
    }
  }

  /**
   * 重新订阅交易所
   */
  private async resubscribeExchange(exchangeName: string): Promise<void> {
    const connection = this.connections.get(exchangeName);
    if (!connection) {
      return;
    }

    const subscriptionsToRestore: SubscriptionInfo[] = [];

    // 找到需要恢复的订阅
    for (const subscription of this.subscriptions.values()) {
      if (connection.subscriptions.has(subscription.id)) {
        subscriptionsToRestore.push(subscription);
      }
    }

    this.logger.info('重新订阅交易所', {
      exchangeName,
      subscriptionCount: subscriptionsToRestore.length
    });

    // 恢复订阅
    for (const subscription of subscriptionsToRestore) {
      try {
        if (subscription.type === 'ticker') {
          await this.subscribeOnExchange(exchangeName, subscription);
        } else {
          await this.subscribeKlineOnExchange(exchangeName, subscription);
        }

        subscription.activeExchanges.add(exchangeName);
      } catch (error) {
        this.logger.error('重新订阅失败', {
          exchangeName,
          subscriptionId: subscription.id,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  }

  /**
   * 启动REST降级模式
   */
  private startRestFallbackMode(
    subscriptionId: string,
    symbols: TradingSymbol[],
    callback: (data: any) => void
  ): void {
    this.logger.info('启动REST降级模式', {
      subscriptionId,
      symbols: symbols.map(s => s.symbol),
      interval: '30秒'
    });

    // 立即执行一次数据获取
    this.fetchRestDataForSubscription(symbols, callback);

    // 设置定时器每30秒获取一次数据
    const interval = setInterval(() => {
      this.fetchRestDataForSubscription(symbols, callback);
    }, 30000);

    this.restFallbackIntervals.set(subscriptionId, interval);
  }

  /**
   * 通过REST API获取订阅数据
   */
  private async fetchRestDataForSubscription(
    symbols: TradingSymbol[],
    callback: (data: any) => void
  ): Promise<void> {
    for (const symbol of symbols) {
      try {
        // 按优先级尝试不同的交易所
        const exchanges = ['binance', 'okx', 'coinbase', 'kraken', 'huobi'];

        for (const exchangeName of exchanges) {
          try {
            const connection = this.connections.get(exchangeName);
            if (connection && connection.adapter) {
              // 尝试获取ticker数据
              const tickerData = await this.getRestTickerData(connection.adapter, symbol);

              if (tickerData) {
                // 调用回调函数，模拟WebSocket数据格式
                const fallbackData = {
                  exchange: exchangeName,
                  symbol: symbol.symbol,
                  price: tickerData.price,
                  volume: tickerData.volume,
                  change24h: tickerData.change24h,
                  timestamp: new Date(),
                  source: 'rest-fallback'
                };

                callback(fallbackData);

                // 调用全局数据回调
                this.triggerDataCallback(fallbackData);

                this.logger.debug('REST降级数据获取成功', {
                  exchangeName,
                  symbol: symbol.symbol,
                  price: tickerData.price
                });
                break; // 成功获取数据后跳出交易所循环
              }
            }
          } catch (error) {
            this.logger.debug('REST数据获取失败', {
              exchangeName,
              symbol: symbol.symbol,
              error: error instanceof Error ? error.message : String(error)
            });
            continue; // 尝试下一个交易所
          }
        }
      } catch (error) {
        this.logger.error('REST降级数据获取失败', {
          symbol: symbol.symbol,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  }

  /**
   * 从适配器获取REST ticker数据
   */
  private async getRestTickerData(adapter: any, symbol: TradingSymbol): Promise<any> {
    // 尝试不同的方法名称，因为不同适配器可能有不同的接口
    const methods = ['getTickerData', 'getTicker', 'getPrice', 'getMarketData'];

    for (const methodName of methods) {
      if (typeof adapter[methodName] === 'function') {
        try {
          return await adapter[methodName](symbol.symbol);
        } catch (error) {
          continue; // 尝试下一个方法
        }
      }
    }

    // 如果适配器没有直接的ticker方法，尝试通过HTTP请求
    return await this.fallbackHttpRequest(symbol);
  }

  /**
   * 备用HTTP请求方法
   * 当所有WebSocket适配器都失败时，使用直接HTTP请求作为最后备用方案
   */
  private async fallbackHttpRequest(symbol: TradingSymbol): Promise<any> {
    try {
      this.logger.info('尝试备用HTTP请求获取数据', { symbol: symbol.symbol });

      // 使用Binance公开API作为备用数据源（无需API密钥）
      const binanceSymbol = symbol.symbol.toUpperCase() + 'USDT';
      const url = `https://api.binance.com/api/v3/ticker/24hr?symbol=${binanceSymbol}`;

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

      let response: Response;
      try {
        response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'CryptoMonitor/1.0'
          },
          signal: controller.signal
        });
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        clearTimeout(timeoutId);
        if (error instanceof Error && error.name === 'AbortError') {
          throw new Error('Request timeout');
        }
        throw error;
      }

      const data = await response.json() as any;

      // 转换为标准格式
      const standardData = {
        symbol: symbol.symbol,
        price: parseFloat(data.lastPrice || '0'),
        priceChange: parseFloat(data.priceChange || '0'),
        priceChangePercent: parseFloat(data.priceChangePercent || '0'),
        volume: parseFloat(data.volume || '0'),
        quoteVolume: parseFloat(data.quoteVolume || '0'),
        high: parseFloat(data.highPrice || '0'),
        low: parseFloat(data.lowPrice || '0'),
        open: parseFloat(data.openPrice || '0'),
        timestamp: new Date(),
        source: 'BINANCE_HTTP_FALLBACK'
      };

      this.logger.info('备用HTTP请求成功', {
        symbol: symbol.symbol,
        price: standardData.price,
        source: 'BINANCE_HTTP_FALLBACK'
      });

      return standardData;
    } catch (error) {
      this.logger.error('备用HTTP请求失败', {
        symbol: symbol.symbol,
        error: error instanceof Error ? error.message : String(error)
      });

      // 返回null表示无法获取数据
      return null;
    }
  }

  /**
   * 设置数据回调函数
   * 实现IMultiExchangeWebSocketManager接口
   */
  onDataReceived(callback: (data: any) => void): void {
    this.dataCallback = callback;
    this.logger.info('数据回调函数已设置');
  }

  /**
   * 设置错误回调函数
   * 实现IMultiExchangeWebSocketManager接口
   */
  onError(callback: (error: Error) => void): void {
    this.errorCallback = callback;
    this.logger.info('错误回调函数已设置');
  }

  /**
   * 获取连接状态
   * 实现IMultiExchangeWebSocketManager接口
   */
  getConnectionStatus(): Record<string, boolean> {
    const status: Record<string, boolean> = {};
    for (const [exchangeName, connection] of this.connections) {
      status[exchangeName] = connection.status === ConnectionStatus.CONNECTED;
    }
    return status;
  }

  /**
   * 重新连接指定交易所
   * 实现IMultiExchangeWebSocketManager接口
   */
  async reconnect(exchange: string): Promise<void> {
    const connection = this.connections.get(exchange);
    if (!connection) {
      throw new Error(`交易所连接不存在: ${exchange}`);
    }

    this.logger.info(`开始重连交易所: ${exchange}`);

    try {
      // 断开现有连接
      await this.closeConnection(exchange);

      // 重新连接
      await this.connectExchange(exchange);

      this.logger.info(`交易所重连成功: ${exchange}`);
    } catch (error) {
      this.logger.error(`交易所重连失败: ${exchange}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取实时价格数据
   * 实现IMultiExchangeWebSocketManager接口
   */
  async getRealTimePrice(symbol: string, exchange: string): Promise<number | null> {
    const connection = this.connections.get(exchange);
    if (!connection || connection.status !== ConnectionStatus.CONNECTED) {
      this.logger.warn(`交易所连接不可用: ${exchange}`);
      return null;
    }

    try {
      // WebSocket适配器不直接提供getRealTimePrice方法
      // 这里应该从缓存的实时数据中获取价格
      // 或者通过REST API适配器获取
      this.logger.warn(`WebSocket适配器不支持直接获取实时价格，请使用REST适配器: ${exchange}`);
      return null;
    } catch (error) {
      this.logger.error(`获取实时价格失败: ${exchange}:${symbol}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 触发数据回调
   */
  private triggerDataCallback(data: any): void {
    if (this.dataCallback) {
      try {
        this.dataCallback(data);
      } catch (error) {
        this.logger.error('数据回调执行失败', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  }

  /**
   * 触发错误回调
   */
  private triggerErrorCallback(error: Error): void {
    if (this.errorCallback) {
      try {
        this.errorCallback(error);
      } catch (callbackError) {
        this.logger.error('错误回调执行失败', {
          originalError: error.message,
          callbackError: callbackError instanceof Error ? callbackError.message : String(callbackError)
        });
      }
    }
  }

  /**
   * 订阅交易对数据
   * 实现IMultiExchangeWebSocketManager接口
   */
  async subscribe(symbol: string, exchange: string): Promise<void> {
    const tradingSymbol = new TradingSymbol(symbol);

    // 使用现有的subscribePriceStream方法
    await this.subscribePriceStream([tradingSymbol], (data) => {
      this.triggerDataCallback(data);
    }, [exchange]);

    this.logger.info(`已订阅交易对: ${exchange}:${symbol}`);
  }

  /**
   * 取消订阅交易对数据
   * 实现IMultiExchangeWebSocketManager接口
   */
  async unsubscribeSymbol(symbol: string, exchange: string): Promise<void> {
    // 查找相关的订阅
    for (const [subscriptionId, subscription] of this.subscriptions) {
      const hasSymbol = subscription.symbols.some(s => s.symbol === symbol);
      const hasExchange = subscription.activeExchanges.has(exchange);

      if (hasSymbol && hasExchange) {
        await this.unsubscribeFromExchange(subscriptionId, exchange);
        this.logger.info(`已取消订阅交易对: ${exchange}:${symbol}`);
        return;
      }
    }

    this.logger.warn(`未找到订阅: ${exchange}:${symbol}`);
  }

  /**
   * 获取支持的交易所列表
   * 实现IMultiExchangeWebSocketManager接口
   */
  getSupportedExchanges(): string[] {
    return Array.from(this.connections.keys());
  }

  /**
   * 检查交易所是否支持指定交易对
   * 实现IMultiExchangeWebSocketManager接口
   */
  isSymbolSupported(symbol: string, exchange: string): boolean {
    const connection = this.connections.get(exchange);
    if (!connection) {
      return false;
    }

    // 简单的符号格式检查
    // 项目专注于BTC交易，只支持BTC相关交易对
    const commonSymbols = ['BTCUSDT', 'BTC/USDT'];
    const normalizedSymbol = symbol.toUpperCase().replace('/', '');

    return commonSymbols.some(s => s.replace('/', '') === normalizedSymbol);
  }
}
