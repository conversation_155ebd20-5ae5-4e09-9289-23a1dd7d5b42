import { injectable } from 'inversify';
import {
  IExchangeAdapter,
  RealTimePriceData,
  KlineData,
  ExchangeSymbolInfo,
  HistoricalDataQuery,
  ExchangeError,
  ExchangeErrorType,
  ExchangeConfig
} from '../../domain/services/exchange-adapter';
import { Price } from '../../domain/value-objects/price';
import { Volume } from '../../domain/value-objects/volume';
import { TradingSymbol } from '../../domain/value-objects/trading-symbol';
import { Timeframe } from '../../domain/value-objects/timeframe';
import { ServiceHealthStatus } from '../../../../shared/application/interfaces/external-service';
import { ExternalDataAdapterBase, AdapterConfig, AdapterHealthStatus } from '../../../../shared/infrastructure/data-processing';
import { getLogger } from '../../../../config/logging';

/**
 * OKX API响应类型定义
 */
interface OKXBaseResponse<T> {
  code: string;
  msg: string;
  data: T;
}

interface OKXTickerData {
  instType: string;
  instId: string;
  last: string;
  lastSz: string;
  askPx: string;
  askSz: string;
  bidPx: string;
  bidSz: string;
  open24h: string;
  high24h: string;
  low24h: string;
  vol24h: string;
  volCcy24h: string;
  ts: string;
  sodUtc0: string;
  sodUtc8: string;
}

interface OKXInstrumentData {
  instType: string;
  instId: string;
  uly: string;
  baseCcy: string;
  quoteCcy: string;
  settleCcy: string;
  ctVal: string;
  ctMult: string;
  ctValCcy: string;
  optType: string;
  stk: string;
  listTime: string;
  expTime: string;
  lever: string;
  tickSz: string;
  lotSz: string;
  minSz: string;
  ctType: string;
  alias: string;
  state: string;
}

/**
 * OKX交易所适配器
 * 重构后继承ExternalDataAdapterBase，消除重复实现
 */
@injectable()
export class OKXAdapter extends ExternalDataAdapterBase implements IExchangeAdapter {
  readonly adapterName = 'okx';
  readonly sourceName = 'okx';
  public readonly serviceName = 'OKX';
  public readonly exchangeName = 'okx';

  // OKX时间框架映射
  private readonly timeframeMap: Record<string, string> = {
    '1m': '1m',
    '3m': '3m',
    '5m': '5m',
    '15m': '15m',
    '30m': '30m',
    '1h': '1H',
    '2h': '2H',
    '4h': '4H',
    '6h': '6H',
    '12h': '12H',
    '1d': '1D',
    '1w': '1W',
    '1M': '1M',
  };

  constructor(
    config?: AdapterConfig,
    pipeline?: any,
    logger?: any,
    errorHandler?: any
  ) {
    // 如果没有提供配置，使用默认配置
    const adapterConfig: AdapterConfig = config || {
      name: 'okx',
      baseUrl: 'https://www.okx.com/api/v5',
      timeout: 10000,
      maxRetries: 3,
      rateLimitConfig: {
        requestsPerSecond: 20, // OKX限制相对宽松
        burstSize: 40
      },
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'crypto-monitor/1.0.0',
      },
      enableCache: true,
      cacheValidityMinutes: 1 // 实时数据缓存1分钟
    };

    // 调用基类构造函数，自动集成数据处理管道
    super(adapterConfig, pipeline, logger, errorHandler);
  }

  /**
   * 获取实时价格数据
   * 重构后使用统一的数据处理管道
   */
  async getRealTimePrice(symbol: TradingSymbol): Promise<RealTimePriceData> {
    const okxSymbol = symbol.toOKXFormat();

    // 使用统一的数据处理管道
    const rawData = await this.fetchAndProcess<OKXBaseResponse<OKXTickerData[]>>(
      '/market/ticker',
      {
        params: { instId: okxSymbol },
        useCache: true,
        cacheKey: `ticker:${okxSymbol}`
      },
      {
        dataType: 'ticker',
        businessSystem: 'market-data',
        metadata: {
          symbol: symbol.symbol,
          exchange: 'okx'
        }
      }
    );

    // 处理OKX特有的错误格式
    if (rawData.code !== '0') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `OKX API错误: ${rawData.msg}`
      );
    }

    const tickerData = rawData.data[0];
    if (!tickerData) {
      throw new ExchangeError(
        ExchangeErrorType.INVALID_SYMBOL,
        this.exchangeName,
        `交易对不存在: ${symbol.symbol}`
      );
    }

    return this.transformPriceData(tickerData, symbol);
  }

  /**
   * 批量获取实时价格数据
   * 重构后使用统一的数据处理管道
   */
  async getRealTimePrices(symbols: TradingSymbol[]): Promise<RealTimePriceData[]> {
    // 使用统一的数据处理管道
    const rawData = await this.fetchAndProcess<OKXBaseResponse<OKXTickerData[]>>(
      '/market/tickers',
      {
        params: { instType: 'SPOT' },
        useCache: true,
        cacheKey: 'spot-tickers',
        timeout: 15000 // 批量请求超时时间更长
      },
      {
        dataType: 'batch-ticker',
        businessSystem: 'market-data',
        metadata: {
          symbols: symbols.map(s => s.symbol),
          exchange: 'okx',
          requestType: 'batch'
        }
      }
    );

    // 处理OKX特有的错误格式
    if (rawData.code !== '0') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `OKX API错误: ${rawData.msg}`
      );
    }

    const results: RealTimePriceData[] = [];
    const symbolMap = new Map(symbols.map(s => [s.toOKXFormat(), s]));

    for (const tickerData of rawData.data) {
      const symbol = symbolMap.get(tickerData.instId);
      if (symbol) {
        results.push(this.transformPriceData(tickerData, symbol));
      }
    }

    return results;
  }

  /**
   * 获取历史K线数据
   * 重构后使用统一的数据处理管道
   */
  async getHistoricalKlines(query: HistoricalDataQuery): Promise<KlineData[]> {
    const okxSymbol = query.symbol.toOKXFormat();
    const bar = this.timeframeMap[query.timeframe.value];

    if (!bar) {
      throw new ExchangeError(
        ExchangeErrorType.INVALID_TIMEFRAME,
        this.exchangeName,
        `不支持的时间框架: ${query.timeframe.value}`
      );
    }

    const params: any = {
      instId: okxSymbol,
      bar,
      limit: query.limit || 100,
    };

    if (query.startTime) {
      params.before = query.startTime.getTime().toString();
    }
    if (query.endTime) {
      params.after = query.endTime.getTime().toString();
    }

    // 使用统一的数据处理管道
    const rawData = await this.fetchAndProcess<OKXBaseResponse<string[][]>>(
      '/market/candles',
      {
        params,
        useCache: false, // 历史数据不缓存
        timeout: 20000 // 历史数据请求超时时间更长
      },
      {
        dataType: 'kline',
        businessSystem: 'market-data',
        metadata: {
          symbol: query.symbol.symbol,
          timeframe: query.timeframe.value,
          exchange: 'okx',
          limit: query.limit
        }
      }
    );

    // 处理OKX特有的错误格式
    if (rawData.code !== '0') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `OKX API错误: ${rawData.msg}`
      );
    }

    // OKX返回的数据是按时间倒序的，需要反转
    const sortedData = rawData.data.reverse();

    return sortedData.map(item => this.transformKlineData(item, query.symbol as TradingSymbol, query.timeframe));
  }

  /**
   * 获取交易对信息
   * 重构后使用统一的数据处理管道
   */
  async getSymbolInfo(symbol: TradingSymbol): Promise<ExchangeSymbolInfo> {
    const okxSymbol = symbol.toOKXFormat();

    // 使用统一的数据处理管道
    const rawData = await this.fetchAndProcess<OKXBaseResponse<OKXInstrumentData[]>>(
      '/public/instruments',
      {
        params: {
          instType: 'SPOT',
          instId: okxSymbol,
        },
        useCache: true,
        cacheKey: `symbol-info:${okxSymbol}`,
        timeout: 10000
      },
      {
        dataType: 'symbol-info',
        businessSystem: 'market-data',
        metadata: {
          symbol: symbol.symbol,
          exchange: 'okx'
        }
      }
    );

    // 处理OKX特有的错误格式
    if (rawData.code !== '0') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `OKX API错误: ${rawData.msg}`
      );
    }

    const instrumentData = rawData.data[0];
    if (!instrumentData) {
      throw new ExchangeError(
        ExchangeErrorType.INVALID_SYMBOL,
        this.exchangeName,
        `交易对不存在: ${symbol.value}`
      );
    }

    return this.transformSymbolInfo(instrumentData, symbol);
  }

  /**
   * 获取所有交易对信息
   * 重构后使用统一的数据处理管道
   */
  async getAllSymbols(): Promise<ExchangeSymbolInfo[]> {
    // 使用统一的数据处理管道
    const rawData = await this.fetchAndProcess<OKXBaseResponse<OKXInstrumentData[]>>(
      '/public/instruments',
      {
        params: { instType: 'SPOT' },
        useCache: true,
        cacheKey: 'spot-instruments',
        timeout: 15000 // 获取所有交易对可能需要更长时间
      },
      {
        dataType: 'all-symbols',
        businessSystem: 'market-data',
        metadata: {
          exchange: 'okx',
          requestType: 'bulk-symbols'
        }
      }
    );

    // 处理OKX特有的错误格式
    if (rawData.code !== '0') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `OKX API错误: ${rawData.msg}`
      );
    }

    const results: ExchangeSymbolInfo[] = [];

    for (const instrumentData of rawData.data) {
      // 只返回USDT交易对
      if (!instrumentData.instId.endsWith('-USDT') || instrumentData.state !== 'live') {
        continue;
      }

      try {
        const symbol = TradingSymbol.fromOKXFormat(instrumentData.instId);
        const symbolInfo = this.transformSymbolInfo(instrumentData, symbol);
        results.push(symbolInfo);
      } catch (error) {
        this.logger.warn('跳过无效交易对', {
          symbol: instrumentData.instId,
          error: error instanceof Error ? error.message : '未知错误'
        });
        continue;
      }
    }

    return results;
  }

  /**
   * 检查交易所服务是否可用
   * 重构后使用基类的健康检查机制
   */
  async isAvailable(): Promise<boolean> {
    try {
      const healthStatus = await super.healthCheck();
      return healthStatus.isHealthy;
    } catch {
      return false;
    }
  }

  /**
   * 获取服务器时间
   * 重构后使用统一的数据处理管道
   */
  async getServerTime(): Promise<Date> {
    const rawData = await this.fetchAndProcess<OKXBaseResponse<{ ts: string }[]>>(
      '/public/time',
      {
        useCache: false, // 服务器时间不缓存
        timeout: 5000
      },
      {
        dataType: 'server-time',
        businessSystem: 'market-data',
        metadata: {
          exchange: 'okx',
          requestType: 'server-time'
        }
      }
    );

    // 处理OKX特有的错误格式
    if (rawData.code !== '0') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `OKX API错误: ${rawData.msg}`
      );
    }

    return new Date(parseInt(rawData.data[0].ts));
  }

  /**
   * 测试连接
   * 重构后使用基类的健康检查机制
   */
  async testConnection(): Promise<boolean> {
    try {
      const healthStatus = await (this as any).getHealthStatus();
      return healthStatus.isHealthy || healthStatus.status === 'healthy';
    } catch {
      return false;
    }
  }

  // 健康检查逻辑已由基类ExternalDataAdapterBase提供
  // 基类的getHealthStatus()方法会自动调用testConnection()并处理错误

  /**
   * 转换价格数据
   */
  private transformPriceData(ticker: OKXTickerData, symbol: TradingSymbol): RealTimePriceData {
    const currentPrice = parseFloat(ticker.last);
    const openPrice = parseFloat(ticker.open24h);
    const change24h = currentPrice - openPrice;
    const changePercent24h = openPrice === 0 ? 0 : (change24h / openPrice) * 100;

    return {
      symbol,
      price: new Price(currentPrice),
      change24h, // 价格变化保持原始值（可以为负数）
      changePercent24h,
      volume24h: new Volume(parseFloat(ticker.vol24h)),
      high24h: new Price(parseFloat(ticker.high24h)),
      low24h: new Price(parseFloat(ticker.low24h)),
      timestamp: new Date(parseInt(ticker.ts)),
    };
  }

  /**
   * 转换K线数据
   */
  private transformKlineData(
    data: string[],
    symbol: TradingSymbol,
    timeframe: Timeframe
  ): KlineData {
    const openTime = new Date(parseInt(data[0]));
    const closeTime = new Date(openTime.getTime() + timeframe.milliseconds - 1);

    return {
      symbol,
      timeframe,
      openTime,
      closeTime,
      open: new Price(parseFloat(data[1])),
      high: new Price(parseFloat(data[2])),
      low: new Price(parseFloat(data[3])),
      close: new Price(parseFloat(data[4])),
      volume: new Volume(parseFloat(data[5])),
    };
  }

  /**
   * 转换交易对信息
   */
  private transformSymbolInfo(instrumentData: OKXInstrumentData, symbol: TradingSymbol): ExchangeSymbolInfo {
    return {
      symbol,
      baseAsset: instrumentData.baseCcy,
      quoteAsset: instrumentData.quoteCcy,
      status: instrumentData.state === 'live' ? 'TRADING' : 'INACTIVE',
      minOrderSize: parseFloat(instrumentData.minSz),
      maxOrderSize: Number.MAX_SAFE_INTEGER,
      tickSize: parseFloat(instrumentData.tickSz),
      stepSize: parseFloat(instrumentData.lotSz),
      pricePrecision: this.getPrecision(instrumentData.tickSz),
      quantityPrecision: this.getPrecision(instrumentData.lotSz),
      filters: [],
    };
  }

  /**
   * 获取精度
   */
  private getPrecision(value: string): number {
    const parts = value.split('.');
    return parts.length > 1 ? parts[1].length : 0;
  }



  /**
   * 获取健康状态
   */
  async getHealthStatus(): Promise<any> {
    try {
      // 测试API连接
      await this.httpClient.get('/api/v5/public/time');
      return {
        status: 'healthy',
        timestamp: new Date(),
        responseTime: 0,
        error: undefined
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date(),
        responseTime: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // 错误处理逻辑已由基类ExternalDataAdapterBase和数据处理管道提供
  // 基类会自动处理HTTP错误、速率限制、网络错误等常见情况
  // 数据处理管道会统一处理所有错误并记录日志
}
