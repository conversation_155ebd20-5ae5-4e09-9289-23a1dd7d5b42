/**
 * Mempool.Space API适配器
 * 获取比特币网络状态和费用数据
 * 完全免费，无需API密钥
 */

import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { HttpClientFactory, HttpClientType } from '../../../../shared/infrastructure/http/http-client-factory';
import { BaseHttpClient } from '../../../../shared/infrastructure/http/base-http-client';
import { IMempoolAdapter, MempoolStatsData, NetworkFeesData, AddressInfoData, NetworkStatsData, NetworkDifficultyData, BlockInfoData, TransactionInfoData } from '../../domain/services/external-adapters/IMempoolAdapter';

export interface MempoolStats {
  count: number;
  vsize: number;
  totalFee: number;
  feeHistogram: number[][];
}

export interface FeeEstimates {
  '1': number;    // 下一个区块
  '3': number;    // 3个区块内
  '6': number;    // 6个区块内
  '144': number;  // 1天内
  '504': number;  // 3天内
  '1008': number; // 1周内
}

export interface NetworkStats {
  mempoolCount: number;
  mempoolSize: number;
  totalFeeToday: number;
  averageFeeRate: number;
  congestionLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  feeEstimates: FeeEstimates;
  timestamp: Date;
}

export class MempoolAdapter implements IMempoolAdapter {
  private readonly httpClient: BaseHttpClient;
  private readonly logger: IBasicLogger;
  private readonly baseURL = 'https://mempool.space/api';

  constructor(logger: IBasicLogger) {
    this.logger = logger;
    // 使用统一的HTTP客户端工厂
    const httpClientFactory = new HttpClientFactory(this.logger);
    this.httpClient = httpClientFactory.createClient(
      'mempool',
      this.baseURL,
      HttpClientType.EXTERNAL_API,
      {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'crypto-monitor/1.0.0',
        },
        rateLimitConfig: {
          requestsPerSecond: 10,
          burstSize: 20
        }
      }
    );
  }

  /**
   * 获取费用估算
   */
  async getFeeEstimates(): Promise<FeeEstimates> {
    try {
      const response = await this.httpClient.get<FeeEstimates>('/v1/fees/recommended');
      return response.data;
    } catch (error) {
      this.logger.error('获取费用估算失败', { error });
      throw new Error(`获取费用估算失败: ${error}`);
    }
  }

  /**
   * 获取综合网络状态
   */
  async getNetworkStats(): Promise<NetworkStatsData> {
    try {
      const [mempoolStats, feeEstimates] = await Promise.all([
        this.getMempoolStats(),
        this.getFeeEstimates()
      ]);

      // 计算网络拥堵程度
      const congestionLevel = this.calculateCongestionLevel(
        mempoolStats.count,
        feeEstimates['1']
      );

      // 计算平均费率
      const averageFeeRate = mempoolStats.totalFee / mempoolStats.vsize;

      return {
        mempoolCount: mempoolStats.count,
        mempoolSize: mempoolStats.vsize,
        totalFeeToday: mempoolStats.totalFee,
        averageFeeRate: averageFeeRate,
        congestionLevel: congestionLevel,
        feeEstimates: feeEstimates,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('获取网络状态失败', { error });
      throw new Error(`获取网络状态失败: ${error}`);
    }
  }

  /**
   * 计算网络拥堵程度
   */
  private calculateCongestionLevel(
    mempoolCount: number,
    nextBlockFee: number
  ): 'LOW' | 'MEDIUM' | 'HIGH' {
    // 基于mempool交易数量和费用判断拥堵程度
    if (mempoolCount > 100000 || nextBlockFee > 50) {
      return 'HIGH';
    } else if (mempoolCount > 50000 || nextBlockFee > 20) {
      return 'MEDIUM';
    } else {
      return 'LOW';
    }
  }

  /**
   * 获取费用趋势分析
   */
  async getFeeTrend(): Promise<{
    currentFee: number;
    trend: 'RISING' | 'FALLING' | 'STABLE';
    congestionScore: number;
  }> {
    try {
      const stats = await this.getNetworkStats();
      
      // 简化的趋势分析（实际应该基于历史数据）
      const currentFee = stats.feeEstimates['1'];
      const mediumTermFee = stats.feeEstimates['144'];
      
      let trend: 'RISING' | 'FALLING' | 'STABLE';
      if (currentFee > mediumTermFee * 1.2) {
        trend = 'RISING';
      } else if (currentFee < mediumTermFee * 0.8) {
        trend = 'FALLING';
      } else {
        trend = 'STABLE';
      }

      // 拥堵评分 (0-100)
      const congestionScore = Math.min(
        100,
        (stats.mempoolCount / 1000) + (currentFee * 2)
      );

      return {
        currentFee: currentFee,
        trend,
        congestionScore: congestionScore
      };
    } catch (error) {
      this.logger.error('获取费用趋势失败', { error });
      throw new Error(`获取费用趋势失败: ${error}`);
    }
  }

  /**
   * 获取网络费用信息
   */
  async getNetworkFees(): Promise<NetworkFeesData> {
    try {
      const response = await this.httpClient.get('/v1/fees/recommended');
      const data = response.data;

      return {
        fastestFee: data.fastestFee,
        halfHourFee: data.halfHourFee,
        hourFee: data.hourFee,
        economyFee: data.economyFee,
        minimumFee: data.minimumFee,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('获取网络费用失败', { error });
      throw new Error(`获取网络费用失败: ${error}`);
    }
  }

  /**
   * 获取内存池统计信息
   */
  async getMempoolStats(): Promise<MempoolStatsData> {
    try {
      const response = await this.httpClient.get('/mempool');
      const data = response.data;

      return {
        count: data.count,
        vsize: data.vsize,
        totalFee: data.total_fee,
        feeHistogram: data.fee_histogram || [],
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('获取内存池统计失败', { error });
      throw new Error(`获取内存池统计失败: ${error}`);
    }
  }

  /**
   * 获取网络难度信息
   */
  async getNetworkDifficulty(): Promise<NetworkDifficultyData> {
    try {
      const response = await this.httpClient.get('/difficulty-adjustment');
      const data = response.data;

      return {
        difficulty: data.difficulty || 0,
        adjustmentProgress: data.progressPercent || 0,
        nextAdjustmentHeight: data.nextRetargetHeight || 0,
        estimatedAdjustment: data.estimatedNewDifficulty || 0,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('获取网络难度失败', { error });
      throw new Error(`获取网络难度失败: ${error}`);
    }
  }

  /**
   * 获取区块信息
   */
  async getBlockInfo(blockHeight?: number): Promise<BlockInfoData> {
    try {
      const endpoint = blockHeight ? `/block-height/${blockHeight}` : '/blocks/tip/height';
      const response = await this.httpClient.get(endpoint);
      const data = response.data;

      return {
        height: data.height || blockHeight || 0,
        hash: data.id || '',
        timestamp: new Date(data.timestamp * 1000 || Date.now()),
        size: data.size || 0,
        weight: data.weight || 0,
        txCount: data.tx_count || 0,
        totalFees: data.total_fee || 0,
        medianFee: data.median_fee || 0,
        feeRange: {
          min: data.min_fee || 0,
          max: data.max_fee || 0
        }
      };
    } catch (error) {
      this.logger.error('获取区块信息失败', { error });
      throw new Error(`获取区块信息失败: ${error}`);
    }
  }

  /**
   * 获取交易信息
   */
  async getTransactionInfo(txid: string): Promise<TransactionInfoData> {
    try {
      const response = await this.httpClient.get(`/tx/${txid}`);
      const data = response.data;

      return {
        txid: data.txid || txid,
        size: data.size || 0,
        weight: data.weight || 0,
        fee: data.fee || 0,
        feeRate: data.fee / data.size || 0,
        inputCount: data.vin?.length || 0,
        outputCount: data.vout?.length || 0,
        confirmations: data.status?.confirmed ? data.status.blockHeight || data.status.block_height : 0,
        status: {
          confirmed: data.status?.confirmed || false,
          blockHeight: data.status?.blockHeight || data.status?.block_height || undefined,
          blockHash: data.status?.blockHash || data.status?.block_hash || undefined,
          blockTime: data.status?.blockTime || (data.status?.block_time ? new Date(data.status.block_time * 1000) : undefined)
        }
      };
    } catch (error) {
      this.logger.error('获取交易信息失败', { error });
      throw new Error(`获取交易信息失败: ${error}`);
    }
  }

  /**
   * 获取地址信息
   */
  async getAddressInfo(address: string): Promise<AddressInfoData> {
    try {
      const response = await this.httpClient.get(`/address/${address}`);
      const data = response.data;

      return {
        address: data.address,
        balance: data.chain_stats.funded_txo_sum - data.chain_stats.spent_txo_sum,
        totalReceived: data.chain_stats.funded_txo_sum,
        totalSent: data.chain_stats.spent_txo_sum,
        txCount: data.chain_stats.tx_count,
        unconfirmedBalance: data.mempool_stats.funded_txo_sum - data.mempool_stats.spent_txo_sum,
        unconfirmedTxCount: data.mempool_stats.tx_count
      };
    } catch (error) {
      this.logger.error('获取地址信息失败', { error });
      throw new Error(`获取地址信息失败: ${error}`);
    }
  }

  /**
   * 检查服务可用性
   */
  async isAvailable(): Promise<boolean> {
    try {
      // 测试一个简单的API调用来检查服务可用性
      await this.httpClient.get('/v1/fees/recommended');
      return true;
    } catch (error) {
      this.logger.warn('Mempool.space服务不可用', { error: error instanceof Error ? error.message : String(error) });
      return false;
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    return this.isAvailable();
  }

  // 健康检查已由基类 ExternalDataAdapterBase 提供 - 避免重复实现
  // 基类的 testConnection() 方法会自动调用根路径进行健康检查
}

export default MempoolAdapter;
