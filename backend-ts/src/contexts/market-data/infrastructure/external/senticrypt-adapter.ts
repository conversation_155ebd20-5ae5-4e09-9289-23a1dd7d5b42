/**
 * SentiCrypt API适配器
 * 获取BTC专业情绪分析数据
 * 完全免费，无需API密钥，每2小时更新
 * 重构后继承ExternalDataAdapterBase，消除重复实现
 */

import { injectable } from 'inversify';
import {
  ISentiCryptAdapter,
  ProcessedSentimentData,
  SentimentData,
  SocialMediaSentimentData,
  NewsSentimentData,
  SentimentTrendData,
  SentimentSummaryData
} from '../../domain/services/external-adapters/ISentiCryptAdapter';
import { ServiceHealthStatus } from '../../../../shared/application/interfaces/external-service';
import { ExternalDataAdapterBase, AdapterConfig } from '../../../../shared/infrastructure/data-processing';
import { getLogger } from '../../../../config/logging';

export interface SentiCryptData {
  date: string;
  price: number;
  volume: number;
  score1: number;    // 情绪评分1 (-1.0 到 1.0)
  score2: number;    // 情绪评分2 (-1.0 到 1.0)
  score3: number;    // 情绪评分3 (-1.0 到 1.0)
  count: number;     // 数据点数量
  mean: number;      // 平均情绪值
  sum: number;       // 总情绪分值
}

// 使用导入的 ProcessedSentimentData 接口

/**
 * SentiCrypt情绪分析适配器
 * 重构后继承ExternalDataAdapterBase，消除重复实现
 */
@injectable()
export class SentiCryptAdapter extends ExternalDataAdapterBase implements ISentiCryptAdapter {
  readonly adapterName = 'senticrypt';
  readonly sourceName = 'senticrypt';
  public readonly serviceName = 'SentiCrypt';

  private dataCache: SentiCryptData[] = [];
  private cacheTimestamp: Date | null = null;
  private readonly cacheValidityHours = 2; // 2小时缓存，匹配API更新频率

  constructor(
    config?: AdapterConfig,
    pipeline?: any,
    logger?: any,
    errorHandler?: any
  ) {
    // 如果没有提供配置，使用默认配置
    const adapterConfig: AdapterConfig = config || {
      name: 'senticrypt',
      baseUrl: 'https://api.senticrypt.com/v2',
      timeout: 10000,
      maxRetries: 3,
      rateLimitConfig: {
        requestsPerSecond: 1, // SentiCrypt更新频率较低，限制请求频率
        burstSize: 3
      },
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'crypto-monitor/1.0.0',
      },
      enableCache: true,
      cacheValidityMinutes: 120 // 2小时缓存，匹配API更新频率
    };

    // 调用基类构造函数，自动集成数据处理管道
    super(adapterConfig, pipeline, logger, errorHandler);
  }


  /**
   * 获取指定日期的情绪数据
   * 重构后使用统一的数据处理管道
   */
  async getHistoryByDate(date: string): Promise<SentiCryptData[]> {
    // 使用统一的数据处理管道
    const rawData = await this.fetchAndProcess<SentiCryptData[]>(
      `/history/${date}.json`,
      {
        useCache: true,
        cacheKey: `history:${date}`,
        timeout: 10000
      },
      {
        dataType: 'sentiment-history',
        businessSystem: 'market-data',
        metadata: {
          date,
          source: 'senticrypt'
        }
      }
    );

    return rawData;
  }

  /**
   * 获取最近的情绪数据
   * 重构后使用统一的数据处理管道
   */
  async getRecentSentimentData(days: number = 7): Promise<SentiCryptData[]> {
    // 检查缓存
    if (this.isCacheValid()) {
      this.logger.debug('使用缓存的SentiCrypt数据');
      return this.dataCache.slice(-days);
    }

    // 使用统一的数据处理管道获取全部数据
    const rawData = await this.fetchAndProcess<SentiCryptData[]>(
      '/all.json',
      {
        useCache: true,
        cacheKey: 'all-sentiment-data',
        timeout: 15000 // 获取全部数据可能需要更长时间
      },
      {
        dataType: 'sentiment-all',
        businessSystem: 'market-data',
        metadata: {
          days,
          source: 'senticrypt',
          requestType: 'recent-data'
        }
      }
    );

    // 更新本地缓存
    this.dataCache = rawData;
    this.cacheTimestamp = new Date();

    this.logger.info(`获取到${rawData.length}条SentiCrypt历史数据`);

    // 返回最近N天的数据
    return rawData.slice(-days);
  }

  /**
   * 获取今日情绪数据
   * 重构后使用统一的数据处理管道和错误处理
   */
  async getTodaySentiment(): Promise<ProcessedSentimentData> {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式

    // 直接获取最近的数据，因为SentiCrypt API没有按日期的端点
    let todayData: SentiCryptData[] = [];
    try {
      const recentData = await this.getRecentSentimentData(1);
      todayData = recentData;

      // 尝试找到今日的数据
      const todayDataFiltered = recentData.filter(data => data.date === today);
      if (todayDataFiltered.length > 0) {
        todayData = todayDataFiltered;
      }
    } catch (error) {
      this.logger.error('获取SentiCrypt数据失败', { error, date: today });
      throw new Error(`无法获取情绪数据: ${error instanceof Error ? error.message : String(error)}`);
    }

    if (todayData.length === 0) {
      throw new Error('无法获取情绪数据');
    }

    const latestData = todayData[todayData.length - 1];
    return this.processSentimentData(latestData, todayData);
  }

  /**
   * 处理情绪数据
   */
  private processSentimentData(
    latestData: SentiCryptData,
    historicalData: SentiCryptData[]
  ): ProcessedSentimentData {
    // 计算综合情绪评分 (转换为0-100分)
    const sentimentScore = this.calculateSentimentScore(latestData);
    
    // 确定情绪级别
    const sentimentLevel = this.determineSentimentLevel(latestData.mean);
    
    // 计算情绪趋势
    const sentimentTrend = this.calculateSentimentTrend(historicalData);
    
    // 计算情绪强度
    const sentimentStrength = Math.abs(latestData.mean);
    
    // 计算与价格的相关性
    const priceCorrelation = this.calculatePriceCorrelation(historicalData);
    
    // 计算数据质量
    const dataQuality = this.calculateDataQuality(latestData);
    
    // 生成分析
    const analysis = this.generateSentimentAnalysis(
      latestData, sentimentLevel, sentimentTrend, priceCorrelation
    );

    return {
      timestamp: new Date(latestData.date),
      sentimentScore: sentimentScore,
      sentimentLevel: sentimentLevel,
      sentimentTrend: sentimentTrend,
      sentimentStrength: sentimentStrength,
      rawScores: {
        score1: latestData.score1,
        score2: latestData.score2,
        score3: latestData.score3,
        mean: latestData.mean,
        sum: latestData.sum
      },
      priceCorrelation: priceCorrelation,
      dataQuality: dataQuality,
      analysis
    };
  }

  /**
   * 计算综合情绪评分
   */
  private calculateSentimentScore(data: SentiCryptData): number {
    // 将mean值从[-1, 1]转换为[0, 100]
    const normalizedScore = (data.mean + 1) * 50;
    
    // 考虑情绪强度调整
    const strengthAdjustment = Math.abs(data.sum) / Math.max(data.count, 1) * 10;
    
    // 最终评分
    let finalScore = normalizedScore + (data.mean > 0 ? strengthAdjustment : -strengthAdjustment);
    
    return Math.max(0, Math.min(100, finalScore));
  }

  /**
   * 确定情绪级别
   */
  private determineSentimentLevel(mean: number): 'VERY_NEGATIVE' | 'NEGATIVE' | 'NEUTRAL' | 'POSITIVE' | 'VERY_POSITIVE' {
    if (mean >= 0.5) return 'VERY_POSITIVE';
    if (mean >= 0.2) return 'POSITIVE';
    if (mean >= -0.2) return 'NEUTRAL';
    if (mean >= -0.5) return 'NEGATIVE';
    return 'VERY_NEGATIVE';
  }

  /**
   * 计算情绪趋势
   */
  private calculateSentimentTrend(data: SentiCryptData[]): ProcessedSentimentData['sentimentTrend'] {
    if (data.length < 2) return 'STABLE';
    
    const recent = data.slice(-3); // 最近3天
    const avgRecent = recent.reduce((sum, item) => sum + item.mean, 0) / recent.length;
    
    const earlier = data.slice(-6, -3); // 之前3天
    if (earlier.length === 0) return 'STABLE';
    
    const avgEarlier = earlier.reduce((sum, item) => sum + item.mean, 0) / earlier.length;
    
    const change = avgRecent - avgEarlier;
    
    if (change > 0.1) return 'IMPROVING';
    if (change < -0.1) return 'DECLINING';
    return 'STABLE';
  }

  /**
   * 计算与价格的相关性
   */
  private calculatePriceCorrelation(data: SentiCryptData[]): number {
    if (data.length < 2) return 0;
    
    // 简化的相关性计算
    let correlation = 0;
    for (let i = 1; i < data.length; i++) {
      const priceChange = (data[i].price - data[i-1].price) / data[i-1].price;
      const sentimentChange = data[i].mean - data[i-1].mean;
      
      if ((priceChange > 0 && sentimentChange > 0) || (priceChange < 0 && sentimentChange < 0)) {
        correlation += 1;
      } else if ((priceChange > 0 && sentimentChange < 0) || (priceChange < 0 && sentimentChange > 0)) {
        correlation -= 1;
      }
    }
    
    return correlation / (data.length - 1);
  }

  /**
   * 计算数据质量
   */
  private calculateDataQuality(data: SentiCryptData): number {
    let quality = 0.8; // 基础质量分
    
    // 数据点数量影响质量
    if (data.count >= 10) quality += 0.1;
    else if (data.count >= 5) quality += 0.05;
    
    // 情绪评分的一致性
    const scores = [data.score1, data.score2, data.score3];
    const variance = this.calculateVariance(scores);
    if (variance < 0.5) quality += 0.1; // 评分一致性好
    
    return Math.min(1, quality);
  }

  /**
   * 计算方差
   */
  private calculateVariance(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return variance;
  }

  /**
   * 生成情绪分析
   */
  private generateSentimentAnalysis(
    data: SentiCryptData,
    level: string,
    trend: string,
    correlation: number
  ): string {
    let analysis = `BTC情绪分析: ${level}情绪，`;
    
    analysis += `平均情绪值${data.mean.toFixed(3)}，`;
    analysis += `基于${data.count}个数据点。`;
    
    switch (trend) {
      case 'IMPROVING':
        analysis += '情绪趋势向好，';
        break;
      case 'DECLINING':
        analysis += '情绪趋势下降，';
        break;
      default:
        analysis += '情绪趋势稳定，';
    }
    
    if (correlation > 0.3) {
      analysis += '与价格正相关性较强。';
    } else if (correlation < -0.3) {
      analysis += '与价格负相关性较强。';
    } else {
      analysis += '与价格相关性一般。';
    }
    
    return analysis;
  }

  /**
   * 检查缓存是否有效 - 保留本地缓存逻辑，因为SentiCrypt有特殊的2小时更新周期
   */
  private isCacheValid(): boolean {
    if (!this.cacheTimestamp || this.dataCache.length === 0) {
      return false;
    }

    const now = new Date();
    const diffHours = (now.getTime() - this.cacheTimestamp.getTime()) / (1000 * 60 * 60);
    return diffHours < this.cacheValidityHours;
  }

  /**
   * 清除缓存 - 清除本地缓存和基类缓存
   */
  clearCache(): void {
    this.dataCache = [];
    this.cacheTimestamp = null;
    super.clearCache(); // 调用基类的缓存清除
  }



  /**
   * 测试连接 - 重写基类方法，使用SentiCrypt特有的健康检查端点
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.fetchAndProcess<any>(
        '/index.json',
        {
          useCache: false,
          timeout: 5000
        },
        {
          dataType: 'health-check',
          businessSystem: 'market-data',
          metadata: {
            source: 'senticrypt',
            requestType: 'health-check'
          }
        }
      );
      return true;
    } catch (error) {
      this.logger.warn('SentiCrypt API健康检查失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  // 实现ISentiCryptAdapter接口的其他方法
  async getCurrentSentiment(asset?: string): Promise<SentimentData> {
    const processedData = await this.getTodaySentiment();
    return this.convertToSentimentData(processedData, asset || 'BTC');
  }

  async getHistoricalSentiment(asset: string, days?: number): Promise<SentimentData[]> {
    const historicalData = await this.getRecentSentimentData(days || 7);
    return historicalData.map(data => this.convertRawToSentimentData(data, asset));
  }

  async getSocialMediaSentiment(asset: string): Promise<SocialMediaSentimentData> {
    // SentiCrypt主要提供综合情绪数据，这里返回基于综合数据的社交媒体情绪
    const processedData = await this.getTodaySentiment();
    return this.convertToSocialMediaSentiment(processedData, asset);
  }

  async getNewsSentiment(asset: string): Promise<NewsSentimentData> {
    // SentiCrypt主要提供综合情绪数据，这里返回基于综合数据的新闻情绪
    const processedData = await this.getTodaySentiment();
    return this.convertToNewsSentiment(processedData, asset);
  }

  async getSentimentTrend(asset: string, period: 'hour' | 'day' | 'week'): Promise<SentimentTrendData> {
    const days = period === 'hour' ? 1 : period === 'day' ? 7 : 30;
    const historicalData = await this.getRecentSentimentData(days);
    return this.convertToSentimentTrend(historicalData, asset, period);
  }

  async getSentimentSummary(asset: string): Promise<SentimentSummaryData> {
    const processedData = await this.getTodaySentiment();
    const historicalData = await this.getRecentSentimentData(30);
    return this.convertToSentimentSummary(processedData, historicalData, asset);
  }

  async isAvailable(): Promise<boolean> {
    return this.testConnection();
  }

  // 健康检查已由基类 ExternalDataAdapterBase 提供 - 避免重复实现
  // 基类的 healthCheck() 方法已实现相同的逻辑和返回类型

  // 私有转换方法
  private convertToSentimentData(processedData: ProcessedSentimentData, asset: string): SentimentData {
    return {
      asset,
      timestamp: processedData.timestamp,
      overallSentiment: processedData.rawScores.mean,
      sentimentClassification: this.mapSentimentLevel(processedData.sentimentLevel),
      confidence: processedData.dataQuality,
      sources: {
        twitter: processedData.twitterSentiment,
        reddit: processedData.redditSentiment,
        news: processedData.newsScore
      },
      volume: {
        mentions: 0, // SentiCrypt不提供具体数量
        posts: 0,
        comments: 0,
        shares: 0
      },
      keywords: [] // SentiCrypt不提供关键词数据
    };
  }

  private convertRawToSentimentData(rawData: SentiCryptData, asset: string): SentimentData {
    return {
      asset,
      timestamp: new Date(rawData.date),
      overallSentiment: rawData.mean,
      sentimentClassification: this.mapSentimentLevelFromMean(rawData.mean),
      confidence: Math.min(rawData.count / 100, 1), // 基于数据点数量估算置信度
      sources: {},
      volume: {
        mentions: rawData.count,
        posts: 0,
        comments: 0,
        shares: 0
      },
      keywords: []
    };
  }

  private convertToSocialMediaSentiment(processedData: ProcessedSentimentData, asset: string): SocialMediaSentimentData {
    return {
      asset,
      timestamp: processedData.timestamp,
      platforms: {
        twitter: {
          sentiment: processedData.twitterSentiment || processedData.rawScores.mean,
          volume: 0,
          engagement: 0,
          influencerSentiment: processedData.rawScores.mean,
          trendingHashtags: []
        },
        reddit: {
          sentiment: processedData.redditSentiment || processedData.rawScores.mean,
          volume: 0,
          upvoteRatio: 0.5,
          commentSentiment: processedData.rawScores.mean,
          popularSubreddits: []
        },
        telegram: {
          sentiment: processedData.rawScores.mean,
          volume: 0,
          groupActivity: processedData.rawScores.mean,
          memberGrowth: 0
        },
        discord: {
          sentiment: processedData.rawScores.mean,
          volume: 0,
          serverActivity: 0,
          memberEngagement: 0
        }
      },
      aggregatedSentiment: processedData.rawScores.mean,
      socialVolume: 0,
      viralityScore: 0
    };
  }

  private convertToNewsSentiment(processedData: ProcessedSentimentData, asset: string): NewsSentimentData {
    return {
      asset,
      timestamp: processedData.timestamp,
      overallSentiment: processedData.rawScores.mean,
      newsVolume: 0,
      sources: [],
      categories: {
        regulatory: 0,
        technical: 0,
        adoption: 0,
        market: processedData.rawScores.mean,
        security: 0
      },
      topHeadlines: []
    };
  }

  private convertToSentimentTrend(historicalData: SentiCryptData[], asset: string, period: string): SentimentTrendData {
    const dataPoints = historicalData.map(data => ({
      timestamp: new Date(data.date),
      sentiment: data.mean,
      volume: data.count
    }));

    const trend = this.calculateTrendDirection(historicalData);

    return {
      asset,
      period,
      startTime: new Date(historicalData[0]?.date || new Date()),
      endTime: new Date(historicalData[historicalData.length - 1]?.date || new Date()),
      trend,
      trendStrength: this.calculateTrendStrength(historicalData),
      volatility: this.calculateVolatility(historicalData),
      momentum: this.calculateMomentum(historicalData),
      dataPoints,
      significantEvents: []
    };
  }

  private convertToSentimentSummary(processedData: ProcessedSentimentData, historicalData: SentiCryptData[], asset: string): SentimentSummaryData {
    return {
      asset,
      timestamp: processedData.timestamp,
      currentSentiment: processedData.rawScores.mean,
      sentimentGrade: this.calculateSentimentGrade(processedData.sentimentScore),
      weeklyChange: this.calculateWeeklyChange(historicalData),
      monthlyChange: this.calculateMonthlyChange(historicalData),
      volatilityIndex: this.calculateVolatility(historicalData),
      reliabilityScore: processedData.dataQuality,
      marketCorrelation: processedData.priceCorrelation,
      predictiveAccuracy: 0.75, // 默认值
      riskLevel: this.calculateRiskLevel(processedData.sentimentStrength),
      recommendations: [processedData.analysis],
      alerts: []
    };
  }

  // 辅助方法
  private mapSentimentLevel(level: string): 'Very Negative' | 'Negative' | 'Neutral' | 'Positive' | 'Very Positive' {
    switch (level) {
      case 'VERY_NEGATIVE': return 'Very Negative';
      case 'NEGATIVE': return 'Negative';
      case 'NEUTRAL': return 'Neutral';
      case 'POSITIVE': return 'Positive';
      case 'VERY_POSITIVE': return 'Very Positive';
      default: return 'Neutral';
    }
  }

  private mapSentimentLevelFromMean(mean: number): 'Very Negative' | 'Negative' | 'Neutral' | 'Positive' | 'Very Positive' {
    if (mean >= 0.5) return 'Very Positive';
    if (mean >= 0.2) return 'Positive';
    if (mean >= -0.2) return 'Neutral';
    if (mean >= -0.5) return 'Negative';
    return 'Very Negative';
  }

  private calculateTrendDirection(data: SentiCryptData[]): 'improving' | 'declining' | 'stable' | 'volatile' {
    if (data.length < 2) return 'stable';

    const recent = data.slice(-3);
    const older = data.slice(-6, -3);

    const recentAvg = recent.reduce((sum, d) => sum + d.mean, 0) / recent.length;
    const olderAvg = older.reduce((sum, d) => sum + d.mean, 0) / older.length;

    const diff = recentAvg - olderAvg;

    if (Math.abs(diff) < 0.1) return 'stable';
    if (diff > 0.2) return 'improving';
    if (diff < -0.2) return 'declining';
    return 'volatile';
  }

  private calculateTrendStrength(data: SentiCryptData[]): number {
    if (data.length < 2) return 0;

    const values = data.map(d => d.mean);
    const changes = values.slice(1).map((val, i) => Math.abs(val - values[i]));
    return changes.reduce((sum, change) => sum + change, 0) / changes.length;
  }

  private calculateVolatility(data: SentiCryptData[]): number {
    if (data.length < 2) return 0;

    const values = data.map(d => d.mean);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  private calculateMomentum(data: SentiCryptData[]): number {
    if (data.length < 2) return 0;

    const recent = data[data.length - 1].mean;
    const previous = data[data.length - 2].mean;
    return recent - previous;
  }

  private calculateSentimentGrade(score: number): 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D+' | 'D' | 'F' {
    if (score >= 90) return 'A+';
    if (score >= 85) return 'A';
    if (score >= 80) return 'B+';
    if (score >= 75) return 'B';
    if (score >= 70) return 'C+';
    if (score >= 65) return 'C';
    if (score >= 60) return 'D+';
    if (score >= 55) return 'D';
    return 'F';
  }

  private calculateWeeklyChange(data: SentiCryptData[]): number {
    if (data.length < 7) return 0;

    const recent = data[data.length - 1].mean;
    const weekAgo = data[data.length - 7].mean;
    return recent - weekAgo;
  }

  private calculateMonthlyChange(data: SentiCryptData[]): number {
    if (data.length < 30) return 0;

    const recent = data[data.length - 1].mean;
    const monthAgo = data[data.length - 30].mean;
    return recent - monthAgo;
  }

  private calculateRiskLevel(strength: number): 'low' | 'medium' | 'high' {
    if (strength < 0.3) return 'low';
    if (strength < 0.7) return 'medium';
    return 'high';
  }
}

export default SentiCryptAdapter;
