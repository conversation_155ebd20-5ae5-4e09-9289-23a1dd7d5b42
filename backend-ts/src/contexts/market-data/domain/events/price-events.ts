/**
 * 市场数据相关的领域事件
 */

import { DomainEvent } from '../../../../shared/domain/events/domain-event';
import { TradingSymbol } from '../value-objects/trading-symbol';
import { Price } from '../value-objects/price';
import { Volume } from '../value-objects/volume';

/**
 * 价格更新事件
 */
export class PriceUpdatedEvent extends DomainEvent {
  public readonly symbol: TradingSymbol;
  public readonly price: Price;
  public readonly volume: Volume;
  public readonly change24h: number;
  public readonly changePercent24h: number;
  public readonly timestamp: Date;

  constructor(
    symbol: TradingSymbol,
    price: Price,
    volume: Volume,
    change24h: number,
    changePercent24h: number,
    timestamp?: Date
  ) {
    super(symbol.value, {
      symbol: symbol.value,
      price: price.value,
      volume: volume.value,
      change24h,
      changePercent24h,
      timestamp: (timestamp || new Date()).toISOString(),
    });

    this.symbol = symbol;
    this.price = price;
    this.volume = volume;
    this.change24h = change24h;
    this.changePercent24h = changePercent24h;
    this.timestamp = timestamp || new Date();
  }

  getEventName(): string {
    return 'PriceUpdatedEvent';
  }

  getAggregateId(): string {
    return this.symbol.value;
  }

  getEventData(): Record<string, any> {
    return {
      symbol: this.symbol.value,
      price: this.price.value,
      volume: this.volume.value,
      change24h: this.change24h,
      changePercent24h: this.changePercent24h,
      timestamp: this.timestamp
    };
  }
}

/**
 * K线更新事件
 */
export class KlineUpdatedEvent extends DomainEvent {
  public readonly symbol: TradingSymbol;
  public readonly timeframe: any; // 使用any以兼容Timeframe类型
  public readonly open: Price;
  public readonly high: Price;
  public readonly low: Price;
  public readonly close: Price;
  public readonly volume: Volume;
  public readonly timestamp: Date;
  public readonly isFinal: boolean;

  constructor(
    symbol: TradingSymbol,
    timeframe: any,
    open: Price,
    high: Price,
    low: Price,
    close: Price,
    volume: Volume,
    timestamp: Date,
    isFinal: boolean = false
  ) {
    super(`${symbol.value}-${timeframe.value || timeframe}`, {
      symbol: symbol.value,
      timeframe: timeframe.value || timeframe,
      open: open.value,
      high: high.value,
      low: low.value,
      close: close.value,
      volume: volume.value,
      timestamp: timestamp.toISOString(),
      isFinal,
    });

    this.symbol = symbol;
    this.timeframe = timeframe;
    this.open = open;
    this.high = high;
    this.low = low;
    this.close = close;
    this.volume = volume;
    this.timestamp = timestamp;
    this.isFinal = isFinal;
  }

  getEventName(): string {
    return 'KlineUpdatedEvent';
  }

  getAggregateId(): string {
    return `${this.symbol.value}-${this.timeframe.value || this.timeframe}`;
  }

  getEventData(): Record<string, any> {
    return {
      symbol: this.symbol.value,
      timeframe: this.timeframe.value || this.timeframe,
      open: this.open.value,
      high: this.high.value,
      low: this.low.value,
      close: this.close.value,
      volume: this.volume.value,
      timestamp: this.timestamp,
      isFinal: this.isFinal
    };
  }
}

/**
 * 市场数据连接状态事件
 */
export class MarketDataConnectionEvent extends DomainEvent {
  public readonly connectionId: string;
  public readonly status: 'connected' | 'disconnected' | 'error';
  public readonly error?: string;

  constructor(
    connectionId: string,
    status: 'connected' | 'disconnected' | 'error',
    error?: string
  ) {
    super(connectionId, {
      connectionId,
      status,
      error
    });
    this.connectionId = connectionId;
    this.status = status;
    this.error = error;
  }

  getEventName(): string {
    return 'MarketDataConnectionEvent';
  }

  getAggregateId(): string {
    return this.connectionId;
  }

  getEventData(): Record<string, any> {
    return {
      connectionId: this.connectionId,
      status: this.status,
      error: this.error,
      timestamp: this.occurredAt
    };
  }
}
