/**
 * 环境变量配置
 * @deprecated 请使用 ../shared/infrastructure/config/config-validation 中的统一验证逻辑
 */

import {
  ENVIRONMENT_SCHEMA,
  Environment,
  validateEnvironment
} from '../shared/infrastructure/config/config-validation';

/**
 * 环境变量验证模式
 * @deprecated 请使用 ENVIRONMENT_SCHEMA 替代
 */
/**
 * @deprecated 请使用 ENVIRONMENT_SCHEMA 替代
 */
const environmentSchema = ENVIRONMENT_SCHEMA;

/**
 * 环境变量类型
 * @deprecated 请使用 Environment 从 config-validation 替代
 */
export type { Environment };

/**
 * 验证并获取环境变量
 * @deprecated 请使用 validateEnvironment 替代
 */
export function getEnvironment(): Environment {
  return validateEnvironment();
}

// 重新导出统一配置验证中的实用函数
export {
  validateEnvironment,
  getRedisUrl,
  isDevelopment,
  isProduction,
  isTest
} from '../shared/infrastructure/config/config-validation';

// 这些函数已经移动到统一配置验证中，这里保留是为了向后兼容
// 实际实现已经通过重新导出提供
