import { getEnvironmentManager } from '../shared/infrastructure/config/config-validation';

/**
 * 数据库配置
 * 提供数据库连接和配置信息
 */
export interface DatabaseConfig {
  url: string;
  connectionPoolSize?: number;
  queryTimeout?: number;
  slowQueryThreshold?: number;
  batchSize?: number;
  indexOptimization?: boolean;
  connectionIdleTimeout?: number;
}

/**
 * 获取数据库配置
 * @returns 数据库配置对象
 */
export function getDatabaseConfig(): DatabaseConfig {
  const envManager = getEnvironmentManager();
  return envManager.getDatabaseConfig();
}

/**
 * 获取数据库URL
 * @returns 数据库连接URL
 */
export function getDatabaseUrl(): string {
  const config = getDatabaseConfig();
  return config.url;
}

export default {
  getDatabaseConfig,
  getDatabaseUrl
};