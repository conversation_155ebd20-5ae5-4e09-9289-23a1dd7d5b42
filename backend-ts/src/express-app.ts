/**
 * Express应用程序类 - 重构版本
 * 
 * 重构说明：
 * - 原始的555行大文件已按功能模块拆分为多个小文件
 * - 中间件设置移至 express-app/modules/middleware-setup.ts
 * - 路由设置移至 express-app/modules/routes-setup.ts
 * - Swagger配置移至 express-app/modules/swagger-setup.ts
 * - 错误处理移至 express-app/modules/error-handling-setup.ts
 * - WebSocket设置移至 express-app/modules/websocket-setup.ts
 * - 服务启动移至 express-app/modules/services-startup.ts
 * 
 * 此文件现在作为应用组合器，使用现有的应用基础设施
 */

import express, { Express } from 'express';
import { createServer, Server as HTTPServer } from 'http';
import { Container } from 'inversify';
import { Logger } from 'winston';
import * as crypto from 'crypto';
import { TYPES } from './shared/infrastructure/di/types/index';
import { createRateLimitMiddleware } from './api/middleware/modules/rate-limit-middleware';

// 导入模块化应用组件 - 使用现有的应用基础设施
import { createMiddlewareSetup } from './express-app/modules/middleware-setup';
import { createRoutesSetup } from './express-app/modules/routes-setup';
import { createSwaggerSetup } from './express-app/modules/swagger-setup';
import { createErrorHandlingSetup } from './express-app/modules/error-handling-setup';
import { createWebSocketSetup } from './express-app/modules/websocket-setup';
import { createServicesStartup } from './express-app/modules/services-startup';

/**
 * Express应用程序组合器 - 使用现有应用基础设施
 * 遵循单一职责原则，将大应用拆分为功能模块
 */
export class ExpressApplication {
  private readonly app: Express;
  private readonly container: Container;
  private readonly logger: Logger;
  private httpServer: HTTPServer | null = null;

  constructor(container: Container) {
    console.log('🔧 ExpressApplication构造函数开始执行...');
    this.app = express();
    console.log('✅ Express应用实例创建完成');
    this.container = container;
    console.log('✅ 容器引用设置完成');
    
    console.log('🔧 开始从容器获取Logger...');
    this.logger = container.get<Logger>(TYPES.Logger);
    console.log('✅ Logger获取完成');

    console.log('🔧 开始设置基础中间件...');
    // 初始化基础中间件
    this.setupBasicMiddleware();
    console.log('✅ ExpressApplication构造函数执行完成');
  }

  /**
   * 设置基础中间件
   */
  private setupBasicMiddleware(): void {
    console.log('🔧 正在设置基础中间件...');

    // 创建限流中间件
    const rateLimitMiddleware = createRateLimitMiddleware(this.container);
    console.log('✅ 限流中间件创建完成');

    // API限流中间件 - 在解析中间件之前应用
    console.log('🔧 正在应用限流中间件...');
    this.app.use('/api', (req, res, next) => {
      console.log(`🔍 限流中间件被调用: ${req.method} ${req.path}`);
      next();
    });
    this.app.use('/api', rateLimitMiddleware.smart);
    this.app.use('/health', rateLimitMiddleware.healthCheck);
    console.log('✅ 限流中间件应用完成');

    // 基础中间件
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // CORS
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // 🔥 修复虚假实现：使用加密安全的请求ID生成
    this.app.use((req: any, res, next) => {
      // 使用crypto.randomUUID()生成安全的请求ID
      req.id = crypto.randomUUID().replace(/-/g, '').substring(0, 12);
      next();
    });

    // 响应格式化中间件
    this.app.use((req: any, res: any, next) => {
      res.success = (data: any, message = 'Success') => {
        res.json({
          success: true,
          message,
          data,
          requestId: req.id,
          timestamp: new Date().toISOString()
        });
      };

      res.error = (message: string, statusCode = 500, details?: any) => {
        res.status(statusCode).json({
          success: false,
          error: message,
          details,
          requestId: req.id,
          timestamp: new Date().toISOString()
        });
      };

      next();
    });
  }

  /**
   * 初始化路由（异步）
   */
  async initializeRoutes(): Promise<void> {
    this.logger.info('🔧 ExpressApplication: 开始设置根路由...');
    // 设置根路由
    this.setupRootRoutes();
    this.logger.info('✅ ExpressApplication: 根路由设置完成');

    this.logger.info('🔧 ExpressApplication: 开始设置Swagger文档...');
    // 设置Swagger API文档
    this.setupSwaggerDocumentation();
    this.logger.info('✅ ExpressApplication: Swagger文档设置完成');

    this.logger.info('🔧 ExpressApplication: 开始导入RouteRegistrar...');
    // 使用现有的路由注册器
    const { RouteRegistrar } = await import('./api/routes');
    this.logger.info('✅ ExpressApplication: RouteRegistrar导入完成');
    
    this.logger.info('🔧 ExpressApplication: 开始创建RouteRegistrar实例...');
    const routeRegistrar = new RouteRegistrar(this.app, this.container);
    this.logger.info('✅ ExpressApplication: RouteRegistrar实例创建完成');
    
    this.logger.info('🔧 ExpressApplication: 开始注册路由...');
    await routeRegistrar.registerRoutes();
    this.logger.info('✅ ExpressApplication: 路由注册完成');

    this.logger.info('🔧 ExpressApplication: 开始设置错误处理...');
    // 设置错误处理
    this.setupErrorHandling();
    this.logger.info('✅ ExpressApplication: 错误处理设置完成');
    
    this.logger.info('✅ ExpressApplication: initializeRoutes方法执行完成');
  }

  /**
   * 设置Swagger API文档
   */
  private setupSwaggerDocumentation(): void {
    try {
      const swaggerSetup = createSwaggerSetup(this.container);
      swaggerSetup.setupSwagger(this.app);
      swaggerSetup.setupDocumentationRedirects(this.app);
      swaggerSetup.setupDevelopmentDocs(this.app);

      this.logger.info('✅ Swagger API文档设置完成');
    } catch (error) {
      this.logger.error('❌ Swagger API文档设置失败', { error });
      // 不抛出错误，让应用继续运行
    }
  }

  /**
   * 设置根路由
   */
  private setupRootRoutes(): void {
    // 根路径
    this.app.get('/', (req: any, res: any) => {
      res.success({
        message: 'Welcome to Trading System API',
        version: process.env.npm_package_version || '1.0.0',
        documentation: '/api-docs',
        health: '/health',
        endpoints: {
          ai: '/api/v1/ai',
          auth: '/api/v1/auth',
          admin: '/api/v1/admin',
          marketData: '/api/v1/market-data',
          riskAssessment: '/api/v1/risk-assessment',
          trendAnalysis: '/api/v1/trend-analysis',
          tradingExecution: '/api/v1/trading-execution',
          tradingSignals: '/api/v1/trading-signals'
        }
      }, 'API服务正常运行');
    });

    // API根路径
    this.app.get('/api', (req: any, res: any) => {
      res.success({
        message: 'Trading System API',
        version: 'v1',
        endpoints: {
          ai: '/api/v1/ai',
          auth: '/api/v1/auth',
          admin: '/api/v1/admin',
          marketData: '/api/v1/market-data',
          riskAssessment: '/api/v1/risk-assessment',
          trendAnalysis: '/api/v1/trend-analysis',
          tradingExecution: '/api/v1/trading-execution',
          tradingSignals: '/api/v1/trading-signals'
        }
      }, 'API端点列表');
    });

    // 健康检查
    this.app.get('/health', (req: any, res: any) => {
      res.success({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.env.npm_package_version || '1.0.0'
      }, '系统健康状态正常');
    });
  }

  /**
   * 动态获取所有可用的API端点
   * 通过反射Express应用的路由表获取真实的端点列表
   */
  private getAvailableEndpoints(): string[] {
    const endpoints: string[] = [];

    try {
      // 递归遍历Express应用的路由栈
      const extractRoutes = (stack: any[], basePath: string = '') => {
        stack.forEach((layer: any) => {
          if (layer.route) {
            // 直接路由
            const path = basePath + layer.route.path;
            const methods = Object.keys(layer.route.methods)
              .filter(method => method !== '_all')
              .map(method => method.toUpperCase());

            methods.forEach(method => {
              endpoints.push(`${method} ${path}`);
            });
          } else if (layer.name === 'router' && layer.handle?.stack) {
            // 嵌套路由器
            const routerPath = layer.regexp.source
              .replace(/\\\//g, '/')
              .replace(/\$.*/, '')
              .replace(/\^/, '')
              .replace(/\?\(\?\=/g, '')
              .replace(/\)\?\$/, '');

            const cleanPath = basePath + routerPath.replace(/\\/g, '');
            extractRoutes(layer.handle.stack, cleanPath);
          }
        });
      };

      // 从Express应用的路由栈开始提取
      if (this.app._router && this.app._router.stack) {
        extractRoutes(this.app._router.stack);
      }

      // 去重并排序
      const uniqueEndpoints = [...new Set(endpoints)].sort();

      // 如果反射失败，返回基础端点作为后备
      if (uniqueEndpoints.length === 0) {
        return [
          "GET /health",
          "GET /api/v1/market-data/prices",
          "POST /api/v1/ai/reasoning",
          "POST /api/v1/auth/login"
        ];
      }

      return uniqueEndpoints;
    } catch (error) {
      this.logger.error('获取可用端点失败，使用后备列表', { error: error instanceof Error ? error.message : String(error) });

      // 错误时返回基础端点
      return [
        "GET /health",
        "GET /api/v1/market-data/prices",
        "POST /api/v1/ai/reasoning",
        "POST /api/v1/auth/login",
        "注意: 端点列表获取失败，这可能不是完整列表"
      ];
    }
  }

  /**
   * 设置错误处理
   */
  private setupErrorHandling(): void {
    // 404处理 - 动态获取真实可用端点
    this.app.use((req: any, res: any) => {
      const availableEndpoints = this.getAvailableEndpoints();

      res.error(`Route not found: ${req.method} ${req.url}`, 404, {
        availableEndpoints: availableEndpoints,
        note: "这是通过反射Express路由表动态获取的真实端点列表",
        documentation: "完整API文档请查看 /API_DOCUMENTATION.md",
        totalEndpoints: availableEndpoints.length
      });
    });

    // 错误处理
    this.app.use((error: any, req: any, res: any, next: any) => {
      this.logger.error('Express错误处理', {
        requestId: req.id,
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method
      });

      const statusCode = error.statusCode || error.status || 500;
      const message = statusCode === 500 ? 'Internal Server Error' : error.message;

      res.error(message, statusCode, {
        ...(process.env.NODE_ENV === 'development' && {
          stack: error.stack
        })
      });
    });
  }

  /**
   * 启动服务器
   */
  async listen(port: number): Promise<void> {
    this.logger.info('🔧 创建HTTP服务器...');
    // 创建HTTP服务器
    this.httpServer = createServer(this.app);
    this.logger.info('✅ HTTP服务器创建完成');

    this.logger.info('🔧 开始设置WebSocket（异步非阻塞）...');
    // 设置WebSocket（完全异步，不阻塞启动）
    this.setupWebSocketAsync();
    this.logger.info('✅ WebSocket设置已调度为异步执行');

    this.logger.info('🔧 开始监听端口...', { port });
    // 启动服务器
    return new Promise<void>((resolve, reject) => {
      this.httpServer!.listen(port, (error?: Error) => {
        if (error) {
          this.logger.error('❌ 服务器启动失败', { error: error.message, port });
          reject(error);
        } else {
          this.logger.info(`🚀 服务器启动成功，端口: ${port}`);
          resolve();
        }
      });
    });
  }

  /**
   * 异步设置WebSocket（不阻塞主线程）
   */
  private setupWebSocketAsync(): void {
    // 延迟启动WebSocket，确保HTTP服务器先完全启动
    setTimeout(async () => {
      try {
        this.logger.info('🔧 开始异步初始化WebSocket服务器...');
        const { setupWebSocket } = await import('./shared/infrastructure/websocket/websocket-server');
        await setupWebSocket(this.httpServer!);
        this.logger.info('✅ WebSocket服务器异步初始化完成');
      } catch (error) {
        this.logger.error('❌ WebSocket服务器异步初始化失败，但不影响主服务器运行', { error });
      }
    }, 1000); // 延迟1秒启动
  }
}

// 重新导出模块化组件，保持向后兼容性
export { createMiddlewareSetup } from './express-app/modules/middleware-setup';
export { createRoutesSetup } from './express-app/modules/routes-setup';
export { createSwaggerSetup } from './express-app/modules/swagger-setup';
export { createErrorHandlingSetup } from './express-app/modules/error-handling-setup';
export { createWebSocketSetup } from './express-app/modules/websocket-setup';
export { createServicesStartup } from './express-app/modules/services-startup';
