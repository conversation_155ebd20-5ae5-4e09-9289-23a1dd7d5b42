/**
 * 风险评估综合分析路由模块
 * 从大文件中拆分出来，遵循单一职责原则
 */

import { Router, Request, Response } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { RiskAssessmentApplicationService } from '../../../../contexts/risk-management/application/services/risk-assessment-application-service';
import { TradingExecutionApplicationService } from '../../../../contexts/trading-execution/application/services/trading-execution-application-service';
import { Logger } from 'winston';
import { Position, PositionType } from '../../../../contexts/risk-management/domain/value-objects/position';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      id?: string;
      startTime?: number;
    }
  }
}

/**
 * 创建综合分析路由
 * 包含：/comprehensive-analysis/:accountId
 */
export function createComprehensiveAnalysisRoutes(container: Container): Router {
  const router = Router();
  const logger = container.get<Logger>(TYPES.Logger);

  // 获取服务
  const getRiskAssessmentService = (): RiskAssessmentApplicationService => {
    return container.get<RiskAssessmentApplicationService>(
      TYPES.RiskManagement.RiskAssessmentApplicationService
    );
  };

  const getTradingExecutionService = (): TradingExecutionApplicationService => {
    return container.get<TradingExecutionApplicationService>(
      TYPES.TradingExecution.TradingExecutionApplicationService
    );
  };

  // 异步处理器
  const asyncHandler = (fn: (req: Request, res: Response, next?: Function) => Promise<any>) =>
    (req: Request, res: Response, next: Function) => {
      Promise.resolve(fn(req, res, next)).catch((error: any) => next(error));
    };

  /**
   * @swagger
   * /api/v1/risk-assessment/comprehensive-analysis/{accountId}:
   *   get:
   *     summary: 获取综合风险分析
   *     tags: [风险评估]
   *     parameters:
   *       - in: path
   *         name: accountId
   *         required: true
   *         schema:
   *           type: string
   *         description: 账户ID
   *       - in: query
   *         name: includeStressTest
   *         schema:
   *           type: boolean
   *           default: true
   *         description: 是否包含压力测试
   *       - in: query
   *         name: includeScenarioAnalysis
   *         schema:
   *           type: boolean
   *           default: true
   *         description: 是否包含场景分析
   *       - in: query
   *         name: timeHorizon
   *         schema:
   *           type: number
   *           default: 30
   *         description: 分析时间范围（天）
   *     responses:
   *       200:
   *         description: 综合风险分析结果
   */
  router.get('/comprehensive-analysis/:accountId', asyncHandler(async (req: Request, res: Response) => {
    try {
      const { accountId } = req.params;
      const { 
        includeStressTest = true, 
        includeScenarioAnalysis = true,
        timeHorizon = 30
      } = req.query;

      const riskAssessmentService = getRiskAssessmentService();
      const tradingExecutionService = getTradingExecutionService();

      const account = await tradingExecutionService.getTradingAccountDetails(accountId);
      if (!account) {
        return res.status(404).json({
          success: false,
          error: '账户不存在',
          requestId: req.id
        });
      }

      const positions = await tradingExecutionService.getActivePositions(accountId);
      const balance = await tradingExecutionService.getAccountBalance(accountId);

      const positionRiskAssessments = [];
      for (const position of positions) {
        try {
          const riskRequest = {
            userId: 'default-user', // TODO: 从认证中获取真实用户ID
            symbol: position.symbol,
            portfolioId: accountId,
            position: Position.create(
              position.symbol,
              position.side === 'BUY' ? PositionType.LONG : PositionType.SHORT,
              position.size,
              position.averagePrice,
              position.currentPrice || position.averagePrice,
              new Date()
            ),
            marketData: {
              currentPrice: position.currentPrice || position.averagePrice,
              volume: position.volume || 0,
            }
          };

          const assessment = await riskAssessmentService.assessRisk(riskRequest);
          positionRiskAssessments.push({
            position,
            assessment: assessment.assessment,
            alerts: assessment.alerts || [],
            recommendations: assessment.recommendations || []
          });

        } catch (positionError) {
          logger.warn('持仓风险评估失败', {
            symbol: position.symbol,
            error: positionError instanceof Error ? positionError.message : '未知错误'
          });
        }
      }

      // 3. 计算投资组合整体风险指标
      const portfolioMetrics = {
        totalValue: balance.totalBalance || 0,
        totalPnl: positions.reduce((sum, pos) => sum + (pos.unrealizedPnl || 0), 0),
        positionCount: positions.length,
        averageRiskScore: positionRiskAssessments.length > 0 
          ? positionRiskAssessments.reduce((sum, item) => sum + item.assessment.riskScore, 0) / positionRiskAssessments.length
          : 0,
        highRiskPositions: positionRiskAssessments.filter(item => item.assessment.riskLevel === 'HIGH').length,
        mediumRiskPositions: positionRiskAssessments.filter(item => item.assessment.riskLevel === 'MEDIUM').length,
        lowRiskPositions: positionRiskAssessments.filter(item => item.assessment.riskLevel === 'LOW').length
      };

      // 4. 生成投资组合风险等级
      const portfolioRiskLevel = portfolioMetrics.averageRiskScore > 75 ? 'HIGH' :
                                portfolioMetrics.averageRiskScore > 50 ? 'MEDIUM' : 'LOW';

      // 5. 收集所有告警和建议
      const allAlerts = positionRiskAssessments.flatMap(item => item.alerts);
      const allRecommendations = positionRiskAssessments.flatMap(item => item.recommendations);

      // 6. 生成综合分析结果
      const comprehensiveAnalysis = {
        accountId,
        analysisDate: new Date().toISOString(),
        timeHorizon: parseInt(timeHorizon as string),
        
        // 账户概览
        accountOverview: {
          accountType: account.accountType,
          totalBalance: balance.totalBalance,
          availableBalance: balance.availableBalance,
          marginUsed: balance.marginUsed || 0,
        },

        // 投资组合风险概览
        portfolioRisk: {
          overallRiskLevel: portfolioRiskLevel,
          overallRiskScore: portfolioMetrics.averageRiskScore,
          totalValue: portfolioMetrics.totalValue,
          totalPnl: portfolioMetrics.totalPnl,
        },

        // 持仓分布
        positionDistribution: {
          total: portfolioMetrics.positionCount,
          highRisk: portfolioMetrics.highRiskPositions,
          mediumRisk: portfolioMetrics.mediumRiskPositions,
          lowRisk: portfolioMetrics.lowRiskPositions
        },

        // 详细持仓风险
        positionRisks: positionRiskAssessments.map(item => ({
          symbol: item.position.symbol,
          size: item.position.size,
          value: item.position.size * item.position.currentPrice,
          pnl: item.position.unrealizedPnl || 0,
          riskLevel: item.assessment.riskLevel,
          riskScore: item.assessment.riskScore,
          keyRiskFactors: item.assessment.keyRiskFactors.slice(0, 3),
          maxLoss: item.assessment.maxLoss
        })),

        // 告警和建议
        alerts: allAlerts.slice(0, 10), // 最多显示10个告警
        recommendations: Array.from(new Set(allRecommendations)).slice(0, 8), // 去重后最多8个建议

        // 风险指标摘要
        riskMetrics: {
          portfolioVar95: positionRiskAssessments.reduce((sum, item) => 
            sum + (item.assessment.maxLoss?.var95 || 0), 0),
          portfolioVar99: positionRiskAssessments.reduce((sum, item) => 
            sum + (item.assessment.maxLoss?.var99 || 0), 0),
          expectedShortfall: positionRiskAssessments.reduce((sum, item) => 
            sum + (item.assessment.maxLoss?.expectedShortfall || 0), 0),
          maxDrawdown: Math.max(...positionRiskAssessments.map(item =>
            item.assessment.maxLoss?.maxDrawdown || 0))
        }
      };

      logger.info('综合风险分析完成', {
        accountId,
        positionCount: positions.length,
        portfolioRiskLevel,
        averageRiskScore: portfolioMetrics.averageRiskScore,
        alertCount: allAlerts.length,
        requestId: req.id
      });

      return res.json({
        success: true,
        data: comprehensiveAnalysis,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('综合风险分析失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '综合风险分析失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  return router;
}
