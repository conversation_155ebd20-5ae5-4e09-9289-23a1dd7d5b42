/**
 * 趋势分析综合分析路由模块
 * 从大文件中拆分出来，遵循单一职责原则
 */

import { Router, Request, Response } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { TrendAnalysisApplicationService } from '../../../../contexts/trend-analysis/application/trend-analysis-application.service';
import { Logger } from 'winston';

/**
 * 创建综合分析路由
 * 包含：/comprehensive-analysis
 */
export function createComprehensiveAnalysisRoutes(container: Container): Router {
  const router = Router();
  const logger = container.get<Logger>(TYPES.Logger);

  // 获取趋势分析服务
  const getTrendAnalysisService = (): TrendAnalysisApplicationService => {
    return container.get<TrendAnalysisApplicationService>(TYPES.TrendAnalysis.TrendAnalysisApplicationService);
  };

  // 异步处理器
  const asyncHandler = (fn: Function) => (req: Request, res: Response, next: Function) => {
    Promise.resolve(fn(req, res, next)).catch((error) => next(error));
  };

  /**
   * @swagger
   * /api/v1/trend/comprehensive-analysis:
   *   post:
   *     summary: 四维度综合分析
   *     tags: [趋势分析]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               symbol:
   *                 type: string
   *                 description: 交易对符号
   *               timeframe:
   *                 type: string
   *                 description: 时间框架
   *               analysisDepth:
   *                 type: string
   *                 enum: [basic, standard, comprehensive]
   *                 description: 分析深度
   *               includeTechnical:
   *                 type: boolean
   *                 description: 是否包含技术分析
   *               includeFundamental:
   *                 type: boolean
   *                 description: 是否包含基本面分析
   *               includeSentiment:
   *                 type: boolean
   *                 description: 是否包含情绪分析
   *               includeQuantitative:
   *                 type: boolean
   *                 description: 是否包含量化分析
   *     responses:
   *       200:
   *         description: 四维度综合分析结果
   */
  router.post('/comprehensive-analysis', asyncHandler(async (req: Request, res: Response) => {
    try {
      const { 
        symbol, 
        timeframe = '1h', 
        analysisDepth = 'comprehensive',
        includeTechnical = true,
        includeFundamental = true,
        includeSentiment = true,
        includeQuantitative = true
      } = req.body;

      if (!symbol) {
        return res.status(400).json({
          success: false,
          error: '缺少必需参数: symbol',
          requestId: req.id
        });
      }

      const normalizedSymbol = symbol.toUpperCase();
      const trendAnalysisService = getTrendAnalysisService();

      // 执行四维度综合分析
      const result = await trendAnalysisService.analyzeTrend({
        symbol: normalizedSymbol,
        primaryTimeframe: timeframe,
        analysisTimeframes: [timeframe],
        analysisDepth: 'comprehensive', // 强制使用comprehensive深度以启用四维度分析
        includePatterns: true,
        includePredictions: true
      });

      logger.info('四维度综合分析完成', {
        symbol: normalizedSymbol,
        timeframe,
        analysisDepth,
        includeTechnical,
        includeFundamental,
        includeSentiment,
        includeQuantitative,
        requestId: req.id
      });

      return res.json({
        success: true,
        data: result,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('四维度综合分析失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '四维度综合分析失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  return router;
}
