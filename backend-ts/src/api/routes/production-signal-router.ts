/**
 * 生产级交易信号API路由
 * 基于真实数据的四维度信号融合系统
 * 替换原有的虚假信号生成逻辑
 */

import { Request, Response } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../shared/infrastructure/di/types/index';
import { BaseRouter } from './base-router';
import { SignalGenerationApplicationService, SignalGenerationRequest } from '../../contexts/trading-signals/application/services/signal-generation-application-service';

/**
 * 交易信号路由 (按照设计文档重构)
 */
export class ProductionSignalRouter extends BaseRouter {
  private readonly signalGenerationService: SignalGenerationApplicationService;

  constructor(container: Container) {
    super(container);
    // 🔥 按照设计文档：使用正确的SignalGenerationApplicationService
    try {
      this.signalGenerationService = container.get<SignalGenerationApplicationService>(TYPES.TradingSignals.SignalGenerationApplicationService);
    } catch (error) {
      this.logger.error('无法从容器获取SignalGenerationApplicationService', { error });
      throw new Error('SignalGenerationApplicationService未正确注册到DI容器');
    }
  }

  protected setupRoutes(): void {
    /**
     * @swagger
     * /api/v2/trading/signals/generate:
     *   post:
     *     summary: 生成生产级交易信号 (真实数据)
     *     tags: [生产级交易信号]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               symbol:
     *                 type: string
     *                 description: 交易对符号 (如 BTC, BTCUSDT)
     *                 example: "BTC"
     *               timeframe:
     *                 type: string
     *                 description: 时间框架
     *                 enum: [1m, 5m, 15m, 30m, 1h, 2h, 4h, 8h, 1d]
     *                 default: "1h"
     *               analysisDepth:
     *                 type: string
     *                 description: 分析深度
     *                 enum: [quick, standard, comprehensive]
     *                 default: "standard"
     *               strategy:
     *                 type: string
     *                 description: 权重策略
     *                 enum: [CONSERVATIVE, BALANCED, AGGRESSIVE]
     *                 default: "BALANCED"
     *               enableQualityMonitoring:
     *                 type: boolean
     *                 description: 启用质量监控
     *                 default: true
     *               enablePerformanceTracking:
     *                 type: boolean
     *                 description: 启用性能跟踪
     *                 default: false
     *             required:
     *               - symbol
     *     responses:
     *       200:
     *         description: 生产级交易信号生成成功
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: object
     *                   description: 完整的四维度信号分析结果
     *       400:
     *         description: 请求参数错误
     *       500:
     *         description: 服务器内部错误
     */
    this.router.post('/signals/generate', this.asyncHandler(this.generateProductionSignal.bind(this)));

    /**
     * @swagger
     * /api/v2/trading/signals/batch:
     *   post:
     *     summary: 批量生成生产级交易信号
     *     tags: [生产级交易信号]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               requests:
     *                 type: array
     *                 items:
     *                   type: object
     *                   properties:
     *                     symbol:
     *                       type: string
     *                     timeframe:
     *                       type: string
     *                     strategy:
     *                       type: string
     *                 description: 批量信号请求
     *             required:
     *               - requests
     *     responses:
     *       200:
     *         description: 批量信号生成成功
     */
    this.router.post('/signals/batch', this.asyncHandler(this.generateBatchSignals.bind(this)));

    /**
     * @swagger
     * /api/v2/trading/signals/{symbol}/latest:
     *   get:
     *     summary: 获取最新生产级交易信号
     *     tags: [生产级交易信号]
     *     parameters:
     *       - in: path
     *         name: symbol
     *         required: true
     *         schema:
     *           type: string
     *         description: 交易对符号
     *       - in: query
     *         name: strategy
     *         schema:
     *           type: string
     *           enum: [CONSERVATIVE, BALANCED, AGGRESSIVE]
     *         description: 权重策略
     *     responses:
     *       200:
     *         description: 成功获取最新生产级交易信号
     */
    this.router.get('/signals/:symbol/latest', this.asyncHandler(this.getLatestProductionSignal.bind(this)));

    /**
     * @swagger
     * /api/v2/trading/signals/{symbol}/validate:
     *   get:
     *     summary: 验证交易信号数据真实性
     *     tags: [生产级交易信号]
     *     parameters:
     *       - in: path
     *         name: symbol
     *         required: true
     *         schema:
     *           type: string
     *         description: 交易对符号
     *     responses:
     *       200:
     *         description: 数据真实性验证结果
     */
    this.router.get('/signals/:symbol/validate', this.asyncHandler(this.validateSignalData.bind(this)));

    /**
     * @swagger
     * /api/v2/trading/signals/health:
     *   get:
     *     summary: 检查生产级信号系统健康状态
     *     tags: [生产级交易信号]
     *     responses:
     *       200:
     *         description: 系统健康状态
     */
    this.router.get('/signals/health', this.asyncHandler(this.checkSystemHealth.bind(this)));
  }

  /**
   * 生成生产级交易信号
   */
  private async generateProductionSignal(req: Request, res: Response): Promise<void> {
    try {
      // 从请求体或查询参数获取symbol和其他选项
      const symbol = req.body?.symbol || req.query?.symbol;
      const {
        timeframe = '1h',
        analysisDepth = 'standard',
        strategy = 'BALANCED',
        enableQualityMonitoring = 'true',
        enablePerformanceTracking = 'false'
      } = { ...req.query, ...req.body };

      // 🔍 调试日志：检查参数提取
      this.logger.info('🔍 路由参数提取', {
        symbol,
        timeframe,
        analysisDepth,
        strategy,
        queryParams: req.query
      });

      if (!symbol) {
        res.error('Symbol is required', 400);
        return;
      }

      // 标准化符号格式
      const normalizedSymbol = this.normalizeSymbol(symbol);

      this.logger.info('生成生产级交易信号', {
        requestId: req.id,
        symbol: normalizedSymbol,
        timeframe,
        analysisDepth,
        strategy,
        enableQualityMonitoring,
        enablePerformanceTracking
      });

      // 🔥 按照设计文档：构建正确的SignalGenerationRequest
      const request: SignalGenerationRequest = {
        userId: 'default-user', // 默认用户ID，后续可从认证中获取
        symbol: normalizedSymbol,
        timeframe: timeframe as string,
        strategy: strategy as string,
        forceGeneration: true
      };

      const signal = await this.signalGenerationService.generateSignal(request);

      // 添加API元数据
      const response = {
        ...signal,
        apiVersion: 'v2',
        dataSource: 'REAL_TIME_INTEGRATED',
        systemType: 'FOUR_DIMENSIONAL_FUSION',
        disclaimer: '基于真实市场数据的四维度信号融合分析，AI系统对分析质量负责'
      };

      this.logger.info('生产级交易信号生成成功', {
        requestId: req.id,
        signalId: signal.id,
        symbol: signal.symbol,
        signal: signal.signal,
        confidence: signal.confidence,
        processingTime: signal.processingTime
      });

      res.success(response);
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 批量生成生产级交易信号
   */
  private async generateBatchSignals(req: Request, res: Response): Promise<void> {
    try {
      const { requests } = req.body;

      if (!requests || !Array.isArray(requests) || requests.length === 0) {
        res.error('Requests array is required and cannot be empty', 400);
        return;
      }

      if (requests.length > 10) {
        res.error('Maximum 10 requests allowed per batch', 400);
        return;
      }

      this.logger.info('批量生成生产级交易信号', {
        requestId: req.id,
        batchSize: requests.length
      });

      // 标准化请求
      const normalizedRequests: ProductionSignalRequest[] = requests.map(request => ({
        symbol: this.normalizeSymbol(request.symbol),
        timeframe: request.timeframe || '1h',
        analysisDepth: request.analysisDepth || 'standard',
        strategy: request.strategy || 'BALANCED',
        enableQualityMonitoring: request.enableQualityMonitoring !== false,
        enablePerformanceTracking: request.enablePerformanceTracking === true
      }));

      const signals = await this.productionSignalService.generateBatchSignals(normalizedRequests);

      const response = {
        batchId: `batch_${Date.now()}_${require('uuid').v4().replace(/-/g, '').substring(0, 9)}`,
        totalRequests: requests.length,
        successfulSignals: signals.length,
        failedSignals: requests.length - signals.length,
        signals,
        apiVersion: 'v2',
        dataSource: 'REAL_TIME_INTEGRATED',
        systemType: 'FOUR_DIMENSIONAL_FUSION',
        timestamp: new Date().toISOString()
      };

      this.logger.info('批量生产级交易信号生成完成', {
        requestId: req.id,
        batchId: response.batchId,
        successful: response.successfulSignals,
        failed: response.failedSignals
      });

      res.success(response);
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 获取最新生产级交易信号
   */
  private async getLatestProductionSignal(req: Request, res: Response): Promise<void> {
    try {
      // 从路径参数获取symbol，从查询参数获取其他选项
      const { symbol } = req.params;
      const {
        timeframe = '1h',
        analysisDepth = 'standard',
        strategy = 'BALANCED',
        enableQualityMonitoring = 'true',
        enablePerformanceTracking = 'false'
      } = req.query;

      if (!symbol) {
        res.error('Symbol parameter is required', 400);
        return;
      }

      const normalizedSymbol = this.normalizeSymbol(symbol);

      // 🔍 调试日志：检查参数提取
      this.logger.info('🔍 getLatestProductionSignal 参数提取', {
        requestId: req.id,
        symbol: normalizedSymbol,
        timeframe,
        analysisDepth,
        strategy,
        queryParams: req.query
      });

      const request: ProductionSignalRequest = {
        symbol: normalizedSymbol,
        timeframe: timeframe as string,
        analysisDepth: analysisDepth as any,
        strategy: strategy as any,
        enableQualityMonitoring: enableQualityMonitoring === 'true',
        enablePerformanceTracking: enablePerformanceTracking === 'true'
      };

      // 🔥 按照设计文档：使用正确的服务
      const signalRequest: SignalGenerationRequest = {
        userId: 'default-user',
        symbol: normalizedSymbol,
        timeframe: timeframe as string,
        strategy: strategy as string,
        forceGeneration: true
      };

      const signal = await this.signalGenerationService.generateSignal(signalRequest);

      const response = {
        ...signal,
        apiVersion: 'v2',
        dataSource: 'REAL_TIME_INTEGRATED',
        systemType: 'FOUR_DIMENSIONAL_FUSION'
      };

      res.success(response);
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 验证交易信号数据真实性
   */
  private async validateSignalData(req: Request, res: Response): Promise<void> {
    try {
      const { symbol } = req.params;

      if (!symbol) {
        res.error('Symbol is required', 400);
        return;
      }

      const normalizedSymbol = this.normalizeSymbol(symbol);

      this.logger.info('开始验证交易信号数据真实性', {
        requestId: req.id,
        symbol: normalizedSymbol
      });

      // 生成最新交易信号
      const signalRequest: ProductionSignalRequest = {
        symbol: normalizedSymbol,
        timeframe: '1h',
        analysisDepth: 'standard',
        strategy: 'BALANCED',
        enableQualityMonitoring: false,
        enablePerformanceTracking: false
      };

      // 🔥 按照设计文档：使用正确的服务
      const correctSignalRequest: SignalGenerationRequest = {
        userId: 'default-user',
        symbol: normalizedSymbol,
        timeframe: '1h',
        strategy: 'BALANCED',
        forceGeneration: true
      };

      const signalData = await this.signalGenerationService.generateSignal(correctSignalRequest);

      // 验证数据真实性
      const { DataAuthenticityValidator } = await import('../../contexts/trading-signals/infrastructure/validation/data-authenticity-validator');
      const validator = new DataAuthenticityValidator(this.logger);

      const validationResult = await validator.validateTradingSignalData(signalData);

      const response = {
        symbol: normalizedSymbol,
        timestamp: new Date().toISOString(),
        dataAuthenticity: validationResult,
        signalSummary: {
          signal: signalData.signal,
          confidence: signalData.confidence,
          currentPrice: signalData.currentPrice,
          riskLevel: signalData.riskLevel
        },
        apiVersion: 'v2',
        validationVersion: '1.0'
      };

      this.logger.info('数据真实性验证完成', {
        requestId: req.id,
        symbol: normalizedSymbol,
        isValid: validationResult.isValid,
        validationCount: validationResult.validations.length,
        suspiciousValuesCount: validationResult.suspiciousValues.length
      });

      res.success(response);
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 检查系统健康状态
   */
  private async checkSystemHealth(req: Request, res: Response): Promise<void> {
    try {
      this.logger.info('检查生产级信号系统健康状态', {
        requestId: req.id
      });

      // 🔥 按照设计文档：使用正确的服务进行健康检查
      const healthCheckStart = Date.now();

      try {
        // 尝试生成一个简单的信号来验证系统
        const testRequest: SignalGenerationRequest = {
          userId: 'health-check-user',
          symbol: 'BTC',
          timeframe: '1h',
          strategy: 'BALANCED',
          forceGeneration: true
        };

        const testSignal = await this.signalGenerationService.generateSignal(testRequest);
        const healthCheckTime = Date.now() - healthCheckStart;

        const response = {
          status: 'HEALTHY',
          timestamp: new Date().toISOString(),
          systemType: 'FOUR_DIMENSIONAL_FUSION',
          dataSource: 'REAL_TIME_INTEGRATED',
          healthCheck: {
            responseTime: healthCheckTime,
            signalGeneration: 'OPERATIONAL',
            dataQuality: testSignal.dataQuality.overall,
            dataSourceStatus: testSignal.dataSourceStatus
          },
          capabilities: {
            fourDimensionalAnalysis: true,
            realTimeData: true,
            multipleStrategies: true,
            qualityMonitoring: true,
            batchProcessing: true
          },
          apiVersion: 'v2'
        };

        res.success(response);
      } catch (error) {
        const response = {
          status: 'DEGRADED',
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'Unknown error',
          healthCheck: {
            responseTime: Date.now() - healthCheckStart,
            signalGeneration: 'ERROR'
          },
          apiVersion: 'v2'
        };

        res.status(503).json({
          success: false,
          data: response
        });
      }
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 标准化符号格式
   */
  private normalizeSymbol(symbol: string): string {
    const upperSymbol = symbol.toUpperCase();
    
    // 如果已经包含斜杠，直接返回
    if (upperSymbol.includes('/')) {
      return upperSymbol;
    }
    
    // 如果是BTC、ETH等，添加/USDT
    if (['BTC', 'ETH', 'BNB', 'ADA', 'DOT', 'LINK', 'XRP'].includes(upperSymbol)) {
      return `${upperSymbol}/USDT`;
    }
    
    // 如果已经是BTCUSDT格式，转换为BTC/USDT
    if (upperSymbol.endsWith('USDT') && upperSymbol.length > 4) {
      const base = upperSymbol.slice(0, -4);
      return `${base}/USDT`;
    }
    
    // 默认添加/USDT
    return `${upperSymbol}/USDT`;
  }
}

export default ProductionSignalRouter;
