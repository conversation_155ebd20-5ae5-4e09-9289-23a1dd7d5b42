import { Request, Response } from 'express';
import { inject, injectable, Container } from 'inversify';
import { Logger } from 'winston';
import { TYPES } from '../../shared/infrastructure/di/types/index';
import { SecureIdGenerator } from '../../shared/infrastructure/utils/secure-id-generator';
import { SignalGenerationApplicationService } from '../../contexts/trading-signals/application/services/signal-generation-application-service';
import { SignalGenerationRequest, SignalGenerationResponse } from '../../contexts/trading-signals/domain/interfaces/signal-generation.interface';
import { UserProfile } from '../../contexts/trading-signals/domain/entities/user-profile';
import { MarketData } from '../../contexts/trading-signals/domain/entities/market-data';
import { TradingSignalEntity } from '../../contexts/trading-signals/domain/entities/trading-signal';
import { RiskLevel } from '../../contexts/trading-signals/domain/value-objects/risk-level';
import { InvestmentHorizon } from '../../contexts/trading-signals/domain/value-objects/investment-horizon';
import { body, validationResult } from 'express-validator';

/**
 * 交易信号请求DTO (本地控制器使用)
 */
export interface TradingSignalRequestDTO {
  symbol: string;
  marketData?: {
    price?: number;
    open?: number;
    high?: number;
    low?: number;
    volume?: number;
    timestamp?: Date;
    technicalIndicators?: {
      rsi?: number;
      macd?: number;
      bollinger?: {
        upper: number;
        middle: number;
        lower: number;
      };
      volume?: number;
      volatility?: number;
    };
  };
  userProfile?: {
    riskLevel?: 'conservative' | 'moderate' | 'aggressive';
    investmentHorizon?: 'short' | 'medium' | 'long';
    tradingStyle?: 'scalping' | 'day_trading' | 'swing_trading' | 'position_trading';
    preferences?: {
      maxPositionSize?: number;
      stopLossPercentage?: number;
      takeProfitPercentage?: number;
      diversificationLevel?: string;
    };
  };
}

/**
 * 批量信号生成请求DTO
 */
export interface BatchSignalGenerationRequest {
  requests: SignalGenerationRequest[];
}



/**
 * 批量信号生成响应DTO
 */
export interface BatchSignalGenerationResponse {
  signals: SignalGenerationResponse[];
  summary: {
    total: number;
    buySignals: number;
    sellSignals: number;
    holdSignals: number;
    averageConfidence: number;
    processingTime: number;
  };
  errors?: Array<{ symbol: string; error: string }>;
}

/**
 * 交易信号控制器
 */
@injectable()
export class TradingSignalsController {
  constructor(
    @inject(TYPES.TradingSignals.SignalGenerationApplicationService)
    private readonly signalService: SignalGenerationApplicationService,
    @inject(TYPES.Logger)
    private readonly logger: Logger,
    @inject(TYPES.Container)
    private readonly container: Container
  ) {}

  /**
   * 生成单个交易信号
   */
  async generateSignal(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    const requestId = SecureIdGenerator.generateProductionSignalId();

    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array(),
          requestId
        });
        return;
      }

      const request = req.body as SignalGenerationRequest;
      
      this.logger.info('开始生成交易信号', {
        requestId,
        symbol: request.symbol,
        userId: req.user?.id
      });

      // 构建用户画像
      const userProfile = await this.buildUserProfile(req.user?.id || 'anonymous');
      
      // 构建市场数据
      const marketData = this.buildMarketData(request.symbol, request.marketData || {});

      // 构建信号生成请求
      const signalRequest = {
        userId: req.user?.id || 'default-user',
        symbol: request.symbol,
        timeframe: request.timeframe || '1h'
      };

      // 生成信号
      const signalResponse = await this.signalService.generateSignal(signalRequest);

      const processingTime = Date.now() - startTime;

      // 构建响应 - 适配应用服务的返回结构
      const response: SignalGenerationResponse = {
        signalId: requestId,
        symbol: request.symbol,
        signalType: signalResponse.signalType || 'HOLD',
        strength: signalResponse.strength || 5,
        confidence: signalResponse.confidence || 0.5,
        reasoning: signalResponse.reasoning || [],
        entryPrice: signalResponse.entryPrice,
        stopLoss: signalResponse.stopLoss,
        takeProfit: signalResponse.takeProfit,
        positionSize: signalResponse.positionSize,
        riskLevel: (signalResponse.riskLevel as any) === 'VERY_HIGH' ? 'HIGH' : signalResponse.riskLevel || 'MEDIUM',
        timeframe: request.timeframe || '1h',
        timestamp: new Date(),
        metadata: {
          processingTime,
          dataQuality: (signalResponse.metadata?.dataQuality as any)?.overall || signalResponse.metadata?.dataQuality || 0.8,
          modelVersion: '1.0.0'
        }
      };

      this.logger.info('交易信号生成成功', {
        requestId,
        symbol: signalResponse.symbol || request.symbol,
        action: signalResponse.signalType || 'NONE',
        confidence: signalResponse.confidence || 0,
        processingTime
      });

      res.status(200).json(response);
      
    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      this.logger.error('交易信号生成失败', {
        requestId,
        error: error instanceof Error ? error.message : String(error),
        processingTime
      });

      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to generate trading signal',
        requestId,
        timestamp: new Date()
      });
    }
  }

  /**
   * 批量生成交易信号
   */
  async generateBatchSignals(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    const requestId = SecureIdGenerator.generateRequestId();

    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array(),
          requestId
        });
        return;
      }

      const batchRequest = req.body as BatchSignalGenerationRequest;
      
      this.logger.info('开始批量生成交易信号', {
        requestId,
        count: batchRequest.requests.length,
        userId: req.user?.id
      });

      const results: (SignalGenerationResponse | { error: string; symbol: string })[] = [];
      let successful = 0;
      let failed = 0;

      // 并行处理所有请求
      const promises = batchRequest.requests.map(async (request) => {
        try {
          const userProfile = await this.buildUserProfile(req.user?.id || 'anonymous');
          const marketData = this.buildMarketData(request.symbol, request.marketData || {});
          const signalRequest = {
            userId: req.user?.id || 'default-user',
            symbol: request.symbol,
            timeframe: request.timeframe || '1h'
          };

          const signalResponse = await this.signalService.generateSignal(signalRequest);

          successful++;
          return {
            signalId: `${requestId}_${request.symbol}`,
            symbol: request.symbol,
            signalType: signalResponse.signalType || 'HOLD',
            strength: signalResponse.strength || 5,
            confidence: signalResponse.confidence || 0.5,
            reasoning: signalResponse.reasoning || [],
            entryPrice: signalResponse.entryPrice,
            stopLoss: signalResponse.stopLoss,
            takeProfit: signalResponse.takeProfit,
            positionSize: signalResponse.positionSize,
            riskLevel: signalResponse.riskLevel || 'MEDIUM',
            timeframe: request.timeframe || '1h',
            timestamp: new Date(),
            metadata: {
              processingTime: signalResponse.metadata?.processingTime || 0,
              dataQuality: (signalResponse.metadata?.dataQuality as any)?.overall || signalResponse.metadata?.dataQuality || 0.8,
              modelVersion: '1.0.0'
            }
          } as SignalGenerationResponse;
        } catch (error) {
          failed++;
          return {
            symbol: request.symbol,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      });

      const batchResults = await Promise.all(promises);
      results.push(...batchResults);

      const processingTime = Date.now() - startTime;

      // 分离成功和失败的结果
      const signals = results.filter(r => !('error' in r)) as SignalGenerationResponse[];
      const failedResults = results.filter(r => 'error' in r) as Array<{ symbol: string; error: string }>;

      const response: BatchSignalGenerationResponse = {
        signals,
        summary: {
          total: signals.length,
          buySignals: signals.filter(s => s.signalType === 'BUY').length,
          sellSignals: signals.filter(s => s.signalType === 'SELL').length,
          holdSignals: signals.filter(s => s.signalType === 'HOLD').length,
          averageConfidence: signals.length > 0 ? signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length : 0,
          processingTime
        },
        errors: failedResults.length > 0 ? failedResults : undefined
      };

      this.logger.info('批量交易信号生成完成', {
        requestId,
        total: batchRequest.requests.length,
        successful,
        failed,
        processingTime
      });

      res.status(200).json(response);
      
    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      this.logger.error('批量交易信号生成失败', {
        requestId,
        error: error instanceof Error ? error.message : String(error),
        processingTime
      });

      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to generate batch trading signals',
        requestId,
        timestamp: new Date()
      });
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      res.status(200).json({
        status: 'healthy',
        service: 'trading-signals',
        timestamp: new Date(),
        version: '1.0.0'
      });
    } catch (error) {
      res.status(503).json({
        status: 'unhealthy',
        service: 'trading-signals',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date()
      });
    }
  }

  /**
   * 构建用户画像 - 使用统一的User-Config系统，避免硬编码
   * 🔥 重要：不再硬编码用户配置，使用统一的UserConfigSystemAdapter
   */
  private async buildUserProfile(userId: string): Promise<UserProfile> {
    try {
      // 🔥 使用统一的User-Config系统获取用户画像
      const userConfigAdapter = this.container.get<any>(TYPES.UserConfig.UserConfigSystemAdapter);
      const userProfileResponse = await userConfigAdapter.getUserProfile(userId);

      if (userProfileResponse.success && userProfileResponse.data) {
        return userProfileResponse.data;
      }

      // 如果获取失败，记录警告并返回默认配置
      this.logger.warn('无法获取用户画像，使用默认配置', {
        userId,
        error: userProfileResponse.error
      });

      // 🔥 使用统一的默认配置创建，而不是硬编码
      return this.createDefaultUserProfile(userId);

    } catch (error) {
      this.logger.error('构建用户画像失败', { userId, error });
      // 降级到默认配置
      return this.createDefaultUserProfile(userId);
    }
  }

  /**
   * 创建默认用户画像 - 使用统一的默认配置
   */
  private createDefaultUserProfile(userId: string): UserProfile {
    return UserProfile.createDefault(userId);
  }

  /**
   * 构建市场数据
   */
  private buildMarketData(symbol: string, marketDataInput: any): MarketData {
    return new MarketData(
      symbol,
      marketDataInput.price,
      marketDataInput.open,
      marketDataInput.high,
      marketDataInput.low,
      marketDataInput.volume,
      marketDataInput.timestamp || new Date(),
      marketDataInput.technicalIndicators || {}
    );
  }



  /**
   * 映射风险等级
   */
  private mapRiskLevel(riskLevel: string): 'CONSERVATIVE' | 'BALANCED' | 'AGGRESSIVE' {
    switch (riskLevel.toLowerCase()) {
      case 'conservative':
        return 'CONSERVATIVE';
      case 'moderate':
      case 'balanced':
        return 'BALANCED';
      case 'aggressive':
        return 'AGGRESSIVE';
      default:
        return 'BALANCED';
    }
  }

  /**
   * 映射投资期限
   */
  private mapInvestmentHorizon(horizon: string): 'SHORT' | 'MEDIUM' | 'LONG' {
    switch (horizon.toLowerCase()) {
      case 'short':
        return 'SHORT';
      case 'medium':
        return 'MEDIUM';
      case 'long':
        return 'LONG';
      default:
        return 'MEDIUM';
    }
  }

  /**
   * 请求验证规则
   */
  static getValidationRules() {
    return {
      generateSignal: [
        body('symbol').isString().notEmpty().withMessage('Symbol is required'),
        body('marketData.price').isNumeric().withMessage('Price must be a number'),
        body('marketData.open').isNumeric().withMessage('Open price must be a number'),
        body('marketData.high').isNumeric().withMessage('High price must be a number'),
        body('marketData.low').isNumeric().withMessage('Low price must be a number'),
        body('marketData.volume').isNumeric().withMessage('Volume must be a number')
      ],
      generateBatchSignals: [
        body('requests').isArray().withMessage('Requests must be an array'),
        body('requests.*.symbol').isString().notEmpty().withMessage('Symbol is required for each request'),
        body('requests.*.marketData.price').isNumeric().withMessage('Price must be a number for each request')
      ]
    };
  }
}