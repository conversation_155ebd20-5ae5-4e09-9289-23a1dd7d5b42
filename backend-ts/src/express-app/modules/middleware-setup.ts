/**
 * Express应用中间件设置模块
 * 从大文件中拆分出来，遵循单一职责原则
 */

import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { Container } from 'inversify';
import { TYPES } from '../../shared/infrastructure/di/types/index';
import { ExpressMiddleware } from '../../api/middleware/express-middleware';
import { IBasicLogger } from '../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { SecureIdGenerator } from '../../shared/infrastructure/utils/secure-id-generator';
// 注意：限流中间件已在 express-app.ts 中实现，避免重复

/**
 * 创建中间件设置器
 * 负责配置所有Express中间件
 */
export function createMiddlewareSetup(container: Container) {
  const logger = container.get<IBasicLogger>(TYPES.Shared.Logger);
  const middleware = container.get<ExpressMiddleware>(TYPES.Api.ExpressMiddleware);
  // 注意：限流中间件已在 express-app.ts 中实现，避免重复

  return {
    /**
     * 设置基础中间件
     */
    setupBasicMiddleware(app: Application): void {
      try {
        // 请求ID生成中间件
        app.use(middleware.asyncHandler(async (req, res, next) => {
          req.id = SecureIdGenerator.generateRequestId();
          req.startTime = Date.now();
          next();
        }));

        // 请求日志中间件
        app.use((req, res, next) => {
          const startTime = Date.now();
          
          logger.info('API请求开始', {
            requestId: req.id,
            method: req.method,
            url: req.url,
            userAgent: req.get('User-Agent'),
            ip: req.ip,
            timestamp: new Date().toISOString(),
          });

          res.on('finish', () => {
            const duration = Date.now() - startTime;
            
            logger.info('API请求完成', {
              requestId: req.id,
              method: req.method,
              url: req.url,
              statusCode: res.statusCode,
              duration: `${duration}ms`,
              timestamp: new Date().toISOString(),
            });
          });

          next();
        });

        // 增强安全中间件
        app.use(helmet({
          contentSecurityPolicy: {
            directives: {
              defaultSrc: ["'self'"],
              styleSrc: ["'self'", "'unsafe-inline'"],
              scriptSrc: ["'self'"],
              imgSrc: ["'self'", "data:", "https:"],
              connectSrc: ["'self'", "wss:", "https:"],
              fontSrc: ["'self'", "https:", "data:"],
              objectSrc: ["'none'"],
              mediaSrc: ["'self'"],
              frameSrc: ["'none'"],
            },
          },
          crossOriginEmbedderPolicy: false,
          hsts: {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true
          },
          noSniff: true,
          xssFilter: true,
          referrerPolicy: { policy: "strict-origin-when-cross-origin" }
        }));

        // 额外安全头
        app.use((req, res, next) => {
          res.setHeader('X-Powered-By', 'TradingSystem/1.0');
          res.setHeader('X-API-Version', '1.0');
          res.setHeader('X-Rate-Limit-Policy', 'standard');
          res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
          next();
        });

        // CORS中间件
        app.use(cors({
          origin: process.env.CORS_ORIGIN || '*',
          methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
          allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
          credentials: true,
        }));

        // 注意：限流中间件已在 express-app.ts 中实现，避免重复
        // 如需修改限流配置，请在 express-app.ts 的 setupBasicMiddleware 方法中修改

        // 压缩中间件
        app.use(compression());

        // JSON解析中间件
        app.use(express.json({ 
          limit: '10mb',
          verify: (req, res, buf) => {
            // 验证JSON格式
            try {
              JSON.parse(buf.toString());
            } catch (error) {
              logger.warn('无效的JSON格式', {
                requestId: req.id,
                error: error instanceof Error ? error.message : '未知错误',
                url: req.url,
                method: req.method
              });
              throw new Error('Invalid JSON format');
            }
          }
        }));

        // URL编码解析中间件
        app.use(express.urlencoded({ 
          extended: true, 
          limit: '10mb' 
        }));

        // 静态文件服务
        app.use('/static', express.static('public', {
          maxAge: '1d',
          etag: true,
          lastModified: true
        }));

        logger.info('✅ 基础中间件设置完成');
      } catch (error) {
        logger.error('❌ 基础中间件设置失败', { error });
        throw error;
      }
    },

    /**
     * 设置应用中间件
     */
    setupApplicationMiddleware(app: Application): void {
      try {
        // 容器注入中间件
        app.use(middleware.injectContainer());

        // 响应格式化中间件
        app.use(middleware.formatResponse());

        // 超时中间件
        app.use(middleware.timeout(30000));

        logger.info('✅ 应用中间件设置完成');
      } catch (error) {
        logger.error('❌ 应用中间件设置失败', { error });
        throw error;
      }
    },

    /**
     * 设置健康检查中间件
     */
    setupHealthCheckMiddleware(app: Application): void {
      try {
        // 健康检查端点
        app.get('/health', (req, res) => {
          const healthStatus = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            requestId: req.id
          };

          logger.info('健康检查请求', {
            requestId: req.id,
            status: healthStatus.status
          });

          res.json(healthStatus);
        });

        // 就绪检查端点
        app.get('/ready', (req, res) => {
          // 这里可以添加更复杂的就绪检查逻辑
          const readyStatus = {
            status: 'ready',
            timestamp: new Date().toISOString(),
            services: {
              database: 'connected',
              redis: 'connected',
              websocket: 'running'
            },
            requestId: req.id
          };

          logger.info('就绪检查请求', {
            requestId: req.id,
            status: readyStatus.status
          });

          res.json(readyStatus);
        });

        // 存活检查端点
        app.get('/alive', (req, res) => {
          res.json({
            status: 'alive',
            timestamp: new Date().toISOString(),
            requestId: req.id
          });
        });

        logger.info('✅ 健康检查中间件设置完成');
      } catch (error) {
        logger.error('❌ 健康检查中间件设置失败', { error });
        throw error;
      }
    },

    /**
     * 设置开发环境中间件
     */
    setupDevelopmentMiddleware(app: Application): void {
      if (process.env.NODE_ENV !== 'development') {
        return;
      }

      try {
        // 开发环境请求详细日志
        app.use((req, res, next) => {
          logger.debug('开发环境请求详情', {
            requestId: req.id,
            method: req.method,
            url: req.url,
            headers: req.headers,
            body: req.body,
            query: req.query,
            params: req.params
          });
          next();
        });

        // 开发环境错误详情
        app.use((error: Error, req: any, res: any, next: any) => {
          logger.debug('开发环境错误详情', {
            requestId: req.id,
            error: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method
          });
          next(error);
        });

        logger.info('✅ 开发环境中间件设置完成');
      } catch (error) {
        logger.error('❌ 开发环境中间件设置失败', { error });
        throw error;
      }
    },

    /**
     * 设置生产环境中间件
     */
    setupProductionMiddleware(app: Application): void {
      if (process.env.NODE_ENV !== 'production') {
        return;
      }

      try {
        // 生产环境安全头
        app.use((req, res, next) => {
          res.set({
            'X-Powered-By': 'Trading System',
            'Server': 'TradingAPI/1.0',
            'X-Frame-Options': 'DENY',
            'X-Content-Type-Options': 'nosniff',
            'Referrer-Policy': 'strict-origin-when-cross-origin'
          });
          next();
        });

        // 生产环境请求限制
        app.use((req, res, next) => {
          // 这里可以添加生产环境特定的限制逻辑
          next();
        });

        logger.info('✅ 生产环境中间件设置完成');
      } catch (error) {
        logger.error('❌ 生产环境中间件设置失败', { error });
        throw error;
      }
    }
  };
}
