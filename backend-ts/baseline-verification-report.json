{"timestamp": "2025-07-22T01:49:16.608Z", "summary": {"totalChecks": 6, "successCount": 5, "criticalFailures": 1}, "results": {"compilation": {"success": false, "message": "编译失败，发现 403 个错误", "details": {"errorCount": 403, "sample": ["src/contexts/ai-reasoning/infrastructure/traceability/reasoning-process-recorder.ts(10,10): error TS2459: Module '\"../../../../shared/infrastructure/logging/logger.interface\"' declares 'IBasicLogger' locally, but it is not exported.", "src/contexts/ai-reasoning/infrastructure/traceability/reasoning-process-recorder.ts(345,27): error TS2304: Cannot find name 'Decimal'.", "src/contexts/ai-reasoning/infrastructure/traceability/reasoning-process-recorder.ts(472,41): error TS2503: Cannot find namespace 'Prisma'.", "src/contexts/ai-reasoning/infrastructure/traceability/reasoning-visualization-service.ts(8,10): error TS2459: Module '\"../../../../shared/infrastructure/logging/logger.interface\"' declares 'IBasicLogger' locally, but it is not exported.", "src/contexts/ai-reasoning/infrastructure/traceability/reasoning-visualization-service.ts(471,11): error TS2365: Operator '+' cannot be applied to types 'Error' and 'number'.", "src/contexts/ai-reasoning/infrastructure/traceability/reasoning-visualization-service.ts(786,11): error TS2741: Property 'errorRate' is missing in type '{ totalInvocations: any; averageResponseTime: number; tokenUsage: { totalTokens: any; promptTokens: any; completionTokens: any; averageTokensPerCall: number; efficiency: number; }; costAnalysis: { ...; }; }' but required in type 'ModelPerformanceAnalysis'.", "src/contexts/market-data/application/services/real-data-integration-service.ts(973,7): error TS2554: Expected 1 arguments, but got 3.", "src/contexts/market-data/infrastructure/repositories/prisma-historical-data-repository.ts(22,14): error TS2420: Class 'PrismaHistoricalDataRepository' incorrectly implements interface 'IHistoricalDataRepository'.", "  Type 'PrismaHistoricalDataRepository' is missing the following properties from type 'IHistoricalDataRepository': update, exists, saveMany, updateMany, deleteMany", "src/contexts/market-data/infrastructure/repositories/prisma-historical-data-repository.ts(498,21): error TS2362: The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type."]}, "critical": true}, "dependencies": {"success": true, "message": "依赖包完整"}, "database-schema": {"success": true, "message": "Prisma schema有效"}, "env-config": {"success": true, "message": "环境配置完整"}, "core-services": {"success": true, "message": "核心服务文件存在"}, "api-routes": {"success": true, "message": "API路由文件存在"}}}