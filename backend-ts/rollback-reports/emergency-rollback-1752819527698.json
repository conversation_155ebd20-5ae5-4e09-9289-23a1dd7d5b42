{"timestamp": "2025-07-18T06:18:47.698Z", "summary": {"totalActions": 49, "totalFixes": 65}, "actions": [{"file": "src/application/real-time-sync/queues/queue-manager.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/application/sync/monitoring/sync-statistics-collector.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/ai-reasoning/infrastructure/config/performance-config.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/ai-reasoning/infrastructure/reasoning/unified-decision-engine.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/ai-reasoning/infrastructure/services/gradual-adjuster.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/ai-reasoning/infrastructure/services/learning-analysis-engine.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/market-data/infrastructure/external/database-query-index-optimizer.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/market-data/infrastructure/external/exchange-router.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/market-data/infrastructure/external/predictive-data-preloader.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/market-data/infrastructure/external/real-time-data-quality-monitor.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/trading-execution/domain/services/real-slippage-calculator.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/trading-execution/domain/services/simulation-engine.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/trading-signals/application/services/production-signal-service.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/trading-signals/infrastructure/validation/data-authenticity-validator.ts", "description": "修复2个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}, {"oldCode": "[0, 1, 50, 100, 1000, 10000, /* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })(), 100000, 1000000]", "newCode": "[0, 1, 50, 100, 1000, 10000, 50000, 100000, 1000000]"}]}, {"file": "src/contexts/trend-analysis/application/trend-analysis-application.service.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/trend-analysis/infrastructure/services/data-quality-validator.ts", "description": "修复2个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}, {"oldCode": "[0, /* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格1，请从真实市场数据源获取\"); })(), 50, 100, 0.5, 1000, 10000]", "newCode": "[0, 50000, 50, 100, 0.5, 1000, 10000]"}]}, {"file": "src/contexts/trend-analysis/infrastructure/services/data-source-health-monitor.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/user-config/application/services/ModelSelectionService.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/user-config/domain/entities/UserModelPreference.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/user-config/infrastructure/services/AESEncryptionService.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/user-config/infrastructure/services/EnhancedLLMRouter.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/contexts/user-config/presentation/http/StatisticsController.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/shared/infrastructure/ai/scheduled-ai-call-decorator.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/shared/infrastructure/ai/vector-service.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/shared/infrastructure/analysis/services/PatternRecognitionService.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/shared/infrastructure/di/modules/ai-reasoning-container-module.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/shared/infrastructure/risk/real-time-risk-monitor.ts", "description": "修复1个被破坏的数组定义", "fixes": [{"oldCode": "/* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })()", "newCode": "50000"}]}, {"file": "src/api/controllers/trend-analysis-controller.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/market-data/application/services/real-data-integration-service.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/market-data/infrastructure/external/data-anomaly-detection-engine.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/market-data/infrastructure/external/exchange-router.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "description": "修复5个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格100", "newCode": "= 100;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格100", "newCode": "= 100;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格2", "newCode": "= 2;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格100", "newCode": "= 100;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.ts", "description": "修复3个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/trading-execution/domain/services/real-slippage-calculator.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格2", "newCode": "= 2;"}]}, {"file": "src/contexts/trend-analysis/application/trend-analysis-application.service.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/trend-analysis/infrastructure/services/confidence-calculator.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格2", "newCode": "= 2;"}]}, {"file": "src/contexts/trend-analysis/infrastructure/services/data-source-health-monitor.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/trend-analysis/infrastructure/services/key-level-analysis-engine.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/trend-analysis/infrastructure/services/key-level-helpers.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/trend-analysis/infrastructure/services/professional-pivot-detector.ts", "description": "修复3个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/trend-analysis/infrastructure/services/real-time-monitor.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格2", "newCode": "= 2;"}]}, {"file": "src/contexts/trend-analysis/infrastructure/services/target-stop-calculator.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-analysis-engine.ts", "description": "修复5个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/user-management/domain/value-objects/ThreatScore.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/shared/infrastructure/ai/realtime-push-service.ts", "description": "修复1个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/shared/infrastructure/analysis/services/MultiTimeframeService.ts", "description": "修复3个被破坏的函数调用", "fixes": [{"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}, {"oldCode": "= await this.getRealPrice(); // 替换硬编码价格1", "newCode": "= 1;"}]}, {"file": "src/contexts/trading-signals/infrastructure/validation/data-authenticity-validator.ts", "description": "修复commonDefaults数组", "fixes": [{"oldCode": "const commonDefaults = [0, 1, 50, 100, 1000, 10000, /* 拒绝硬编码价格 */ (() => { throw new Error(\"拒绝使用硬编码价格50000，请从真实市场数据源获取\"); })(), 100000, 1000000];", "newCode": "const commonDefaults = [0, 1, 50, 100, 1000, 10000, 50000, 100000, 1000000];"}]}]}