/**
 * Trend Analysis 上下文核心功能验证测试
 * 目标：验证trend-analysis模块的核心功能是否真正工作
 */

import 'reflect-metadata';
import { Container } from 'inversify';
import { TYPES } from './src/shared/infrastructure/di/types/index';

async function testTrendAnalysisContext() {
  console.log('🔍 开始Trend Analysis上下文核心功能验证...\n');

  try {
    // 1. 测试依赖注入容器配置
    console.log('1️⃣ 测试依赖注入容器配置...');
    
    console.log('检查TYPES定义:');
    console.log('- TrendAnalysisApplicationService:', TYPES.TrendAnalysis?.TrendAnalysisApplicationService?.toString());
    console.log('- TrendDetectionService:', TYPES.TrendAnalysis?.TrendDetectionService?.toString());
    console.log('- TechnicalAnalysisService:', TYPES.TrendAnalysis?.TechnicalAnalysisService?.toString());
    console.log('- TrendAnalysisController:', TYPES.TrendAnalysis?.TrendAnalysisController?.toString());
    
    if (!TYPES.TrendAnalysis?.TrendAnalysisApplicationService) {
      throw new Error('❌ TrendAnalysisApplicationService类型未定义');
    }
    
    console.log('✅ 基础类型定义正常\n');

    // 2. 测试模块加载
    console.log('2️⃣ 测试模块加载...');
    
    const appServices = [
      './src/contexts/trend-analysis/application/services/trend-analysis-application-service',
      './src/contexts/trend-analysis/application/services/pattern-recognition-service',
      './src/contexts/trend-analysis/application/services/technical-indicator-service'
    ];
    
    for (const servicePath of appServices) {
      try {
        const module = await import(servicePath);
        console.log(`✅ ${servicePath.split('/').pop()} 加载成功`);
        console.log(`- 模块导出:`, Object.keys(module));
      } catch (error) {
        console.log(`❌ ${servicePath.split('/').pop()} 加载失败:`, error instanceof Error ? error.message.substring(0, 100) + '...' : String(error));
      }
    }
    
    console.log('✅ 模块加载检查完成\n');

    // 3. 测试领域实体和值对象
    console.log('3️⃣ 测试领域实体和值对象...');
    
    const domainObjects = [
      './src/contexts/trend-analysis/domain/entities/trend-analysis',
      './src/contexts/trend-analysis/domain/entities/pattern-recognition-result',
      './src/contexts/trend-analysis/domain/value-objects/trend-direction',
      './src/contexts/trend-analysis/domain/value-objects/trend-strength'
    ];
    
    for (const objPath of domainObjects) {
      try {
        const module = await import(objPath);
        console.log(`✅ ${objPath.split('/').pop()} 存在`);
        console.log(`- 导出:`, Object.keys(module));
      } catch (error) {
        console.log(`⚠️ ${objPath.split('/').pop()} 不存在或有问题`);
      }
    }
    
    console.log('✅ 领域对象检查完成\n');

    // 4. 测试仓储接口
    console.log('4️⃣ 测试仓储接口...');
    
    const repositories = [
      './src/contexts/trend-analysis/domain/repositories/trend-analysis-repository',
      './src/contexts/trend-analysis/domain/repositories/pattern-repository'
    ];
    
    for (const repoPath of repositories) {
      try {
        const module = await import(repoPath);
        console.log(`✅ ${repoPath.split('/').pop()} 接口存在`);
        console.log(`- 导出:`, Object.keys(module));
      } catch (error) {
        console.log(`⚠️ ${repoPath.split('/').pop()} 接口不存在或有问题`);
      }
    }
    
    console.log('✅ 仓储接口检查完成\n');

    // 5. 测试技术指标实现
    console.log('5️⃣ 测试技术指标实现...');
    
    const indicators = [
      './src/contexts/trend-analysis/domain/services/technical-indicators/moving-average',
      './src/contexts/trend-analysis/domain/services/technical-indicators/rsi',
      './src/contexts/trend-analysis/domain/services/technical-indicators/macd',
      './src/contexts/trend-analysis/domain/services/technical-indicators/bollinger-bands'
    ];
    
    for (const indicatorPath of indicators) {
      try {
        const module = await import(indicatorPath);
        console.log(`✅ ${indicatorPath.split('/').pop()} 指标存在`);
        console.log(`- 导出:`, Object.keys(module));
      } catch (error) {
        console.log(`⚠️ ${indicatorPath.split('/').pop()} 指标不存在或有问题`);
      }
    }
    
    console.log('✅ 技术指标检查完成\n');

    // 6. 测试模式识别实现
    console.log('6️⃣ 测试模式识别实现...');
    
    const patterns = [
      './src/contexts/trend-analysis/domain/services/pattern-recognition/head-and-shoulders',
      './src/contexts/trend-analysis/domain/services/pattern-recognition/double-top-bottom',
      './src/contexts/trend-analysis/domain/services/pattern-recognition/triangle-patterns'
    ];
    
    for (const patternPath of patterns) {
      try {
        const module = await import(patternPath);
        console.log(`✅ ${patternPath.split('/').pop()} 模式存在`);
        console.log(`- 导出:`, Object.keys(module));
      } catch (error) {
        console.log(`⚠️ ${patternPath.split('/').pop()} 模式不存在或有问题`);
      }
    }
    
    console.log('✅ 模式识别检查完成\n');

    // 7. 测试基础设施实现
    console.log('7️⃣ 测试基础设施实现...');
    
    const infrastructure = [
      './src/contexts/trend-analysis/infrastructure/repositories/prisma-trend-analysis-repository',
      './src/contexts/trend-analysis/infrastructure/services/trend-calculation-service',
      './src/contexts/trend-analysis/infrastructure/services/pattern-detection-service'
    ];
    
    for (const infraPath of infrastructure) {
      try {
        const module = await import(infraPath);
        console.log(`✅ ${infraPath.split('/').pop()} 基础设施存在`);
        console.log(`- 导出:`, Object.keys(module));
      } catch (error) {
        console.log(`⚠️ ${infraPath.split('/').pop()} 基础设施不存在或有问题`);
      }
    }
    
    console.log('✅ 基础设施检查完成\n');

    // 8. 检查依赖注入绑定
    console.log('8️⃣ 检查依赖注入绑定...');
    
    try {
      const containerModule = await import('./src/shared/infrastructure/di/modules/trend-analysis-container-module');
      console.log('✅ trend-analysis-container-module 存在');
      console.log('- 导出:', Object.keys(containerModule));
      
    } catch (error) {
      console.log('⚠️ trend-analysis-container-module 不存在或有问题');
    }
    
    console.log('✅ 依赖注入绑定检查完成\n');

    console.log('🎉 Trend Analysis上下文核心功能验证完成！');
    console.log('\n📊 验证结果总结:');
    console.log('✅ 类型定义检查完成');
    console.log('✅ 模块加载检查完成');
    console.log('✅ 领域对象检查完成');
    console.log('✅ 仓储接口检查完成');
    console.log('✅ 技术指标检查完成');
    console.log('✅ 模式识别检查完成');
    console.log('✅ 基础设施检查完成');
    console.log('✅ 依赖注入绑定检查完成');

  } catch (error) {
    console.error('\n❌ Trend Analysis上下文验证失败:');
    console.error('错误:', error instanceof Error ? error.message : String(error));
    console.error('堆栈:', error instanceof Error ? error.stack : '无堆栈信息');
    
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testTrendAnalysisContext().catch(console.error);
}
