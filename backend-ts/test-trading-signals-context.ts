/**
 * Trading Signals 上下文核心功能验证测试
 * 目标：验证trading-signals模块的核心功能是否真正工作
 */

import 'reflect-metadata';
import { Container } from 'inversify';
import { TYPES } from './src/shared/infrastructure/di/types/index';

async function testTradingSignalsContext() {
  console.log('🔍 开始Trading Signals上下文核心功能验证...\n');

  try {
    // 1. 测试依赖注入容器配置
    console.log('1️⃣ 测试依赖注入容器配置...');
    
    console.log('检查TYPES定义:');
    console.log('- SignalGenerationApplicationService:', TYPES.TradingSignals?.SignalGenerationApplicationService?.toString());
    console.log('- TradingSignalApplicationService:', TYPES.TradingSignals?.TradingSignalApplicationService?.toString());
    console.log('- DegradationManagerService:', TYPES.TradingSignals?.DegradationManagerService?.toString());
    console.log('- StrategySelector:', TYPES.TradingSignals?.StrategySelector?.toString());
    console.log('- StrategyFactory:', TYPES.TradingSignals?.StrategyFactory?.toString());
    
    if (!TYPES.TradingSignals?.SignalGenerationApplicationService) {
      throw new Error('❌ SignalGenerationApplicationService类型未定义');
    }
    
    console.log('✅ 基础类型定义正常\n');

    // 2. 测试模块加载
    console.log('2️⃣ 测试模块加载...');
    
    try {
      // 检查应用服务是否存在
      const appServices = [
        './src/contexts/trading-signals/application/services/signal-generation-application-service',
        './src/contexts/trading-signals/application/services/trading-signal-application-service'
      ];
      
      for (const servicePath of appServices) {
        try {
          const module = await import(servicePath);
          console.log(`✅ ${servicePath.split('/').pop()} 加载成功`);
          console.log(`- 模块导出:`, Object.keys(module));
        } catch (error) {
          console.log(`❌ ${servicePath.split('/').pop()} 加载失败:`, error instanceof Error ? error.message : String(error));
        }
      }
      
    } catch (error) {
      console.error('❌ 模块加载失败:', error);
    }
    
    console.log('✅ 模块加载检查完成\n');

    // 3. 测试领域实体和值对象
    console.log('3️⃣ 测试领域实体和值对象...');
    
    try {
      // 检查核心领域对象
      const domainObjects = [
        './src/contexts/trading-signals/domain/entities/trading-signal',
        './src/contexts/trading-signals/domain/value-objects/signal-strength',
        './src/contexts/trading-signals/domain/value-objects/signal-type'
      ];
      
      for (const objPath of domainObjects) {
        try {
          const module = await import(objPath);
          console.log(`✅ ${objPath.split('/').pop()} 存在`);
          console.log(`- 导出:`, Object.keys(module));
        } catch (error) {
          console.log(`⚠️ ${objPath.split('/').pop()} 不存在或有问题`);
        }
      }
      
    } catch (error) {
      console.error('❌ 领域对象检查失败:', error);
    }
    
    console.log('✅ 领域对象检查完成\n');

    // 4. 测试仓储接口
    console.log('4️⃣ 测试仓储接口...');
    
    try {
      const repositories = [
        './src/contexts/trading-signals/domain/repositories/trading-signal-repository',
        './src/contexts/trading-signals/domain/repositories/strategy-repository'
      ];
      
      for (const repoPath of repositories) {
        try {
          const module = await import(repoPath);
          console.log(`✅ ${repoPath.split('/').pop()} 接口存在`);
          console.log(`- 导出:`, Object.keys(module));
        } catch (error) {
          console.log(`⚠️ ${repoPath.split('/').pop()} 接口不存在或有问题`);
        }
      }
      
    } catch (error) {
      console.error('❌ 仓储接口检查失败:', error);
    }
    
    console.log('✅ 仓储接口检查完成\n');

    // 5. 测试策略模式实现
    console.log('5️⃣ 测试策略模式实现...');
    
    try {
      const strategies = [
        './src/contexts/trading-signals/domain/strategies/trend-following-strategy',
        './src/contexts/trading-signals/domain/strategies/mean-reversion-strategy',
        './src/contexts/trading-signals/domain/strategies/strategy-factory'
      ];
      
      for (const strategyPath of strategies) {
        try {
          const module = await import(strategyPath);
          console.log(`✅ ${strategyPath.split('/').pop()} 策略存在`);
          console.log(`- 导出:`, Object.keys(module));
        } catch (error) {
          console.log(`⚠️ ${strategyPath.split('/').pop()} 策略不存在或有问题`);
        }
      }
      
    } catch (error) {
      console.error('❌ 策略实现检查失败:', error);
    }
    
    console.log('✅ 策略实现检查完成\n');

    // 6. 测试基础设施实现
    console.log('6️⃣ 测试基础设施实现...');
    
    try {
      const infrastructure = [
        './src/contexts/trading-signals/infrastructure/repositories/prisma-trading-signal-repository',
        './src/contexts/trading-signals/infrastructure/services/signal-calculation-service',
        './src/contexts/trading-signals/infrastructure/services/degradation-manager-service'
      ];
      
      for (const infraPath of infrastructure) {
        try {
          const module = await import(infraPath);
          console.log(`✅ ${infraPath.split('/').pop()} 基础设施存在`);
          console.log(`- 导出:`, Object.keys(module));
        } catch (error) {
          console.log(`⚠️ ${infraPath.split('/').pop()} 基础设施不存在或有问题`);
        }
      }
      
    } catch (error) {
      console.error('❌ 基础设施检查失败:', error);
    }
    
    console.log('✅ 基础设施检查完成\n');

    // 7. 检查依赖注入绑定
    console.log('7️⃣ 检查依赖注入绑定...');
    
    try {
      // 检查相关的容器模块
      const containerModules = [
        'trading-execution-container-module',
        'trading-analysis-container-module'
      ];

      for (const moduleName of containerModules) {
        try {
          const containerModule = await import(`./src/shared/infrastructure/di/modules/${moduleName}`);
          console.log(`✅ ${moduleName} 存在`);
          console.log(`- 导出:`, Object.keys(containerModule));
        } catch (importError) {
          console.log(`⚠️ ${moduleName} 不存在或有问题`);
        }
      }

      // 检查是否有专门的trading-signals容器模块
      console.log('⚠️ 注意：没有找到专门的trading-signals-container-module');

    } catch (error) {
      console.log('⚠️ 依赖注入绑定检查出现问题:', error instanceof Error ? error.message : String(error));
    }
    
    console.log('✅ 依赖注入绑定检查完成\n');

    console.log('🎉 Trading Signals上下文核心功能验证完成！');
    console.log('\n📊 验证结果总结:');
    console.log('✅ 类型定义检查完成');
    console.log('✅ 模块加载检查完成');
    console.log('✅ 领域对象检查完成');
    console.log('✅ 仓储接口检查完成');
    console.log('✅ 策略实现检查完成');
    console.log('✅ 基础设施检查完成');
    console.log('✅ 依赖注入绑定检查完成');

  } catch (error) {
    console.error('\n❌ Trading Signals上下文验证失败:');
    console.error('错误:', error instanceof Error ? error.message : String(error));
    console.error('堆栈:', error instanceof Error ? error.stack : '无堆栈信息');
    
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testTradingSignalsContext().catch(console.error);
}
