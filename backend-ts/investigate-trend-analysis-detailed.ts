/**
 * Trend Analysis 上下文模块详细调查
 * 基于真实文件检查，提供具体证据
 */

import 'reflect-metadata';
import { TYPES } from './src/shared/infrastructure/di/types/index';

interface FileCheck {
  path: string;
  exists: boolean;
  lineCount?: number;
  hasContent?: boolean;
  compilationError?: string;
}

interface ModuleAssessment {
  name: string;
  files: FileCheck[];
  completionPercentage: number;
  unifiedComponentUsage: string[];
  issues: string[];
  status: 'EXCELLENT' | 'GOOD' | 'NEEDS_IMPROVEMENT' | 'POOR';
}

async function checkFile(filePath: string): Promise<FileCheck> {
  const result: FileCheck = {
    path: filePath,
    exists: false
  };

  try {
    // 尝试导入文件
    const module = await import(filePath);
    result.exists = true;
    result.hasContent = Object.keys(module).length > 0;
    
    // 如果文件存在，尝试读取行数（这里简化处理）
    result.lineCount = 100; // 占位符，实际应该读取文件
    
    console.log(`  ✅ ${filePath.split('/').pop()} - 存在且可导入`);
    if (result.hasContent) {
      console.log(`    📄 导出内容: ${Object.keys(module).join(', ')}`);
    }
    
  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('Cannot find module')) {
        console.log(`  ❌ ${filePath.split('/').pop()} - 文件不存在`);
      } else {
        result.exists = true;
        result.compilationError = error.message.substring(0, 100) + '...';
        console.log(`  ⚠️ ${filePath.split('/').pop()} - 存在但有编译错误`);
        console.log(`    🔥 错误: ${result.compilationError}`);
      }
    }
  }

  return result;
}

async function investigateTrendAnalysisContext(): Promise<ModuleAssessment> {
  console.log('🔍 开始 Trend Analysis 上下文模块详细调查...\n');

  const assessment: ModuleAssessment = {
    name: 'Trend Analysis',
    files: [],
    completionPercentage: 0,
    unifiedComponentUsage: [],
    issues: [],
    status: 'POOR'
  };

  // 1. 检查应用服务层
  console.log('1️⃣ 检查应用服务层...');
  const applicationServices = [
    './src/contexts/trend-analysis/application/services/trend-analysis-application-service',
    './src/contexts/trend-analysis/application/services/pattern-recognition-service',
    './src/contexts/trend-analysis/application/services/technical-indicator-service'
  ];

  for (const path of applicationServices) {
    const fileCheck = await checkFile(path);
    assessment.files.push(fileCheck);
  }

  // 2. 检查领域层
  console.log('\n2️⃣ 检查领域实体...');
  const domainEntities = [
    './src/contexts/trend-analysis/domain/entities/trend-analysis',
    './src/contexts/trend-analysis/domain/entities/pattern-recognition-result',
    './src/contexts/trend-analysis/domain/entities/technical-indicator-result'
  ];

  for (const path of domainEntities) {
    const fileCheck = await checkFile(path);
    assessment.files.push(fileCheck);
  }

  console.log('\n3️⃣ 检查值对象...');
  const valueObjects = [
    './src/contexts/trend-analysis/domain/value-objects/trend-direction',
    './src/contexts/trend-analysis/domain/value-objects/trend-strength',
    './src/contexts/trend-analysis/domain/value-objects/pattern-type'
  ];

  for (const path of valueObjects) {
    const fileCheck = await checkFile(path);
    assessment.files.push(fileCheck);
  }

  // 3. 检查基础设施层
  console.log('\n4️⃣ 检查基础设施服务...');
  const infrastructureServices = [
    './src/contexts/trend-analysis/infrastructure/services/trend-analysis-engine',
    './src/contexts/trend-analysis/infrastructure/services/pattern-detection-service',
    './src/contexts/trend-analysis/infrastructure/services/trend-calculation-service'
  ];

  for (const path of infrastructureServices) {
    const fileCheck = await checkFile(path);
    assessment.files.push(fileCheck);
  }

  // 4. 检查仓储层
  console.log('\n5️⃣ 检查仓储层...');
  const repositories = [
    './src/contexts/trend-analysis/domain/repositories/trend-analysis-repository',
    './src/contexts/trend-analysis/infrastructure/repositories/prisma-trend-analysis-repository'
  ];

  for (const path of repositories) {
    const fileCheck = await checkFile(path);
    assessment.files.push(fileCheck);
  }

  // 5. 检查依赖注入配置
  console.log('\n6️⃣ 检查依赖注入配置...');
  const diConfig = [
    './src/shared/infrastructure/di/modules/trend-analysis-container-module'
  ];

  for (const path of diConfig) {
    const fileCheck = await checkFile(path);
    assessment.files.push(fileCheck);
  }

  // 6. 分析统一组件使用情况
  console.log('\n7️⃣ 分析统一组件使用情况...');
  
  // 检查是否有违反统一组件使用的文件
  const potentialViolations = [
    './src/contexts/trend-analysis/infrastructure/services/technical-indicator-calculator',
    './src/contexts/trend-analysis/infrastructure/services/pattern-recognition-engine'
  ];

  for (const path of potentialViolations) {
    try {
      await import(path);
      assessment.issues.push(`⚠️ 可能违反统一组件使用: ${path}`);
      console.log(`  ⚠️ 发现可能违规实现: ${path.split('/').pop()}`);
    } catch (error) {
      // 文件不存在，说明没有重复实现，这是好事
      console.log(`  ✅ 无重复实现: ${path.split('/').pop()}`);
    }
  }

  // 检查是否正确使用统一组件
  const unifiedComponentChecks = [
    'UnifiedTechnicalIndicatorCalculator',
    'PatternRecognitionService',
    'MultiTimeframeService'
  ];

  unifiedComponentChecks.forEach(component => {
    const isAvailable = !!(TYPES.Shared as any)?.[component];
    if (isAvailable) {
      assessment.unifiedComponentUsage.push(`✅ ${component} 可用`);
      console.log(`  ✅ 统一组件可用: ${component}`);
    } else {
      assessment.unifiedComponentUsage.push(`❌ ${component} 不可用`);
      console.log(`  ❌ 统一组件不可用: ${component}`);
    }
  });

  // 7. 计算完成度
  const existingFiles = assessment.files.filter(f => f.exists).length;
  const totalFiles = assessment.files.length;
  assessment.completionPercentage = Math.round((existingFiles / totalFiles) * 100);

  // 8. 确定状态
  const hasCompilationErrors = assessment.files.some(f => f.compilationError);
  const hasViolations = assessment.issues.length > 0;

  if (assessment.completionPercentage >= 90 && !hasCompilationErrors && !hasViolations) {
    assessment.status = 'EXCELLENT';
  } else if (assessment.completionPercentage >= 70 && !hasViolations) {
    assessment.status = 'GOOD';
  } else if (assessment.completionPercentage >= 40) {
    assessment.status = 'NEEDS_IMPROVEMENT';
  } else {
    assessment.status = 'POOR';
  }

  // 9. 生成报告
  console.log('\n📊 Trend Analysis 模块评估结果:');
  console.log(`  完成度: ${assessment.completionPercentage}% (${existingFiles}/${totalFiles})`);
  console.log(`  状态: ${assessment.status}`);
  console.log(`  编译错误: ${hasCompilationErrors ? '有' : '无'}`);
  console.log(`  统一组件违规: ${hasViolations ? '有' : '无'}`);

  if (assessment.issues.length > 0) {
    console.log('\n⚠️ 发现的问题:');
    assessment.issues.forEach(issue => console.log(`  ${issue}`));
  }

  console.log('\n📋 文件存在性详情:');
  assessment.files.forEach(file => {
    const status = file.exists ? (file.compilationError ? '⚠️' : '✅') : '❌';
    console.log(`  ${status} ${file.path.split('/').pop()}`);
    if (file.compilationError) {
      console.log(`    🔥 ${file.compilationError}`);
    }
  });

  console.log('\n🎉 Trend Analysis 上下文调查完成！');
  
  return assessment;
}

// 运行调查
if (require.main === module) {
  investigateTrendAnalysisContext()
    .then(result => {
      console.log('\n详细结果已生成，基于真实文件检查。');
    })
    .catch(console.error);
}
