#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 开始pre-commit检查..."

# 1. 清理测试编译产物
echo "🧹 清理测试编译产物..."
find tests -name "*.js" -delete 2>/dev/null || true
find tests -name "*.js.map" -delete 2>/dev/null || true
find tests -name "*.d.ts.map" -delete 2>/dev/null || true

# 2. 检查是否有编译产物被意外添加到暂存区
echo "🔍 检查暂存区中的编译产物..."
STAGED_COMPILED_FILES=$(git diff --cached --name-only | grep -E "\.(js|js\.map|d\.ts\.map)$" | grep "tests/" || true)

if [ -n "$STAGED_COMPILED_FILES" ]; then
  echo "❌ 检测到测试编译产物被添加到暂存区！"
  echo "以下文件将被自动移除："
  echo "$STAGED_COMPILED_FILES"
  echo "$STAGED_COMPILED_FILES" | xargs git reset HEAD
  echo "✅ 编译产物已从暂存区移除"
fi

# 3. 运行lint-staged进行代码格式化
echo "💅 运行代码格式化..."
npx lint-staged

# 4. 最终检查
echo "🔍 最终检查..."
FINAL_CHECK=$(git diff --cached --name-only | grep -E "\.(js|js\.map|d\.ts\.map)$" | grep "tests/" || true)
if [ -n "$FINAL_CHECK" ]; then
  echo "❌ 仍然检测到编译产物，提交被阻止！"
  echo "$FINAL_CHECK"
  exit 1
fi

echo "✅ Pre-commit检查完成，可以安全提交"
