#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 检查提交信息格式（可选）
commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
  echo "❌ 提交信息格式不正确！"
  echo "格式应为: type(scope): description"
  echo "类型: feat, fix, docs, style, refactor, test, chore"
  echo "示例: feat(api): add user authentication"
  echo "示例: fix: resolve database connection issue"
  exit 1
fi

echo "✅ 提交信息格式正确"
