/**
 * 所有上下文模块快速综合检查
 * 目标：快速评估所有核心业务模块的状态
 */

import 'reflect-metadata';
import { TYPES } from './src/shared/infrastructure/di/types/index';

interface ContextCheckResult {
  name: string;
  typesExist: boolean;
  containerModuleExists: boolean;
  coreServicesExist: number;
  totalCoreServices: number;
  status: 'GOOD' | 'PARTIAL' | 'POOR' | 'MISSING';
  issues: string[];
}

async function checkContext(
  contextName: string,
  typesPath: any,
  containerModuleName: string,
  coreServicePaths: string[]
): Promise<ContextCheckResult> {
  const result: ContextCheckResult = {
    name: contextName,
    typesExist: false,
    containerModuleExists: false,
    coreServicesExist: 0,
    totalCoreServices: coreServicePaths.length,
    status: 'MISSING',
    issues: []
  };

  // 检查类型定义
  if (typesPath && Object.keys(typesPath).length > 0) {
    result.typesExist = true;
  } else {
    result.issues.push('类型定义缺失或为空');
  }

  // 检查容器模块
  try {
    await import(`./src/shared/infrastructure/di/modules/${containerModuleName}`);
    result.containerModuleExists = true;
  } catch (error) {
    result.issues.push(`容器模块${containerModuleName}不存在`);
  }

  // 检查核心服务
  for (const servicePath of coreServicePaths) {
    try {
      await import(servicePath);
      result.coreServicesExist++;
    } catch (error) {
      // 服务不存在，不记录具体错误以保持输出简洁
    }
  }

  // 计算状态
  const serviceRatio = result.coreServicesExist / result.totalCoreServices;
  if (result.typesExist && result.containerModuleExists && serviceRatio >= 0.8) {
    result.status = 'GOOD';
  } else if (result.typesExist && serviceRatio >= 0.5) {
    result.status = 'PARTIAL';
  } else if (result.typesExist || serviceRatio > 0) {
    result.status = 'POOR';
  } else {
    result.status = 'MISSING';
  }

  return result;
}

async function testAllContexts() {
  console.log('🔍 开始所有上下文模块综合检查...\n');

  const contexts = [
    {
      name: 'Market Data',
      types: TYPES.MarketData,
      containerModule: 'market-data-container-module',
      coreServices: [
        './src/contexts/market-data/application/services/market-data-application-service',
        './src/contexts/market-data/infrastructure/repositories/prisma-market-symbol-repository',
        './src/contexts/market-data/infrastructure/repositories/prisma-price-data-repository'
      ]
    },
    {
      name: 'Trading Signals',
      types: TYPES.TradingSignals,
      containerModule: 'trading-signals-container-module',
      coreServices: [
        './src/contexts/trading-signals/application/services/signal-generation-application-service',
        './src/contexts/trading-signals/domain/entities/trading-signal',
        './src/contexts/trading-signals/domain/strategies/strategy-factory'
      ]
    },
    {
      name: 'Trend Analysis',
      types: TYPES.TrendAnalysis,
      containerModule: 'trend-analysis-container-module',
      coreServices: [
        './src/contexts/trend-analysis/application/services/trend-analysis-application-service',
        './src/contexts/trend-analysis/domain/entities/trend-analysis',
        './src/contexts/trend-analysis/infrastructure/repositories/prisma-trend-analysis-repository'
      ]
    },
    {
      name: 'AI Reasoning',
      types: TYPES.AIReasoning,
      containerModule: 'ai-reasoning-container-module',
      coreServices: [
        './src/contexts/ai-reasoning/application/services/ai-reasoning-application-service',
        './src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine',
        './src/contexts/ai-reasoning/infrastructure/services/unified-learning-engine'
      ]
    },
    {
      name: 'Risk Management',
      types: TYPES.RiskManagement,
      containerModule: 'risk-management-container-module',
      coreServices: [
        './src/contexts/risk-management/application/services/risk-assessment-application-service',
        './src/contexts/risk-management/domain/entities/risk-assessment',
        './src/contexts/risk-management/infrastructure/repositories/unified-risk-assessment-repository'
      ]
    },
    {
      name: 'Trading Execution',
      types: TYPES.TradingExecution,
      containerModule: 'trading-execution-container-module',
      coreServices: [
        './src/contexts/trading-execution/application/services/trading-execution-application-service',
        './src/contexts/trading-execution/domain/entities/trade-order',
        './src/contexts/trading-execution/infrastructure/repositories/trade-order-repository'
      ]
    },
    {
      name: 'User Management',
      types: TYPES.UserManagement,
      containerModule: 'user-management-container-module',
      coreServices: [
        './src/contexts/user-management/application/services/user-management-application-service',
        './src/contexts/user-management/domain/entities/user',
        './src/contexts/user-management/infrastructure/repositories/UnifiedUserRepository'
      ]
    }
  ];

  const results: ContextCheckResult[] = [];

  for (const context of contexts) {
    console.log(`🔍 检查 ${context.name}...`);
    const result = await checkContext(
      context.name,
      context.types,
      context.containerModule,
      context.coreServices
    );
    results.push(result);
    
    const statusIcon = {
      'GOOD': '✅',
      'PARTIAL': '⚠️',
      'POOR': '❌',
      'MISSING': '💀'
    }[result.status];
    
    console.log(`${statusIcon} ${context.name}: ${result.status} (${result.coreServicesExist}/${result.totalCoreServices} 服务)`);
  }

  console.log('\n📊 综合检查结果总结:');
  console.log('=' .repeat(80));
  
  results.forEach(result => {
    const statusIcon = {
      'GOOD': '✅',
      'PARTIAL': '⚠️', 
      'POOR': '❌',
      'MISSING': '💀'
    }[result.status];
    
    console.log(`${statusIcon} ${result.name.padEnd(20)} | 状态: ${result.status.padEnd(8)} | 服务: ${result.coreServicesExist}/${result.totalCoreServices} | 类型: ${result.typesExist ? '✓' : '✗'} | 容器: ${result.containerModuleExists ? '✓' : '✗'}`);
    
    if (result.issues.length > 0) {
      result.issues.forEach(issue => {
        console.log(`    ⚠️ ${issue}`);
      });
    }
  });

  // 统计分析
  const statusCounts = results.reduce((acc, result) => {
    acc[result.status] = (acc[result.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  console.log('\n📈 状态统计:');
  console.log(`✅ 良好 (GOOD): ${statusCounts.GOOD || 0}`);
  console.log(`⚠️ 部分 (PARTIAL): ${statusCounts.PARTIAL || 0}`);
  console.log(`❌ 较差 (POOR): ${statusCounts.POOR || 0}`);
  console.log(`💀 缺失 (MISSING): ${statusCounts.MISSING || 0}`);

  const totalServices = results.reduce((sum, r) => sum + r.totalCoreServices, 0);
  const existingServices = results.reduce((sum, r) => sum + r.coreServicesExist, 0);
  console.log(`\n🎯 整体服务实现率: ${existingServices}/${totalServices} (${Math.round(existingServices/totalServices*100)}%)`);

  console.log('\n🎉 所有上下文模块综合检查完成！');
}

// 运行测试
if (require.main === module) {
  testAllContexts().catch(console.error);
}
