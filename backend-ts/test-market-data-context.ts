/**
 * Market Data 上下文核心功能验证测试
 * 目标：验证market-data模块的核心功能是否真正工作，而不是仅仅让测试通过
 */

import 'reflect-metadata';
import { Container } from 'inversify';
import { TYPES } from './src/shared/infrastructure/di/types/index';

// 导入核心模块
import { MarketDataApplicationService } from './src/contexts/market-data/application/services/market-data-application-service';

async function testMarketDataContext() {
  console.log('🔍 开始Market Data上下文核心功能验证...\n');

  try {
    // 1. 测试依赖注入容器配置
    console.log('1️⃣ 测试依赖注入容器配置...');
    
    const container = new Container();
    
    // 检查关键类型是否定义
    console.log('检查TYPES定义:');
    console.log('- MarketDataApplicationService:', TYPES.MarketData?.MarketDataApplicationService?.toString());
    console.log('- PriceDataRepository:', TYPES.MarketData?.PriceDataRepository?.toString());
    console.log('- HistoricalDataRepository:', TYPES.MarketData?.HistoricalDataRepository?.toString());
    console.log('- MarketSymbolRepository:', TYPES.MarketData?.MarketSymbolRepository?.toString());
    
    if (!TYPES.MarketData?.MarketDataApplicationService) {
      throw new Error('❌ MarketDataApplicationService类型未定义');
    }
    
    console.log('✅ 基础类型定义正常\n');

    // 2. 测试模块加载
    console.log('2️⃣ 测试模块加载...');
    
    try {
      // 尝试加载MarketDataApplicationService
      const serviceClass = MarketDataApplicationService;
      console.log('✅ MarketDataApplicationService类加载成功');
      console.log('- 构造函数参数数量:', serviceClass.length);
      
      // 检查装饰器
      const metadata = Reflect.getMetadata('inversify:tagged', serviceClass);
      console.log('- 依赖注入元数据存在:', !!metadata);
      
    } catch (error) {
      console.error('❌ 模块加载失败:', error);
      throw error;
    }
    
    console.log('✅ 模块加载正常\n');

    // 3. 测试依赖关系
    console.log('3️⃣ 测试依赖关系...');
    
    // 检查MarketDataApplicationService的依赖
    const dependencies = [
      'Logger',
      'SymbolRepository', 
      'PriceDataRepository',
      'HistoricalDataRepository',
      'ExchangeAdapterFactory',
      'MultiExchangeDataService',
      'UnifiedDtoMapperRegistry'
    ];
    
    console.log('MarketDataApplicationService需要的依赖:');
    dependencies.forEach(dep => {
      console.log(`- ${dep}: 需要实现`);
    });
    
    console.log('✅ 依赖关系分析完成\n');

    // 4. 测试仓储接口
    console.log('4️⃣ 测试仓储接口...');
    
    try {
      // 检查仓储接口是否存在
      const marketSymbolRepo = await import('./src/contexts/market-data/domain/repositories/market-symbol-repository');
      const priceDataRepo = await import('./src/contexts/market-data/domain/repositories/price-data-repository');
      const historicalDataRepo = await import('./src/contexts/market-data/domain/repositories/historical-data-repository');

      console.log('✅ 仓储接口加载成功:');
      console.log('- market-symbol-repository模块:', !!marketSymbolRepo);
      console.log('- price-data-repository模块:', !!priceDataRepo);
      console.log('- historical-data-repository模块:', !!historicalDataRepo);

      // 检查接口是否在模块中定义
      console.log('- IMarketSymbolRepository接口:', 'IMarketSymbolRepository' in marketSymbolRepo);

    } catch (error) {
      console.error('❌ 仓储接口加载失败:', error);
      throw error;
    }
    
    console.log('✅ 仓储接口正常\n');

    // 5. 测试领域实体
    console.log('5️⃣ 测试领域实体...');
    
    try {
      const { MarketSymbol } = await import('./src/contexts/market-data/domain/entities/market-symbol');
      const { TradingSymbol } = await import('./src/contexts/market-data/domain/value-objects/trading-symbol');
      const { Timeframe } = await import('./src/contexts/market-data/domain/value-objects/timeframe');
      
      console.log('✅ 领域实体加载成功:');
      console.log('- MarketSymbol:', !!MarketSymbol);
      console.log('- TradingSymbol:', !!TradingSymbol);
      console.log('- Timeframe:', !!Timeframe);
      
      // 测试创建实体
      const symbol = new TradingSymbol('BTC/USDT');
      console.log('- TradingSymbol创建测试:', symbol.symbol);
      
    } catch (error) {
      console.error('❌ 领域实体加载失败:', error);
      throw error;
    }
    
    console.log('✅ 领域实体正常\n');

    // 6. 检查基础设施实现
    console.log('6️⃣ 检查基础设施实现...');
    
    try {
      // 检查仓储实现是否存在
      const repositories = [
        './src/contexts/market-data/infrastructure/repositories/prisma-market-symbol-repository',
        './src/contexts/market-data/infrastructure/repositories/prisma-price-data-repository',
        './src/contexts/market-data/infrastructure/repositories/prisma-historical-data-repository'
      ];
      
      for (const repoPath of repositories) {
        try {
          await import(repoPath);
          console.log(`✅ ${repoPath.split('/').pop()} 存在`);
        } catch (error) {
          console.log(`⚠️ ${repoPath.split('/').pop()} 不存在或有问题`);
        }
      }
      
    } catch (error) {
      console.error('❌ 基础设施检查失败:', error);
    }
    
    console.log('✅ 基础设施检查完成\n');

    console.log('🎉 Market Data上下文核心功能验证完成！');
    console.log('\n📊 验证结果总结:');
    console.log('✅ 类型定义正常');
    console.log('✅ 模块加载正常');
    console.log('✅ 依赖关系清晰');
    console.log('✅ 仓储接口完整');
    console.log('✅ 领域实体可用');
    console.log('⚠️ 基础设施实现需要进一步检查');

  } catch (error) {
    console.error('\n❌ Market Data上下文验证失败:');
    console.error('错误:', error instanceof Error ? error.message : String(error));
    console.error('堆栈:', error instanceof Error ? error.stack : '无堆栈信息');
    
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testMarketDataContext().catch(console.error);
}
