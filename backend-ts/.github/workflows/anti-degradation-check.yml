name: 反降级逻辑检测

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  anti-degradation-check:
    name: 检测降级逻辑和模拟数据
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: backend-ts/package-lock.json
        
    - name: 安装依赖
      working-directory: backend-ts
      run: npm ci
      
    - name: 运行反降级逻辑检测
      working-directory: backend-ts
      run: |
        echo "🔍 开始反降级逻辑检测..."
        npx ts-node scripts/quality-assurance/anti-degradation-detector.ts
        
    - name: 检测结果处理
      if: failure()
      run: |
        echo "❌ 发现降级逻辑或模拟数据问题！"
        echo "请修复所有高严重性问题后重新提交。"
        echo ""
        echo "常见问题和修复方法："
        echo "1. getDefault方法 → 删除方法，改为抛出错误"
        echo "2. catch块降级返回 → 改为抛出具体错误"
        echo "3. 模拟数据 → 连接真实数据源"
        echo "4. TODO实现 → 完成真实实现"
        exit 1
        
    - name: 成功通知
      if: success()
      run: |
        echo "✅ 反降级逻辑检测通过！"
        echo "代码质量符合'零容忍虚假实现'要求。"

  # 额外的严格检查
  strict-pattern-check:
    name: 严格模式检查
    runs-on: ubuntu-latest
    needs: anti-degradation-check
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 检查禁用模式
      working-directory: backend-ts
      run: |
        echo "🔍 执行严格模式检查..."
        
        # 检查是否存在多个PrismaClient实例
        echo "检查PrismaClient实例..."
        PRISMA_COUNT=$(grep -r "new PrismaClient" src/ || true | wc -l)
        if [ "$PRISMA_COUNT" -gt 0 ]; then
          echo "❌ 发现 $PRISMA_COUNT 个直接PrismaClient实例化"
          echo "请使用DI容器中的统一PrismaClient实例"
          grep -r "new PrismaClient" src/ || true
          exit 1
        fi
        
        # 检查是否存在硬编码默认值
        echo "检查硬编码默认值..."
        DEFAULT_COUNT=$(grep -r "return.*{.*0\.[0-9].*}" src/ | grep -v test | wc -l || true)
        if [ "$DEFAULT_COUNT" -gt 10 ]; then
          echo "⚠️ 发现较多硬编码默认值，请检查是否为降级逻辑"
        fi
        
        # 检查是否存在空的catch块
        echo "检查空catch块..."
        EMPTY_CATCH=$(grep -r "catch.*{.*}" src/ | wc -l || true)
        if [ "$EMPTY_CATCH" -gt 0 ]; then
          echo "❌ 发现 $EMPTY_CATCH 个空catch块"
          echo "空catch块可能隐藏错误，请添加适当的错误处理"
          exit 1
        fi
        
        echo "✅ 严格模式检查通过"

  # 代码质量评分
  quality-score:
    name: 代码质量评分
    runs-on: ubuntu-latest
    needs: [anti-degradation-check, strict-pattern-check]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 计算质量评分
      working-directory: backend-ts
      run: |
        echo "📊 计算代码质量评分..."
        
        # 统计文件数量
        TOTAL_FILES=$(find src/ -name "*.ts" | grep -v test | wc -l)
        echo "总文件数: $TOTAL_FILES"
        
        # 统计TODO数量
        TODO_COUNT=$(grep -r "TODO\|FIXME" src/ | wc -l || true)
        echo "TODO/FIXME数量: $TODO_COUNT"
        
        # 统计注释覆盖率（简化计算）
        COMMENT_LINES=$(grep -r "^\s*\*\|^\s*//" src/ | wc -l || true)
        CODE_LINES=$(find src/ -name "*.ts" -exec wc -l {} + | tail -1 | awk '{print $1}')
        COMMENT_RATIO=$(echo "scale=2; $COMMENT_LINES * 100 / $CODE_LINES" | bc -l || echo "0")
        echo "注释覆盖率: ${COMMENT_RATIO}%"
        
        # 计算质量评分
        QUALITY_SCORE=100
        
        # TODO扣分
        TODO_PENALTY=$(echo "scale=0; $TODO_COUNT * 2" | bc -l || echo "0")
        QUALITY_SCORE=$(echo "$QUALITY_SCORE - $TODO_PENALTY" | bc -l)
        
        # 注释覆盖率加分
        if (( $(echo "$COMMENT_RATIO > 20" | bc -l) )); then
          QUALITY_SCORE=$(echo "$QUALITY_SCORE + 5" | bc -l)
        fi
        
        echo ""
        echo "🏆 代码质量评分: $QUALITY_SCORE/100"
        
        if (( $(echo "$QUALITY_SCORE >= 90" | bc -l) )); then
          echo "✅ 优秀！代码质量很高"
        elif (( $(echo "$QUALITY_SCORE >= 80" | bc -l) )); then
          echo "✅ 良好！代码质量不错"
        elif (( $(echo "$QUALITY_SCORE >= 70" | bc -l) )); then
          echo "⚠️ 一般，建议改进代码质量"
        else
          echo "❌ 代码质量需要大幅改进"
          exit 1
        fi
