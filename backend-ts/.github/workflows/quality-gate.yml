name: Quality Gate Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  quality-gate:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        cd backend-ts
        npm ci
        
    - name: Install bc calculator
      run: sudo apt-get update && sudo apt-get install -y bc
        
    - name: Make quality gate script executable
      run: chmod +x backend-ts/scripts/ci/quality-gate-check.sh
      
    - name: Run Quality Gate Checks
      id: quality_gate
      run: |
        cd backend-ts
        ./scripts/ci/quality-gate-check.sh
      continue-on-error: true
      
    - name: Upload Quality Reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: quality-reports-${{ github.run_number }}
        path: backend-ts/reports/
        retention-days: 30
        
    - name: Comment PR with Quality Results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          try {
            // 查找最新的质量报告
            const reportsDir = 'backend-ts/reports';
            const files = fs.readdirSync(reportsDir);
            const summaryFile = files.find(f => f.startsWith('quality-gate-summary-'));
            
            if (summaryFile) {
              const summaryPath = path.join(reportsDir, summaryFile);
              const summary = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
              
              const status = summary.status === 'PASSED' ? '✅' : '❌';
              const successRate = Math.round(summary.success_rate);
              
              const comment = \`## \${status} 质量门禁检查结果

**检查时间**: \${summary.timestamp}
**总检查项**: \${summary.total_checks}
**通过检查**: \${summary.passed_checks}
**失败检查**: \${summary.failed_checks}
**成功率**: \${successRate}%
**状态**: \${summary.status}

\${summary.status === 'FAILED' ?
  '⚠️ **发现质量问题，请修复后重新提交**\\n\\n详细报告请查看 Actions 中的 Artifacts。' :
  '🎉 **所有质量检查通过，代码质量良好！**'
}

---
*此评论由质量门禁自动生成*\`;

              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }
          } catch (error) {
            console.log('无法生成质量报告评论:', error);
          }
          
    - name: Fail if quality gate failed
      if: steps.quality_gate.outcome == 'failure'
      run: |
        echo "❌ 质量门禁检查失败"
        echo "请修复发现的问题后重新提交"
        exit 1
        
    - name: Success notification
      if: steps.quality_gate.outcome == 'success'
      run: |
        echo "✅ 质量门禁检查通过"
        echo "代码质量符合要求"

  # 额外的代码质量检查任务
  code-analysis:
    runs-on: ubuntu-latest
    needs: quality-gate
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        cd backend-ts
        npm ci
        
    - name: Run Redundancy Gate Check
      run: |
        cd backend-ts
        npx ts-node scripts/ci/redundancy-gate-check.ts
      continue-on-error: true

    - name: Run Anti-Degradation Detector
      run: |
        cd backend-ts
        npx ts-node scripts/quality-assurance/anti-degradation-detector.ts . reports/detailed-analysis.txt
      continue-on-error: true
      
    - name: Check for specific violations
      run: |
        cd backend-ts
        echo "🔍 检查特定违规模式..."
        
        # 检查PrismaClient实例
        echo "检查PrismaClient实例..."
        if grep -r "new PrismaClient" src/ --include="*.ts" | wc -l | grep -v "^0$"; then
          echo "❌ 发现多个PrismaClient实例"
          grep -r "new PrismaClient" src/ --include="*.ts"
          exit 1
        fi
        
        # 检查getDefault方法
        echo "检查getDefault方法..."
        if grep -r "getDefault[A-Z]" src/ --include="*.ts" | wc -l | grep -v "^0$"; then
          echo "❌ 发现getDefault方法"
          grep -r "getDefault[A-Z]" src/ --include="*.ts"
          exit 1
        fi
        
        # 检查模拟数据
        echo "检查模拟数据..."
        if grep -r -i "mock\|fake\|stub\|simulate\|模拟" src/ --include="*.ts" | grep -v "test\|spec" | wc -l | grep -v "^0$"; then
          echo "❌ 发现模拟数据"
          grep -r -i "mock\|fake\|stub\|simulate\|模拟" src/ --include="*.ts" | grep -v "test\|spec" | head -5
          exit 1
        fi
        
        echo "✅ 特定违规检查通过"
        
    - name: Upload detailed analysis
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: detailed-analysis-${{ github.run_number }}
        path: backend-ts/reports/detailed-analysis.txt
        retention-days: 30
