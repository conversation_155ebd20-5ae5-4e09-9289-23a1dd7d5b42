{"metadata": {"generatedAt": "2025-07-19T02:01:33.525Z", "totalEndpoints": 260, "sources": ["swagger", "runtime"], "version": "3.1.0"}, "endpoints": [{"name": "get-api-v1-admin-users", "method": "GET", "path": "/api/v1/admin/users", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-admin-users-userId", "method": "GET", "path": "/api/v1/admin/users/{userId}", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}}, {"name": "post-api-v1-admin-invitation-codes", "method": "POST", "path": "/api/v1/admin/invitation-codes", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-admin-invitation-codes", "method": "GET", "path": "/api/v1/admin/invitation-codes", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-admin-invitation-codes-stats", "method": "GET", "path": "/api/v1/admin/invitation-codes/stats", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}}, {"name": "patch-api-v1-admin-users-userId-status", "method": "PATCH", "path": "/api/v1/admin/users/{userId}/status", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "patch-api-v1-admin-users-userId-role", "method": "PATCH", "path": "/api/v1/admin/users/{userId}/role", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-admin-users-userId-revoke-sessions", "method": "POST", "path": "/api/v1/admin/users/{userId}/revoke-sessions", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "delete-api-v1-admin-invitation-codes-cleanup", "method": "DELETE", "path": "/api/v1/admin/invitation-codes/cleanup", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-ai-analytics-calls", "method": "GET", "path": "/api/ai-analytics/calls", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/ai-analytics/calls", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-ai-analytics-cost-statistics", "method": "GET", "path": "/api/ai-analytics/cost-statistics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/ai-analytics/cost-statistics", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-ai-analytics-quality-statistics", "method": "GET", "path": "/api/ai-analytics/quality-statistics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/ai-analytics/quality-statistics", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-ai-analytics-cache-statistics", "method": "GET", "path": "/api/ai-analytics/cache-statistics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/ai-analytics/cache-statistics", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-ai-analytics-report", "method": "GET", "path": "/api/ai-analytics/report", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/ai-analytics/report", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-ai-analytics-recommendations", "method": "GET", "path": "/api/ai-analytics/recommendations", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/ai-analytics/recommendations", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-ai-analytics-models", "method": "GET", "path": "/api/ai-analytics/models", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/ai-analytics/models", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-ai-decision-symbol", "method": "GET", "path": "/api/v1/ai/decision/{symbol}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-ai-analyze", "method": "POST", "path": "/api/v1/ai/analyze", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-ai-reasoning", "method": "POST", "path": "/api/v1/ai/reasoning", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-ai-market-context", "method": "GET", "path": "/api/v1/ai/market-context", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-ai-predictions-short-term", "method": "GET", "path": "/api/v1/ai/predictions/short-term", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-ai-predictions-long-term", "method": "GET", "path": "/api/v1/ai/predictions/long-term", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-ai-predictions-stats", "method": "GET", "path": "/api/v1/ai/predictions/stats", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-ai-health", "method": "GET", "path": "/api/v1/ai/health", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-auth-register", "method": "POST", "path": "/api/v1/auth/register", "expectedStatus": [200, 400], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"customValidator": "validation_error_ok"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-auth-login", "method": "POST", "path": "/api/v1/auth/login", "expectedStatus": [200, 400], "critical": true, "timeout": 10000, "description": "认证接口", "validation": {"customValidator": "validation_error_ok"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-auth-logout", "method": "POST", "path": "/api/v1/auth/logout", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-auth-refresh", "method": "POST", "path": "/api/v1/auth/refresh", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-auth-validate-invitation", "method": "POST", "path": "/api/v1/auth/validate-invitation", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-auth-me", "method": "GET", "path": "/api/v1/auth/me", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-auth-change-password", "method": "POST", "path": "/api/v1/auth/change-password", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-auth-revoke-all-sessions", "method": "POST", "path": "/api/v1/auth/revoke-all-sessions", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-config-key", "method": "GET", "path": "/api/v1/config/{key}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/config/{key}", "validation": {"requiredFields": ["success"]}}, {"name": "put-api-v1-config-key", "method": "PUT", "path": "/api/v1/config/{key}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/config/{key}", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-configs", "method": "GET", "path": "/api/v1/configs", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/configs", "validation": {"requiredFields": ["success"]}}, {"name": "put-api-v1-configs", "method": "PUT", "path": "/api/v1/configs", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/configs", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-config-key-reset", "method": "POST", "path": "/api/v1/config/{key}/reset", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/config/{key}/reset", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-config-key-history", "method": "GET", "path": "/api/v1/config/{key}/history", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/config/{key}/history", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-configs-history", "method": "GET", "path": "/api/v1/configs/history", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/configs/history", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-configs-stats", "method": "GET", "path": "/api/v1/configs/stats", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/configs/stats", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-configs-validate", "method": "POST", "path": "/api/v1/configs/validate", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/configs/validate", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-configs-validation-rules", "method": "GET", "path": "/api/v1/configs/validation-rules", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/configs/validation-rules", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-config-key-validation-rule", "method": "GET", "path": "/api/v1/config/{key}/validation-rule", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/config/{key}/validation-rule", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-user-userId-llm-configs", "method": "GET", "path": "/api/v1/user/{userId}/llm-configs", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-user-userId-llm-configs", "method": "POST", "path": "/api/v1/user/{userId}/llm-configs", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-user-userId-llm-configs-active", "method": "GET", "path": "/api/v1/user/{userId}/llm-configs/active", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"requiredFields": ["success"]}}, {"name": "put-api-v1-user-userId-llm-configs-provider", "method": "PUT", "path": "/api/v1/user/{userId}/llm-configs/{provider}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "delete-api-v1-user-userId-llm-configs-provider", "method": "DELETE", "path": "/api/v1/user/{userId}/llm-configs/{provider}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-monitoring-dashboard-accountId", "method": "GET", "path": "/api/v1/monitoring/dashboard/{accountId}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/dashboard/{accountId}", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-monitoring-metrics", "method": "POST", "path": "/api/v1/monitoring/metrics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/metrics", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-monitoring-anomalies-accountId", "method": "GET", "path": "/api/v1/monitoring/anomalies/{accountId}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/anomalies/{accountId}", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-monitoring-performance-comparison", "method": "GET", "path": "/api/v1/monitoring/performance-comparison", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/performance-comparison", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-monitoring-trigger-alert", "method": "POST", "path": "/api/v1/monitoring/trigger-alert", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/trigger-alert", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-monitoring-webhooks", "method": "GET", "path": "/api/v1/monitoring/webhooks", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/webhooks", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-monitoring-webhooks", "method": "POST", "path": "/api/v1/monitoring/webhooks", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/webhooks", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-monitoring-webhooks-id", "method": "PUT", "path": "/api/v1/monitoring/webhooks/{id}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/webhooks/{id}", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "delete-api-v1-monitoring-webhooks-id", "method": "DELETE", "path": "/api/v1/monitoring/webhooks/{id}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/webhooks/{id}", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-monitoring-webhooks-id-test", "method": "POST", "path": "/api/v1/monitoring/webhooks/{id}/test", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/webhooks/{id}/test", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-monitoring-webhooks-id-statistics", "method": "GET", "path": "/api/v1/monitoring/webhooks/{id}/statistics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/monitoring/webhooks/{id}/statistics", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-market-data-prices", "method": "GET", "path": "/api/v1/market-data/prices", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-market-data-prices-symbol", "method": "GET", "path": "/api/v1/market-data/prices/{symbol}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-market-data-klines", "method": "GET", "path": "/api/v1/market-data/klines", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-market-data-overview", "method": "GET", "path": "/api/v1/market-data/overview", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-market-data-symbols", "method": "GET", "path": "/api/v1/market-data/symbols", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-market-data-refresh", "method": "POST", "path": "/api/v1/market-data/refresh", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-model-preferences-scenarios", "method": "GET", "path": "/api/v1/model-preferences/scenarios", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/model-preferences/scenarios", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-model-preferences-scenarios-scenario-recommendations", "method": "GET", "path": "/api/v1/model-preferences/scenarios/{scenario}/recommendations", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/model-preferences/scenarios/{scenario}/recommendations", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-model-preferences-stats", "method": "GET", "path": "/api/v1/model-preferences/stats", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/model-preferences/stats", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-model-preferences-select", "method": "POST", "path": "/api/v1/model-preferences/select", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/model-preferences/select", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-model-preferences", "method": "GET", "path": "/api/v1/model-preferences", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/model-preferences", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-model-preferences", "method": "POST", "path": "/api/v1/model-preferences", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/model-preferences", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-model-preferences-scenario", "method": "GET", "path": "/api/v1/model-preferences/{scenario}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/model-preferences/{scenario}", "validation": {"requiredFields": ["success"]}}, {"name": "put-api-v1-model-preferences-scenario", "method": "PUT", "path": "/api/v1/model-preferences/{scenario}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/model-preferences/{scenario}", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "delete-api-v1-model-preferences-scenario", "method": "DELETE", "path": "/api/v1/model-preferences/{scenario}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/model-preferences/{scenario}", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v2-trading-signals-generate", "method": "POST", "path": "/api/v2/trading/signals/generate", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v2/trading/signals/generate", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v2-trading-signals-batch", "method": "POST", "path": "/api/v2/trading/signals/batch", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v2/trading/signals/batch", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v2-trading-signals-symbol-latest", "method": "GET", "path": "/api/v2/trading/signals/{symbol}/latest", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v2/trading/signals/{symbol}/latest", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v2-trading-signals-symbol-validate", "method": "GET", "path": "/api/v2/trading/signals/{symbol}/validate", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v2/trading/signals/{symbol}/validate", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v2-trading-signals-health", "method": "GET", "path": "/api/v2/trading/signals/health", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v2/trading/signals/health", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-risk-assessment-assess", "method": "POST", "path": "/api/v1/risk-assessment/assess", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "风险评估接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-risk-assessment-assessment", "method": "POST", "path": "/api/v1/risk-assessment/assessment", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "风险评估接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-risk-assessment-assessment-symbol", "method": "GET", "path": "/api/v1/risk-assessment/assessment/{symbol}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "风险评估接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-risk-assessment-comprehensive-analysis-accountId", "method": "GET", "path": "/api/v1/risk-assessment/comprehensive-analysis/{accountId}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "风险评估接口", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-risk-assessment-monitoring", "method": "POST", "path": "/api/v1/risk-assessment/monitoring", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "风险评估接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-risk-assessment-stress-test", "method": "POST", "path": "/api/v1/risk-assessment/stress-test", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "风险评估接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-strategy-sync-validate", "method": "POST", "path": "/api/v1/strategy-sync/validate", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/strategy-sync/validate", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-strategy-sync-sync", "method": "POST", "path": "/api/v1/strategy-sync/sync", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/strategy-sync/sync", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-strategy-sync-status-syncId", "method": "GET", "path": "/api/v1/strategy-sync/status/{syncId}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/strategy-sync/status/{syncId}", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-strategy-sync-history-accountId", "method": "GET", "path": "/api/v1/strategy-sync/history/{accountId}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/strategy-sync/history/{accountId}", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-strategy-sync-cancel-syncId", "method": "POST", "path": "/api/v1/strategy-sync/cancel/{syncId}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/strategy-sync/cancel/{syncId}", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-strategy-sync-config-recommendations-accountId", "method": "GET", "path": "/api/v1/strategy-sync/config-recommendations/{accountId}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/strategy-sync/config-recommendations/{accountId}", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-strategy-sync-auto-sync-check", "method": "POST", "path": "/api/v1/strategy-sync/auto-sync-check", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/strategy-sync/auto-sync-check", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-trading-execution-accounts", "method": "GET", "path": "/api/v1/trading-execution/accounts", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "post-api-v1-trading-execution-accounts", "method": "POST", "path": "/api/v1/trading-execution/accounts", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-trading-execution-accounts-accountId", "method": "GET", "path": "/api/v1/trading-execution/accounts/{accountId}", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-accounts-accountId-balance", "method": "GET", "path": "/api/v1/trading-execution/accounts/{accountId}/balance", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-accounts-accountId-statistics", "method": "GET", "path": "/api/v1/trading-execution/accounts/{accountId}/statistics", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "post-api-v1-trading-execution-dual-track-enable", "method": "POST", "path": "/api/v1/trading-execution/dual-track/enable", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trading-execution-dual-track-disable", "method": "POST", "path": "/api/v1/trading-execution/dual-track/disable", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trading-execution-dual-track-sync-strategy", "method": "POST", "path": "/api/v1/trading-execution/dual-track/sync-strategy", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-trading-execution-dual-track-settings", "method": "PUT", "path": "/api/v1/trading-execution/dual-track/settings", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trading-execution-execute", "method": "POST", "path": "/api/v1/trading-execution/execute", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trading-execution-toggle-auto-trading", "method": "POST", "path": "/api/v1/trading-execution/toggle-auto-trading", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-trading-execution-accounts-accountId-positions", "method": "GET", "path": "/api/v1/trading-execution/accounts/{accountId}/positions", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-accounts-accountId-orders", "method": "GET", "path": "/api/v1/trading-execution/accounts/{accountId}/orders", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "post-api-v1-trading-execution-accounts-accountId-manage-positions", "method": "POST", "path": "/api/v1/trading-execution/accounts/{accountId}/manage-positions", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trading-execution-accounts-accountId-sync-binance-history", "method": "POST", "path": "/api/v1/trading-execution/accounts/{accountId}/sync-binance-history", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-trading-execution-status", "method": "GET", "path": "/api/v1/trading-execution/status", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-risk-events", "method": "GET", "path": "/api/v1/trading-execution/risk-events", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "post-api-v1-trading-execution-credentials", "method": "POST", "path": "/api/v1/trading-execution/credentials", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-trading-execution-credentials", "method": "GET", "path": "/api/v1/trading-execution/credentials", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "post-api-v1-trend-analysis", "method": "POST", "path": "/api/v1/trend/analysis", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-trend-quick-symbol-timeframe", "method": "GET", "path": "/api/v1/trend/quick/{symbol}/{timeframe}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-trend-prediction-symbol", "method": "GET", "path": "/api/v1/trend/prediction/{symbol}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-v1-trend-comprehensive-analysis", "method": "POST", "path": "/api/v1/trend/comprehensive-analysis", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trend-fundamental-analysis", "method": "POST", "path": "/api/v1/trend/fundamental-analysis", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trend-indicators", "method": "POST", "path": "/api/v1/trend/indicators", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trend-patterns", "method": "POST", "path": "/api/v1/trend/patterns", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-health", "method": "GET", "path": "/api/health", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/health", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-health-quick", "method": "GET", "path": "/api/health/quick", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/health/quick", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-health-metrics", "method": "GET", "path": "/api/health/metrics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/health/metrics", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-health-service-serviceName", "method": "GET", "path": "/api/health/service/{serviceName}", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/health/service/{serviceName}", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-health-ready", "method": "GET", "path": "/api/health/ready", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/health/ready", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-health-live", "method": "GET", "path": "/api/health/live", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/health/live", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-performance-stats", "method": "GET", "path": "/api/performance/stats", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/performance/stats", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-performance-recommendations", "method": "GET", "path": "/api/performance/recommendations", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/performance/recommendations", "validation": {"requiredFields": ["success"]}}, {"name": "post-api-performance-cache-clear", "method": "POST", "path": "/api/performance/cache/clear", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/performance/cache/clear", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-monitoring-metrics", "method": "GET", "path": "/api/monitoring/metrics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/monitoring/metrics", "validation": {"requiredFields": ["success"]}}, {"name": "delete-api-v1-api-keys-keyId", "method": "DELETE", "path": "/api/v1/api-keys/:keyId", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys/:keyId", "validation": {"customValidator": "auth_required"}}, {"name": "delete-api-v1-auth-api-keys-keyId", "method": "DELETE", "path": "/api/v1/auth/api-keys/:keyId", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}}, {"name": "delete-api-v1-user-config-users-userId-preferences", "method": "DELETE", "path": "/api/v1/user-config/users/:userId/preferences", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "delete-api-v1-user-config-users-userId-profile", "method": "DELETE", "path": "/api/v1/user-config/users/:userId/profile", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-", "method": "GET", "path": "/", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /", "validation": {"requiredFields": ["success"]}}, {"name": "get-api", "method": "GET", "path": "/api", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-docs", "method": "GET", "path": "/api-docs", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api-docs", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-docs-json", "method": "GET", "path": "/api-docs.json", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api-docs.json", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-admin-users-userId", "method": "GET", "path": "/api/v1/admin/users/:userId", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-ai-decision-symbol", "method": "GET", "path": "/api/v1/ai/decision/:symbol", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-ai-unified-learning-diagnostics", "method": "GET", "path": "/api/v1/ai/unified-learning/diagnostics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-ai-unified-learning-health", "method": "GET", "path": "/api/v1/ai/unified-learning/health", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-ai-unified-learning-metrics", "method": "GET", "path": "/api/v1/ai/unified-learning/metrics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-ai-unified-learning-status", "method": "GET", "path": "/api/v1/ai/unified-learning/status", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-analytics-cache-statistics", "method": "GET", "path": "/api/v1/analytics/cache-statistics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "分析统计接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-analytics-calls", "method": "GET", "path": "/api/v1/analytics/calls", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "分析统计接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-analytics-cost-statistics", "method": "GET", "path": "/api/v1/analytics/cost-statistics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "分析统计接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-analytics-models", "method": "GET", "path": "/api/v1/analytics/models", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "分析统计接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-analytics-quality-statistics", "method": "GET", "path": "/api/v1/analytics/quality-statistics", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "分析统计接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-analytics-recommendations", "method": "GET", "path": "/api/v1/analytics/recommendations", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "分析统计接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-analytics-report", "method": "GET", "path": "/api/v1/analytics/report", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "分析统计接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-api-keys", "method": "GET", "path": "/api/v1/api-keys", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-api-keys-health", "method": "GET", "path": "/api/v1/api-keys/health", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys/health", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-api-keys-test-admin", "method": "GET", "path": "/api/v1/api-keys/test/admin", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys/test/admin", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-api-keys-test-high-frequency", "method": "GET", "path": "/api/v1/api-keys/test/high-frequency", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys/test/high-frequency", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-api-keys-test-market", "method": "GET", "path": "/api/v1/api-keys/test/market", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys/test/market", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-api-keys-test-signal", "method": "GET", "path": "/api/v1/api-keys/test/signal", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys/test/signal", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-auth-api-keys", "method": "GET", "path": "/api/v1/auth/api-keys", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-auth-api-keys-health", "method": "GET", "path": "/api/v1/auth/api-keys/health", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-auth-api-keys-test-admin", "method": "GET", "path": "/api/v1/auth/api-keys/test/admin", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-auth-api-keys-test-high-frequency", "method": "GET", "path": "/api/v1/auth/api-keys/test/high-frequency", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-auth-api-keys-test-market", "method": "GET", "path": "/api/v1/auth/api-keys/test/market", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-auth-api-keys-test-signal", "method": "GET", "path": "/api/v1/auth/api-keys/test/signal", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-dual-layer-reasoning-learning-status", "method": "GET", "path": "/api/v1/dual-layer-reasoning/learning-status", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/dual-layer-reasoning/learning-status", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-dual-layer-reasoning-transparency-analysisId", "method": "GET", "path": "/api/v1/dual-layer-reasoning/transparency/:analysisId", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/dual-layer-reasoning/transparency/:analysisId", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-market-data-price-symbol", "method": "GET", "path": "/api/v1/market-data/price/:symbol", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-market-data-prices-symbol", "method": "GET", "path": "/api/v1/market-data/prices/:symbol", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-risk-assessment-assessment-symbol", "method": "GET", "path": "/api/v1/risk-assessment/assessment/:symbol", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "风险评估接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-risk-assessment-comprehensive-analysis-accountId", "method": "GET", "path": "/api/v1/risk-assessment/comprehensive-analysis/:accountId", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "风险评估接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-signals-signals-symbol-latest", "method": "GET", "path": "/api/v1/signals/signals/:symbol/latest", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "交易信号接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-signals-signals-symbol-validate", "method": "GET", "path": "/api/v1/signals/signals/:symbol/validate", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "交易信号接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-signals-signals-health", "method": "GET", "path": "/api/v1/signals/signals/health", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "交易信号接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-statistics-statistics-calls", "method": "GET", "path": "/api/v1/statistics/statistics/calls", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/statistics/statistics/calls", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-statistics-statistics-costs", "method": "GET", "path": "/api/v1/statistics/statistics/costs", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/statistics/statistics/costs", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-statistics-statistics-overview", "method": "GET", "path": "/api/v1/statistics/statistics/overview", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/statistics/statistics/overview", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-statistics-statistics-performance", "method": "GET", "path": "/api/v1/statistics/statistics/performance", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/statistics/statistics/performance", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-statistics-statistics-popular-models", "method": "GET", "path": "/api/v1/statistics/statistics/popular-models", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/statistics/statistics/popular-models", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-trading-execution-accounts-accountId", "method": "GET", "path": "/api/v1/trading-execution/accounts/:accountId", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-accounts-accountId-balance", "method": "GET", "path": "/api/v1/trading-execution/accounts/:accountId/balance", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-accounts-accountId-orders", "method": "GET", "path": "/api/v1/trading-execution/accounts/:accountId/orders", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-accounts-accountId-positions", "method": "GET", "path": "/api/v1/trading-execution/accounts/:accountId/positions", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-accounts-accountId-statistics", "method": "GET", "path": "/api/v1/trading-execution/accounts/:accountId/statistics", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-accounts-default", "method": "GET", "path": "/api/v1/trading-execution/accounts/default", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-dual-track-account-pairing", "method": "GET", "path": "/api/v1/trading-execution/dual-track/account-pairing", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-dual-track-accounts", "method": "GET", "path": "/api/v1/trading-execution/dual-track/accounts", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-dual-track-metrics", "method": "GET", "path": "/api/v1/trading-execution/dual-track/metrics", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-dual-track-performance-comparison", "method": "GET", "path": "/api/v1/trading-execution/dual-track/performance-comparison", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-dual-track-statistics", "method": "GET", "path": "/api/v1/trading-execution/dual-track/statistics", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-dual-track-status", "method": "GET", "path": "/api/v1/trading-execution/dual-track/status", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-execution-dual-track-sync-status", "method": "GET", "path": "/api/v1/trading-execution/dual-track/sync-status", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-trading-signals-health", "method": "GET", "path": "/api/v1/trading-signals/health", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-trend-analysis-prediction-symbol", "method": "GET", "path": "/api/v1/trend-analysis/prediction/:symbol", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-trend-analysis-quick-symbol-timeframe", "method": "GET", "path": "/api/v1/trend-analysis/quick/:symbol/:timeframe", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-user-config-notifications-notificationType-users", "method": "GET", "path": "/api/v1/user-config/notifications/:notificationType/users", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-config-preferences-statistics", "method": "GET", "path": "/api/v1/user-config/preferences/statistics", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-config-profiles-statistics", "method": "GET", "path": "/api/v1/user-config/profiles/statistics", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-config-users-userId-preferences", "method": "GET", "path": "/api/v1/user-config/users/:userId/preferences", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-config-users-userId-preferences-localization", "method": "GET", "path": "/api/v1/user-config/users/:userId/preferences/localization", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-config-users-userId-preferences-validate", "method": "GET", "path": "/api/v1/user-config/users/:userId/preferences/validate", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-config-users-userId-profile", "method": "GET", "path": "/api/v1/user-config/users/:userId/profile", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-config-users-userId-profile-risk-level", "method": "GET", "path": "/api/v1/user-config/users/:userId/profile/risk-level", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-config-users-userId-profile-strategy-adjustments", "method": "GET", "path": "/api/v1/user-config/users/:userId/profile/strategy-adjustments", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-config-users-userId-profile-strategy-compatibility", "method": "GET", "path": "/api/v1/user-config/users/:userId/profile/strategy-compatibility", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-config-users-userId-profile-validate", "method": "GET", "path": "/api/v1/user-config/users/:userId/profile/validate", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-management", "method": "GET", "path": "/api/v1/user-management", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-management-userId", "method": "GET", "path": "/api/v1/user-management/:userId", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-api-v1-user-management-profile", "method": "GET", "path": "/api/v1/user-management/profile", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}}, {"name": "get-dev-routes", "method": "GET", "path": "/dev/routes", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /dev/routes", "validation": {"requiredFields": ["success"]}}, {"name": "get-docs", "method": "GET", "path": "/docs", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /docs", "validation": {"requiredFields": ["success"]}}, {"name": "get-documentation", "method": "GET", "path": "/documentation", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /documentation", "validation": {"requiredFields": ["success"]}}, {"name": "get-health", "method": "GET", "path": "/health", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "系统健康检查", "validation": {"requiredFields": ["success"]}}, {"name": "get-swagger", "method": "GET", "path": "/swagger", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /swagger", "validation": {"requiredFields": ["success"]}}, {"name": "patch-api-v1-admin-users-userId-role", "method": "PATCH", "path": "/api/v1/admin/users/:userId/role", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "patch-api-v1-admin-users-userId-status", "method": "PATCH", "path": "/api/v1/admin/users/:userId/status", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "patch-api-v1-user-management-userId-role", "method": "PATCH", "path": "/api/v1/user-management/:userId/role", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "patch-api-v1-user-management-userId-status", "method": "PATCH", "path": "/api/v1/user-management/:userId/status", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-admin-users-userId-revoke-sessions", "method": "POST", "path": "/api/v1/admin/users/:userId/revoke-sessions", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "管理员接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-ai-unified-learning-predict-comprehensive", "method": "POST", "path": "/api/v1/ai/unified-learning/predict/comprehensive", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-ai-unified-learning-predict-macro", "method": "POST", "path": "/api/v1/ai/unified-learning/predict/macro", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-ai-unified-learning-predict-meso", "method": "POST", "path": "/api/v1/ai/unified-learning/predict/meso", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-ai-unified-learning-predict-micro", "method": "POST", "path": "/api/v1/ai/unified-learning/predict/micro", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-ai-unified-learning-restart", "method": "POST", "path": "/api/v1/ai/unified-learning/restart", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-api-keys", "method": "POST", "path": "/api/v1/api-keys", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-api-keys-test-trading", "method": "POST", "path": "/api/v1/api-keys/test/trading", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys/test/trading", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-api-keys-validate", "method": "POST", "path": "/api/v1/api-keys/validate", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys/validate", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-auth-api-keys", "method": "POST", "path": "/api/v1/auth/api-keys", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-auth-api-keys-test-trading", "method": "POST", "path": "/api/v1/auth/api-keys/test/trading", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-auth-api-keys-validate", "method": "POST", "path": "/api/v1/auth/api-keys/validate", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-dual-layer-reasoning-analyze", "method": "POST", "path": "/api/v1/dual-layer-reasoning/analyze", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/dual-layer-reasoning/analyze", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-dual-layer-reasoning-compare", "method": "POST", "path": "/api/v1/dual-layer-reasoning/compare", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/dual-layer-reasoning/compare", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-dual-layer-reasoning-pure-ai", "method": "POST", "path": "/api/v1/dual-layer-reasoning/pure-ai", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/dual-layer-reasoning/pure-ai", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-signals-signals-batch", "method": "POST", "path": "/api/v1/signals/signals/batch", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "交易信号接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-signals-signals-generate", "method": "POST", "path": "/api/v1/signals/signals/generate", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "交易信号接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trading-execution-accounts-accountId-manage-positions", "method": "POST", "path": "/api/v1/trading-execution/accounts/:accountId/manage-positions", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trading-execution-accounts-accountId-sync-binance-history", "method": "POST", "path": "/api/v1/trading-execution/accounts/:accountId/sync-binance-history", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trading-signals-generate", "method": "POST", "path": "/api/v1/trading-signals/generate", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trading-signals-generate-batch", "method": "POST", "path": "/api/v1/trading-signals/generate-batch", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "交易接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trend-analysis-analysis", "method": "POST", "path": "/api/v1/trend-analysis/analysis", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trend-analysis-comprehensive-analysis", "method": "POST", "path": "/api/v1/trend-analysis/comprehensive-analysis", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trend-analysis-fundamental-analysis", "method": "POST", "path": "/api/v1/trend-analysis/fundamental-analysis", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trend-analysis-indicators", "method": "POST", "path": "/api/v1/trend-analysis/indicators", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trend-analysis-multi-timeframe", "method": "POST", "path": "/api/v1/trend-analysis/multi-timeframe", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trend-analysis-patterns", "method": "POST", "path": "/api/v1/trend-analysis/patterns", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-trend-analysis-timeframes", "method": "POST", "path": "/api/v1/trend-analysis/timeframes", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "趋势分析接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-user-config-users-userId-preferences", "method": "POST", "path": "/api/v1/user-config/users/:userId/preferences", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-user-config-users-userId-preferences-reset", "method": "POST", "path": "/api/v1/user-config/users/:userId/preferences/reset", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-user-config-users-userId-profile", "method": "POST", "path": "/api/v1/user-config/users/:userId/profile", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-user-config-users-preferences-batch", "method": "POST", "path": "/api/v1/user-config/users/preferences/batch", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-user-config-users-profiles-batch-risk-levels", "method": "POST", "path": "/api/v1/user-config/users/profiles/batch-risk-levels", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-user-management-login", "method": "POST", "path": "/api/v1/user-management/login", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-user-management-logout", "method": "POST", "path": "/api/v1/user-management/logout", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-user-management-refresh-token", "method": "POST", "path": "/api/v1/user-management/refresh-token", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-user-management-register", "method": "POST", "path": "/api/v1/user-management/register", "expectedStatus": [200, 400], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "validation_error_ok"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-api-keys-keyId", "method": "PUT", "path": "/api/v1/api-keys/:keyId", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "API接口: /api/v1/api-keys/:keyId", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-auth-api-keys-keyId", "method": "PUT", "path": "/api/v1/auth/api-keys/:keyId", "expectedStatus": [200], "critical": false, "timeout": 10000, "description": "认证接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-user-config-users-userId-preferences", "method": "PUT", "path": "/api/v1/user-config/users/:userId/preferences", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-user-config-users-userId-preferences-display", "method": "PUT", "path": "/api/v1/user-config/users/:userId/preferences/display", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-user-config-users-userId-preferences-notifications", "method": "PUT", "path": "/api/v1/user-config/users/:userId/preferences/notifications", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-user-config-users-userId-preferences-trading", "method": "PUT", "path": "/api/v1/user-config/users/:userId/preferences/trading", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-user-config-users-userId-profile", "method": "PUT", "path": "/api/v1/user-config/users/:userId/profile", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-user-management-password", "method": "PUT", "path": "/api/v1/user-management/password", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "put-api-v1-user-management-profile", "method": "PUT", "path": "/api/v1/user-management/profile", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口", "validation": {"customValidator": "auth_required"}, "headers": {"Content-Type": "application/json"}, "body": {}}], "monitoring": {"interval": 300000, "retryAttempts": 3, "retryDelay": 5000, "alertThreshold": {"consecutiveFailures": 3, "uptimeBelow": 95}}, "notifications": {"enabled": true, "channels": ["console", "log"], "webhook": {"url": null, "enabled": false}}}