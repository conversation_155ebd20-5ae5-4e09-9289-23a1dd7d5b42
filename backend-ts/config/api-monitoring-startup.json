{"metadata": {"generatedAt": "2025-07-19T02:01:33.526Z", "totalEndpoints": 6, "purpose": "startup-validation", "description": "项目启动时验证关键API端点", "version": "3.1.0"}, "endpoints": [{"name": "post-api-v1-ai-reasoning", "method": "POST", "path": "/api/v1/ai/reasoning", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "AI推理接口", "validation": {"requiredFields": ["success"]}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "post-api-v1-auth-login", "method": "POST", "path": "/api/v1/auth/login", "expectedStatus": [200, 400], "critical": true, "timeout": 10000, "description": "认证接口", "validation": {"customValidator": "validation_error_ok"}, "headers": {"Content-Type": "application/json"}, "body": {}}, {"name": "get-api-v1-market-data-prices", "method": "GET", "path": "/api/v1/market-data/prices", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-market-data-overview", "method": "GET", "path": "/api/v1/market-data/overview", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-api-v1-market-data-symbols", "method": "GET", "path": "/api/v1/market-data/symbols", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "市场数据接口", "validation": {"requiredFields": ["success"]}}, {"name": "get-health", "method": "GET", "path": "/health", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "系统健康检查", "validation": {"requiredFields": ["success"]}}], "monitoring": {"interval": 60000, "retryAttempts": 2, "retryDelay": 3000, "alertThreshold": {"consecutiveFailures": 2, "uptimeBelow": 90}}}