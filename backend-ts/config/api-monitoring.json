{"endpoints": [{"name": "health-check", "method": "GET", "path": "/health", "expectedStatus": [200], "critical": true, "timeout": 5000, "description": "系统健康检查", "validation": {"requiredFields": ["success", "message"]}}, {"name": "market-data-prices", "method": "GET", "path": "/api/v1/market-data/prices", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "市场数据价格接口", "validation": {"requiredFields": ["success", "data"]}}, {"name": "market-data-symbols", "method": "GET", "path": "/api/v1/market-data/symbols", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "交易对列表接口", "validation": {"requiredFields": ["success", "data"]}}, {"name": "market-data-overview", "method": "GET", "path": "/api/v1/market-data/overview", "expectedStatus": [200], "critical": true, "timeout": 10000, "description": "市场概览接口", "validation": {"requiredFields": ["success", "data"]}}, {"name": "admin-users", "method": "GET", "path": "/api/v1/admin/users", "expectedStatus": [200, 401], "critical": false, "timeout": 10000, "description": "用户管理接口（需要认证）", "validation": {"customValidator": "auth_required"}}, {"name": "auth-login", "method": "POST", "path": "/api/v1/auth/login", "expectedStatus": [200, 400], "critical": false, "timeout": 10000, "description": "用户登录接口", "body": {}, "headers": {"Content-Type": "application/json"}, "validation": {"customValidator": "validation_error_ok"}}], "monitoring": {"interval": 60000, "retryAttempts": 3, "retryDelay": 5000, "alertThreshold": {"consecutiveFailures": 3, "uptimeBelow": 95}}, "notifications": {"enabled": true, "channels": ["console", "webhook"], "webhook": {"url": null, "enabled": false}}}