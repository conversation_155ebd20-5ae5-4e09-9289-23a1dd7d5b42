{"timestamp": "2025-07-17T12:48:48.676Z", "totalTests": 6, "passedTests": 5, "failedTests": 1, "config": {"testDuration": 120000, "sampleTradesCount": 20, "riskThreshold": 0.8, "enforcementMode": "STRICT"}, "results": [{"testName": "风险限制规则配置测试", "passed": true, "message": "风险限制规则配置正常，默认规则数量: 5", "duration": 130, "timestamp": "2025-07-17T12:48:47.572Z", "details": {"defaultRulesCount": 5, "addSuccess": true, "updateSuccess": true, "deleteSuccess": true}}, {"testName": "交易前风险检查测试", "passed": true, "message": "交易前风险检查正常，测试用例: 3个", "duration": 304, "timestamp": "2025-07-17T12:48:47.876Z", "details": {"testCases": [{"testCase": "正常交易", "allowed": true, "expectedAllowed": true, "violationsCount": 0, "enforcementActions": []}, {"testCase": "超大仓位交易", "allowed": false, "expectedAllowed": false, "violationsCount": 1, "enforcementActions": ["BLOCK_NEW_ORDERS"]}, {"testCase": "高杠杆交易", "allowed": false, "expectedAllowed": false, "violationsCount": 1, "enforcementActions": ["BLOCK_NEW_ORDERS"]}]}}, {"testName": "风险违规检测测试", "passed": true, "message": "风险违规检测正常，测试场景: 3个", "duration": 154, "timestamp": "2025-07-17T12:48:48.030Z", "details": {"detectionResults": [{"type": "DAILY_LOSS", "detected": true, "expected": true, "severity": "HIGH", "action": "BLOCK_NEW_ORDERS"}, {"type": "POSITION_SIZE", "detected": true, "expected": true, "severity": "HIGH", "action": "BLOCK_NEW_ORDERS"}, {"type": "LEVERAGE", "detected": false, "expected": false}]}}, {"testName": "强制执行动作测试", "passed": true, "message": "强制执行动作正常，测试动作: 4个", "duration": 455, "timestamp": "2025-07-17T12:48:48.485Z", "details": {"actionResults": [{"action": "BLOCK_NEW_ORDERS", "success": true, "message": "强制执行动作 BLOCK_NEW_ORDERS 执行成功", "executionTime": 116}, {"action": "REDUCE_POSITION", "success": true, "message": "强制执行动作 REDUCE_POSITION 执行成功", "executionTime": 115}, {"action": "CLOSE_POSITION", "success": true, "message": "强制执行动作 CLOSE_POSITION 执行成功", "executionTime": 149}, {"action": "SEND_ALERT", "success": true, "message": "强制执行动作 SEND_ALERT 执行成功", "executionTime": 75}]}}, {"testName": "紧急停止机制测试", "passed": false, "message": "紧急停止机制测试失败: 紧急停止期间交易未被阻止", "duration": 143, "timestamp": "2025-07-17T12:48:48.628Z"}, {"testName": "风险限制修改测试", "passed": true, "message": "风险限制修改功能正常", "duration": 48, "timestamp": "2025-07-17T12:48:48.676Z", "details": {"orderSizeLimit": {"modifiedQuantity": 0.5, "originalQuantity": 1, "reason": "POSITION_SIZE_LIMIT"}, "approvalRequired": {"requiresApproval": true, "reason": "HIGH_VALUE_ORDER"}}}]}