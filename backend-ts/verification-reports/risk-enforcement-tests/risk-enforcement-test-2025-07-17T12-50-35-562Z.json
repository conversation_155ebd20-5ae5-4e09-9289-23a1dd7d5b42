{"timestamp": "2025-07-17T12:50:35.562Z", "totalTests": 6, "passedTests": 6, "failedTests": 0, "config": {"testDuration": 120000, "sampleTradesCount": 20, "riskThreshold": 0.8, "enforcementMode": "STRICT"}, "results": [{"testName": "风险限制规则配置测试", "passed": true, "message": "风险限制规则配置正常，默认规则数量: 5", "duration": 130, "timestamp": "2025-07-17T12:50:34.454Z", "details": {"defaultRulesCount": 5, "addSuccess": true, "updateSuccess": true, "deleteSuccess": true}}, {"testName": "交易前风险检查测试", "passed": true, "message": "交易前风险检查正常，测试用例: 3个", "duration": 305, "timestamp": "2025-07-17T12:50:34.759Z", "details": {"testCases": [{"testCase": "正常交易", "allowed": true, "expectedAllowed": true, "violationsCount": 0, "enforcementActions": []}, {"testCase": "超大仓位交易", "allowed": false, "expectedAllowed": false, "violationsCount": 1, "enforcementActions": ["BLOCK_NEW_ORDERS"]}, {"testCase": "高杠杆交易", "allowed": false, "expectedAllowed": false, "violationsCount": 1, "enforcementActions": ["BLOCK_NEW_ORDERS"]}]}}, {"testName": "风险违规检测测试", "passed": true, "message": "风险违规检测正常，测试场景: 3个", "duration": 155, "timestamp": "2025-07-17T12:50:34.914Z", "details": {"detectionResults": [{"type": "DAILY_LOSS", "detected": true, "expected": true, "severity": "HIGH", "action": "BLOCK_NEW_ORDERS"}, {"type": "POSITION_SIZE", "detected": true, "expected": true, "severity": "HIGH", "action": "BLOCK_NEW_ORDERS"}, {"type": "LEVERAGE", "detected": false, "expected": false}]}}, {"testName": "强制执行动作测试", "passed": true, "message": "强制执行动作正常，测试动作: 4个", "duration": 424, "timestamp": "2025-07-17T12:50:35.338Z", "details": {"actionResults": [{"action": "BLOCK_NEW_ORDERS", "success": true, "message": "强制执行动作 BLOCK_NEW_ORDERS 执行成功", "executionTime": 54}, {"action": "REDUCE_POSITION", "success": true, "message": "强制执行动作 REDUCE_POSITION 执行成功", "executionTime": 106}, {"action": "CLOSE_POSITION", "success": true, "message": "强制执行动作 CLOSE_POSITION 执行成功", "executionTime": 144}, {"action": "SEND_ALERT", "success": true, "message": "强制执行动作 SEND_ALERT 执行成功", "executionTime": 119}]}}, {"testName": "紧急停止机制测试", "passed": true, "message": "紧急停止机制正常工作", "duration": 176, "timestamp": "2025-07-17T12:50:35.514Z", "details": {"stopTriggered": true, "tradeBlocked": true, "stopCleared": true}}, {"testName": "风险限制修改测试", "passed": true, "message": "风险限制修改功能正常", "duration": 48, "timestamp": "2025-07-17T12:50:35.562Z", "details": {"orderSizeLimit": {"modifiedQuantity": 0.5, "originalQuantity": 1, "reason": "POSITION_SIZE_LIMIT"}, "approvalRequired": {"requiresApproval": true, "reason": "HIGH_VALUE_ORDER"}}}]}