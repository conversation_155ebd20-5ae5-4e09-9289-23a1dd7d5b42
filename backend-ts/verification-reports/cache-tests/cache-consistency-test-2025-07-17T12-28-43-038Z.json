{"timestamp": "2025-07-17T12:28:43.038Z", "totalTests": 6, "passedTests": 3, "failedTests": 3, "config": {"testDuration": 120000, "cacheOperations": 100, "consistencyCheckInterval": 5000, "expectedConsistencyRate": 0.95}, "results": [{"testName": "多层缓存基本功能测试", "passed": false, "message": "多层缓存基本功能测试失败: 缓存数据不一致", "duration": 57, "timestamp": "2025-07-17T12:28:39.953Z"}, {"testName": "缓存失效策略测试", "passed": true, "message": "缓存失效策略工作正常", "duration": 230, "timestamp": "2025-07-17T12:28:40.183Z", "details": {"ttlInvalidation": {"success": true, "message": "TTL失效正常"}, "manualInvalidation": {"success": true, "message": "手动失效正常"}, "patternInvalidation": {"success": true, "message": "模式失效正常"}}}, {"testName": "缓存同步机制测试", "passed": true, "message": "缓存同步机制正常，同步率: 80.0%", "duration": 958, "timestamp": "2025-07-17T12:28:41.141Z", "details": {"totalOperations": 5, "successfulSyncs": 4, "syncRate": 0.8, "syncResults": [{"synced": true, "layers": ["L1", "L2", "L3"]}, {"synced": false, "layers": ["L1", "L2", "L3"]}, {"synced": true, "layers": ["L1", "L2", "L3"]}, {"synced": true, "layers": ["L1", "L2", "L3"]}, {"synced": true, "layers": ["L1", "L2", "L3"]}]}}, {"testName": "缓存依赖管理测试", "passed": false, "message": "缓存依赖管理测试失败: 依赖失效机制未工作", "duration": 1082, "timestamp": "2025-07-17T12:28:42.223Z"}, {"testName": "缓存一致性检查测试", "passed": false, "message": "缓存一致性检查测试失败: 一致性率低于预期: 90.0% < 95.0%", "duration": 704, "timestamp": "2025-07-17T12:28:42.928Z"}, {"testName": "并发缓存操作测试", "passed": true, "message": "并发缓存操作正常，成功率: 100.0%", "duration": 110, "timestamp": "2025-07-17T12:28:43.038Z", "details": {"totalOperations": 20, "successful": 20, "failed": 0, "successRate": 1}}]}