{"timestamp": "2025-07-17T14:09:48.878Z", "totalTests": 6, "passedTests": 2, "failedTests": 4, "config": {"testDuration": 120000, "cacheOperations": 100, "consistencyCheckInterval": 5000, "expectedConsistencyRate": 0.95}, "results": [{"testName": "多层缓存基本功能测试", "passed": false, "message": "多层缓存基本功能测试失败: 缓存数据不一致", "duration": 69, "timestamp": "2025-07-17T14:09:45.863Z"}, {"testName": "缓存失效策略测试", "passed": true, "message": "缓存失效策略工作正常", "duration": 230, "timestamp": "2025-07-17T14:09:46.093Z", "details": {"ttlInvalidation": {"success": true, "message": "TTL失效正常"}, "manualInvalidation": {"success": true, "message": "手动失效正常"}, "patternInvalidation": {"success": true, "message": "模式失效正常"}}}, {"testName": "缓存同步机制测试", "passed": false, "message": "缓存同步机制测试失败: 同步率过低: 60.0%", "duration": 909, "timestamp": "2025-07-17T14:09:47.002Z"}, {"testName": "缓存依赖管理测试", "passed": false, "message": "缓存依赖管理测试失败: 依赖失效机制未工作", "duration": 1164, "timestamp": "2025-07-17T14:09:48.166Z"}, {"testName": "缓存一致性检查测试", "passed": false, "message": "缓存一致性检查测试失败: 一致性率低于预期: 90.0% < 95.0%", "duration": 617, "timestamp": "2025-07-17T14:09:48.784Z"}, {"testName": "并发缓存操作测试", "passed": true, "message": "并发缓存操作正常，成功率: 100.0%", "duration": 93, "timestamp": "2025-07-17T14:09:48.877Z", "details": {"totalOperations": 20, "successful": 20, "failed": 0, "successRate": 1}}]}