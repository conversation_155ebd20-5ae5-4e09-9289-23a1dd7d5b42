{"timestamp": "2025-07-17T12:03:20.191Z", "totalTests": 6, "passedTests": 4, "failedTests": 2, "config": {"testDuration": 120000, "cacheOperations": 100, "consistencyCheckInterval": 5000, "expectedConsistencyRate": 0.95}, "results": [{"testName": "多层缓存基本功能测试", "passed": false, "message": "多层缓存基本功能测试失败: 缓存数据不一致", "duration": 47, "timestamp": "2025-07-17T12:03:16.985Z"}, {"testName": "缓存失效策略测试", "passed": true, "message": "缓存失效策略工作正常", "duration": 229, "timestamp": "2025-07-17T12:03:17.214Z", "details": {"ttlInvalidation": {"success": true, "message": "TTL失效正常"}, "manualInvalidation": {"success": true, "message": "手动失效正常"}, "patternInvalidation": {"success": true, "message": "模式失效正常"}}}, {"testName": "缓存同步机制测试", "passed": true, "message": "缓存同步机制正常，同步率: 80.0%", "duration": 970, "timestamp": "2025-07-17T12:03:18.185Z", "details": {"totalOperations": 5, "successfulSyncs": 4, "syncRate": 0.8, "syncResults": [{"synced": true, "layers": ["L1", "L2", "L3"]}, {"synced": true, "layers": ["L1", "L2", "L3"]}, {"synced": true, "layers": ["L1", "L2", "L3"]}, {"synced": false, "layers": ["L1", "L2", "L3"]}, {"synced": true, "layers": ["L1", "L2", "L3"]}]}}, {"testName": "缓存依赖管理测试", "passed": false, "message": "缓存依赖管理测试失败: 依赖失效机制未工作", "duration": 1159, "timestamp": "2025-07-17T12:03:19.344Z"}, {"testName": "缓存一致性检查测试", "passed": true, "message": "缓存一致性检查正常，一致性率: 100.0%", "duration": 740, "timestamp": "2025-07-17T12:03:20.084Z", "details": {"totalChecks": 10, "consistentCount": 10, "consistencyRate": 1, "expectedRate": 0.95}}, {"testName": "并发缓存操作测试", "passed": true, "message": "并发缓存操作正常，成功率: 100.0%", "duration": 106, "timestamp": "2025-07-17T12:03:20.190Z", "details": {"totalOperations": 20, "successful": 20, "failed": 0, "successRate": 1}}]}