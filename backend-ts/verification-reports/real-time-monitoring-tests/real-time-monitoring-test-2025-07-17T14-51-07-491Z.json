{"timestamp": "2025-07-17T14:51:07.492Z", "totalTests": 7, "passedTests": 5, "failedTests": 2, "config": {"testDuration": 180000, "updateInterval": 1000, "alertThresholds": {"riskScore": 60, "volatility": 0.4, "drawdown": 0.12, "exposure": 0.7}, "simulationAccounts": 5}, "results": [{"testName": "监控器初始化和启动测试", "passed": true, "message": "监控器初始化和启动正常", "duration": 0, "timestamp": "2025-07-17T14:51:03.243Z", "details": {"startSuccess": true, "configUpdateSuccess": true, "isMonitoring": true, "updateInterval": 500}}, {"testName": "实时数据更新测试", "passed": true, "message": "实时数据更新正常，完成10次更新", "duration": 1023, "timestamp": "2025-07-17T14:51:04.266Z", "details": {"updateCount": 10, "historyLength": 10, "currentRiskScore": 69.**************, "updates": [{"updateIndex": 0, "riskScore": 92.**************, "updateSuccess": true, "timestamp": "2025-07-17T14:51:03.244Z"}, {"updateIndex": 1, "riskScore": 11.**************, "updateSuccess": true, "timestamp": "2025-07-17T14:51:03.346Z"}, {"updateIndex": 2, "riskScore": 65.**************, "updateSuccess": true, "timestamp": "2025-07-17T14:51:03.449Z"}]}}, {"testName": "风险警报生成测试", "passed": false, "message": "部分警报生成测试失败", "duration": 607, "timestamp": "2025-07-17T14:51:04.874Z", "details": {"alertResults": [{"scenario": "高风险评分警报", "expectedAlert": true, "actualAlert": true, "alertCount": 1, "passed": true}, {"scenario": "高波动率警报", "expectedAlert": true, "actualAlert": false, "alertCount": 0, "passed": false}, {"scenario": "正常风险无警报", "expectedAlert": false, "actualAlert": false, "alertCount": 0, "passed": true}]}}, {"testName": "趋势分析测试", "passed": true, "message": "趋势分析功能正常", "duration": 1297, "timestamp": "2025-07-17T14:51:06.171Z", "details": {"riskScoreTrend": {"direction": "INCREASING", "slope": 20, "confidence": 0.8}, "volatilityTrend": {"direction": "INCREASING", "slope": 0.10000000000000003, "confidence": 0.7}, "prediction": {"nextRiskScore": 98, "confidence": 0.6}, "dataPoints": 25}}, {"testName": "异常检测测试", "passed": true, "message": "异常检测功能正常", "duration": 680, "timestamp": "2025-07-17T14:51:06.852Z", "details": {"anomalyDetected": true, "totalAlerts": 2, "anomalyAlert": {"type": "ANOMALY_DETECTED", "severity": "HIGH", "message": "检测到风险异常: 95 (偏离均值 45.13)"}}}, {"testName": "性能和延迟测试", "passed": true, "message": "性能和延迟符合要求", "duration": 2, "timestamp": "2025-07-17T14:51:06.854Z", "details": {"updateCount": 100, "avgLatency": 0, "maxLatency": 0, "minLatency": 0, "p95Latency": 0, "performanceOk": true}}, {"testName": "多账户并发监控测试", "passed": false, "message": "多账户并发监控正常，监控5个账户", "duration": 636, "timestamp": "2025-07-17T14:51:07.491Z", "details": {"accountCount": 5, "activeStreams": 10, "totalDataPoints": 204, "accountStats": [{"accountId": "test-account-0", "hasCurrentData": true, "historyLength": 10, "lastRiskScore": 38.**************}, {"accountId": "test-account-1", "hasCurrentData": true, "historyLength": 10, "lastRiskScore": 53.***************}, {"accountId": "test-account-2", "hasCurrentData": true, "historyLength": 10, "lastRiskScore": 51.***************}]}}]}