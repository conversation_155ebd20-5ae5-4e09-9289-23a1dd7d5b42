{"timestamp": "2025-07-17T13:03:43.608Z", "totalTests": 7, "passedTests": 4, "failedTests": 3, "config": {"testDuration": 180000, "updateInterval": 1000, "alertThresholds": {"riskScore": 70, "volatility": 0.5, "drawdown": 0.15, "exposure": 0.8}, "simulationAccounts": 5}, "results": [{"testName": "监控器初始化和启动测试", "passed": true, "message": "监控器初始化和启动正常", "duration": 0, "timestamp": "2025-07-17T13:03:39.451Z", "details": {"startSuccess": true, "configUpdateSuccess": true, "isMonitoring": true, "updateInterval": 500}}, {"testName": "实时数据更新测试", "passed": true, "message": "实时数据更新正常，完成10次更新", "duration": 1013, "timestamp": "2025-07-17T13:03:40.464Z", "details": {"updateCount": 10, "historyLength": 10, "currentRiskScore": 41.***************, "updates": [{"updateIndex": 0, "riskScore": 37.**************, "updateSuccess": true, "timestamp": "2025-07-17T13:03:39.451Z"}, {"updateIndex": 1, "riskScore": 89.**************, "updateSuccess": true, "timestamp": "2025-07-17T13:03:39.552Z"}, {"updateIndex": 2, "riskScore": 52.**************, "updateSuccess": true, "timestamp": "2025-07-17T13:03:39.654Z"}]}}, {"testName": "风险警报生成测试", "passed": false, "message": "部分警报生成测试失败", "duration": 605, "timestamp": "2025-07-17T13:03:41.069Z", "details": {"alertResults": [{"scenario": "高风险评分警报", "expectedAlert": true, "actualAlert": true, "alertCount": 1, "passed": true}, {"scenario": "高波动率警报", "expectedAlert": true, "actualAlert": false, "alertCount": 0, "passed": false}, {"scenario": "正常风险无警报", "expectedAlert": false, "actualAlert": false, "alertCount": 0, "passed": true}]}}, {"testName": "趋势分析测试", "passed": true, "message": "趋势分析功能正常", "duration": 1280, "timestamp": "2025-07-17T13:03:42.349Z", "details": {"riskScoreTrend": {"direction": "INCREASING", "slope": 20, "confidence": 0.8}, "volatilityTrend": {"direction": "INCREASING", "slope": 0.10000000000000003, "confidence": 0.7}, "prediction": {"nextRiskScore": 98, "confidence": 0.6}, "dataPoints": 25}}, {"testName": "异常检测测试", "passed": false, "message": "未检测到预期的异常", "duration": 667, "timestamp": "2025-07-17T13:03:43.016Z", "details": {"anomalyDetected": false, "totalAlerts": 1, "anomalyAlert": null}}, {"testName": "性能和延迟测试", "passed": true, "message": "性能和延迟符合要求", "duration": 1, "timestamp": "2025-07-17T13:03:43.017Z", "details": {"updateCount": 100, "avgLatency": 0, "maxLatency": 0, "minLatency": 0, "p95Latency": 0, "performanceOk": true}}, {"testName": "多账户并发监控测试", "passed": false, "message": "多账户并发监控正常，监控5个账户", "duration": 589, "timestamp": "2025-07-17T13:03:43.607Z", "details": {"accountCount": 5, "activeStreams": 10, "totalDataPoints": 204, "accountStats": [{"accountId": "test-account-0", "hasCurrentData": true, "historyLength": 10, "lastRiskScore": 40.**************}, {"accountId": "test-account-1", "hasCurrentData": true, "historyLength": 10, "lastRiskScore": 59.**************}, {"accountId": "test-account-2", "hasCurrentData": true, "historyLength": 10, "lastRiskScore": 64.**************}]}}]}