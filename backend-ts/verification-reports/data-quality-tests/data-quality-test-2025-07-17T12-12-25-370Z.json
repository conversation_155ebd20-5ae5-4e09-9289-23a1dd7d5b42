{"timestamp": "2025-07-17T12:12:25.371Z", "totalTests": 6, "passedTests": 6, "failedTests": 0, "config": {"testDuration": 180000, "sampleDataCount": 50, "qualityThreshold": 0.8, "anomalyDetectionSensitivity": 0.8}, "results": [{"testName": "数据质量评分系统测试", "passed": true, "message": "质量评分系统正常，高质量数据评分: 100.0%", "duration": 153, "timestamp": "2025-07-17T12:12:23.279Z", "details": {"highQualityScore": 1, "mediumQualityScore": 0.8, "lowQualityScore": 0.49999999999999994}}, {"testName": "异常检测机制测试", "passed": true, "message": "异常检测机制正常，检测到 3 个异常", "duration": 62, "timestamp": "2025-07-17T12:12:23.341Z", "details": {"normalDataAnomalies": 0, "detectedAnomalies": 3, "detectionAccuracy": 1}}, {"testName": "数据完整性检查测试", "passed": true, "message": "数据完整性检查正常，完整数据评分: 100.0%", "duration": 42, "timestamp": "2025-07-17T12:12:23.383Z", "details": {"completeDataScore": 1, "incompleteDataScore": 0.6, "scoreDifference": 0.4}}, {"testName": "数据一致性验证测试", "passed": true, "message": "数据一致性验证正常，一致数据评分: 100.0%", "duration": 53, "timestamp": "2025-07-17T12:12:23.436Z", "details": {"consistentScore": 1, "inconsistentScore": 0.49999999999999994, "consistencyDifference": 0.5}}, {"testName": "实时质量监控测试", "passed": true, "message": "实时质量监控正常，平均处理时间: 82.1ms", "duration": 1832, "timestamp": "2025-07-17T12:12:25.268Z", "details": {"monitoredSamples": 10, "averageProcessingTime": 82.1, "averageQuality": 1, "qualityScores": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}, {"testName": "质量趋势分析测试", "passed": true, "message": "质量趋势分析正常，趋势方向: IMPROVING", "duration": 102, "timestamp": "2025-07-17T12:12:25.370Z", "details": {"trendDirection": "IMPROVING", "averageScore": 0.8594482176188015, "qualityEvents": 1, "scoreVariance": 0.008453715153995399}}]}