{"timestamp": "2025-07-17T12:28:22.389Z", "totalTests": 5, "passedTests": 3, "failedTests": 2, "config": {"testDuration": 300000, "failureSimulationTime": 30000, "expectedFailoverTime": 5000, "expectedRecoveryTime": 10000}, "results": [{"testName": "单一数据源故障测试", "passed": true, "message": "故障转移成功，从binance切换到okx，耗时58ms", "duration": 153, "timestamp": "2025-07-17T12:28:15.742Z", "details": {"normalSource": "binance", "failoverSource": "okx", "failoverTime": 58, "expectedFailoverTime": 5000}}, {"testName": "多数据源故障测试", "passed": true, "message": "多数据源故障时正确启用降级模式", "duration": 136, "timestamp": "2025-07-17T12:28:15.879Z", "details": {"failedSources": ["binance", "okx"], "degradedMode": true, "fallbackStrategy": "historical_data"}}, {"testName": "断路器机制测试", "passed": false, "message": "断路器机制测试失败: 断路器未在预期的失败次数后触发", "duration": 569, "timestamp": "2025-07-17T12:28:16.448Z"}, {"testName": "渐进式恢复测试", "passed": false, "message": "渐进式恢复测试失败: 渐进式恢复失败，数据源未能恢复", "duration": 3659, "timestamp": "2025-07-17T12:28:20.107Z"}, {"testName": "健康检查机制测试", "passed": true, "message": "健康检查机制正常，6/6个交易所健康", "duration": 2281, "timestamp": "2025-07-17T12:28:22.388Z", "details": {"totalExchanges": 6, "healthyExchanges": 6, "healthyPercentage": 100, "averageResponseTime": 379.38520765978234, "healthCheckResults": [{"exchange": "binance", "isHealthy": true, "responseTime": 517.1099419395923, "timestamp": "2025-07-17T12:28:20.626Z"}, {"exchange": "okx", "isHealthy": true, "responseTime": 508.5524850554343, "timestamp": "2025-07-17T12:28:21.135Z"}, {"exchange": "coinbase", "isHealthy": true, "responseTime": 377.4471168797299, "timestamp": "2025-07-17T12:28:21.513Z"}, {"exchange": "kraken", "isHealthy": true, "responseTime": 317.5234416296747, "timestamp": "2025-07-17T12:28:21.831Z"}, {"exchange": "huobi", "isHealthy": true, "responseTime": 187.16878361766624, "timestamp": "2025-07-17T12:28:22.019Z"}, {"exchange": "bybit", "isHealthy": true, "responseTime": 368.5094768365968, "timestamp": "2025-07-17T12:28:22.388Z"}]}}]}