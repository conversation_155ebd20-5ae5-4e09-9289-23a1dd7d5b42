{"timestamp": "2025-07-17T14:09:30.298Z", "totalTests": 5, "passedTests": 3, "failedTests": 2, "config": {"testDuration": 300000, "failureSimulationTime": 30000, "expectedFailoverTime": 5000, "expectedRecoveryTime": 10000}, "results": [{"testName": "单一数据源故障测试", "passed": true, "message": "故障转移成功，从binance切换到okx，耗时83ms", "duration": 179, "timestamp": "2025-07-17T14:09:22.199Z", "details": {"normalSource": "binance", "failoverSource": "okx", "failoverTime": 83, "expectedFailoverTime": 5000}}, {"testName": "多数据源故障测试", "passed": true, "message": "多数据源故障时正确启用降级模式", "duration": 115, "timestamp": "2025-07-17T14:09:22.315Z", "details": {"failedSources": ["binance", "okx"], "degradedMode": true, "fallbackStrategy": "historical_data"}}, {"testName": "断路器机制测试", "passed": false, "message": "断路器机制测试失败: 断路器未在预期的失败次数后触发", "duration": 417, "timestamp": "2025-07-17T14:09:22.732Z"}, {"testName": "渐进式恢复测试", "passed": false, "message": "渐进式恢复测试失败: 渐进式恢复失败，数据源未能恢复", "duration": 3656, "timestamp": "2025-07-17T14:09:26.388Z"}, {"testName": "健康检查机制测试", "passed": true, "message": "健康检查机制正常，5/6个交易所健康", "duration": 3909, "timestamp": "2025-07-17T14:09:30.297Z", "details": {"totalExchanges": 6, "healthyExchanges": 5, "healthyPercentage": 83.33333333333334, "averageResponseTime": 650.8006949004816, "healthCheckResults": [{"exchange": "binance", "isHealthy": true, "responseTime": 595.6727810727313, "timestamp": "2025-07-17T14:09:26.985Z"}, {"exchange": "okx", "isHealthy": true, "responseTime": 512.8201480824276, "timestamp": "2025-07-17T14:09:27.499Z"}, {"exchange": "coinbase", "isHealthy": true, "responseTime": 1074.5690031661115, "timestamp": "2025-07-17T14:09:28.574Z"}, {"exchange": "kraken", "isHealthy": false, "responseTime": 274.3315010557525, "timestamp": "2025-07-17T14:09:28.848Z"}, {"exchange": "huobi", "isHealthy": true, "responseTime": 959.6209118126114, "timestamp": "2025-07-17T14:09:29.808Z"}, {"exchange": "bybit", "isHealthy": true, "responseTime": 487.78982421325514, "timestamp": "2025-07-17T14:09:30.297Z"}]}}]}