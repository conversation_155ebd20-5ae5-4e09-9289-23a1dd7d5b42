{"timestamp": "2025-07-17T11:56:20.247Z", "totalTests": 5, "passedTests": 3, "failedTests": 2, "config": {"testDuration": 300000, "failureSimulationTime": 30000, "expectedFailoverTime": 5000, "expectedRecoveryTime": 10000}, "results": [{"testName": "单一数据源故障测试", "passed": true, "message": "故障转移成功，从binance切换到okx，耗时151ms", "duration": 380, "timestamp": "2025-07-17T11:56:12.641Z", "details": {"normalSource": "binance", "failoverSource": "okx", "failoverTime": 151, "expectedFailoverTime": 5000}}, {"testName": "多数据源故障测试", "passed": true, "message": "多数据源故障时正确启用降级模式", "duration": 244, "timestamp": "2025-07-17T11:56:12.885Z", "details": {"failedSources": ["binance", "okx"], "degradedMode": true, "fallbackStrategy": "historical_data"}}, {"testName": "断路器机制测试", "passed": false, "message": "断路器机制测试失败: 断路器未在预期的失败次数后触发", "duration": 621, "timestamp": "2025-07-17T11:56:13.506Z"}, {"testName": "渐进式恢复测试", "passed": false, "message": "渐进式恢复测试失败: 渐进式恢复失败，数据源未能恢复", "duration": 3781, "timestamp": "2025-07-17T11:56:17.287Z"}, {"testName": "健康检查机制测试", "passed": true, "message": "健康检查机制正常，6/6个交易所健康", "duration": 2960, "timestamp": "2025-07-17T11:56:20.247Z", "details": {"totalExchanges": 6, "healthyExchanges": 6, "healthyPercentage": 100, "averageResponseTime": 492.69653671125417, "healthCheckResults": [{"exchange": "binance", "isHealthy": true, "responseTime": 494.3299535348308, "timestamp": "2025-07-17T11:56:17.783Z"}, {"exchange": "okx", "isHealthy": true, "responseTime": 312.45210496981736, "timestamp": "2025-07-17T11:56:18.095Z"}, {"exchange": "coinbase", "isHealthy": true, "responseTime": 1001.9173874635875, "timestamp": "2025-07-17T11:56:19.098Z"}, {"exchange": "kraken", "isHealthy": true, "responseTime": 230.0923237519076, "timestamp": "2025-07-17T11:56:19.328Z"}, {"exchange": "huobi", "isHealthy": true, "responseTime": 124.38757243612687, "timestamp": "2025-07-17T11:56:19.453Z"}, {"exchange": "bybit", "isHealthy": true, "responseTime": 792.9998781112552, "timestamp": "2025-07-17T11:56:20.246Z"}]}}]}