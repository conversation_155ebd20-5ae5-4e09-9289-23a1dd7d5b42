{"timestamp": "2025-07-17T14:47:56.618Z", "totalTests": 5, "passedTests": 3, "failedTests": 2, "config": {"testDuration": 300000, "failureSimulationTime": 30000, "expectedFailoverTime": 5000, "expectedRecoveryTime": 10000}, "results": [{"testName": "单一数据源故障测试", "passed": false, "message": "单一数据源故障测试失败: 故障转移失败，无法获取数据", "duration": 187, "timestamp": "2025-07-17T14:47:51.380Z"}, {"testName": "多数据源故障测试", "passed": true, "message": "多数据源故障时正确启用降级模式", "duration": 112, "timestamp": "2025-07-17T14:47:51.492Z", "details": {"failedSources": ["binance", "okx"], "degradedMode": true, "fallbackStrategy": "historical_data"}}, {"testName": "断路器机制测试", "passed": false, "message": "断路器机制测试失败: 断路器开启期间仍然允许请求通过", "duration": 454, "timestamp": "2025-07-17T14:47:51.947Z"}, {"testName": "渐进式恢复测试", "passed": true, "message": "渐进式恢复成功，经过2次尝试", "duration": 1494, "timestamp": "2025-07-17T14:47:53.441Z", "details": {"recoveryAttempts": 2, "recovered": true}}, {"testName": "健康检查机制测试", "passed": true, "message": "健康检查机制正常，5/6个交易所健康", "duration": 3176, "timestamp": "2025-07-17T14:47:56.617Z", "details": {"totalExchanges": 6, "healthyExchanges": 5, "healthyPercentage": 83.33333333333334, "averageResponseTime": 528.3079897369078, "healthCheckResults": [{"exchange": "binance", "isHealthy": true, "responseTime": 398.2749410025098, "timestamp": "2025-07-17T14:47:53.841Z"}, {"exchange": "okx", "isHealthy": true, "responseTime": 711.3903441282603, "timestamp": "2025-07-17T14:47:54.553Z"}, {"exchange": "coinbase", "isHealthy": false, "responseTime": 426.1999427977107, "timestamp": "2025-07-17T14:47:54.981Z"}, {"exchange": "kraken", "isHealthy": true, "responseTime": 1005.8173554591833, "timestamp": "2025-07-17T14:47:55.987Z"}, {"exchange": "huobi", "isHealthy": true, "responseTime": 229.72407916620176, "timestamp": "2025-07-17T14:47:56.217Z"}, {"exchange": "bybit", "isHealthy": true, "responseTime": 398.4412758675807, "timestamp": "2025-07-17T14:47:56.617Z"}]}}]}