{"timestamp": "2025-07-20T14:40:44.425Z", "totalTests": 7, "passedTests": 6, "failedTests": 1, "config": {"testDuration": 180000, "sampleOperationsCount": 25, "monitoringInterval": 2000, "performanceThreshold": 1000}, "results": [{"testName": "监控引擎初始化测试", "passed": true, "message": "监控引擎初始化正常", "duration": 0, "timestamp": "2025-07-20T14:40:41.131Z", "details": {"startSuccess": true, "configUpdateSuccess": true, "isRunning": true, "activeOperationTypes": 0}}, {"testName": "执行指标记录测试", "passed": true, "message": "执行指标记录正常，记录25个指标", "duration": 1278, "timestamp": "2025-07-20T14:40:42.409Z", "details": {"recordedMetricsCount": 25, "expectedCount": 25, "performanceStatsCount": 5, "operationHistoryCount": 5, "allMetricsRecorded": true, "statsGenerated": true, "historyAvailable": true}}, {"testName": "性能统计计算测试", "passed": true, "message": "性能统计计算正常，生成5个统计项", "duration": 1, "timestamp": "2025-07-20T14:40:42.410Z", "details": {"totalStatsCount": 5, "validStatsCount": 5, "statsCalculationCorrect": true, "hasOrderExecutionStats": true, "detailedStatsCorrect": true, "orderExecutionStats": {"totalOperations": 5, "successRate": 1, "averageDuration": 567.2404365246691, "errorRate": 0}}}, {"testName": "异常检测功能测试", "passed": true, "message": "异常检测功能正常，检测到4个异常", "duration": 2, "timestamp": "2025-07-20T14:40:42.412Z", "details": {"totalAnomalies": 4, "criticalAnomalies": 2, "highAnomalies": 2, "hasAnomalies": true, "hasCriticalAnomalies": true, "hasPerformanceAnomalies": true, "hasErrorRateAnomalies": true, "anomalyStructureValid": true, "anomalyTypes": ["PERFORMANCE_DEGRADATION", "HIGH_ERROR_RATE"]}}, {"testName": "实时告警系统测试", "passed": true, "message": "实时告警系统正常，触发4个告警", "duration": 1508, "timestamp": "2025-07-20T14:40:43.920Z", "details": {"alertsTriggered": 4, "alertTypes": ["HIGH_LATENCY", "CONSECUTIVE_FAILURES"], "alertSystemWorking": true, "hasHighLatencyAlerts": true, "hasConsecutiveFailureAlerts": true}}, {"testName": "监控装饰器功能测试", "passed": false, "message": "监控装饰器功能正常，记录0个装饰器指标", "duration": 503, "timestamp": "2025-07-20T14:40:44.423Z", "details": {"testMethodsCount": 3, "executionResultsCount": 3, "decoratorMetricsCount": 0, "successfulExecutions": 3, "decoratorWorking": false, "allMethodsExecuted": true, "executionResults": [{"method": "simulatedOrderCreation", "success": true, "result": {"orderId": "test_order_123", "success": true}}, {"method": "simulatedPositionOpen", "success": true, "result": {"positionId": "test_position_456", "success": true}}, {"method": "simulatedRiskCheck", "success": true, "result": {"riskLevel": "LOW", "approved": true}}]}}, {"testName": "监控仪表板数据测试", "passed": true, "message": "监控仪表板数据生成正常", "duration": 1, "timestamp": "2025-07-20T14:40:44.424Z", "details": {"hasOverview": true, "hasPerformanceStats": true, "hasRecentAnomalies": true, "hasOperationTrends": true, "overviewValid": true, "dashboardDataValid": true, "overview": {"totalOperations": 49, "successRate": 0.6938775510204082, "averageLatency": 1360, "activeAnomalies": 4, "systemHealth": "CRITICAL"}, "performanceStatsCount": 5, "recentAnomaliesCount": 4, "operationTrendsCount": 12}}]}