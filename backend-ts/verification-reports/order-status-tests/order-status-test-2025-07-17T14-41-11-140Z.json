{"timestamp": "2025-07-17T14:41:11.141Z", "totalTests": 7, "passedTests": 4, "failedTests": 3, "config": {"testDuration": 120000, "sampleOrdersCount": 15, "syncInterval": 5000, "maxRetries": 3}, "results": [{"testName": "订单生命周期管理器初始化测试", "passed": true, "message": "订单生命周期管理器初始化正常", "duration": 1, "timestamp": "2025-07-17T14:41:08.611Z", "details": {"startSuccess": true, "configUpdateSuccess": true, "isRunning": true, "totalOrders": 0}}, {"testName": "订单创建和状态更新测试", "passed": true, "message": "订单创建和状态更新正常，创建5个订单，执行20次状态更新", "duration": 2022, "timestamp": "2025-07-17T14:41:10.633Z", "details": {"createdOrdersCount": 5, "updateSuccessCount": 20, "expectedUpdates": 20, "allOrdersFilled": true, "finalOrderStatuses": ["FILLED", "FILLED", "FILLED", "FILLED", "FILLED"]}}, {"testName": "订单状态转换验证测试", "passed": false, "message": "部分状态转换验证失败", "duration": 0, "timestamp": "2025-07-17T14:41:10.634Z", "details": {"validationResults": [{"transition": "CREATED -> PENDING", "expected": true, "actual": true, "passed": true}, {"transition": "PENDING -> SUBMITTED", "expected": true, "actual": true, "passed": true}, {"transition": "SUBMITTED -> FILLED", "expected": true, "actual": true, "passed": true}, {"transition": "FILLED -> PENDING", "expected": false, "actual": true, "passed": false}, {"transition": "CANCELLED -> SUBMITTED", "expected": false, "actual": true, "passed": false}, {"transition": "REJECTED -> FILLED", "expected": false, "actual": true, "passed": false}]}}, {"testName": "批量订单状态更新测试", "passed": false, "message": "批量订单状态更新正常，成功更新5个订单", "duration": 2, "timestamp": "2025-07-17T14:41:10.636Z", "details": {"batchSize": 10, "successCount": 5, "failedCount": 5, "filledCount": 0, "cancelledCount": 5, "errors": ["无效的状态转换", "无效的状态转换", "无效的状态转换", "无效的状态转换", "无效的状态转换"]}}, {"testName": "订单状态同步服务测试", "passed": true, "message": "订单状态同步服务正常，同步5个订单", "duration": 1, "timestamp": "2025-07-17T14:41:10.637Z", "details": {"syncResult": {"success": true, "syncedOrders": 5, "failedOrders": 0, "errors": [], "timestamp": "2025-07-17T14:41:10.637Z", "duration": 500}, "activeOrdersCount": 5, "syncedCount": 5, "syncDuration": 500}}, {"testName": "订单差异检测和对账测试", "passed": true, "message": "订单差异检测和对账正常", "duration": 502, "timestamp": "2025-07-17T14:41:11.139Z", "details": {"syncResult": true, "reconciledOrder": {"status": "FILLED", "filledQuantity": 50, "averagePrice": 25.5}, "wasReconciled": true}}, {"testName": "订单统计和性能指标测试", "passed": false, "message": "订单统计和性能指标正常，总订单15个", "duration": 1, "timestamp": "2025-07-17T14:41:11.140Z", "details": {"statistics": {"total": 15, "byStatus": {"CREATED": 8, "CANCELLED": 3, "FAILED": 2, "PENDING": 2}, "byType": {"LIMIT": 15}, "totalVolume": 0, "totalCommission": 0}, "metrics": {"averageExecutionTime": 1500, "fillRate": 0, "cancellationRate": 0.2, "failureRate": 0.13333333333333333, "totalOrders": 15}, "expectedTotal": 15, "statsCorrect": false, "metricsCorrect": false}}]}