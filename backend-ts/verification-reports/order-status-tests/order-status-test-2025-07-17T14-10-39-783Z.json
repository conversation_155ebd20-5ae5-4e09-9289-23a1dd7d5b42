{"timestamp": "2025-07-17T14:10:39.784Z", "totalTests": 7, "passedTests": 2, "failedTests": 5, "config": {"testDuration": 120000, "sampleOrdersCount": 15, "syncInterval": 5000, "maxRetries": 3}, "results": [{"testName": "订单生命周期管理器初始化测试", "passed": true, "message": "订单生命周期管理器初始化正常", "duration": 1, "timestamp": "2025-07-17T14:10:37.253Z", "details": {"startSuccess": true, "configUpdateSuccess": true, "isRunning": true, "totalOrders": 0}}, {"testName": "订单创建和状态更新测试", "passed": true, "message": "订单创建和状态更新正常，创建5个订单，执行20次状态更新", "duration": 2025, "timestamp": "2025-07-17T14:10:39.278Z", "details": {"createdOrdersCount": 5, "updateSuccessCount": 20, "expectedUpdates": 20, "allOrdersFilled": true, "finalOrderStatuses": ["FILLED", "FILLED", "FILLED", "FILLED", "FILLED"]}}, {"testName": "订单状态转换验证测试", "passed": false, "message": "部分状态转换验证失败", "duration": 0, "timestamp": "2025-07-17T14:10:39.278Z", "details": {"validationResults": [{"transition": "CREATED -> PENDING", "expected": true, "actual": true, "passed": true}, {"transition": "PENDING -> SUBMITTED", "expected": true, "actual": true, "passed": true}, {"transition": "SUBMITTED -> FILLED", "expected": true, "actual": true, "passed": true}, {"transition": "FILLED -> PENDING", "expected": false, "actual": true, "passed": false}, {"transition": "CANCELLED -> SUBMITTED", "expected": false, "actual": true, "passed": false}, {"transition": "REJECTED -> FILLED", "expected": false, "actual": true, "passed": false}]}}, {"testName": "批量订单状态更新测试", "passed": false, "message": "批量订单状态更新正常，成功更新0个订单", "duration": 0, "timestamp": "2025-07-17T14:10:39.279Z", "details": {"batchSize": 10, "successCount": 0, "failedCount": 10, "filledCount": 0, "cancelledCount": 0, "errors": ["无效的状态转换", "无效的状态转换", "无效的状态转换", "无效的状态转换", "无效的状态转换", "无效的状态转换", "无效的状态转换", "无效的状态转换", "无效的状态转换", "无效的状态转换"]}}, {"testName": "订单状态同步服务测试", "passed": false, "message": "订单状态同步服务测试失败: Cannot read properties of undefined (reading 'orders')", "duration": 0, "timestamp": "2025-07-17T14:10:39.279Z"}, {"testName": "订单差异检测和对账测试", "passed": false, "message": "订单差异检测或对账失败", "duration": 502, "timestamp": "2025-07-17T14:10:39.781Z", "details": {"syncResult": false, "reconciledOrder": {"status": "SUBMITTED", "filledQuantity": 0}, "wasReconciled": false}}, {"testName": "订单统计和性能指标测试", "passed": false, "message": "订单统计和性能指标正常，总订单15个", "duration": 1, "timestamp": "2025-07-17T14:10:39.783Z", "details": {"statistics": {"total": 15, "byStatus": {"CREATED": 11, "FAILED": 2, "PENDING": 2}, "byType": {"LIMIT": 15}, "totalVolume": 0, "totalCommission": 0}, "metrics": {"averageExecutionTime": 1500, "fillRate": 0, "cancellationRate": 0, "failureRate": 0.13333333333333333, "totalOrders": 15}, "expectedTotal": 15, "statsCorrect": false, "metricsCorrect": false}}]}