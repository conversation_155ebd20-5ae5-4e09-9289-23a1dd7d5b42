{"timestamp": "2025-07-18T05:55:43.471Z", "summary": {"total": 161, "critical": 0, "high": 159, "medium": 2, "low": 0}, "issues": [{"file": "src/contexts/trading-signals/infrastructure/strategies/mean-reversion-strategy.ts", "line": 579, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟实现", "codeSnippet": "* 🔥 零容忍虚假数据 - 完全移除require('crypto').randomBytes(4).readUInt32BE(0) / 0xFFFFFFFF模拟实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/application/services/ai-reasoning-application-service.ts", "line": 538, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟实现", "codeSnippet": "// 执行推理链 - 使用模拟实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/application/services/ai-reasoning-application-service.ts", "line": 560, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟实现", "codeSnippet": "// 执行AI投资决策 - 使用模拟实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/di/modules/ai-reasoning-container-module.ts", "line": 153, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟实现", "codeSnippet": "// 绑定推理审计服务（暂时使用模拟实现）", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/di/modules/ai-reasoning-container-module.ts", "line": 208, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟实现", "codeSnippet": "// 模拟实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/market-data/infrastructure/external/database-query-index-optimizer.ts", "line": 1095, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 验证表和列的存在性（简化实现）", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/market-data/infrastructure/external/database-query-index-optimizer.ts", "line": 1986, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 检查列是否存在（简化实现）", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "line": 176, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "* 执行单个资产的风险评估 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "line": 1207, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，使用第一个持仓进行评估", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "line": 1270, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，仅记录日志", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "line": 1282, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回空数组", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "line": 1291, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回空数组", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "line": 610, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现的方法 - 返回基本结构", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "line": 619, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回基本统计信息", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "line": 637, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 添加剩余的接口方法 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "line": 678, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回空数组", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "line": 698, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回空数组", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "line": 718, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回空数组", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "line": 752, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回空数组", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "line": 771, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回空数组", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "line": 784, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回空数组", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/infrastructure/repositories/UnifiedRiskAssessmentRepository.ts", "line": 808, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回基本结构", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/user-management/infrastructure/services/AuditService.ts", "line": 1513, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/modules/quantitative/quantitative-analysis-module.ts", "line": 299, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 各种机器学习模型预测（简化实现）", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/modules/ai-enhancement/pattern-ai-enhancer.ts", "line": 142, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// AI模型预测 (简化实现，基于特征权重)", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/modules/ai-enhancement/pattern-ai-enhancer.ts", "line": 307, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 辅助方法 (简化实现)", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-prediction.ts", "line": 374, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 计算价格目标准确率（简化实现）", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-prediction.ts", "line": 971, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 动态计算价格目标 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-prediction.ts", "line": 978, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 智能持续时间预测 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-prediction.ts", "line": 984, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 动态概率分布 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts", "line": 244, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "* 验证形态完整性 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts", "line": 279, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "* 预测形态发展 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts", "line": 365, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "* 启动实时形态监控 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts", "line": 391, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "* 获取形态警报 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts", "line": 396, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，返回空数组", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts", "line": 413, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现：基于形态类型和置信度比较", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts", "line": 457, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "* 生成形态报告 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/pattern-recognition.ts", "line": 507, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/di/modules/ai-reasoning-container-module.ts", "line": 57, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 推理链 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/di/modules/ai-reasoning-container-module.ts", "line": 70, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 统一决策引擎 - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/di/modules/ai-reasoning-container-module.ts", "line": 114, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 降级到简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/di/modules/market-data-container-module.ts", "line": 172, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 先绑定必要的依赖服务（简化实现）", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/di/modules/market-data-container-module.ts", "line": 176, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// MultiExchangeDataService - 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/di/base/di-configuration-optimizer.ts", "line": 301, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 注意：这是一个简化实现，实际需要访问Inversify内部API", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/unified-analysis-service-manager.ts", "line": 338, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现 - 实际应该从市场数据服务获取", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/unified-analysis-service-manager.ts", "line": 398, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现 - 实际应该调用真实的趋势分析服务", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/unified-analysis-service-manager.ts", "line": 420, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现 - 实际应该调用真实的技术分析服务", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/unified-analysis-service-manager.ts", "line": 471, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现 - 调用基础的形态识别方法", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/unified-analysis-service-manager.ts", "line": 511, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现 - 实际应该调用真实的多时间框架分析服务", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/unified-analysis-service-manager.ts", "line": 541, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现 - 实际应该调用真实的信号生成服务", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/services/MultiTimeframeService.ts", "line": 372, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，实际可以根据成交量等因素加权", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/services/MultiTimeframeService.ts", "line": 417, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现：基于动量的一致性", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/services/MultiTimeframeService.ts", "line": 649, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 其他接口方法的简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/services/MultiTimeframeService.ts", "line": 1148, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，实际应该检查各时间框架的数据同步状态", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/services/PatternRecognitionService.ts", "line": 355, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 计算成功率（简化实现，基于置信度）", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/services/PatternRecognitionService.ts", "line": 942, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "* 其他形态检测方法的简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/cache/cache-consistency-manager.ts", "line": 441, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现：假设pattern是前缀匹配", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/data-processing/pipeline-stage-coordinator.ts", "line": 390, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现：按order分组", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/data-processing/pipeline-stage-coordinator.ts", "line": 414, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现：返回所有必需阶段", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/data-processing/stage-executor-factory.ts", "line": 40, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 直接创建执行器实例（简化实现）", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/data-processing/executors/processing-stage-executor.ts", "line": 393, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，实际应该根据更多因素判断", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/data-processing/executors/processing-stage-executor.ts", "line": 454, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，实际应该根据具体业务逻辑计算", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/data-processing/executors/processing-stage-executor.ts", "line": 462, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，实际应该检查数据的内部一致性", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/data-processing/executors/validation-stage-executor.ts", "line": 436, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，实际应该支持更复杂的规则引擎", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/data-processing/executors/mapping-stage-executor.ts", "line": 559, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，实际应该使用真正的压缩算法", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/data-processing/executors/mapping-stage-executor.ts", "line": 567, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，实际应该使用哈希算法", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/data-processing/executors/timestamp-stage-executor.ts", "line": 312, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现，假设输入已经是UTC", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/monitoring/performance/performance-dashboard.ts", "line": 393, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现：基于成功率估算运行时间", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/event-broadcaster/monitoring/statistics-collector.ts", "line": 332, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "const peak = Math.max(current, 0); // 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/event-broadcaster/monitoring/statistics-collector.ts", "line": 333, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "const average = current; // 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/event-broadcaster/monitoring/statistics-collector.ts", "line": 353, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "const recentErrors = total; // 简化实现，实际需要时间窗口", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/event-broadcaster/monitoring/statistics-collector.ts", "line": 402, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/event-broadcaster/monitoring/statistics-collector.ts", "line": 410, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 简化实现", "codeSnippet": "// 简化实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/market-data/infrastructure/repositories/prisma-historical-data-repository.ts", "line": 259, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 其他接口方法的占位符实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/learning/infrastructure/optimizers/parameter-optimizer.ts", "line": 547, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 其他辅助方法的占位符实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/learning/infrastructure/engines/experience-engine.ts", "line": 423, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 其他辅助方法的占位符实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine.ts", "line": 65, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 暂时注释掉不存在的接口，使用占位符实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine.ts", "line": 108, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 占位符实现 - 获取时间框架参数", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine.ts", "line": 117, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 占位符实现 - 获取知识洞察", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine.ts", "line": 182, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 占位符实现 - 获取时间框架参数", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine.ts", "line": 191, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 占位符实现 - 获取跨时间框架洞察", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine.ts", "line": 256, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 占位符实现 - 获取时间框架参数", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine.ts", "line": 318, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 占位符实现 - 验证跨时间框架一致性", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/unified-prediction-engine.ts", "line": 406, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 占位符实现 - 获取时间框架参数", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts", "line": 604, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "* 加载时间框架特定参数 - 占位符实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts", "line": 610, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 占位符实现 - 基于时间框架返回默认参数", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts", "line": 658, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 占位符实现 - 生成基于规则的洞察", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-collaboration-service.ts", "line": 890, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 占位符实现", "codeSnippet": "// 其他方法的占位符实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts", "line": 286, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟AI", "codeSnippet": "// 模拟AI确认过程", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts", "line": 297, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟AI", "codeSnippet": "// 模拟AI预测", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trading-signals/infrastructure/strategies/mean-reversion-strategy.ts", "line": 641, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 🔥 零容忍虚假数据：失败时抛出错误，不返回模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trading-signals/infrastructure/strategies/mean-reversion-strategy.ts", "line": 723, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 🔥 零容忍虚假数据：失败时抛出错误，不返回模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trading-signals/infrastructure/strategies/mean-reversion-strategy.ts", "line": 738, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 🔥 零容忍虚假数据：失败时抛出错误，不返回模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/market-data/infrastructure/external/binance-futures-adapter.ts", "line": 145, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 抛出真实错误，不提供模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/market-data/infrastructure/external/api-fallback-manager.ts", "line": 289, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// TokenMetrics需要API密钥，这里返回模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/market-data/infrastructure/external/predictive-data-preloader.ts", "line": 914, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 模拟数据加载（实际实现中会调用数据源）", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/market-data/infrastructure/external/predictive-data-preloader.ts", "line": 969, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "* 模拟数据加载", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "line": 1480, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 严格要求使用真实历史数据，绝不允许模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trading-execution/domain/services/simulation-engine.ts", "line": 3, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "* 基于真实市场数据和算法的执行引擎，零容忍随机数和模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trading-execution/domain/services/webhook-alert-service.ts", "line": 340, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 由于我们还没有创建发送历史表，先返回模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/user-management/infrastructure/services/ThreatDetectionService.ts", "line": 575, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 返回基础信息而非固定模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/di/base/di-performance-monitor.ts", "line": 118, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 目前使用模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/risk/risk-enforcement-engine.ts", "line": 468, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 暂时返回模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/risk/risk-enforcement-engine.ts", "line": 477, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 暂时返回模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/risk/risk-enforcement-engine.ts", "line": 488, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 暂时返回模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/risk/risk-enforcement-engine.ts", "line": 497, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 暂时返回模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/validation/real-data-validator.ts", "line": 3, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "* 确保系统只接受真实数据，杜绝任何模拟数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/validation/real-data-validator.ts", "line": 30, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 反模拟数据检测", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 107, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟数据", "codeSnippet": "// 模拟数据处理延迟", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trading-execution/infrastructure/di/container.ts", "line": 106, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 模拟执行", "codeSnippet": "// 注册模拟执行引擎", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/user-config/application/services/UserConfigService.ts", "line": 533, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "* 🔥 修复虚假实现：从数据库获取真实的用户配置统计数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/user-config/application/services/UserConfigService.ts", "line": 582, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "* 🔥 修复虚假实现：从数据库获取真实的提供者相关配置数据", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-prediction.ts", "line": 753, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 拒绝虚假默认分析 - 这是金融交易中极度危险的虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/ai-pattern-analyzer.ts", "line": 308, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 拒绝虚假AI风险评估 - 这是金融交易中极度危险的虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-analysis-engine.ts", "line": 406, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 拒绝虚假默认价格 - 这是金融交易中极度危险的虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/express-app.ts", "line": 70, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 修复虚假实现：使用加密安全的请求ID生成", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/analysis/services/MultiTimeframeService.ts", "line": 719, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 修复虚假实现：使用真实的技术分析算法计算价格区间", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/messaging/redis.ts", "line": 98, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "* 系统现在要求真实的Redis连接，不接受虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/controllers/production-signal-controller.ts", "line": 88, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 使用真实的AI信号生成服务 - 零容忍虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/controllers/production-signal-controller.ts", "line": 458, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 使用真实的批量信号生成 - 零容忍虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/controllers/admin-controller.ts", "line": 90, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 使用真实的系统指标替换虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/controllers/admin-controller.ts", "line": 369, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 执行真实的维护操作替换Math.random虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/controllers/admin-controller.ts", "line": 443, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 使用真实的系统指标替换Math.random虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/controllers/auth-controller.ts", "line": 76, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 使用真实的认证服务替换Math.random虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/routes/ai-reasoning-router.ts", "line": 457, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 修复虚假实现：使用真实的AI推理服务获取市场上下文", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/routes/ai-reasoning-router.ts", "line": 480, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 修复虚假实现：使用真实的AI推理服务获取短期预测", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/routes/ai-reasoning-router.ts", "line": 503, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 修复虚假实现：使用真实的AI推理服务获取长期预测", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/routes/ai-reasoning-router.ts", "line": 526, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "// 🔥 修复虚假实现：使用真实的AI推理服务获取预测统计", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/websocket-event-server.ts", "line": 367, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "* 🔥 生成客户端ID - 使用UUID替代虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/websocket-event-server.ts", "line": 374, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 虚假实现", "codeSnippet": "* 🔥 生成消息ID - 使用UUID替代虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 108, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: simulate", "codeSnippet": "await this.simulateProcessing(50, 200);", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 143, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: simulate", "codeSnippet": "await this.simulateProcessing(30, 100);", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 176, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: simulate", "codeSnippet": "await this.simulateProcessing(100, 300);", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 209, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: simulate", "codeSnippet": "await this.simulateProcessing(200, 500);", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 294, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: simulate", "codeSnippet": "await this.simulateProcessing(10, 50);", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 307, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: simulate", "codeSnippet": "await this.simulateProcessing(20, 100);", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 325, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: simulate", "codeSnippet": "await this.simulateProcessing(20, 100);", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 357, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: simulate", "codeSnippet": "await this.simulateProcessing(5, 20);", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 376, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: simulate", "codeSnippet": "await this.simulateProcessing(10, 50);", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/application/real-time-sync/processors/data-sync-processor.ts", "line": 393, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: simulate", "codeSnippet": "private async simulateProcessing(minMs", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/llm-providers/openai-provider.ts", "line": 140, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: placeholder", "codeSnippet": "if (!API_KEY || API_KEY === 'placeholder-key') {", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/llm-providers/anthropic-provider.ts", "line": 90, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: placeholder", "codeSnippet": "if (!API_KEY || API_KEY === 'placeholder-key') {", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/llm-providers/gemini-provider.ts", "line": 101, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: placeholder", "codeSnippet": "if (!this.apiKey || this.apiKey === 'placeholder-key') {", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/effectiveness-validator.ts", "line": 388, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: placeholder", "codeSnippet": "beforeMetrics", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/ai-reasoning/infrastructure/services/effectiveness-validator.ts", "line": 389, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: placeholder", "codeSnippet": "afterMetrics", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "line": 194, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: Math.random()", "codeSnippet": "requestId", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/user-management/infrastructure/services/PasswordService.ts", "line": 151, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: Math.random()", "codeSnippet": "// Math.random()在密码生成中极度危险，必须使用crypto.getRandomValues()", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/shared/infrastructure/database/query-manager.ts", "line": 442, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: Math.random()", "codeSnippet": "// 🔥 使用加密安全的随机数生成器替换Math.random()", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trading-signals/application/services/production-signal-service.ts", "line": 420, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 默认价格", "codeSnippet": "// 默认价格信息", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "line": 1394, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 默认价格", "codeSnippet": "let currentPrice = 50000; // 更合理的默认价格（BTC价格范围）", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/risk-management/application/services/risk-assessment-application-service.ts", "line": 1468, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 默认价格", "codeSnippet": "currentPrice", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-prediction.ts", "line": 691, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 默认价格", "codeSnippet": "this.logger.error('无法获取当前价格，拒绝使用虚假默认价格', { error });", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-prediction.ts", "line": 1711, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 默认价格", "codeSnippet": "// 如果没有数据，返回默认价格", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-analysis-engine.ts", "line": 406, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 默认价格", "codeSnippet": "// 🔥 拒绝虚假默认价格 - 这是金融交易中极度危险的虚假实现", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/api/controllers/risk-assessment-controller.ts", "line": 126, "type": "HIGH", "category": "🚨 虚假实现标记", "description": "发现虚假实现关键词: 默认价格", "codeSnippet": "return 50000; // BTC默认价格", "recommendation": "立即实现真实逻辑，移除虚假标记"}, {"file": "src/contexts/trend-analysis/application/trend-analysis-application.service.ts", "line": 1660, "type": "HIGH", "category": "🔒 固定返回值", "description": "方法总是返回固定值", "codeSnippet": "return 50000; // BTC基础成交量", "recommendation": "实现动态的业务逻辑"}, {"file": "src/contexts/trend-analysis/infrastructure/services/trend-prediction.ts", "line": 1712, "type": "HIGH", "category": "🔒 固定返回值", "description": "方法总是返回固定值", "codeSnippet": "return 50000;", "recommendation": "实现动态的业务逻辑"}, {"file": "src/api/controllers/risk-assessment-controller.ts", "line": 126, "type": "HIGH", "category": "🔒 固定返回值", "description": "方法总是返回固定值", "codeSnippet": "return 50000; // BTC默认价格", "recommendation": "实现动态的业务逻辑"}, {"file": "src/api/controllers/risk-assessment-controller.ts", "line": 129, "type": "HIGH", "category": "🔒 固定返回值", "description": "方法总是返回固定值", "codeSnippet": "return 50000;", "recommendation": "实现动态的业务逻辑"}, {"file": "src/contexts/market-data/infrastructure/external/high-performance-data-aggregator.ts", "line": 520, "type": "MEDIUM", "category": "🧪 测试数据泄露", "description": "发现测试数据标识: sampleData", "codeSnippet": "const sampleData = window.data[0];", "recommendation": "移除测试数据，使用真实数据源"}, {"file": "src/contexts/market-data/infrastructure/external/high-performance-data-aggregator.ts", "line": 522, "type": "MEDIUM", "category": "🧪 测试数据泄露", "description": "发现测试数据标识: sampleData", "codeSnippet": "dimensions[dimension] = this.extractDimensionValue(sampleData, dimension);", "recommendation": "移除测试数据，使用真实数据源"}]}