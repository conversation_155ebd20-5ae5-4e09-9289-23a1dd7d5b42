# Gemini 工作指南 (v5.0)

此文档是我在该项目中所有工作的**唯一、最终的行为准则和事实来源**。我将在每次任务开始时重新加载并严格遵循。

## Ⅰ. 项目概述

**项目名称**: 基于DDD和清洁架构的加密货币监控系统  
**项目描述**: 一个复杂的、事件驱动的AI交易分析与执行系统  
**主要功能**: 加密货币市场数据监控、AI驱动的交易信号生成、风险管理、交易执行

## Ⅱ. 核心技术栈

### 2.1 后端技术栈
- **编程语言**: TypeScript
- **运行时**: Node.js (>=18.0.0)
- **Web框架**: Express.js
- **数据库**: PostgreSQL
- **ORM**: Prisma
- **依赖注入**: InversifyJS
- **测试框架**: Vitest
- **构建工具**: TypeScript Compiler + tsc-alias
- **代码质量**: ESLint + Prettier

### 2.2 核心依赖库
- **AI集成**: OpenAI (`openai`), Anthrop<PERSON> Claude (`@anthropic-ai/sdk`), Google Generative AI (`@google/generative-ai`)
- **加密货币API**: Binance API Node (`binance-api-node`)
- **实时通信**: Socket.IO (`socket.io`), WebSocket (`ws`)
- **缓存与队列**: Redis (`redis`), Bull (`bull`)
- **监控**: Prometheus Client (`prom-client`)
- **安全**: Helmet (`helmet`), bcrypt (`bcrypt`), JWT (`jsonwebtoken`)
- **数据验证**: Zod (`zod`), Express Validator (`express-validator`)
- **日志**: Winston (`winston`)
- **HTTP客户端**: Axios (`axios`)
- **工具库**: RxJS (`rxjs`), UUID (`uuid`), EventEmitter3 (`eventemitter3`)
- **任务调度**: Node-cron (`node-cron`)
- **API文档**: Swagger (`swagger-jsdoc`, `swagger-ui-express`)

## Ⅲ. 架构设计

### 3.1 整体架构模式
- **设计模式**: 领域驱动设计 (DDD) + 整洁架构 (Clean Architecture)
- **分层结构**: Domain → Application → Infrastructure → Presentation
- **模块化**: 按业务上下文 (Bounded Context) 组织

### 3.2 三层金字塔架构

#### 第一层: 业务系统 (Business Contexts)
**位置**: `src/contexts/`  
**完整系统** (10个):
1. `ai-reasoning` - AI推理系统
2. `learning` - 学习系统
3. `market-data` - 市场数据业务系统
4. `risk-management` - 风险管理系统
5. `shared` - 共享领域组件
6. `trading-execution` - 交易执行系统
7. `trading-signals` - 交易信号系统
8. `trend-analysis` - 趋势分析系统
9. `user-config` - 用户配置系统
10. `user-management` - 用户管理系统

#### 第二层: 应用服务层 (Application Services)
**位置**: `src/shared/application/`  
**核心服务**:
- 基础应用服务 (`BaseApplicationService`)
- DTO映射器 (`dto-mappers/`)
- 服务接口定义 (`interfaces/`)
- 系统集成协调器 (`services/system-integration-coordinator.ts`)

#### 第三层: 共享基础设施 (Shared Infrastructure)
**位置**: `src/shared/infrastructure/`  
**关键组件** (24个模块):
- **AI服务**: `ai/` - AI集成、向量数据库、实时推送
- **分析服务**: `analysis/` - 技术分析、模式识别
- **认证授权**: `auth/` - 身份验证和授权
- **配置管理**: `config/` - 统一配置、动态配置
- **数据处理**: `data-processing/`, `data-quality/` - 数据处理和质量控制
- **数据库**: `database/` - 数据库访问和查询管理
- **依赖注入**: `di/` - IoC容器配置
- **错误处理**: `error/` - 统一错误处理
- **健康检查**: `health/` - 系统健康监控
- **HTTP客户端**: `http/` - HTTP客户端工厂、熔断器、限流
- **LLM服务**: `llm/` - 大语言模型集成
- **日志系统**: `logging/` - 统一日志管理
- **市场数据**: `market-data/` - 市场数据基础设施
- **消息系统**: `messaging/` - 消息队列和事件
- **监控系统**: `monitoring/` - 性能监控、指标收集
- **订单管理**: `order/` - 订单处理基础设施
- **性能优化**: `performance/` - 性能分析和优化
- **风险管理**: `risk/` - 风险评估基础设施
- **技术指标**: `technical-indicators/` - 技术指标计算
- **测试工具**: `testing/` - 测试基础设施
- **工具类**: `utils/` - 通用工具函数
- **数据验证**: `validation/` - 数据验证框架
- **WebSocket**: `websocket/` - 实时通信

## Ⅳ. 数据库设计

### 4.1 数据库类型
- **数据库**: PostgreSQL
- **ORM**: Prisma
- **模式文件**: `backend-ts/prisma/schema.prisma`

### 4.2 核心数据模型

#### 交易相关模型
- **TradingAccounts** - 交易账户管理
- **TradingOrders** - 交易订单
- **TradingPositions** - 交易持仓
- **TradingSignals** - 交易信号
- **TradingStatistics** - 交易统计

#### 用户管理模型
- **Users** - 用户基础信息
- **UserProfiles** - 用户配置文件
- **UserLLMConfigs** - 用户AI模型配置
- **UserModelPreferences** - 用户模型偏好

#### 市场数据模型
- **Symbols** - 交易对符号
- **HistoricalData** - 历史价格数据
- **PriceData** - 实时价格数据
- **MarketDataSources** - 市场数据源

#### AI与分析模型
- **AiCallLogs** - AI调用日志
- **AiReasoningChains** - AI推理链
- **PromptTemplates** - 提示模板
- **SignalPerformanceTracking** - 信号性能跟踪

#### 风险管理模型
- **RiskAssessments** - 风险评估
- **RiskEvents** - 风险事件
- **SecurityEvents** - 安全事件

## Ⅴ. 开发规范

### 5.1 架构原则
- **领域模型优先**: 领域实体是业务逻辑的核心
- **依赖注入**: 使用InversifyJS进行依赖管理
- **仓储模式**: 通过Repository抽象数据访问
- **CQRS**: 命令查询职责分离

### 5.2 代码组织
- **按上下文分组**: 每个业务上下文独立组织
- **分层架构**: Domain → Application → Infrastructure → Presentation
- **共享组件**: 通用功能放在`src/shared`

### 5.3 命名规范
- **文件命名**: kebab-case (如: `user-management-service.ts`)
- **类命名**: PascalCase (如: `UserManagementService`)
- **接口命名**: 以`I`开头 (如: `IUserRepository`)
- **常量命名**: UPPER_SNAKE_CASE

## Ⅵ. 项目配置与路径

### 6.1 关键文件路径
- **项目根目录**: `/Users/<USER>/Ai/06/`
- **后端源码**: `/Users/<USER>/Ai/06/backend-ts/src/`
- **数据库模型**: `/Users/<USER>/Ai/06/backend-ts/prisma/schema.prisma`
- **依赖注入配置**: `/Users/<USER>/Ai/06/backend-ts/src/shared/infrastructure/di/`
- **包配置**: `/Users/<USER>/Ai/06/backend-ts/package.json`
- **环境配置**: `/Users/<USER>/Ai/06/backend-ts/.env`

### 6.2 核心目录结构
```
backend-ts/
├── src/
│   ├── contexts/          # 业务上下文 (10个)
│   │   ├── ai-reasoning/  # AI推理系统
│   │   ├── learning/      # 学习系统
│   │   ├── market-data/   # 市场数据
│   │   ├── risk-management/ # 风险管理
│   │   ├── shared/        # 共享领域
│   │   ├── trading-execution/ # 交易执行
│   │   ├── trading-signals/ # 交易信号
│   │   ├── trend-analysis/ # 趋势分析
│   │   ├── user-config/   # 用户配置
│   │   └── user-management/ # 用户管理
│   ├── shared/            # 共享基础设施
│   │   ├── application/   # 应用服务层
│   │   ├── domain/        # 领域层
│   │   └── infrastructure/ # 基础设施层 (24个模块)
│   ├── api/               # API路由
│   ├── express-app/       # Express应用配置
│   └── express-main.ts    # 应用入口
├── prisma/                # 数据库模式
├── tests/                 # 测试文件
├── scripts/               # 工具脚本
├── verification-reports/  # 验证报告
└── docs/                  # 项目文档
```

## Ⅶ. 常用命令

### 7.1 开发命令
```bash
# 开发模式启动
npm run dev

# 构建项目
npm run build

# 启动生产版本
npm run start
```

### 7.2 测试命令
```bash
# 运行所有测试
npm run test

# 单元测试
npm run test:unit

# 集成测试
npm run test:integration

# 测试覆盖率
npm run test:coverage
```

### 7.3 代码质量
```bash
# 代码检查
npm run lint

# 代码格式化
npm run format

# 重复代码检查
npm run check:duplication

# 综合质量检查
npm run quality
```

### 7.4 数据库管理
```bash
# 生成Prisma客户端
npm run db:generate

# 推送模式到数据库
npm run db:push

# 数据库迁移
npm run db:migrate

# 打开数据库管理界面
npm run db:studio
```

## Ⅷ. Gemini 工作规则与协作协议

此章节明确了我在本项目中的核心工作准则和与您协作的方式。

### A. 我的核心指令

1.  **`GEMINI.md` 是最高指令:** 此文件的所有内容是我行动的唯一、最终依据。在执行任何任务前，我都会重新加载并严格遵循。
2.  **领域模型优先:** 我理解项目的核心是领域模型，而非数据库。我的所有修复和重构都将以领域层的实体和接口为最高事实来源。
3.  **最小化、原子化修改:** 我会尽可能地将大型修改分解为小步骤，每一步都进行独立的编译和验证，确保过程的稳定和可控。
4.  **透明化思考过程:** 在执行任何有风险或复杂的修改前，我会向您清晰地阐述我的“修复计划”或“行动计划”，解释我为什么这么做，并征得您的同意。
5.  **绝不提交未验证的代码:** 我承诺，在交付任何代码修改前，都必须通过 **Ⅲ.C 完整验证流程** 中定义的所有检查。

### B. 安全与回滚协议

1.  **优先使用非破坏性操作:** 在有选择的情况下，我会优先使用如 `read_file`, `search_file_content`, `glob` 等非破坏性工具来分析问题。
2.  **解释破坏性操作:** 在执行任何可能修改文件系统的操作（如 `replace`, `write_file`, `run_shell_command`）之前，我必须向您解释该操作的目的和潜在影响。
3.  **错误立即停止原则:** 如果在修复或验证过程中遇到任何未预期的错误，我会立即停止后续操作，回滚当前步骤的修改，并重新进行"前置侦察"，分析问题根源。
4.  **保留事实原则:** 我承诺，绝不会删除或修改您添加到此文档中、且经验证为事实的项目信息。

### C. 上下文理解与决策

1.  **深度理解业务边界:** 我将深入理解项目中10个业务上下文（ai-reasoning、learning、market-data、risk-management、shared、trading-execution、trading-signals、trend-analysis、user-config、user-management）的职责边界和相互关系。
2.  **跨上下文影响识别:** 在进行任何修改前，我会分析该修改对其他业务上下文的潜在影响，确保不会破坏上下文间的协作关系。
3.  **系统整体一致性优先:** 我将始终以系统整体架构的一致性为最高优先级，避免为了解决局部问题而引入全局性的架构违规。
4.  **防止局部优化陷阱:** 我会警惕局部优化可能导致的全局问题，在优化单个模块时必须考虑对整个系统的影响。

### D. 质量保证机制

1.  **代码质量检查点:** 每次代码修改后，我将执行自动质量验证，包括编译检查、代码规范检查、测试覆盖率验证等。
2.  **架构合规性检查:** 确保所有修改都严格遵循DDD（领域驱动设计）和Clean Architecture（整洁架构）原则，维护分层架构的完整性。
3.  **依赖关系验证:** 在修改任何组件前，我会分析其依赖关系图，确保修改不会破坏现有的依赖注入配置和模块间的协作关系。
4.  **文档同步更新:** 确保代码变更与相关文档保持同步，包括API文档、架构文档、开发指南等，维护项目文档的准确性和时效性。

### E. 防重复实现与虚假实现铁律

**核心铁律：宁可不实现，也不能虚假实现！宁可使用现有组件，也不能重复造轮子！**

#### 1. 统一组件强制使用原则

**一个功能，一个实现，一个地方** - 这是项目的最高技术原则。

- **强制检查统一组件表:** 任何新功能实现前，我必须先查看项目中85+个统一组件索引表，确认功能是否已有实现
- **禁止重复实现:** 我严禁重新实现已有的技术指标计算、HTTP客户端、缓存系统、数据库操作、AI服务管理等任何已统一的功能
- **强制使用DI注入:** 我必须通过依赖注入获取统一服务，严禁直接创建第三方库实例
- **继承基类优先:** 我优先继承BaseApplicationService、BaseRepository、ExternalDataAdapterBase等基类，而非重新实现

#### 2. 绝对禁止的虚假实现模式

**我承诺绝不进行以下任何虚假实现行为：**

- **模拟数据生成:** 禁止使用`Math.random()`、固定假数据、简化的测试数据来代替真实业务数据
- **简化实现绕过:** 禁止为了快速完成而简化复杂的业务逻辑或技术实现
- **虚假修复:** 禁止表面修复问题而不解决根本原因，禁止掩盖错误
- **绕过验证:** 禁止跳过数据验证、错误处理、安全检查、业务规则验证
- **假装集成:** 禁止创建空方法、返回固定值、模拟API响应来假装完成集成
- **伪造测试通过:** 禁止修改测试用例来让失败的代码通过测试

#### 3. 严禁的文件命名和代码模式

**严禁的文件命名模式：**
```
❌ enhanced-xxx.ts
❌ optimized-xxx.ts  
❌ intelligent-xxx.ts
❌ xxx-v2.ts / xxx-new.ts / xxx-improved.ts
❌ comprehensive-analysis-routes.ts (在多个目录重复)
❌ 任何暗示"改进版"、"增强版"的命名
```

**严禁的代码实现模式：**
```typescript
// ❌ 禁止直接创建第三方库实例
new PrismaClient()
new Redis()
axios.create()
new WebSocket()

// ❌ 禁止重复实现已统一的核心功能
calculateRSI() / calculateMACD() / calculateSMA()
healthCheck() / performanceCheck()
mapToResponse() / toDto() / fromEntity()
cacheGet() / cacheSet() / cacheDelete()

// ❌ 禁止使用随机数（虚假实现的明显标志）
Math.random()

// ❌ 禁止在应用代码中直接导入第三方库
import axios from 'axios';
import Redis from 'ioredis';
import WebSocket from 'ws';
import { PrismaClient } from '@prisma/client';
```

#### 4. 强制验证检查流程

**开发前强制检查（我必须执行）：**
- [ ] 查看统一组件索引表确认功能是否已在85+个组件中实现
- [ ] 检查`src/shared/infrastructure/types/unified-interfaces.ts`是否有相关接口（466行统一接口）
- [ ] 确认文件命名不使用任何禁止前缀或模式
- [ ] 验证新实现的必要性和合理性

**开发中强制检查（实时验证）：**
- [ ] 运行`npm run lint`进行ESLint规则检查
- [ ] 运行`npm run detect:redundant`检查重复实现
- [ ] 确保使用统一组件而非第三方库直接调用
- [ ] 验证所有服务都通过DI注入获取

**提交前强制验证（最终门禁）：**
- [ ] 运行`npm run validate:known-duplications`验证无重复实现回归
- [ ] 运行`npx ts-node scripts/ci/redundancy-gate-check.ts`通过门禁检查
- [ ] 确保所有测试通过且无虚假实现
- [ ] 验证代码符合真实业务逻辑要求

#### 5. 真实数据与业务逻辑铁律

**数据真实性强制要求：**
- **使用RealDataValidator:** 我必须使用统一数据验证器确保所有数据的真实性和有效性
- **禁止模拟业务数据:** 所有业务数据必须来自真实数据源，禁止使用假数据、测试数据代替
- **AI决策验证:** 使用AIDecisionValidator验证所有AI生成的决策和建议
- **价格数据验证:** 使用PriceValidationEngine验证所有价格数据的合理性和时效性

**业务逻辑完整性要求：**
- **完整实现业务规则:** 我不得简化、跳过或绕过任何复杂的业务逻辑
- **完整错误处理:** 必须使用UnifiedErrorHandler处理所有可能的错误情况
- **事务完整性保证:** 所有数据库操作必须保证ACID特性和业务事务完整性
- **安全检查完整:** 不得绕过任何认证、授权、数据验证、输入检查等安全机制

#### 6. 统一组件强制使用清单

**网络通信（强制使用）：**
- HTTP请求：`BaseHttpClient` + `ExternalDataAdapterBase`
- WebSocket连接：继承`BaseWebSocketAdapter`
- API适配器：继承外部数据适配器基类

**数据存储（强制使用）：**
- 缓存操作：`MultiTierCacheService`
- 数据库操作：继承`BaseRepository`
- 数据映射：`UnifiedDataMapper`

**数据分析（强制使用）：**
- 技术指标：`UnifiedTechnicalIndicatorCalculator`
- 模式识别：`PatternRecognitionService`
- 权重分配：`DynamicWeightingService`
- 多时间框架：`MultiTimeframeService`
- AI服务：`UnifiedAIServiceManager`

**系统管理（强制使用）：**
- 配置管理：`UnifiedConfigManager`
- 健康检查：实现`IHealthCheckProvider`接口
- 性能监控：`UnifiedPerformanceMonitoringService`
- 错误处理：`UnifiedErrorHandler`

**应用架构（强制使用）：**
- 应用服务：继承`BaseApplicationService`
- DTO映射：`UnifiedDtoMapperRegistry`
- 领域实体：继承`BaseEntity`
- 值对象：继承`BaseValueObject`

#### 7. 违规检测与立即处理

**自动检测机制：**
- **ESLint实时检测:** 自动检测并阻止禁止模式和重复实现
- **CI/CD门禁:** 自动阻止包含重复实现或虚假实现的代码合并
- **每日质量报告:** 生成重复实现和代码质量检测报告

**我的违规处理承诺：**
1. **立即停止:** 一旦发现任何违规模式，我立即停止当前操作
2. **详细报告:** 我会详细说明违规原因和正确的实现方式
3. **提供解决方案:** 我会指导使用正确的统一组件和实现模式
4. **验证修复:** 我确保修复后的代码完全符合规范要求

**项目历史教训：**
项目曾因重复实现遭受巨大损失，包括：
- 20个重复实现问题导致的技术债务
- 160个文件命名问题
- 152个接口定义重复
- 大量测试代码重复和Mock数据重复

#### 8. 命名规范强制要求

**核心原则：统一命名，零容忍混乱！**

**TypeScript命名标准（强制执行）：**
- **接口/类/模型：** 严格使用PascalCase（如：`UserProfile`、`TradingSignal`）
- **变量/函数/属性：** 严格使用camelCase（如：`tradingSymbol`、`calculateRisk`）
- **常量：** 严格使用CONSTANT_CASE（如：`MAX_RETRY_COUNT`、`DEFAULT_TIMEOUT`）
- **文件名：** 使用kebab-case（如：`risk-calculator.service.ts`）

**Prisma Schema命名铁律：**
- **绝对禁止@map注解：** 模型名必须与表名完全一致，不允许任何映射
- **字段统一camelCase：** 所有字段必须使用camelCase，禁止snake_case
- **模型名PascalCase：** 所有模型名必须使用PascalCase

**严禁的命名模式：**
```typescript
// ❌ 绝对禁止的命名
GLOBAL_PRISMA_CLIENT  // 变量应该用camelCase
levels, horizons      // 常量应该用CONSTANT_CASE
vol_i, vol_j         // 变量应该用camelCase: volI, volJ
block_height         // 字段应该用camelCase: blockHeight
aggregatedVar95_1d   // 应该用camelCase: aggregatedVar95OneDay
depthResponse_json   // 应该用camelCase: depthResponseJson

// ❌ 禁止在Prisma中使用@map
model User {
  id String @id
  userName String @map("user_name")  // 绝对禁止！
}
```

**正确的命名示例：**
```typescript
// ✅ 正确的命名
const GLOBAL_PRISMA_CLIENT = prisma;     // 常量用CONSTANT_CASE
const LEVELS = [1, 2, 3];               // 常量用CONSTANT_CASE
const HORIZONS = ['1d', '7d', '30d'];   // 常量用CONSTANT_CASE

const volI = data[i];                   // 变量用camelCase
const volJ = data[j];                   // 变量用camelCase
const blockHeight = response.height;    // 字段用camelCase
const aggregatedVarNinetyFiveOneDay = calc(); // 变量用camelCase
const depthResponseJson = JSON.parse(); // 变量用camelCase

// ✅ 正确的Prisma模型
model User {
  id       String @id
  userName String        // 直接使用camelCase，无@map
  email    String
}
```

**命名规范强制验证：**
- **开发时检查：** 运行`npm run lint`自动检测命名违规
- **提交前验证：** 运行命名规范检测脚本确保零违规
- **CI/CD门禁：** 自动阻止包含命名违规的代码合并
- **定期审计：** 每周运行命名规范检测报告

**历史问题教训：**
项目曾发现21个命名规范问题，包括：
- 20个低严重性命名违规（变量、常量命名不规范）
- 1个中等严重性问题（字段名不一致）
- 多个文件中存在snake_case与camelCase混用

**我的命名规范承诺：**
我承诺严格遵循TypeScript社区标准和项目命名规范，绝不引入任何命名混乱。每次代码修改都将通过命名规范检查，确保项目命名的高度一致性和专业性。

**我的最终承诺：**
我将以项目的惨痛教训为警示，严格遵循"一个功能，一个实现，一个地方"的铁律，绝不重蹈覆辙。我承诺宁可花更多时间理解和使用现有组件，也绝不为了图快而创造重复实现或虚假实现。
