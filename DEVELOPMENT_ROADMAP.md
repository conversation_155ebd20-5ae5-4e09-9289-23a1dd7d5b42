# 交易生态系统开发与演进路线图 (V1)

## 1. 核心战略与指导原则

本路线图旨在指导团队，以一种务实、高效、可衡量的方式，分阶段完成“AI驱动的自主进化型交易生态系统”的开发，并为未来战略性地复用其核心基础设施，孵化“软技能学习平台”做好准备。

**指导原则：**

1.  **先固本，后枝叶**: 优先确保核心交易流程（感知-认知-决策-执行）的绝对稳定与可靠，再逐步完善学习、AI等高级功能。
2.  **价值驱动**: 每个阶段的开发任务都应以创造明确的、可验证的业务价值为目标。
3.  **架构一致**: 严格遵守已建立的DDD和分层架构原则，任何新功能都必须融入现有上下文，杜绝野蛮生长。
4.  **接口先行**: 在开发新功能或模块间交互时，优先定义清晰的接口（Interface）和数据传输对象（DTO），实现并行开发和无缝集成。
5.  **为复用而设计**: 在开发 `shared` 和其他核心上下文时，时刻保持“领域无关”的思维，有意识地将通用逻辑与特定业务逻辑分离。

---

## 2. 开发阶段一：铸造“钢铁之躯”——完成核心交易闭环 (预计 0-6 个月)

**目标**: 打造一个功能完备、绝对可靠的半自动化交易辅助系统。此阶段结束时，系统应能作为一个高质量的“交易员终端”独立运行，其AI能力主要体现在“决策辅助”和“风险预警”上。

| 优先级 | 任务 | 所属上下文 | 关键产出与验收标准 |
| :--- | :--- | :--- | :--- |
| **P0** | **`learning` 上下文 MVP** | `learning` | - 创建 `learning` 上下文的基本目录结构（domain, application, infrastructure）。<br>- 将现有的 `AdaptiveLearningEngine` 重构并迁移至 `learning/domain/services`。<br>- 创建 `LearningApplicationService`，提供统一的接口供其他模块调用，以记录交易结果（盈亏、滑点、开平仓理由等）。<br>- **验收标准**: `trading-execution` 模块在每一笔模拟交易完成后，能成功调用 `LearningApplicationService` 记录该笔交易的完整结果。 |
| **P0** | **`risk-management` 增强** | `risk-management` | - 完善 `RealTimeRiskMonitor`，确保其能基于真实的价格波动，对关键指标（如账户回撤、持仓VaR）进行后台监控。<br>- 实现当实时风险突破阈值时，能通过事件总线（EventBus）发出“高风险警报”事件。<br>- **验收标准**: 在模拟盘中，当账户净值回撤超过预设值（如5%）时，系统能立即在日志中记录高风险警报。 |
| **P1** | **`trading-execution` 健壮性** | `trading-execution` | - 全面测试 `ExecutionEngineRouter` 在模拟盘和实盘（若有API Key）之间的切换逻辑。<br>- 完善 `syncPositionsFromBinance` 等状态同步功能，增加异常处理和重试机制。<br>- 压力测试 `RiskEnforcementEngine`，确保其在任何情况下都能正确阻止违规交易。<br>- **验收标准**: 能够连续7x24小时稳定运行模拟盘交易，无内存泄漏，状态同步准确无误。 |
| **P1** | **`ai-reasoning` 生产化** | `ai-reasoning` | - 替换代码中所有的模拟实现（mock），接入真实的LLM提供商（如OpenAI, Gemini）。<br>- 完善 `LLMRouter`，使其能基于成本和性能要求，真实地选择和调用不同的AI模型。<br>- 丰富和优化 `ReasoningChain` 中的提示工程（Prompt Engineering），提升AI分析的深度和准确性。<br>- **验收标准**: `trend-analysis` 和 `risk-management` 模块能成功调用 `ai-reasoning` 服务，获得由真实LLM生成的分析洞察。 |
| **P2** | **UI/API 层完善** | `presentation` (各模块) | - 为所有核心服务提供完整的OpenAPI (Swagger) 文档。<br>- 开发一个基础的前端监控面板（Dashboard），用于展示账户状态、持仓信息、风险警报和AI导师建议。<br>- **验收标准**: 产品经理或测试人员可以通过前端面板，直观地监控和理解系统的运行状态。 |

---

## 3. 开发阶段二：点燃“智慧之火”——激活自主进化能力 (预计 6-12 个月)

**目标**: 将系统从一个“聪明的工具”升级为一个能“自主学习和进化”的有机体。此阶段的核心是激活 `learning` 上下文，并将其与所有其他模块深度联动，形成完整的反馈闭环。

| 优先级 | 任务 | 所属上下文 | 关键产出与验收标准 |
| :--- | :--- | :--- | :--- |
| **P0** | **学习闭环：参数优化** | `learning`, `trend-analysis`, `risk-management` | - `LearningApplicationService` 在分析完一批交易结果后，能够调用 `AdaptiveLearningEngine` 计算出应优化的参数（如：趋势判断的置信度阈值、风险模型中的波动率权重等）。<br>- `trend-analysis` 和 `risk-management` 服务在启动时，能从 `learning` 上下文获取**最新优化过的参数**，而不是使用硬编码的默认值。<br>- **验收标准**: 系统在运行一周后，其核心决策参数与初始默认值相比，发生了可度量的、有数据支撑的自动调整。 |
| **P1** | **学习闭环：策略演进** | `learning`, `trading-signals` | - 扩展 `learning` 上下文，使其能评估不同**交易策略（`ITradingStrategy`）** 在不同市场周期下的表现（夏普比率、最大回撤等）。<br>- 增强 `StrategySelector`，使其在选择策略时，不仅考虑当前市场和用户画像，还要**优先选择近期历史表现更优的策略**。<br>- **验收标准**: 在回测或模拟环境中，系统能够自动识别出某个策略已失效，并逐渐降低其使用权重，转而使用表现更好的其他策略。 |
| **P2** | **AI导师 (`AI-Mentor`) 完善** | `ai-reasoning`, `presentation` | - 开发“决策对比与评分”功能。当用户在模拟盘中交易后，系统能将用户的行为与AI的“最优决策”进行对比，并给出评分和改进建议。<br>- 在前端实现一个“AI复盘”界面，用自然语言和图表，向用户解释某次盈利或亏损的关键原因。<br>- **验收标准**: 用户可以通过复盘功能，清晰地理解自己某次模拟交易的得失，并获得可行的改进方案。 |

---

## 4. 开发阶段三：抽象与剥离——沉淀“核心决策框架” (预计 12-15 个月)

**目标**: 在金融平台功能稳定、价值得到验证后，开始为第二个项目做准备。此阶段的重点是技术层面的“重构”和“抽象”，将通用能力从金融业务中剥离出来。

| 优先级 | 任务 | 所属上下文 | 关键产出与验收标准 |
| :--- | :--- | :--- | :--- |
| **P0** | **核心框架 (`Core Framework`) 抽象** | `shared`, `ai-reasoning`, `user-management`, `user-config` | - 创建一个新的代码仓库或Mono-repo中的独立包，命名为 `CoreDecisionFramework`。<br>- 将 `shared` 目录下的绝大部分通用基础设施代码迁移至此框架中。<br>- 将 `ai-reasoning`, `user-management`, `user-config` 这三个与具体业务领域弱相关的上下文，重构并迁移到核心框架中。<br>- **验收标准**: 原有的金融交易平台项目，可以通过NPM包或内部依赖的方式，直接引用 `CoreDecisionFramework`，并保持所有功能正常运行。 |
| **P1** | **定义通用接口** | `CoreDecisionFramework` | - 在核心框架中，定义一套领域无关的、更加抽象的核心接口。例如，将 `ITradingStrategy` 抽象为 `IDecisionStrategy`，将 `MarketDataContext` 抽象为 `IEnvironmentContext`。<br>- **验收标准**: 新的接口足够通用，可以同时满足金融交易和软技能学习这两个截然不同场景的需求。 |

---

## 5. 开发阶段四：开启“第二曲线”——孵化软技能学习平台 (15个月后)

**目标**: 基于沉淀的“核心决策框架”，以极高的效率和极低的边际成本，快速开发并推出软技能学习平台的MVP版本。

| 优先级 | 任务 | 所属上下文 | 关键产出与验收标准 |
| :--- | :--- | :--- | :--- |
| **P0** | **新项目初始化** | `soft-skills-platform` (新项目) | - 基于 `CoreDecisionFramework` 初始化新项目，瞬间获得用户、AI、配置、日志、DI等所有底层能力。<br>- **验收标准**: 新项目能在一天内完成搭建，并实现用户注册登录功能。 |
| **P0** | **新“器官”开发：`interaction-data` 和 `context-analysis`** | `soft-skills-platform` | - 参照 `market-data` 和 `trend-analysis` 的架构，快速开发出用于处理和分析对话文本、识别情绪和意图的新上下文。<br>- **验收标准**: 能够将一段对话文本输入系统，系统能以结构化的形式输出对话中的关键信息点和情绪转折点。 |
| **P1** | **`InteractionSimulator` 开发** | `soft-skills-platform` | - 复用 `trading-execution` 的“双轨制”和“风控”理念，开发与AI虚拟人进行对话的模拟器。<br>- **验收标准**: 用户可以与AI虚拟人完成一次完整的、有分支选择的对话，并在对话结束后得到初步的反馈。 |

通过遵循此路线图，我们不仅能确保第一个项目的成功，更能战略性地放大我们已投入的研发成本，为公司开辟全新的、充满想象力的增长曲线。这是一个将卓越技术转化为持久商业优势的行动纲领。
