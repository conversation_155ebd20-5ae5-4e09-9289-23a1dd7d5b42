/**
 * 基础功能单元测试
 * 验证核心模块的基本功能，确保代码可以正常运行
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Container } from 'inversify';
import { TYPES } from '../../src/shared/infrastructure/di/types';

describe('基础功能测试', () => {
  let container: Container;

  beforeEach(() => {
    container = new Container();
  });

  describe('依赖注入类型定义', () => {
    it('应该正确定义TYPES对象', () => {
      expect(TYPES).toBeDefined();
      expect(typeof TYPES).toBe('object');
    });

    it('应该包含核心类型', () => {
      expect(TYPES.Logger).toBeDefined();
      expect(TYPES.Database).toBeDefined();
      expect(TYPES.Cache).toBeDefined();
    });

    it('应该包含共享服务类型', () => {
      expect(TYPES.Shared).toBeDefined();
      expect(TYPES.Shared.PatternRecognitionService).toBeDefined();
      expect(TYPES.Shared.DynamicWeightingService).toBeDefined();
      expect(TYPES.Shared.ConfidenceCalculator).toBeDefined();
    });

    it('应该包含市场数据类型', () => {
      expect(TYPES.MarketData).toBeDefined();
      expect(TYPES.MarketData.MarketDataApplicationService).toBeDefined();
      expect(TYPES.MarketData.HistoricalDataRepository).toBeDefined();
    });

    it('应该包含AI推理类型', () => {
      expect(TYPES.AIReasoning).toBeDefined();
      expect(TYPES.AIReasoning.AIReasoningApplicationService).toBeDefined();
    });
  });

  describe('基础数据结构', () => {
    it('应该能创建基本的市场数据结构', () => {
      const marketData = {
        symbol: 'BTCUSDT',
        price: 50000,
        timestamp: new Date(),
        volume: 1000
      };

      expect(marketData.symbol).toBe('BTCUSDT');
      expect(marketData.price).toBe(50000);
      expect(marketData.timestamp).toBeInstanceOf(Date);
      expect(marketData.volume).toBe(1000);
    });

    it('应该能创建基本的K线数据结构', () => {
      const klineData = {
        open: 49000,
        high: 51000,
        low: 48000,
        close: 50000,
        volume: 1000,
        timestamp: new Date()
      };

      expect(klineData.open).toBe(49000);
      expect(klineData.high).toBe(51000);
      expect(klineData.low).toBe(48000);
      expect(klineData.close).toBe(50000);
      expect(klineData.volume).toBe(1000);
      expect(klineData.timestamp).toBeInstanceOf(Date);
    });

    it('应该能创建基本的交易信号结构', () => {
      const signal = {
        symbol: 'BTCUSDT',
        action: 'BUY',
        confidence: 0.85,
        price: 50000,
        timestamp: new Date(),
        reason: '技术指标显示买入信号'
      };

      expect(signal.symbol).toBe('BTCUSDT');
      expect(signal.action).toBe('BUY');
      expect(signal.confidence).toBe(0.85);
      expect(signal.price).toBe(50000);
      expect(signal.timestamp).toBeInstanceOf(Date);
      expect(signal.reason).toBe('技术指标显示买入信号');
    });
  });

  describe('基础计算功能', () => {
    it('应该能计算简单移动平均线', () => {
      const prices = [10, 12, 14, 16, 18, 20];
      const period = 3;
      
      // 计算最后3个价格的移动平均
      const lastPrices = prices.slice(-period);
      const sma = lastPrices.reduce((sum, price) => sum + price, 0) / period;
      
      expect(sma).toBe(18); // (16 + 18 + 20) / 3 = 18
    });

    it('应该能计算价格变化百分比', () => {
      const oldPrice = 100;
      const newPrice = 110;
      
      const changePercent = ((newPrice - oldPrice) / oldPrice) * 100;
      
      expect(changePercent).toBe(10);
    });

    it('应该能计算波动率', () => {
      const prices = [100, 105, 95, 110, 90];
      const mean = prices.reduce((sum, price) => sum + price, 0) / prices.length;
      
      const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
      const volatility = Math.sqrt(variance);
      
      expect(volatility).toBeGreaterThan(0);
      expect(typeof volatility).toBe('number');
    });
  });

  describe('环境配置', () => {
    it('应该能访问环境变量', () => {
      // 这些是在测试环境中应该可用的基本环境变量
      expect(process.env.NODE_ENV).toBeDefined();
    });

    it('应该能创建Date对象', () => {
      const now = new Date();
      expect(now).toBeInstanceOf(Date);
      expect(now.getTime()).toBeGreaterThan(0);
    });

    it('应该能使用JSON序列化', () => {
      const data = { test: 'value', number: 123 };
      const json = JSON.stringify(data);
      const parsed = JSON.parse(json);
      
      expect(parsed.test).toBe('value');
      expect(parsed.number).toBe(123);
    });
  });

  describe('异步功能', () => {
    it('应该能处理Promise', async () => {
      const promise = new Promise(resolve => {
        setTimeout(() => resolve('success'), 10);
      });
      
      const result = await promise;
      expect(result).toBe('success');
    });

    it('应该能处理async/await', async () => {
      const asyncFunction = async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'async result';
      };
      
      const result = await asyncFunction();
      expect(result).toBe('async result');
    });
  });

  describe('错误处理', () => {
    it('应该能捕获同步错误', () => {
      expect(() => {
        throw new Error('测试错误');
      }).toThrow('测试错误');
    });

    it('应该能捕获异步错误', async () => {
      const asyncError = async () => {
        throw new Error('异步错误');
      };
      
      await expect(asyncError()).rejects.toThrow('异步错误');
    });
  });

  describe('模拟功能', () => {
    it('应该能使用vitest的mock功能', () => {
      const mockFn = vi.fn();
      mockFn('test');
      
      expect(mockFn).toHaveBeenCalledWith('test');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('应该能mock对象方法', () => {
      const obj = {
        method: vi.fn().mockReturnValue('mocked')
      };
      
      const result = obj.method();
      expect(result).toBe('mocked');
      expect(obj.method).toHaveBeenCalled();
    });
  });
});

describe('核心模块导入测试', () => {
  it('应该能导入TYPES定义', () => {
    expect(() => {
      const types = TYPES;
      expect(types).toBeDefined();
    }).not.toThrow();
  });

  it('应该能导入Container', () => {
    expect(() => {
      const container = new Container();
      expect(container).toBeDefined();
    }).not.toThrow();
  });
});

describe('数据验证功能', () => {
  it('应该能验证数字范围', () => {
    const validateRange = (value: number, min: number, max: number) => {
      return value >= min && value <= max;
    };
    
    expect(validateRange(50, 0, 100)).toBe(true);
    expect(validateRange(-10, 0, 100)).toBe(false);
    expect(validateRange(150, 0, 100)).toBe(false);
  });

  it('应该能验证字符串格式', () => {
    const validateSymbol = (symbol: string) => {
      return /^[A-Z]{3,10}USDT?$/.test(symbol);
    };
    
    expect(validateSymbol('BTCUSDT')).toBe(true);
    expect(validateSymbol('ETHUSDT')).toBe(true);
    expect(validateSymbol('btcusdt')).toBe(false);
    expect(validateSymbol('INVALID')).toBe(false);
  });

  it('应该能验证时间戳', () => {
    const validateTimestamp = (timestamp: Date) => {
      const now = new Date();
      const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      
      return timestamp >= oneYearAgo && timestamp <= now;
    };
    
    const validTimestamp = new Date();
    const invalidTimestamp = new Date('2020-01-01');
    
    expect(validateTimestamp(validTimestamp)).toBe(true);
    expect(validateTimestamp(invalidTimestamp)).toBe(false);
  });
});
