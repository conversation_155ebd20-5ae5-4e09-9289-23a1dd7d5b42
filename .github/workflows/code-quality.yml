name: 🔍 代码质量检查

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  code-duplication-check:
    runs-on: ubuntu-latest
    name: 重复代码检测
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
      
    - name: 📦 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 📚 安装依赖
      run: |
        cd backend-ts
        npm ci
        
    - name: 🔍 运行重复代码检测
      run: |
        cd backend-ts
        echo "🔍 开始重复代码检测..."
        npm run check:duplication

    - name: 🔍 运行综合重复分析
      run: |
        cd backend-ts
        echo "🔍 运行综合重复代码分析..."
        npm run check:duplication:comprehensive

    - name: 🔍 运行冗余实现检测
      run: |
        cd backend-ts
        echo "🔍 检测冗余实现..."
        npm run detect:redundant
      continue-on-error: true
      id: duplication-check
      
    - name: 📄 上传重复代码报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: duplication-report
        path: |
          backend-ts/reports/jscpd/
          backend-ts/docs/重复实现综合分析与清理执行计划.md
          backend-ts/docs/目前发现的重复实现.md
        retention-days: 30
        
    - name: 💬 评论PR (如果发现重复代码)
      if: github.event_name == 'pull_request' && steps.duplication-check.outcome == 'failure'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const reportPath = 'backend-ts/reports/jscpd/jscpd-report.json';

          let reportContent = '未能读取重复代码报告';

          if (fs.existsSync(reportPath)) {
            try {
              const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
              const duplicates = report.duplicates || [];
              const statistics = report.statistics || {};

              reportContent = '## 📊 重复代码检测结果\\n\\n' +
                '**统计信息:**\\n' +
                '- 总重复率: ' + (statistics.percentage || 'N/A') + '%\\n' +
                '- 重复行数: ' + (statistics.lines || 'N/A') + '\\n' +
                '- 重复文件数: ' + (statistics.files || 'N/A') + '\\n' +
                '- 检测到的重复块: ' + duplicates.length + '\\n\\n' +
                '**前5个重复代码块:**\\n' +
                duplicates.slice(0, 5).map((dup, index) =>
                  (index + 1) + '. **' + (dup.format || 'unknown') + '** (' + (dup.lines || 0) + ' 行)\\n' +
                  '   - 文件: ' + (dup.firstFile?.name || 'unknown') + ':' + (dup.firstFile?.start || 0) + '\\n' +
                  '   - 文件: ' + (dup.secondFile?.name || 'unknown') + ':' + (dup.secondFile?.start || 0) + '\\n'
                ).join('') +
                (duplicates.length > 5 ? '\\n还有 ' + (duplicates.length - 5) + ' 个重复代码块...' : '');
            } catch (error) {
              reportContent = '报告解析失败: ' + error.message;
            }
          }
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 🚨 发现重复代码！

这个PR包含超过阈值的重复代码，需要在合并前清理。

<details>
<summary>📋 重复代码检测报告</summary>

${reportContent}

</details>

**🛠️ 清理指导:**
1. 查看完整报告: 下载 \`duplication-report\` 构建产物
2. 重构重复代码到共享模块 (\`src/shared\`)
3. 使用统一的基础设施组件
4. 遵循DRY原则 (Don't Repeat Yourself)

**📚 参考文档:**
- [杜绝重复代码系统性解决方案](docs/杜绝重复代码系统性解决方案.md)
- [编码规范与最佳实践](backend-ts/docs/编码规范与最佳实践.md)

**重复代码是技术债务的主要来源，必须及时清理！**`
          });
          
    - name: ❌ 如果重复代码超过阈值则失败
      if: steps.duplication-check.outcome == 'failure'
      run: |
        echo "🚨 发现过多重复代码！构建失败。"
        echo "当前重复率超过了5%的阈值。"
        echo "请重构重复代码并使用统一的基础设施组件。"
        exit 1

  architecture-compliance:
    runs-on: ubuntu-latest
    name: 架构合规检查
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
      
    - name: 📦 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 📚 安装依赖
      run: |
        cd backend-ts
        npm ci
        
    - name: 🏗️ 运行架构检查
      run: |
        cd backend-ts
        echo "🏗️ 开始架构合规检查..."
        
        # 检查仓储架构
        if [ -f "scripts/check-repository-architecture.sh" ]; then
          bash scripts/check-repository-architecture.sh
        fi
        
        # 检查WebSocket重构
        if [ -f "scripts/verify-websocket-refactor.sh" ]; then
          bash scripts/verify-websocket-refactor.sh
        fi
        
        # 检查Prisma客户端使用
        if [ -f "scripts/check-prisma-client-usage.sh" ]; then
          bash scripts/check-prisma-client-usage.sh
        fi
      continue-on-error: true
      id: architecture-check
      
    - name: 🧹 运行ESLint架构规则
      run: |
        cd backend-ts
        echo "🧹 运行ESLint架构规则检查..."
        npm run lint
      continue-on-error: true
      id: eslint-check
      
    - name: ❌ 如果架构检查失败
      if: steps.architecture-check.outcome == 'failure' || steps.eslint-check.outcome == 'failure'
      run: |
        echo "🚨 架构合规检查失败！"
        echo "请确保代码遵循项目架构规范。"
        exit 1

  quality-gate:
    runs-on: ubuntu-latest
    needs: [code-duplication-check, architecture-compliance]
    if: always()
    name: 质量门禁
    
    steps:
    - name: 📊 汇总质量检查结果
      run: |
        echo "📊 代码质量检查完成"
        echo "重复代码检测: ${{ needs.code-duplication-check.result }}"
        echo "架构合规检查: ${{ needs.architecture-compliance.result }}"
        
        if [[ "${{ needs.code-duplication-check.result }}" == "failure" ]]; then
          echo "🚨 重复代码检测失败！"
          exit 1
        fi
        
        if [[ "${{ needs.architecture-compliance.result }}" == "failure" ]]; then
          echo "🚨 架构合规检查失败！"
          exit 1
        fi
        
        echo "✅ 所有质量检查通过！"
