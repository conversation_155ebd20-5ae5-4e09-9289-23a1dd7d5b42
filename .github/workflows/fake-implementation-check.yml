name: 🚨 虚假实现检测

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点运行
    - cron: '0 2 * * *'

jobs:
  detect-fake-implementations:
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
      
    - name: 📦 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 📚 安装依赖
      run: npm ci
      
    - name: 🔍 运行虚假实现检测
      run: |
        echo "🚨 开始虚假实现检测..."
        npx ts-node scripts/monitoring/fake-implementation-detector.ts
      continue-on-error: true
      id: fake-detection
      
    - name: 📄 上传检测报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: fake-implementation-report
        path: docs/虚假实现检测报告.md
        retention-days: 30
        
    - name: 💬 评论PR (如果有虚假实现)
      if: github.event_name == 'pull_request' && steps.fake-detection.outcome == 'failure'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = 'docs/虚假实现检测报告.md';
          
          if (fs.existsSync(path)) {
            const report = fs.readFileSync(path, 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🚨 发现虚假实现！
              
这个PR包含虚假实现，必须在合并前清理。

<details>
<summary>📋 检测报告</summary>

${report}

</details>

**⚠️ 重要提醒**:
- 虚假实现可能导致重大金融损失
- 必须使用真实数据和真实实现
- 请参考 [虚假实现清单文档](docs/虚假实现清单文档.md) 了解详情

**🛠️ 清理指导**:
1. 移除所有使用 Math.random() 的生产代码
2. 替换硬编码的默认值为真实实现
3. 移除"简化实现"、"临时实现"标记
4. 确保所有AI服务使用真实模型
5. 确保所有Repository有真实数据库操作

**零容忍虚假数据是我们的底线！**`
            });
          }
          
    - name: ❌ 如果发现虚假实现则失败
      if: steps.fake-detection.outcome == 'failure'
      run: |
        echo "🚨 发现虚假实现！构建失败。"
        echo "请查看检测报告并清理所有虚假实现。"
        echo "零容忍虚假数据是我们的底线！"
        exit 1

  # 额外的安全检查
  security-scan:
    runs-on: ubuntu-latest
    needs: detect-fake-implementations
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
      
    - name: 🔒 检查敏感关键词
      run: |
        echo "🔍 检查敏感关键词..."
        
        # 检查Math.random()在生产代码中的使用
        if grep -r "Math\.random()" src/ --include="*.ts" --exclude-dir=test --exclude-dir=tests; then
          echo "❌ 发现生产代码中使用Math.random()！"
          exit 1
        fi
        
        # 检查模拟相关关键词
        if grep -r -i "模拟\|mock\|fake\|dummy" src/ --include="*.ts" --exclude-dir=test --exclude-dir=tests; then
          echo "⚠️ 发现可疑的模拟相关关键词"
          echo "请确认这些不是虚假实现"
        fi
        
        # 检查简化实现
        if grep -r -i "简化实现\|临时实现\|占位符" src/ --include="*.ts"; then
          echo "❌ 发现简化/临时实现！"
          exit 1
        fi
        
        echo "✅ 安全检查通过"

  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    needs: detect-fake-implementations
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
      
    - name: 📦 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 📚 安装依赖
      run: npm ci
      
    - name: 🧹 运行ESLint
      run: |
        echo "🧹 运行代码质量检查..."
        npx eslint src/ --ext .ts --format=compact
      continue-on-error: true
      
    - name: 🔍 TypeScript类型检查
      run: |
        echo "🔍 运行TypeScript类型检查..."
        npx tsc --noEmit
      continue-on-error: true

  # 通知检查结果
  notify-results:
    runs-on: ubuntu-latest
    needs: [detect-fake-implementations, security-scan, code-quality]
    if: always()
    
    steps:
    - name: 📊 汇总检查结果
      run: |
        echo "📊 虚假实现检测完成"
        echo "检测结果: ${{ needs.detect-fake-implementations.result }}"
        echo "安全扫描: ${{ needs.security-scan.result }}"
        echo "代码质量: ${{ needs.code-quality.result }}"
        
        if [[ "${{ needs.detect-fake-implementations.result }}" == "failure" ]]; then
          echo "🚨 发现虚假实现，必须立即清理！"
        elif [[ "${{ needs.detect-fake-implementations.result }}" == "success" ]]; then
          echo "✅ 未发现虚假实现，系统安全！"
        fi
