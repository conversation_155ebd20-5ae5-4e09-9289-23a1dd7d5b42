## 📋 Pull Request 描述

### 🎯 变更概述
<!-- 简要描述本次PR的主要变更内容 -->

### 🔗 相关Issue
<!-- 关联的Issue编号，如：Closes #123 -->

### 📝 变更类型
<!-- 请勾选适用的变更类型 -->
- [ ] 🐛 Bug修复
- [ ] ✨ 新功能
- [ ] 💄 UI/样式更新
- [ ] ♻️ 代码重构
- [ ] 📝 文档更新
- [ ] 🔧 配置变更
- [ ] 🧪 测试相关
- [ ] 🚀 性能优化
- [ ] 🔒 安全修复

---

## 🛡️ 代码质量检查清单

### ✅ 基础质量要求
- [ ] **我已确认本次变更未引入不必要的代码重复**
- [ ] **所有可复用的逻辑已尽可能抽象到 `shared` 模块中**
- [ ] **本次变更遵循了项目既定的架构和设计模式**
- [ ] **代码通过了所有ESLint规则检查**
- [ ] **代码通过了TypeScript类型检查**

### 🏗️ 架构合规检查
- [ ] **使用了统一的基础设施组件（如BaseHttpClient、RepositoryBaseService等）**
- [ ] **所有服务通过DI容器管理，未直接实例化**
- [ ] **遵循了camelCase/PascalCase命名规范**
- [ ] **数据库表名和字段名使用camelCase，未使用@@map指令**
- [ ] **未在业务逻辑中使用Math.random()或硬编码数据**

### 🚫 反虚假实现检查
- [ ] **未使用"简化实现"、"临时实现"、"占位符"等标记**
- [ ] **错误处理中抛出明确错误，未返回默认值**
- [ ] **所有数据验证使用真实验证逻辑**
- [ ] **所有API调用使用真实的外部服务**
- [ ] **所有计算逻辑基于真实算法实现**

### 🧪 测试要求
- [ ] **为新功能编写了相应的单元测试**
- [ ] **为API变更编写了集成测试**
- [ ] **所有测试用例通过**
- [ ] **测试覆盖率满足要求**
- [ ] **测试使用真实数据和真实逻辑**

### 📚 文档要求
- [ ] **更新了相关的API文档**
- [ ] **更新了README或其他相关文档**
- [ ] **添加了必要的代码注释**
- [ ] **遵循了项目的文档规范**

---

## 🔍 技术细节

### 🛠️ 使用的统一组件
<!-- 列出本次PR中使用的统一基础设施组件 -->
- [ ] UnifiedTechnicalIndicatorCalculator
- [ ] BaseHttpClient
- [ ] RepositoryBaseService
- [ ] MultiTierCacheService
- [ ] UnifiedErrorHandler
- [ ] UnifiedConfigManager
- [ ] BaseWebSocketAdapter
- [ ] 其他：___________

### 📊 性能影响
<!-- 描述本次变更对系统性能的影响 -->
- [ ] 无性能影响
- [ ] 性能提升：___________
- [ ] 性能影响已评估并可接受：___________

### 🔒 安全考虑
<!-- 描述本次变更的安全影响 -->
- [ ] 无安全影响
- [ ] 已进行安全评估
- [ ] 涉及敏感数据处理，已采取适当措施

---

## 🧪 测试说明

### 测试环境
<!-- 描述测试环境和测试数据 -->

### 测试步骤
<!-- 详细的测试步骤 -->
1. 
2. 
3. 

### 预期结果
<!-- 描述预期的测试结果 -->

---

## 📸 截图/演示
<!-- 如果适用，请提供截图或演示视频 -->

---

## 🚨 特别注意事项
<!-- 需要审查者特别关注的内容 -->

---

## ✅ 审查者检查清单

### 代码审查要点
- [ ] **代码逻辑正确且高效**
- [ ] **未发现重复代码实现**
- [ ] **架构设计合理**
- [ ] **错误处理完善**
- [ ] **安全考虑充分**
- [ ] **性能影响可接受**
- [ ] **测试覆盖充分**
- [ ] **文档更新及时**

### 质量门禁
- [ ] **CI/CD检查全部通过**
- [ ] **重复代码检测通过（<5%）**
- [ ] **ESLint检查通过**
- [ ] **架构合规检查通过**
- [ ] **虚假实现检测通过**
- [ ] **所有测试通过**

---

**⚠️ 重要提醒：**
- 重复代码是技术债务的主要来源，必须杜绝
- 虚假实现可能导致重大损失，零容忍
- 请确保遵循 [编码规范与最佳实践](backend-ts/docs/编码规范与最佳实践.md)
- 如有疑问，请参考 [杜绝重复代码系统性解决方案](docs/杜绝重复代码系统性解决方案.md)
